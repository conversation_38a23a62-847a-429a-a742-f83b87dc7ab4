const { exec } = require("child_process");
const path = require("path");
const fs = require("fs");

const files = [
  "./app/assets/tailwind.css",
  "./app/assets/auth.css",
  "./app/assets/auth-style/sign-in.css",
  "./app/assets/ag-grid.css",
  "./app/assets/antd.css",
  "./app/assets/apexcharts.css",
  "./app/assets/calendar.css",
  "./app/assets/light-gallery.css",
  "./app/assets/quill-editor.css",
  "./app/assets/style.css",
  // "./app/assets/gantt-menu-style/dhtmlx.css",
  "./app/assets/gantt-menu-style/dhtmlxmenu_dhx_terrace.css",
];

const outputDir = "./app/assets/minify";

if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

files.forEach((file) => {
  const filename = path.basename(file, ".css");
  const outputFile = path.join(outputDir, `${filename}.style.css`);
  exec(`npx postcss ${file} -o ${outputFile}`, (err, stdout, stderr) => {
    if (err) {
      console.error(`Error processing ${file}:`, stderr);
    }
    //  else {
    //   console.log(`Processed ${file} to ${outputFile}`);
    // }
  });
});
