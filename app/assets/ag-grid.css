.ag-theme-alpine {
  --ag-alpine-active-color: #223558;
  --ag-input-focus-border-color: rgba(34, 53, 88, 0.4);
  --ag-cell-horizontal-padding: var(--ag-grid-size) !important;
  --ag-row-hover-color: rgba(34, 53, 88, 0.08) !important;
  --ag-font-family: "Open Sans", sans-serif !important;
}

.ag-center-cols-viewport
  .ag-row-focus:not(
    .ag-center-cols-viewport .ag-row-no-focus.ag-row-hover
  ):hover,
.ag-center-cols-viewport
  .ag-row-no-focus:not(
    .ag-center-cols-viewport .ag-row-no-focus.ag-row-hover
  ):hover {
  background-color: var(--ag-row-hover-color);
}

.ag-theme-alpine.ag-row-small {
  --ag-grid-size: 5px;
  --ag-list-item-height: 15px;
}

.ag-cell.ag-cell-last-left-pinned:not(.ag-cell-range-right):not(
    .ag-cell-range-single-cell
  )
  > .ag-cell-wrapper {
  @apply h-full;
}

.ag-theme-alpine:not(.ag-row-small) .ag-cell-value,
.ag-theme-alpine:not(.ag-row-small) .ag-group-value {
  line-height: 19px !important;
  padding-top: 9px;
  padding-bottom: 9px;
  font-size: 13px;
}

.ag-theme-alpine:not(.ag-row-small) .ag-cell-value .ant-typography,
.ag-theme-alpine:not(.ag-row-small) .ag-cell-value {
  @apply text-[#212529] dark:text-white/90;
}

.ag-cell-wrap-text {
  white-space: pre-wrap !important;
  word-break: unset !important;
}

.ag-theme-alpine .ag-root-wrapper-body.ag-focus-managed.ag-layout-normal {
  @apply h-fit;
}

.ag-row .ag-cell .ag-icons {
  @apply hidden;
}

.ag-row:hover .ag-cell .ag-icons {
  @apply flex;
}

.ag-theme-alpine .ag-overlay {
  @apply relative;
}

.ag-theme-alpine .ag-overlay-no-rows-center {
  padding: var(--ag-grid-size);
}

.ag-row .ag-cell .ag-buttons {
  @apply flex justify-around items-center gap-1 h-full;
}

.ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within {
  @apply border-transparent;
}

.ag-root-wrapper,
.ag-ltr .ag-cell {
  @apply !border-0;
}

.dark .ag-header {
  @apply bg-dark-500 text-white/90 border-b !border-dark-900;
}

.ag-header {
  @apply !border-[#e8e8e8] border-b;
}

.dark .ag-theme-alpine .ag-header-row {
  @apply text-white/90;
}

.dark .ag-root-wrapper,
.dark .ag-sticky-top,
.dark .ag-dnd-ghost {
  @apply bg-dark-800;
}

.dark .ag-theme-alpine .ag-row-odd {
  @apply bg-dark-600;
}

.dark .ag-row {
  @apply bg-dark-800 text-white/80;
}

/* .ag-header-cell-resize {
  @apply hidden right-0 w-0
} */

.ag-header-cell:not(.ag-header-classification):not(.ag-header-name):not(
    .header-custom-height-remove .ag-header-cell.ag-floating-filter
  ),
.ag-header-group-cell {
  @apply py-[7px] xl:px-2.5 px-1.5;
}

.ag-header-cell.ag-header-classification,
.ag-header-cell.ag-header-name {
  @apply py-[7px] xl:pr-2.5 pr-1.5 pl-8;
}

.ag-pinned-left-header,
.ag-pinned-right-header {
  @apply border-none;
}

.ag-header-row.ag-header-row-column {
  @apply min-h-[32px] !h-auto;
}

.multi-list-table
  .ag-body-viewport
  .ag-full-width-container
  .ag-header
  .ag-header-cell,
.bid-list-table
  .ag-body-viewport
  .ag-full-width-container
  .ag-header
  .ag-header-cell {
  @apply !h-8;
}

.multi-list-table .ag-body-viewport .ag-full-width-container .ag-header,
.bid-list-table .ag-body-viewport .ag-full-width-container .ag-header {
  @apply !min-h-[32px] !h-8 !border-0;
}

.bid-list-table
  .ag-header-row
  .ag-header-cell:first-child
  .ag-header-cell-comp-wrapper {
  @apply pl-7;
}

.multi-list-table
  .ag-cell-wrapper
  > *:not(.ag-cell-value):not(.ag-group-value) {
  @apply h-[18px];
}

.multi-list-table .ag-header {
  @apply !border-white;
}

.multi-list-table .ag-header-container,
.multi-list-table .ag-header-container > div.ag-header-row {
  @apply !w-full;
}

.multi-list-table .ag-group-expanded .ag-icon-tree-open,
.multi-list-table .ag-group-contracted .ag-icon-tree-closed {
  @apply w-3.5 min-w-[14px] flex items-center before:hidden text-sm transition-all ease-in-out duration-300 font-extralight !h-3.5 bg-[#A2B1CB] !text-primary-900 rounded-full !m-0 !p-0 justify-center hover:bg-deep-orange-500;
}

.multi-list-table .ag-group-expanded .ag-icon-tree-open:hover svg,
.multi-list-table .ag-group-contracted .ag-icon-tree-closed:hover svg {
  @apply fill-white;
}

.multi-list-table .ag-group-expanded .ag-icon-tree-open,
.multi-list-table .ag-group-contracted .ag-icon-tree-closed {
  box-shadow: 0px 0px 0px 1px rgb(255, 255, 255),
    0px 0px 0px 2px rgb(162, 177, 203);
}

.multi-list-table .ag-row-group-indent-0.ag-cell-wrapper.ag-row-group {
  @apply items-center pl-1;
}

.multi-list-table .ag-group-expanded .ag-icon-tree-open:hover,
.multi-list-table .ag-group-contracted .ag-icon-tree-closed:hover {
  box-shadow: 0px 0px 0px 1px rgb(255, 255, 255),
    0px 0px 0px 2px rgb(255, 84, 0);
}

.multi-list-table .ag-root-wrapper.ag-layout-auto-height,
.static-multi-table .ag-body .ag-details-row .ag-root-wrapper .ag-root {
  @apply p-2 pb-0.5 bg-white;
}

.ant-collapse .multi-list-table .ag-details-row-auto-height.ag-details-row {
  @apply !rounded-bl-lg !border-l;
}

.ag-theme-alpine .static-table .ag-layout-auto-height .ag-center-cols-container,
.ag-theme-alpine .static-table .ag-layout-auto-height .ag-center-cols-viewport {
  @apply min-h-fit;
}

.ant-collapse .multi-list-table .ag-center-cols-clipper .ag-row-group-expanded {
  @apply rounded-tl-lg border-l;
}

.multi-list-table .ag-center-cols-container .ag-row-group-expanded {
  @apply !bg-[#F1F2F3] border-b-0 rounded-tl-lg dark:!bg-dark-600;
}

.multi-list-table .ag-details-row-auto-height.ag-details-row {
  @apply !bg-[#F1F2F3] pl-8 pt-1 pb-2.5 pr-4 dark:!bg-dark-600;
}

.ag-theme-alpine .ag-row-odd {
  @apply bg-[#f9f9f9];
}

.multi-list-table .ag-theme-alpine:not(.ag-row-small) .ag-cell-value,
.ag-theme-alpine:not(.ag-row-small) .ag-group-value {
  @apply !px-2.5;
}

.multi-list-table .ag-center-cols-container .ag-row:last-child {
  @apply !px-2.5;
}

.ag-theme-alpine .ag-cell {
  @apply h-full grid xl:px-2.5 px-1.5;
}

.ag-theme-alpine .ag-cell .ag-cell-wrapper {
  @apply truncate;
}

.ag-body .ag-body-viewport {
  @apply !w-full;
}

.ag-theme-alpine:not(.ag-row-small) .ag-cell-value,
.ag-theme-alpine:not(.ag-row-small) .ag-group-value {
  @apply !p-0;
}

.ag-theme-alpine:not(.ag-row-small) .ag-cell-value {
  @apply whitespace-nowrap;
}

.ag-row {
  @apply border-b-0;
}

.ag-theme-alpine .ag-row,
.ag-theme-alpine-dark .ag-row {
  font-size: var(--ag-font-size);
}

.ag-cell-label-container {
  @apply py-0;
}

.ag-header-center .ag-cell-label-container,
.ag-header-right .ag-cell-label-container {
  flex-flow: row;
}

.ag-center-aligned-cell,
.ag-cell-center {
  @apply text-center;
}

.ag-header-right .ag-header-cell-label {
  flex-flow: row-reverse;
}

.ag-header-group-cell.ag-header-right {
  @apply justify-end;
}

.ag-header-center .ag-header-cell-label {
  @apply justify-center;
}

.ag-header-cell-label {
  @apply font-semibold;
}

.ag-header.ag-pivot-off:not(
    .header-custom-height-remove .ag-header.ag-pivot-off
  ) {
  @apply !min-h-[32px] rounded-t;
}

.ag-body-viewport.ag-layout-normal {
  overflow-y: overlay;
}

.ag-center-cols-viewport {
  overflow-x: overlay;
}

.ag-horizontal-right-spacer.ag-scroller-corner {
  @apply !w-0 !min-w-0 !max-w-0;
}

.ag-body-horizontal-scroll,
.ag-layout-normal .ag-body .ag-body-vertical-scroll,
.ag-body-viewport.ag-layout-normal::-webkit-scrollbar,
.ag-body-viewport.ag-layout-normal::-webkit-scrollbar-thumb,
.ag-body-viewport.ag-layout-normal::-webkit-scrollbar-track,
.customs-table-scroll .ag-body::-webkit-scrollbar,
.customs-table-scroll .ag-body::-webkit-scrollbar-thumb,
.customs-table-scroll .ag-body::-webkit-scrollbar-track {
  @apply opacity-0 invisible;
}

.ag-layout-normal:hover .ag-body-horizontal-scroll,
.ag-layout-normal:hover .ag-body .ag-body-vertical-scroll,
.ag-body-viewport.ag-layout-normal:hover::-webkit-scrollbar,
.ag-body-viewport.ag-layout-normal:hover::-webkit-scrollbar-thumb,
.ag-body-viewport.ag-layout-normal:hover::-webkit-scrollbar-track,
.customs-table-scroll .ag-body:hover::-webkit-scrollbar,
.customs-table-scroll .ag-body:hover::-webkit-scrollbar-thumb,
.customs-table-scroll .ag-body:hover::-webkit-scrollbar-track {
  @apply opacity-100 visible;
}

/* -------BEGIN:CUSTOM SCROLLBAR IN FIREFOX----------- */
@-moz-document url-prefix() {
  .ag-body-horizontal-scroll-viewport,
  .ag-body-vertical-scroll-viewport {
    scrollbar-color: #b8b8b8 #e8e4e4;
    scrollbar-width: thin;
  }
  .ag-body-vertical-scroll,
  .ag-body-vertical-scroll-viewport {
    @apply !w-2 !max-w-2 !min-w-2;
  }
}

.ag-body-horizontal-scroll-viewport {
  @apply h-1 min-h-[4px] max-h-[4px];
}

.header-height-48 .ag-header-row.ag-header-row-column,
.group-header-48 {
  @apply !h-12;
}

.group-header-cell .ag-header-group-text {
  @apply font-semibold;
}

.client-access-table .ag-header {
  @apply hidden;
}

.ag-full-width-container .ag-row-loading {
  @apply flex-col !h-auto;
}

.ant-collapse .ag-center-cols-clipper .ag-row-group-expanded {
  @apply rounded-tl-xl;
}

.ant-collapse .ag-row-group-expanded,
.dynamic-multi-list-table.multi-list-table .ag-row-group-expanded {
  @apply before:!border-l before:!border-solid before:!border-primary-900 before:absolute before:top-0 before:left-0 before:h-full before:w-2.5 before:bg-transparent before:rounded-tl-lg;
}

.ant-collapse .ag-full-width-container[role="rowgroup"] .ag-details-row,
.ant-collapse .ag-center-cols-clipper .ag-row-group-expanded,
.dynamic-multi-list-table.multi-list-table
  .ag-details-row-auto-height.ag-details-row {
  @apply bg-white border-l-2 border-solid border-primary-900;
}

.ant-collapse .ag-full-width-container[role="rowgroup"] .ag-details-row,
.dynamic-multi-list-table.multi-list-table
  .ag-full-width-container[role="rowgroup"]
  .ag-details-row {
  @apply rounded-bl-lg;
}

.multi-list-table .ag-details-row .ag-root-wrapper.ag-layout-normal {
  @apply rounded-lg;
}

/* .ant-collapse .ag-full-width-container[role="rowgroup"] .ag-details-row::after, */
.dynamic-multi-list-table.multi-list-table
  .ag-details-row-auto-height.ag-details-row:after {
  content: var(--tw-content);
  background-image: linear-gradient(45deg, #223558, transparent);
}

/* .ant-collapse .ag-full-width-container[role="rowgroup"] .ag-details-row::after, */
.dynamic-multi-list-table.multi-list-table
  .ag-details-row-auto-height.ag-details-row:after {
  @apply w-[25px] h-0.5 top-[26px] left-0 absolute;
}

.multi-list-table .ag-sticky-top .ag-row {
  @apply bg-[#f1f2f3] rounded-tl-md;
}

.ag-header .ag-header-cell-text {
  @apply text-black dark:text-white;
}

.ag-cell-wrapper > *:not(.ag-cell-value):not(.ag-group-value) {
  @apply h-auto;
}

.ag-ltr .ag-row-drag {
  @apply mr-0;
}

.ag-move-cell {
  @apply !pr-0;
}

.ag-move-cell .ag-icon {
  @apply text-xl;
}

.checkbox-ml-none .ag-ltr .ag-row-group-leaf-indent {
  @apply ml-0;
}

.checkbox-ml-none .ag-ltr .ag-row-drag,
.checkbox-ml-none .ag-ltr .ag-selection-checkbox,
.checkbox-ml-none .ag-ltr .ag-group-expanded,
.checkbox-ml-none .ag-ltr .ag-group-contracted {
  @apply mr-1.5;
}

.font-small-set .ag-cell-value {
  @apply !text-xs;
}
/* START:CSS FOR COPONENTS FOR NEW STRUCTURE */

body
  .ag-theme-alpine
  .static-table
  .ag-cell:not(
    body .ag-theme-alpine .static-table .ag-cell.ag-move-cell,
    body .ag-theme-alpine .static-table .ag-cell.no-space-td,
    body .ag-theme-alpine .static-table .ad-call-pl-0,
    body .ag-theme-alpine .static-table .ad-call-pr-0
  ) {
  @apply xl:!px-2.5 !px-1.5 items-center grid;
}

body .ag-theme-alpine .static-table .ag-cell.ag-move-cell {
  @apply !pr-0 items-center grid;
}

body .ag-theme-alpine .static-table .ag-cell.ag-move-cell {
  @apply !pr-0 items-center grid;
}

body .ag-theme-alpine .static-table .ad-call-pl-0 {
  @apply xl:!pr-2.5 !pl-0 pr-1.5 items-center grid;
}

/* body .ag-theme-alpine .static-table .header-hidden {
  @apply !hidden;
} */

body .ag-theme-alpine .static-table .to-bill-header-cell {
  @apply xl:!w-[170px] !w-[176px] z-10;
}

body .ag-theme-alpine .static-table .ad-call-pr-0 {
  @apply xl:!pl-2.5 !pr-0 pl-1.5 items-center grid;
}

.ag-theme-alpine .static-table .ag-cell-right {
  @apply flex justify-end;
}

.ag-theme-alpine .static-table .ag-cell-left {
  @apply flex justify-start;
}

.supplier-provided-table
  .ag-header-row:not(:first-child)
  .ag-header-cell:not(.ag-header-span-height.ag-header-span-total) {
  @apply border-0;
}

.multi-list-table
  .ag-details-row-auto-height.ag-details-row
  .ag-body.ag-layout-auto-height
  .ag-cell-value {
  @apply flex items-center;
}

.list-view-table .ag-overlay:not(.list-view-table .ag-overlay.ag-hidden) {
  @apply pointer-events-auto overflow-auto;
}

.list-view-table
  .ag-overlay:not(.list-view-table .ag-overlay.ag-hidden)
  .ag-overlay-wrapper {
  @apply min-h-60;
}

.list-view-table .group-item-table .ag-overlay-wrapper {
  min-height: 0 !important;
}
.sov_change_order_table .ag-invisible {
  @apply w-0 !mr-0;
}

.multi-list-table
  .ag-theme-alpine
  .ag-layout-auto-height
  .ag-center-cols-viewport {
  @apply min-h-fit;
}

.multi-list-table
  .ag-theme-alpine
  .ag-overlay-no-rows-wrapper.ag-layout-auto-height {
  @apply pt-0;
}

.table-input-field input {
  @apply px-1 py-0.5 w-full bg-transparent;
}

.movable-table
  .ag-header-cell-label:not(
    .movable-table .ag-header-cell-label:has(.ag-header-cell-text:empty)
  ) {
  @apply cursor-pointer;
}

.ag-react-container {
  @apply w-full;
}

body .ag-theme-alpine .static-table .ag-cell.qty-unit-column {
  @apply !pl-0 -ml-[9px] z-[1];
}

body
  .ag-theme-alpine
  .static-table
  .ag-cell.quantity-column.ag-cell-inline-editing,
.ag-cell-inline-editing.ag-cell-focus
  .ag-cell-edit-wrapper:has(> .ag-rich-select) {
  @apply shadow-none bg-transparent;
}

.ag-cell-inline-editing {
  @apply !flex;
}

.ag-cell-inline-editing.ag-cell-focus .ag-input-field-input {
  @apply !border-primary-900/50 !border !shadow-none;
}

.ag-cell-inline-editing.ag-cell-focus
  .ag-rich-select-value.ag-picker-field-wrapper {
  @apply !border-primary-900/50 !border !shadow-none;
}

.ag-cell-inline-editing.ag-cell-focus .ag-select-edit-field .ant-select {
  border: 1px solid #22355880 !important;
  border-radius: 4px !important;
}

.table-no-header .ag-header {
  @apply !hidden;
}

.preferences-table
  .hover-table-row
  [col-id="item_shown"]
  .items_shown_dropdown {
  @apply opacity-0 invisible transition-all;
}

.preferences-table
  .hover-table-row
  [col-id="item_shown"]:hover
  .items_shown_dropdown,
.items_shown_dropdown:has(
    > .ant-space .ant-space-item .ant-select.ant-select-open
  ) {
  @apply opacity-100 visible;
}

.preferences-table
  .hover-table-row
  [col-id="template_view_display"]
  .template_select_dropdown
  .ant-select {
  @apply !border-transparent;
}

.preferences-table
  .hover-table-row
  [col-id="template_view_display"]:hover
  .template_select_dropdown
  .ant-select,
.preferences-table
  .hover-table-row
  [col-id="template_view_display"]
  .template_select_dropdown
  .ant-select.ant-select-open {
  @apply !border-[#CED4DA] bg-white;
}

.preferences-table
  .hover-table-row
  [col-id="template_view_display"]
  .template_select_dropdown
  .ant-select
  .ant-select-arrow {
  @apply !opacity-0;
}

.preferences-table
  .hover-table-row
  [col-id="template_view_display"]:hover
  .template_select_dropdown
  .ant-select
  .ant-select-arrow,
.preferences-table
  .hover-table-row
  [col-id="template_view_display"]
  .template_select_dropdown
  .ant-select.ant-select-open
  .ant-select-arrow {
  @apply !opacity-100;
}

.template_select_dropdown_option .ant-select-item-group {
  @apply font-semibold text-primary-900 bg-primary-900/5 text-13;
}

.template_select_dropdown_option .ant-select-item.ant-select-item-option {
  @apply !text-black text-13;
}

.template_select_dropdown_option
  .ant-select-item.ant-select-item-option.ant-select-item-option-active,
.template_select_dropdown_option
  .ant-select-item.ant-select-item-option.ant-select-item-option-selected {
  @apply !text-primary-900;
}

.static-multi-table .ag-center-cols-container {
  @apply mt-0;
}

.multi-list-table.static-multi-table .ag-row-group-expanded,
.dynamic-multi-list-table.multi-list-table .ag-row-group-expanded {
  @apply before:!border-l-2;
}

.static-multi-table .ag-body .ag-details-row {
  @apply !pt-0 !pb-4 !pl-8 !pr-4 !bg-[#F1F2F3];
}

.expand-icon .ag-cell-wrapper {
  @apply w-8 !p-0 justify-center;
}

.expand-icon .ag-cell-wrapper .ag-group-contracted,
.expand-icon .ag-cell-wrapper .ag-group-expanded {
  @apply m-0;
}

.custom-move-icon-set .ag-drag-handle.ag-row-drag {
  @apply opacity-0 z-10;
}

.multi-list-table
  .ag-group-expanded
  .ag-icon-tree-open:hover
  .custom-tooltip.ant-tooltip,
.multi-list-table
  .ag-group-contracted
  .ag-icon-tree-open:hover
  .custom-tooltip.ant-tooltip {
  @apply visible;
}

.multi-list-table .cell-renderer-icon:hover .custom-tooltip.ant-tooltip {
  @apply visible;
}

.multi-list-table .expand-icon,
.multi-list-table .expand-icon .ag-cell-wrapper,
.multi-list-table .expand-icon .ag-cell-value,
.ag-theme-alpine .multi-list-table .ag-cell .ag-cell-expandable {
  @apply overflow-visible;
}

.custom-tooltip.ant-tooltip {
  @apply z-[1070] absolute invisible;
}

.custom-tooltip.ant-tooltip .ant-tooltip-arrow {
  transform: translateX(-50%) translateY(100%) rotate(180deg);
  z-index: 1;
  display: block;
  pointer-events: none;
  width: 16px;
  height: 16px;
  overflow: hidden;
}

.custom-tooltip.ant-tooltip .ant-tooltip-inner {
  min-width: 1em;
  min-height: 28px;
  padding: 6px 8px;
  font-weight: 400;
  color: #fff;
  text-align: start;
  text-decoration: none;
  word-wrap: break-word;
  background-color: rgba(0, 0, 0, 0.85);
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.custom-tooltip.ant-tooltip .ant-tooltip-arrow::before {
  position: absolute;
  bottom: 0;
  inset-inline-start: 0;
  width: 16px;
  height: 8px;
  background: black;
  clip-path: polygon(
    1.6568542494923806px 100%,
    50% 1.6568542494923806px,
    14.34314575050762px 100%,
    1.6568542494923806px 100%
  );
  clip-path: path(
    "M 0 8 A 4 4 0 0 0 2.82842712474619 6.82842712474619 L 6.585786437626905 3.0710678118654755 A 2 2 0 0 1 9.414213562373096 3.0710678118654755 L 13.17157287525381 6.82842712474619 A 4 4 0 0 0 16 8 Z"
  );
  content: "";
}

.custom-tooltip.ant-tooltip .ant-tooltip-arrow::after {
  content: "";
  position: absolute;
  width: 8.970562748477143px;
  height: 8.970562748477143px;
  bottom: 0;
  inset-inline: 0;
  margin: auto;
  border-radius: 0 0 2px 0;
  transform: translateY(50%) rotate(-135deg);
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.05);
  z-index: 0;
  background: transparent;
}

.custom-tooltip.ant-tooltip.tooltip-placement-right .ant-tooltip-arrow {
  top: 50%;
  transform: translateY(-50%) translateX(-100%) rotate(-90deg);
}

.ag-checkbox-input-wrapper:not(.ag-checkbox-input-wrapper.ag-disabled)
  .ag-checkbox-input {
  @apply cursor-pointer;
}

.upload-csv-table .ag-body {
  @apply max-h-[calc(100dvh-490px)] overflow-y-auto;
}

.customs-table-height .ag-body,
.customs-table-height .ag-body-viewport {
  @apply max-h-[600px];
}

.customs-vertical-scroll-auto .ag-body-vertical-scroll {
  @apply h-auto;
}

.full-drawer-items-table-height .ag-body {
  @apply max-h-[calc(100dvh-213px)] overflow-y-auto;
}

.full-drawer-items-table-height .ag-body {
  @apply max-h-[calc(100dvh-213px)] overflow-y-auto;
}

.ag-checkbox-input-wrapper.ag-disabled,
.ag-checkbox-input-wrapper.ag-disabled .ag-checkbox-input {
  @apply !cursor-no-drop;
}

.ag-header-cell
  .ant-checkbox-wrapper.ant-checkbox-wrapper-disabled
  .ant-checkbox-disabled {
  @apply opacity-50;
}

.hidden-price {
  @apply invisible;
}

.hidden-price.visible {
  @apply !visible;
}

.cell-visible {
  visibility: visible;
}
.cell-invinsible {
  visibility: hidden;
}
.ag-center-cols-viewport .ag-row-focus:hover,
.ag-center-cols-viewport .ag-row-no-focus:hover .hover-visibility {
  visibility: visible !important;
  transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
}
.ag-center-cols-viewport
  .ag-row-focus:not(
    .ag-center-cols-viewport .ag-row-no-focus.ag-row-hover
  ):hover
  .hover-visibility {
  visibility: visible !important;
  transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
}
.hoverablerow:hover .hover-visibility {
  visibility: visible !important;
  transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
}

.ag-body-horizontal-scroll,
.ag-body-horizontal-scroll-viewport,
.ag-body-horizontal-scroll-container {
  @apply !h-1.5 !max-h-1.5 !min-h-1.5;
}

.ag-body-viewport::-webkit-scrollbar,
.ag-root ::-webkit-scrollbar {
  @apply !h-1.5 !w-1.5;
}

.ag-body-vertical-scroll-viewport,
.ag-body-vertical-scroll-container,
.ag-body-vertical-scroll {
  @apply !w-1.5 !max-w-1.5 !min-w-1.5;
}

.mleso-items-table .cell-checkbox {
  visibility: hidden;
}

.mleso-items-table .ag-row:hover .cell-checkbox,
.mleso-items-table .cell-checkbox:has(.ag-checked) {
  visibility: visible;
}

.group-item-table.multi-list-table .ag-icon-tree-closed,
.group-item-table.multi-list-table .ag-icon-tree-open {
  @apply bg-primary-900 fill-white;
}

.group-item-table.multi-list-table .ag-icon-tree-closed,
.group-item-table.multi-list-table .ag-icon-tree-open {
  box-shadow: 0px 0px 0px 1px rgb(255, 255, 255), 0px 0px 0px 2px #223558;
}

.ag-cell-inline-editing .ag-select-edit-field .ant-select {
  @apply !pl-2.5;
}

.mleso-items-table .ag-header-row-column-filter .ag-header-cell {
  @apply py-0;
}

.mleso-items-table
  .ag-header-row-column-filter
  .ag-header-cell
  .cf-new-input-field:has(.ant-input-affix-wrapper-focused),
.mleso-items-table
  .ag-header-row-column-filter
  .ag-header-cell
  .cf-new-select-field:has(.ant-select-open) {
  @apply !border-primary-900/50;
}

.mleso-items-table
  .ag-header-row:not(:first-child)
  .ag-header-cell:not(.ag-header-span-height.ag-header-span-total),
.mleso-items-table
  .ag-header-row:not(:first-child)
  .ag-header-group-cell.ag-header-group-cell-with-group {
  @apply border-[#E5E7EB];
}

.disabled-selectfield .ant-select-selector {
  @apply !bg-transparent;
}

.mleso-items-table
  .ag-header-row-column-filter
  .ant-input-affix-wrapper
  .ant-input-suffix
  .ant-input-clear-icon-hidden {
  @apply w-0 m-0;
}

.upload-csv-table-drawer .cf-new-select-field .ant-select-selection-item {
  @apply !text-black !font-semibold !text-13;
}

.ag-task-parent-row {
  @apply !bg-[#fff500] hover:!bg-[#22355814];
}

@media (min-width: 767px) {
  .upload-csv-table-drawer .ag-body {
    @apply max-h-[calc(100dvh-480px)] overflow-y-auto;
  }
}

@media (min-width: 920px) {
  .upload-csv-table-drawer .ag-body {
    @apply max-h-[calc(100dvh-420px)] overflow-y-auto;
  }
}

@media (min-width: 1120px) {
  .upload-csv-table-drawer .ag-body {
    @apply max-h-[calc(100dvh-410px)] overflow-y-auto;
  }
}

@media (min-width: 1550px) {
  .upload-csv-table-drawer .ag-body {
    @apply max-h-[calc(100dvh-390px)] overflow-y-auto;
  }
}
