:root {
  --fa-secondary-opacity: 1;
  --financial-option-check: "";
}

::-webkit-scrollbar-track {
  background-color: rgba(196, 196, 196, 0.2);
}

.dark::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.2);
}

.hover-scroll {
  overflow-x: overlay;
}

::-webkit-scrollbar {
  @apply w-1.5 h-1.5;
}

@media (min-width: 768px) {
  .hover-scroll::-webkit-scrollbar,
  .hover-scroll::-webkit-scrollbar-thumb,
  .hover-scroll::-webkit-scrollbar-track {
    @apply opacity-0 invisible;
  }
}

.hover-scroll:hover::-webkit-scrollbar,
.hover-scroll:hover::-webkit-scrollbar-thumb,
.hover-scroll:hover::-webkit-scrollbar-track,
.form-check input:checked + .custom-checkbox-click svg {
  @apply opacity-100 visible;
}

::-webkit-scrollbar-thumb {
  @apply bg-black/30 rounded-3xl;
}

.dark .hover-scroll::-webkit-scrollbar-thumb {
  @apply bg-white/20;
}

/* -------BEGIN:CUSTOM SCROLLBAR IN FIREFOX----------- */
@-moz-document url-prefix() {
  .hover-scroll,
  .cf-textarea-field .ant-input,
  .overflow-y-auto,
  .overflow-x-auto,
  .fr-dropdown-menu .fr-dropdown-wrapper:not(.submit-approval-froala-editor .fr-dropdown-menu .fr-dropdown-wrapper) {
    scrollbar-color: #b8b8b8 #e8e4e4;
    scrollbar-width: thin;
  }
  .fr-dropdown-menu .fr-dropdown-wrapper:not(.submit-approval-froala-editor .fr-dropdown-menu .fr-dropdown-wrapper){
    @apply overflow-y-auto overflow-x-hidden
  }
  .google-map .gm-style-iw-d {
    @apply pr-5;
  }
  input.ant-input-disabled[type="number"] {
    -moz-appearance: textfield;
  }
}

body {
  @apply text-13;
}

.ant-typography,
.ant-radio-button-wrapper {
  font-family: "Open Sans", sans-serif !important;
}

.input-label {
  @apply before:peer-focus:!border-t after:peer-focus:!border-t before:peer-focus:!border-l after:peer-focus:!border-r before:mt-[5.5px] after:mt-[5.5px] peer-placeholder-shown:text-primary-500 peer-disabled:peer-placeholder-shown:text-primary-500 !text-primary-400 peer-focus:!text-primary-900 before:border-primary-400 peer-focus:before:!border-primary-900 after:border-primary-400 peer-focus:after:!border-primary-900 capitalize;
}

.cf-switch {
  @apply bg-[#DEDBDB] min-w-[33px] h-[18px] dark:bg-[#a1a1a1] rounded-xl;
}

.cf-switch .ant-switch-handle {
  @apply top-0 start-0;
}

.cf-switch.ant-switch-small .ant-switch-handle {
  @apply h-4 w-4;
}

.cf-switch.ant-switch-checked.success {
  @apply bg-[#0fb33d33] hover:bg-[#0fb33d33];
}

.ant-switch-checked.primary {
  @apply dark:bg-dark-900;
}

.cf-switch.ant-switch-checked.success .ant-switch-handle {
  @apply before:bg-[#0FB33D];
}

.select-option-block .inline-flex {
  @apply overflow-hidden flex items-center h-7;
}

.select-option-block label {
  @apply py-0.5 px-2.5;
}

.btn-success,
.chat-radio-messages
  .ant-radio-checked
  .ant-radio-input[value="1"]
  + .ant-radio-inner {
  @apply text-white !bg-[#47a447] border border-[#47a447] hover:!border-[#47a447];
}

.chat-radio-messages .ant-radio-inner,
.chat-radio-messages .ant-radio-input[value="1"] + .ant-radio-inner {
  @apply !border-[#47a447];
}

.btn-warning,
.chat-radio-messages
  .ant-radio-checked
  .ant-radio-input[value="2"]
  + .ant-radio-inner {
  @apply text-white !bg-[#ed9c28] border border-[#ed9c28] hover:!border-[#ed9c28];
}

.chat-radio-mentions .ant-radio-inner,
.chat-radio-messages .ant-radio-input[value="2"] + .ant-radio-inner {
  @apply !border-[#ed9c28];
}

.btn-danger,
.chat-radio-messages
  .ant-radio-checked
  .ant-radio-input[value="0"]
  + .ant-radio-inner {
  @apply text-white !bg-[#dc3545] border border-[#dc3545] hover:!border-[#dc3545];
}

.chat-radio-mute .ant-radio-inner,
.chat-radio-messages .ant-radio-input[value="0"] + .ant-radio-inner {
  @apply !border-[#dc3545];
}

.checkbox-transparent .ant-checkbox .ant-checkbox-inner,
.checkbox-transparent:hover .ant-checkbox .ant-checkbox-inner,
.checkbox-transparent:hover .ant-checkbox-checked:after {
  @apply !bg-transparent !border !border-white;
}

.ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover
  .ant-checkbox-checked:not(.ant-checkbox-disabled)
  .ant-checkbox-inner,
.ant-checkbox-wrapper-checked:not(.ant-checkbox-wrapper-disabled):hover
  .ant-checkbox-inner,
.ant-checkbox-checked:not(.ant-checkbox-disabled):hover .ant-checkbox-inner {
  @apply bg-primary-900;
}

.custom-input-newtab .ant-input-suffix,
.retainage-table .ag-header,
.cf-inline-custom-btn .ant-btn span:first-child:empty,
.cf-inline-custom-btn .ant-btn .ant-wave,
.index-dashboard .grid-stack-animate:empty,
.cf-select-field.inline-edit .header-select-with-icon .ant-select-arrow {
  @apply hidden;
}

.linear-gradient-svg svg path {
  fill: var(--fillsvg);
}

.svg-inline--fa .fa-duotone-group .fa-secondary {
  --fa-secondary-opacity: 0.4;
}

.home-icon.svg-inline--fa .fa-primary {
  @apply fill-white;
}

.menu-breadcrumb ol {
  @apply items-center flex-nowrap;
}

.menu-breadcrumb .ant-breadcrumb-separator {
  @apply text-white text-xl leading-5;
}

.tablist-chat .active {
  @apply text-primary-900 hover:!text-white bg-primary-gray-20 dark:!bg-dark-900 dark:!text-[#dcdcdd] cursor-auto;
}

.chat-input .chat-input__control {
  @apply min-h-[30px] max-h-min;
}

.chat-input .chat-input__suggestions {
  @apply !top-[unset] !bottom-[30px] !min-w-[180px] !left-1 text-xs max-h-[430px] overflow-y-auto border border-gray-300 dark:border-white/10 rounded-t-md;
}

#mention-suggestions-portal .chat-input__suggestions {
  @apply !top-[unset] !bottom-[30px] !min-w-[180px] text-xs max-h-[430px] overflow-y-auto border border-gray-300 dark:border-white/10 rounded-t-md h-auto;
}

#mention-suggestions-portal .chat-input__suggestions > ul > li,
.chat-input .chat-input__suggestions > ul > li {
  @apply p-1 border-b border-gray-300 dark:border-white/10 last:border-b-0 text-gray-600 dark:text-white/90 whitespace-normal break-words;
}

.chat-input .chat-input__highlighter {
  @apply py-[5px] px-2 !outline-none min-h-[30px] !text-13;
}

.chat-input .chat-input__highlighter strong {
  @apply bg-blue-50;
}

.chat-input .chat-input__input {
  @apply py-[5px] px-2 !outline-none min-h-[30px] !text-13 h-[30px] dark:text-white/90;
}

body .cf-select-field .select-filed-header .ant-select-arrow,
span.inline-block.text-red-500,
.ant-spin .ant-spin-dot-holder,
.ant-spin svg {
  @apply text-primary-900 dark:text-white/90;
}

.quill .ql-container.ql-snow,
.bid-list-table .ag-cell-wrapper > *:not(.ag-cell-value):not(.ag-group-value) {
  @apply h-fit;
}

.common-card {
  @apply bg-white dark:bg-dark-800 rounded-md shadow-[0px_3px_20px] shadow-black/5;
}

.filter-select-class .ant-select-selection-search-input {
  @apply !h-[26px];
}

.ant-popover .common-filter .cf-input-field input[type="button"],
.ant-popover .common-filter .cf-input-field input[type="input"],
.ant-popover .date-text,
body
  .ant-popover
  .common-filter
  .cf-select-field.inline-edit
  .filter-select-class
  .ant-select-selector
  .ant-select-selection-item:not(
    body
      .ant-popover
      .common-filter
      .cf-select-field.inline-edit
      .filter-select-class.ant-select-multiple
      .ant-select-selector
      .ant-select-selection-item
  ) {
  @apply !text-xs !leading-[15px] !font-semibold;
}

.common-filter .cf-select-field.inline-edit .filter-select-class.ant-select-multiple .ant-select-selector .ant-select-selection-item .ant-select-selection-item-content{
  @apply pl-1
}

body
  .ant-popover
  .common-filter
  .cf-select-field.inline-edit
  .filter-select-class.ant-select-multiple
  .ant-select-selector
  .ant-select-selection-item {
  @apply !text-xs !leading-[18px] !font-semibold !pl-0.5;
}

.common-filter.cf-input-btn-filter:has(
    > div > div > div .cf-new-input-field .ant-input[disabled]
  ),
.common-filter:has(> div > div > div .cf-new-input-field .ant-input[disabled]),
.common-filter:has(
    > .filter-date-picker-block
      > div
      > div
      > div
      .cf-new-datepicker-field
      .ant-picker.ant-picker-disabled
  ),
.common-filter:has(
    > div > div .cf-new-select-field .ant-select.ant-select-disabled
  ),
.common-filter:has(
    > .filter-date-picker-block
      > div
      > div
      > div
      .cf-datepicker-field
      .ant-picker.ant-picker-disabled
  ),
.common-filter:has(> div > div > div .cf-input-field .ant-input[disabled]),
.common-filter:has(
    > div > div .cf-select-field .ant-select.ant-select-disabled
  ) {
  @apply bg-black/10;
}

.cf-input-btn-filter .cf-input-btn-filter-arrow {
  @apply right-[3px];
}

.cf-input-btn-filter .ant-input:not(.ant-popover .cf-input-btn-filter .ant-input),
.filter-field-block:not(.ant-popover .filter-field-block) {
  @apply max-w-[140px];
}

.ant-popover .cf-input-btn-filter .ant-input {
  @apply max-w-[266px];
}

.common-filter:has(
    > .filter-date-picker-block
      > div
      > div
      > div
      .cf-datepicker-field
      .ant-picker.ant-picker-disabled
  )
  .clear-filter-block,
.common-filter:has(> div > div > div .cf-input-field .ant-input[disabled])
  .clear-filter-block,
.common-filter:has(> div > div .cf-select-field .ant-select.ant-select-disabled)
  .clear-filter-block {
  @apply hidden;
}

.common-filter:has(> div > div > div .cf-input-field .ant-input[disabled])
  .cf-input-btn-filter-arrow,
.table-select-filed .ant-select-arrow,
.select-list-item + .select-list-item::before {
  @apply opacity-0;
}

.table-select-filed:hover .ant-select-arrow,
.table-select-filed:focus-within .ant-select-arrow {
  @apply opacity-100;
}

.common-card-select-dropdown {
  @apply w-full p-0 !h-6 font-medium text-black dark:text-white/90;
}

.common-card-select-dropdown span {
  @apply p-0 text-13;
}

.common-card-widget-label {
  @apply bg-[#EDEDED] px-[15px] py-1 sm:text-[17px] text-sm leading-[22px] inline-block text-primary-900 font-semibold dark:text-white dark:bg-[#2f3842];
}

.ql-toolbar.ql-snow {
  @apply !p-[5px] dark:!border-white/30;
}

.ql-container.ql-snow {
  @apply dark:!border-white/30;
}

.quill .ql-toolbar {
  @apply bg-gray-100 dark:bg-dark-400;
}

.quill .ql-toolbar .ql-picker-label,
.quill .ql-toolbar button {
  @apply !bg-white dark:!bg-[#15202B] rounded !border !border-solid !border-[#ccc] dark:!border-white/30 !py-[3px] !px-1.5;
}

.quill .ql-toolbar .ql-formats {
  @apply !mr-1.5;
}

.quill .ql-toolbar .ql-formats .ql-picker {
  @apply !h-[30px] dark:text-white/90;
}

.quill .ql-toolbar .ql-formats .ql-picker.ql-color-picker .ql-picker-label,
.quill .ql-toolbar .ql-formats .ql-picker.ql-color-picker .ql-picker-label {
  @apply flex items-center justify-center;
}

.quill .ql-toolbar button {
  @apply !w-[30px] !h-[30px] flex items-center justify-center;
}

.quill .ql-toolbar button svg {
  @apply w-4 h-4;
}

.quill .ql-toolbar button + button {
  @apply -ml-px;
}

.ql-snow .ql-stroke,
.ql-container .ql-editor {
  @apply dark:stroke-white/90 dark:text-white/90;
}

[role="tooltip"] {
  @apply py-0.5 px-1.5 rounded text-xs;
}

.setting-label-text,
.setting-value-text {
  @apply text-13 text-[#6c757d] font-normal;
}

.setting-form-before {
  @apply before:absolute before:h-px before:w-full before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)] before:bottom-0 before:left-0;
}

/* Start:Antd Dropdown */
.dropdown-option-block .dropdown-content{
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

.dropdown-option-block
  .ant-dropdown-menu-item:not(
    .dropdown-option-block
      .ant-dropdown-menu-item.ant-dropdown-menu-item-disabled
  ),
.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item:hover {
  @apply hover:!bg-blue-50 hover:!text-primary-900;
}

.select-option-block:hover label,
.dropdown-option-block .ant-dropdown-menu-item:hover .dropdown-list-icon {
  @apply text-primary-900;
}

.dropdown-option-block
  .ant-dropdown-menu-item.ant-dropdown-menu-item-disabled
  .ant-dropdown-menu-title-content
  > div {
  @apply !text-black/30;
}

.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-in-range:not(
    .ant-picker-cell-disabled
  ):before,
.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(
    .ant-picker-cell-disabled
  ):before,
.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-range-end:not(
    .ant-picker-cell-disabled
  ):before,
.active-button.ant-dropdown-open {
  @apply !bg-primary-900/10 dark:!bg-dark-950;
}

.dropdown-list-icon {
  @apply text-base w-3.5 h-3.5 text-black/60 dark:text-white/90 group-hover:text-primary-900;
}

.btn-demo-data .dropdown-list-icon {
  @apply !text-white;
}

.dropdown-list-bottom-note {
  @apply py-[5px] px-2.5 text-xs flex items-center bg-gray-100 dark:bg-[#232b36] dark:hover:bg-dark-900 focus:!bg-gray-100 dark:focus:!bg-dark-900 dark:focus:!text-white/90 text-center;
}

.dropdown-list-bottom-note .ant-typography {
  @apply text-xs;
}

/* End:Antd Dropdown */

/***** HelloNext Popup *****/
.hn-widget {
  @apply !z-[999] !mx-0 !my-2.5;
}

.hn-widget .hn-widget-iframe-show {
  @apply !max-h-[70vh];
}

.dropdown-content {
  @apply bg-white shadow-[0_35px_60px_-15px_rgba(0,_0,_0,_0.3)] rounded-lg overflow-hidden;
}

.dropdown-content .ant-dropdown-menu {
  @apply shadow-none !rounded-none;
}

.dropdown-content.menu-h-scroll .ant-dropdown-menu {
  @apply max-h-[calc(100vh-360px)] min-h-36;
}

.grid-stack.customize {
  --grid-lines-color: rgb(243, 243, 243);
  content: "";
  background-color: rgb(234, 234, 234);
  background-image: linear-gradient(
      90deg,
      var(--grid-lines-color),
      transparent 10px
    ),
    linear-gradient(
      90deg,
      transparent calc(100% - 10px),
      var(--grid-lines-color)
    ),
    linear-gradient(var(--grid-lines-color), transparent 10px),
    linear-gradient(transparent calc(100% - 10px), var(--grid-lines-color) 100%);
  width: 100%;
  height: 100%;
  position: relative;
  top: 0;
  left: 0;
  cursor: all-scroll;
}

.grid-stack.customize .common-card > div {
  @apply pointer-events-none cursor-default;
}

.dhx_cal_navline.dhx_cal_navline_flex .dhx_cal_tab_standalone {
  @apply w-fit;
}

.select-project-header {
  @apply !text-base;
}

.project-unpaid-items {
  @apply w-[180px] max-w-[180px] mx-auto;
}

.common-card-head .ant-radio-button-wrapper,
.table-select-filed .ant-select-selector,
.table-select-filed .ant-select-selector input {
  @apply !h-7;
}

.scheduler-calendar .dhx_cal_data {
  @apply min-h-[300px];
}

.gallery-filter-block .gallery-filter-btn.ant-btn {
  @apply !p-0 max-w-[calc(100%-12px)];
}

.reports-collapse .ant-collapse-header,
.dropdown-option-block .ant-dropdown-menu-item,
.reports-collapse .ant-collapse-content-box {
  @apply !p-0;
}

.gallery-filter-block .gallery-filter-btn.ant-btn {
  @apply dark:hover:!bg-transparent;
}

.tooltip-box-body .tooltip-group-div {
  @apply px-2.5 py-0 flex leading-[25px] items-center;
}

.tooltip-box-body .tooltip-group-div span {
  @apply text-xs;
}

.tooltip-box-body .tooltip-group-div span.label {
  @apply mr-[3px];
}

.training-session-header b,
.tooltip-box-body .tooltip-group-div span.value {
  @apply font-semibold;
}

.training-session-header a,
.training-session-top a,
.training-session-bottom a {
  @apply text-primary-900 underline hover:!text-deep-orange-500;
}

.bid-list .ant-collapse-expand-icon {
  border: 1px solid #223558;
  border-radius: 100%;
  background: #223558;
  color: #fff;
  justify-content: center;
  padding: 0 !important;
  margin-right: 5px;
  height: 22px;
  width: 22px;
}

.bid-list .ant-collapse-content-box {
  @apply px-0;
}

.bid-list .ant-collapse-expand-icon {
  @apply rotate-180 hover:!border-deep-orange-500;
}

.bid-list .ant-collapse-item-active .ant-collapse-expand-icon {
  @apply rotate-0;
}

.bid-list .ag-details-row {
  @apply py-0;
}

.ant-popover .avatar-group {
  @apply p-1 w-[200px] border-b border-gray-300 last:border-0 dark:bg-[#1e2732];
}

.ant-popover .avatar-name {
  @apply flex w-[163px];
}

.calculation-preferences-select .ant-select-selection-item {
  @apply font-semibold text-[15px];
}

/* Start: Collapse with table */

.bid-list .ant-collapse-header-text {
  @apply text-base font-semibold text-primary-900 dark:text-white;
}

.bid-list-table .ag-header {
  @apply !border-white;
}

.bid-list-table .ag-header-container,
.bid-list-table .ag-center-cols-container,
.bid-list-table .ag-header-container > div.ag-header-row,
.financial-summary-highchart .highcharts-container,
.financial-summary-highchart .highcharts-container svg {
  @apply !w-full;
}

.bid-list .ant-collapse,
.collapse-with-table {
  @apply !rounded-lg !border-0 !p-0 !border-l-2 bg-[#EEEFF0] border-solid border-primary-900 dark:bg-dark-900 dark:border-white/60;
}

.bid-list .ant-collapse-header,
.collapse-with-table .ant-collapse-header {
  @apply !border-0 !items-baseline !pl-2 !bg-transparent gap-2.5;
}

.move-collapse-table.collapse-with-table .ant-collapse-header {
  @apply !pl-9;
}

.bid-list .ant-collapse-item,
.bid-list .ant-collapse-content,
.collapse-with-table .ant-collapse-item,
.collapse-with-table .ant-collapse-content {
  @apply !bg-transparent !border-0;
}

.bid-list .ant-collapse-expand-icon svg,
.collapse-with-table .ant-collapse-expand-icon svg {
  @apply w-2.5 h-2.5;
}

.bid-list .ant-collapse-expand-icon,
.bid-list-table .ag-group-expanded .ag-icon-tree-open,
.bid-list-table .ag-group-contracted .ag-icon-tree-closed,
.collapse-with-table .ant-collapse-expand-icon {
  @apply w-3.5 min-w-[14px] !h-3.5 bg-primary-900 !text-white rounded-full !m-0 !p-0 justify-center hover:bg-deep-orange-500;
}

.bid-list .ant-collapse-expand-icon,
.bid-list-table .ag-group-expanded .ag-icon-tree-open,
.bid-list-table .ag-group-contracted .ag-icon-tree-closed,
.collapse-with-table .ant-collapse-expand-icon {
  box-shadow: 0px 0px 0px 1px rgb(255, 255, 255),
    0px 0px 0px 2px rgb(37, 53, 88);
}

.bid-list-table .ag-row-group-indent-0.ag-cell-wrapper.ag-row-group {
  @apply items-center pl-0.5;
}

.bid-list-table .ag-group-expanded .ag-icon-tree-open,
.bid-list-table .ag-group-contracted .ag-icon-tree-closed {
  @apply flex items-center justify-center text-[11px] before:content-["\f078"] before:text-[9px] transition-all ease-in-out duration-300 font-extralight;
}

.bid-list-table .ag-group-expanded .ag-icon-tree-open {
  @apply rotate-180;
}

.bid-list-table .ag-group-expanded .ag-icon-tree-open,
.bid-list-table .ag-group-contracted .ag-icon-tree-closed {
  font-family: "Font Awesome 6 Pro" !important;
}

.bid-list .ant-collapse-expand-icon:hover,
.bid-list-table .ag-group-expanded .ag-icon-tree-open:hover,
.bid-list-table .ag-group-contracted .ag-icon-tree-closed:hover,
.collapse-with-table .ant-collapse-expand-icon:hover {
  box-shadow: 0px 0px 0px 1px rgb(255, 255, 255),
    0px 0px 0px 2px rgb(255, 84, 0);
}

.bid-list .ant-collapse-content-box,
.collapse-with-table .ant-collapse-content-box {
  @apply !pt-0 !pb-4 !pl-8 !pr-4;
}

.bid-list .ant-collapse-item-active .collapse-block {
  @apply after:w-[25px];
}

.bid-list-table .ag-root-wrapper.ag-layout-normal {
  @apply p-2 common-card;
}

.bid-list-table
  .ag-header
  .ag-header-container
  .ag-header-row-column
  .ag-header-cell:nth-child(2nd) {
  @apply pl-5;
}

.bid-list-table .ag-center-cols-container {
  @apply mt-1;
}

.bid-list-table .ag-center-cols-container .ag-row {
  @apply bg-white border-b border-solid border-[#e8e8e8] last:border-0 dark:even:bg-[#1e2732] dark:odd:bg-dark-600;
}

.ant-collapse .bid-list-table .ag-details-row-fixed-height.ag-details-row {
  @apply !rounded-bl-lg !border-l;
}

.ant-collapse .bid-list-table .ag-center-cols-clipper .ag-row-group-expanded {
  @apply rounded-tl-lg border-l;
}

.bid-list-table .ag-center-cols-container .ag-row-group-expanded {
  @apply !bg-[#F1F2F3] border-b-0 rounded-t-lg dark:!bg-dark-600;
}

.bid-list-table .ag-details-row-fixed-height.ag-details-row {
  @apply !bg-[#F1F2F3] pl-8 pt-1 pb-2.5 pr-4 dark:!bg-dark-600;
}

/* End: Collapse with table */

.cf-select-field .report-head-dropdown .ant-select-selector {
  @apply !bg-white !pl-[5px] !h-[26px] dark:!bg-dark-800 !rounded;
}

.cf-select-field
  .report-head-dropdown
  .ant-select-selector
  .ant-select-selection-item {
  @apply !leading-[24px] p-[0px_20px_0px_5px];
}

.cf-select-field
  .report-head-dropdown
  .ant-select-selector
  .ant-select-selection-search
  input {
  @apply !h-[24px] !pl-[5px] !pr-5;
}

.report-head-dropdown {
  @apply before:content-none;
}

.common-filter {
  @apply relative min-h-[28px] bg-[#E4ECF6] dark:bg-dark-400 rounded text-xs text-primary-900 dark:text-white/90;
}

.common-filter-label {
  @apply !text-13 leading-5 cursor-pointer whitespace-nowrap !p-0 !text-primary-900 dark:!text-white/90 !w-fit !max-w-[215px] !font-semibold;
}

.add-padding-filter-label.common-filter-label,
.common-filter .filter-date-picker-block .common-filter-label {
  @apply !pl-2.5 !py-1;
}

.common-filter .datepicker-input .cf-datepicker-field {
  @apply h-5;
}

.sidebar-body .search-with-border .ant-input-group-addon {
  @apply !bg-transparent !text-primary-900;
}

.chat-search-input .ant-input-group-addon .ant-btn,
.search-with-border .ant-input-group-addon .ant-btn,
.cf-search-input.page-search-input .ant-input-group-addon,
.page-search-input .ant-input-group-addon .ant-btn,
.retainage-table .ag-theme-alpine .ag-root-wrapper.ag-ltr.ag-layout-normal,
.common-filter .cf-select-field .ant-select:hover {
  @apply !bg-transparent;
}

.sidebar-body .search-input-borderless .ant-input-search-button {
  @apply !text-primary-900;
} 

.cf-search-input .ant-input-affix-wrapper {
  @apply pl-0.5;
}

.common-filter
  .cf-select-field.inline-edit
  .filter-select-class.ant-select-multiple
  .ant-select-selector
  .ant-select-selection-item {
  @apply !h-5;
}

.ant-popover .common-filter .ant-select-multiple {
  @apply focus-within:!bg-transparent;
}

.ant-popover .common-filter .ant-select-multiple .ant-select-selector {
  @apply !pl-2.5;
}

.custom-input-newtab .ant-input-affix-wrapper:hover .ant-input-suffix {
  @apply block;
}

.warning-settings-alert-module .ant-alert-warning {
  @apply p-4 items-baseline;
}

.project-weeks-tasks #project_scheduler .dhx_cal_data,
.project-weeks-tasks
  #project_scheduler
  .dhx_cal_data.dhx_resize_denied.dhx_move_denied {
  @apply !h-0 border-transparent !min-h-0 !hidden;
}

.project-weeks-tasks #project_scheduler .dhx_cal_container {
  @apply -ml-[50px] !w-[calc(100%+50px)] !h-[270px];
}

.project-weeks-tasks {
  @apply !h-[270px] overflow-hidden sm:-mt-[58px] -mt-[15px];
}

.project-weeks-tasks.project-summary-schedule {
  @apply sm:-mt-11 -mt-11 sm:p-0 pt-11;
}

.project-weeks-tasks #project_scheduler .dhx_multi_day {
  @apply h-[190px] max-h-[190px] overflow-x-hidden overflow-y-auto;
}

.project-weeks-tasks #project_scheduler .dhx_multi_day .dhx_multi_day_icon {
  @apply border-0 !hidden;
}

.project-weeks-tasks #project_scheduler .dhx_cal_navline .dhx_cal_date {
  @apply text-primary-900 sm:text-base text-sm font-semibold font-sans;
}
.project-weeks-tasks
  #project_scheduler
  .dhx_cal_navline.dhx_cal_navline_flex
  .dhx_cal_date {
  @apply dark:text-white/90;
}

.project-weeks-tasks #project_scheduler .dhx_cal_navline {
  @apply !h-[59px] !w-full pr-0 pl-12;
}

.dark-bg-table-icon {
  @apply dark:text-white/90 dark:group-hover:text-white/90;
}

.project-weeks-tasks .dhx_cal_container {
  @apply dark:bg-dark-800;
}

.project-weeks-tasks .dhx_cal_container .dhx_cal_date {
  @apply dark:text-white/90;
}

.project-summary-schedule .dhx_cal_container {
  @apply !h-full !min-h-full;
}

.project-summary-schedule #project_scheduler .dhx_cal_navline {
  @apply pr-0 pl-24;
}

.project-summary-schedule
  #project_scheduler
  .dhx_cal_navline
  .dhx_cal_today_button,
.project-summary-schedule
  #project_scheduler
  .dhx_cal_navline
  .dhx_cal_next_button,
.project-summary-schedule
  #project_scheduler
  .dhx_cal_navline
  .dhx_cal_prev_button {
  @apply absolute sm:top-3 -top-[30px] text-primary-900 text-sm py-1 px-3.5 rounded-md bg-white h-[30px] !border-[#d9d9d9] hover:!border-primary-900 z-0 hover:z-10;
}

.project-summary-schedule
  #project_scheduler
  .dhx_cal_navline
  .dhx_cal_prev_button {
  @apply right-8 !rounded-e-none -mr-[1px];
}

.project-summary-schedule
  #project_scheduler
  .dhx_cal_navline
  .dhx_cal_today_button {
  @apply sm:right-[68px] right-16;
}

.project-summary-schedule
  #project_scheduler
  .dhx_cal_navline
  .dhx_cal_next_button {
  @apply right-0 !rounded-s-none;
}

.project-summary-schedule
  #project_scheduler
  .dhx_cal_navline
  .dhx_cal_prev_button:before,
.project-summary-schedule
  #project_scheduler
  .dhx_cal_navline
  .dhx_cal_next_button:before {
  @apply text-primary-900;
}

.double-line-header .ag-header,
.double-line-header .ag-header .ag-header-cell,
.cost-com-type-block .ag-header,
.cost-com-type-block .ag-header .ag-header-cell,
.financial-summary-block .ag-header,
.financial-summary-block .ag-header .ag-header-cell,
.schedule-values .ag-header,
.schedule-values .ag-header .ag-header-cell {
  @apply !h-[48px];
}

.cost-com-type-block
  .ag-body
  .ag-pinned-left-cols-container
  [col-id="head"]
  .ag-cell-value,
.financial-summary-block
  .ag-body
  .ag-pinned-left-cols-container
  [col-id="head"]
  .ag-cell-value,
.financial-summary-block
  .ag-body
  [row-id="7"]
  [col-id="estimated_cost_markup"]
  .ag-cell-value,
.financial-summary-block
  .ag-body
  [row-id="7"]
  [col-id="estimated_profit_cost_markup"]
  .ag-cell-value,
.financial-summary-block
  .ag-body
  [row-id="9"]
  [col-id="estimated_cost_markup"]
  .ag-cell-value,
.financial-summary-block
  .ag-body
  [row-id="9"]
  [col-id="estimated_profit_cost_markup"]
  .ag-cell-value {
  @apply text-gray-500 !font-semibold;
}

.cost-com-type-block .ag-body .ag-row-last {
  @apply bg-[#f4f5f6];
}

.financial-summary-block .ag-body [row-id="4"],
.financial-summary-block .ag-body [row-id="6"] {
  @apply border-b border-primary-900/10 bg-[#f4f5f6];
}

.cost-com-type-block .ag-body .ag-row-last .ag-cell-value,
.financial-summary-block .ag-body [row-id="4"] .ag-cell-value,
.financial-summary-block .ag-body [row-id="6"] .ag-cell-value {
  @apply !text-primary-900 !font-semibold;
}

body .cf-select-field .select-filed-header .ant-select-selection-item {
  @apply !pe-1.5;
}

body .cf-select-field .select-filed-header .ant-select-selector,
body .cf-select-field .select-filed-header .ant-select-selection-item {
  @apply !h-full !leading-6 !border-0;
}

.select-filed-header .ant-select-selection-item {
  @apply text-13;
}

.recover-user .ant-input {
  @apply !bg-[#e8f0fe] !text-black;
}

.project-employee .ant-color-picker-color-block {
  @apply !h-[16px] !w-[16px] !max-w-[16px] !min-w-[16px] !max-h-[16px] !min-h-[16px];
}

.reports-collapse .ant-collapse-expand-icon {
  @apply !p-0 !m-0;
}

.billing-reports-list-tab
  .ant-radio-button-wrapper
  span:not(.ant-radio-button) {
  @apply !text-xs !text-[#253555] dark:!text-white/90;
}

.billing-reports-list-tab
  .ant-radio-button-wrapper.ant-radio-button-wrapper-checked
  span:not(.ant-radio-button) {
  @apply !text-[#253555] dark:!text-[#253555];
}

.billing-reports-list-tab .ant-radio-button-wrapper {
  @apply !px-2;
}

.ant-image-preview-mask {
  @apply !z-[1050];
}

.ant-image-preview-wrap {
  @apply !z-[1051];
}

.ant-modal-root .ant-modal-mask,
.ant-modal-root .ant-modal-wrap,
.ant-drawer,
.modal-root-z-index .ant-modal-wrap,
.modal-root-z-index .ant-modal-mask,
.pac-container {
  @apply z-[1051];
}

.page-header-inner input {
  @apply !text-[16px];
}

.ant-modal.full-width .ant-modal-content {
  @apply !h-full !w-full !rounded-none;
}

.retainage-table .ag-center-cols-container > div.ag-row-even {
  @apply bg-[#F9FACE] rounded dark:bg-dark-600 !h-7 hover:!bg-[#F9FACE];
}

.retainage-table .ag-center-cols-viewport .ag-center-cols-container {
  @apply !w-full !h-7;
}

.retainage-table .ag-theme-alpine .ag-cell {
  @apply !py-1;
}

.notes-card {
  filter: drop-shadow(0px 4px 15px rgba(0, 0, 0, 0.09));
  border-radius: 6px;
}

.cf-select-field
  .ant-select-selection-search-input:not(
    .cf-select-field .ant-select-selection-search-input[disabled],
    .cf-select-field .ant-select-disabled .ant-select-selection-search-input
  ),
.cf-select-field
  .ant-select-selector:not(
    .cf-select-field
      .ant-select-selector:has(> .ant-select-selection-search-input[disabled]),
    .cf-select-field .ant-select-disabled .ant-select-selector
  ),
.cf-new-select-field
  .ant-select-selection-search-input:not(
    .cf-new-select-field .ant-select-selection-search-input[disabled],
    .cf-new-select-field .ant-select-disabled .ant-select-selection-search-input
  ),
.cf-new-select-field
  .ant-select-selector:not(
    .cf-new-select-field
      .ant-select-selector:has(> .ant-select-selection-search-input[disabled]),
    .cf-new-select-field .ant-select-disabled .ant-select-selector
  ),
.cf-new-select-field
  .ant-select-show-search.ant-select:not(
    .ant-select-customize-input,
    .readOnly-select-field
      .cf-new-select-field
      .ant-select-show-search.ant-select,
    .readOnly-select-field
      .cf-new-select-field
      .select-cursor-no-drop.ant-select,
    .cf-new-select-field .ant-select-show-search.ant-select.ant-select-disabled
  )
  .ant-select-selector {
  @apply !cursor-pointer;
}

.ant-picker .ant-picker-input > input,
.ag-grid-cell-pointer .ag-center-cols-container [role="row"],
.cursor-group-input .cf-input-field input {
  @apply cursor-pointer;
}

body
  .common-filter
  .cf-select-field.inline-edit
  .filter-select-class
  .ant-select-selector
  .ant-select-selection-item:not(
    body
      .common-filter
      .cf-select-field.inline-edit
      .filter-select-class.ant-select-multiple
      .ant-select-selection-item
  ) {
  @apply !leading-5;
}

.common-filter
  .cf-select-field.inline-edit
  .filter-select-class
  .ant-select-selector
  .ant-select-selection-item,
.page-header-subtitle-field input {
  @apply !text-13;
}

.common-list-table .ag-header-cell-text {
  @apply !text-sm;
}

.no-data-found-block .table-no-data-image img {
  @apply h-24;
}

.training-session-top a,
.training-session-bottom a {
  @apply hover:underline;
}

.ant-notification-notice-description {
  @apply w-[85%];
}

.ant-notification-notice-close {
  @apply !end-[15px];
}

.tb-admin-message a {
  @apply text-primary-900 hover:underline dark:text-white/90;
}

.getting-started-widget .ant-progress-outer {
  @apply !w-[calc(100%-50px)] !m-0 !p-0;
}

.getting-started-widget .ant-progress-text {
  @apply text-right ml-0.5 w-12 text-primary-900 font-semibold;
}

.cf-inline-custom-btn
  .project-input-btn
  span:not(.cf-inline-custom-btn .project-input-btn .button-placeholder) {
  @apply !text-base !font-medium;
}

.complete-checkbox .ant-checkbox-inner {
  @apply !rounded-full;
}

.preferences-client-filed .ant-select,
.cost-code-field .ant-select {
  @apply !border-transparent hover:!border-[#ccc];
}

.preferences-client-filed .ant-select .ant-select-selection-item,
.cost-code-field .ant-select .ant-select-selection-item {
  @apply !text-[#212529] dark:text-white/90 !text-13;
}

.preferences-client-filed .ant-select .ant-select-selection-placeholder,
.cost-code-field .ant-select .ant-select-selection-placeholder {
  @apply !text-13;
}
/*BEGIN: NO DATA IMAGE  */
.no-data-back-border {
  @apply fill-[#F1F3F9] dark:fill-[#303742];
}

.no-data-main-border {
  @apply fill-[#ABB4C6] dark:fill-[#4A5567];
}

.no-data-file-border {
  @apply fill-white stroke-[#ABB4C6] dark:fill-[#1F2732] dark:stroke-[#4A5567];
}

.no-data-file {
  @apply fill-white dark:fill-[#1F2732];
}

.no-data-file-in {
  @apply fill-[#F1F3F9] dark:fill-[#4A5567];
}

.no-data-file-outer {
  @apply fill-[#F1F3F9] dark:fill-[#444D5A];
}

.no-data-text {
  @apply fill-[#646464] dark:fill-[#596478];
}

/*END: NO DATA IMAGE  */
.field-loader {
  animation-duration: 1s;
}

.project-input-shimmer.ant-skeleton-input {
  @apply !h-6;
}

.page-header-subtitle-shimmer.ant-skeleton-input {
  @apply !h-5 mt-0.5;
}

.mu-progress .ant-progress-circle .ant-progress-circle-path {
  @apply dark:!stroke-white/90;
}

.mu-progress .ant-progress-circle .ant-progress-circle-trail {
  @apply dark:!stroke-white/40;
}

.mu-progress .ant-progress-text {
  @apply dark:!text-white/90;
}

.gallery-filter-block .cf-inline-custom-btn,
.collapse-with-table .ant-collapse-content .ant-collapse-content-box > div {
  @apply before:hidden;
}

.collapse-with-table.sov-proj-nowrap .proj-wrap .hide-collapse {
  @apply !hidden;
}

.collapse-with-table.sov-proj-nowrap
  .ant-collapse-item-active
  .proj-wrap
  .hide-collapse {
  @apply !flex;
}

.ant-popover
  .more-option-filter-list
  .common-filter
  .cf-select-field.inline-edit
  .filter-select-class
  .ant-select-selector,
.common-filter
  .cf-select-field.inline-edit
  .filter-select-class
  .ant-select-selector,
.common-filter.input-filter
  .ant-input:not(.ant-popover .common-filter.input-filter .ant-input) {
  @apply !pl-0;
}

.common-filter
  .cf-select-field.inline-edit
  .filter-select-class
  .ant-select-selector
  .ant-select-selection-item {
  @apply !pl-0.5;
}

.ant-popover
  .common-filter
  .cf-select-field.inline-edit
  .filter-select-class
  .ant-select-selector:not(
    .ant-popover
      .more-option-filter-list
      .common-filter
      .cf-select-field.inline-edit
      .filter-select-class
      .ant-select-selector
  ) {
  @apply !pl-2.5;
}

.ant-popover
  .common-filter
  .cf-select-field.inline-edit
  .filter-select-class
  .ant-select-selector:not(
    .ant-popover
      .more-option-filter-list
      .common-filter
      .cf-select-field.inline-edit
      .filter-select-class
      .ant-select-selector
  ),
.cf-select-field.inline-edit .header-select-with-icon .ant-select-selector {
  @apply !pr-1.5;
}

.ant-popover
  .more-option-filter-list
  .common-filter
  .cf-select-field.inline-edit
  .filter-select-class.ant-select-multiple
  .ant-select-selector {
  @apply !pr-3.5;
}

.cf-inline-custom-btn .project-input-btn,
.common-filter .ant-select-multiple .ant-select-selector {
  @apply !py-0;
}

.cf-select-field.inline-edit
  .header-select-with-icon.ant-select-single.ant-select-show-arrow
  .ant-select-selection-placeholder,
.cf-select-field.inline-edit
  .header-select-with-icon.ant-select-single.ant-select-show-arrow
  .ant-select-selection-item {
  @apply !pe-0;
}

.dashboard-calendar-block .dhx_now .dhx_month_body,
.dropdown-option-block
  .dropdown-content
  .ant-dropdown-menu-item:has(> .ant-dropdown-menu-title-content > div:empty),
.cf-select-field.inline-edit .header-select-with-icon .ant-select-arrow {
  @apply hidden;
}

.dashboard-calendar-block .dhx_year_body table {
  @apply w-full;
}

.dashboard-calendar-block .dhx_year_week {
  @apply !h-5 !border-b-0;
}

.dashboard-calendar-block .dhx_year_month {
  @apply !h-[30px];
}

.dashboard-calendar-block .dhx_cal_container {
  @apply !border-0 !shadow-none !p-1.5;
}

.dashboard-calendar-block .dhx_year_month .dhx_cal_prev_button {
  @apply !left-2.5;
}

.dashboard-calendar-block .dhx_year_month .dhx_cal_next_button {
  @apply !right-2.5;
}

.dashboard-calendar-block .dhx_now .dhx_month_head.dhx_calendar_click {
  @apply !bg-primary-900 text-white;
}

.dashboard-calendar-block .dhx_month_head.dhx_calendar_click {
  @apply !bg-[#88b0d9] text-white;
}

.dashboard-calendar-block .dhx_year_month .dhx_cal_prev_button,
.dashboard-calendar-block .dhx_year_month .dhx_cal_next_button {
  @apply w-5 !top-2.5 before:!w-5 before:!top-0 before:!h-5 before:!leading-5 before:-translate-x-2;
}

.cf-input-field.inline-edit
  .ant-input-group-wrapper-disabled
  .ant-input-group-addon {
  @apply text-[15px];
}

.file-select-list .ant-select-item-option-disabled {
  @apply text-[#000000e0];
}

.cf-input-field .ant-input-group-wrapper-disabled .ant-input-group-addon,
.common-arrow .ant-select-arrow {
  @apply text-primary-900 dark:text-white/90;
}
.spin-icon .fa-primary {
  @apply dark:text-white;
}

.inner-page-select-option.ant-select-dropdown .ant-select-item-option-disabled {
  @apply text-primary-900 dark:text-white/90 cursor-default bg-transparent;
}

.zsiq_floatmain.siq_bR,
.zsiq_theme11.siq_bR,
.siq_bR {
  @apply md:bottom-[70px] bottom-24;
}

.chat-custom-tooltip .ant-tooltip-inner,
.chat-custom-tooltip .ant-tooltip-arrow:before {
  @apply dark:!bg-dark-900;
}

.siqembed.zls-sptwndw {
  @apply !bottom-[31px];
}

.sent-email-body #container {
  @apply !shadow-none sm:!p-5 !p-[15px];
}

.read-only-radio-btn .ant-radio-wrapper {
  @apply pointer-events-none;
}

.checkbox-border .ant-checkbox-inner {
  @apply !border-[#F97316];
}

.path {
  stroke-dasharray: 1000;
  stroke-dashoffset: 0;
}

.path.line {
  stroke-dashoffset: 1000;
}

.path.check {
  stroke-dashoffset: -100;
}

.ant-checkbox-inner,
.ant-radio-inner,
.ant-radio
  .ant-radio-inner:not(
    .ant-radio.ant-radio-disabled .ant-radio-inner,
    .chat-radio-messages .ant-radio-inner,
    .chat-radio-mentions .ant-radio-inner,
    .chat-radio-mute .ant-radio-inner
  ) {
  @apply !border-primary-900/80;
}

.ant-checkbox-checked .ant-checkbox-inner,
.ant-radio-wrapper .ant-radio-checked .ant-radio-inner {
  @apply bg-primary-900;
}

.report .collapse-with-table {
  @apply !bg-[#F0F1F2];
}

body.iframe-loading-disabled {
  @apply pointer-events-none !max-h-screen;
}

.iframe-loading-block {
  transform-origin: 100% 0%;
}

.iframe-loading-block .iframe-loading-inner {
  transform-origin: 0% 0%;
}

.add_team_member_table .ag-body [role="row"] {
  @apply !h-11;
}

.onboarding_modal .ant-modal-content {
  @apply h-[calc(100vh-100px)];
}

.project-input-btn .button-placeholder {
  @apply pr-5;
}

.project-input-btn .button-placeholder + .status-icon-view-div {
  @apply right-1;
}

.new-file-upload-inner:hover + .new-file-upload-inner {
  @apply before:opacity-0;
}

.common-filter .date-text {
  @apply relative;
}

.ant-popover
  .common-filter
  .filter-date-picker-block
  .common-filter-label:not(
    .more-option-filter-popover.ant-popover
      .common-filter
      .filter-date-picker-block
      .common-filter-label
  ) {
  @apply !p-0;
}

.more-option-filter-popover.ant-popover
  .common-filter
  .filter-date-picker-block
  > div {
  @apply w-fit;
}

.ant-popover
  .common-filter
  .date-text:not(
    .more-option-filter-popover.ant-popover .common-filter .date-text
  ) {
  @apply absolute;
}

#ck-pause-wall .ck-modal-container {
  @apply !z-[9999];
}

@media (max-width: 1024px) {
  .ant-tooltip:not(.show-responsive) {
    @apply hidden;
  }
}

@media (max-width: 767px) {
  .module-menuside-bar {
    @apply -translate-x-full;
  }

  .module-menuside-bar.open-sidebar:not(
      .module-menuside-bar.open-sidebar.iframe-open-sidebar
    ) {
    @apply fixed top-[84px] z-[999] h-full transition-all duration-300 translate-x-0;
  }

  .module-menuside-bar.open-sidebar.iframe-open-sidebar {
    @apply fixed top-0 z-[999] h-full transition-all duration-300 translate-x-0;
  }

  .ant-picker-dropdown.ant-picker-dropdown-range
    .ant-picker-panel-container
    .ant-picker-panels {
    @apply !flex-wrap max-h-[calc(100vh-366px)] overflow-y-auto w-[275px];
  }

  .cf-inline-custom-btn.inline-edit:not(
      .gallery-filter-block .cf-inline-custom-btn
    ),
  .cf-input-field.inline-edit .ant-input-group-wrapper,
  .cf-currency-input-field.inline-edit,
  .cf-input-field.inline-edit .ant-input-affix-wrapper,
  .cf-input-field.inline-edit .ant-input-affix-wrapper input,
  .cf-select-field.inline-edit
    .ant-select:not(
      .cf-select-field.inline-edit .select-apply-border,
      .common-filter .cf-select-field.inline-edit .ant-select
    ),
  .cf-datepicker-field.inline-edit .ant-picker,
  .cf-textarea-field.inline-edit .ant-input {
    @apply !bg-[#f4f5f6] dark:!bg-dark-950;
  }

  .cf-new-input-field.edit-inline .ant-input-affix-wrapper,
  .cf-new-select-field.edit-inline
    .ant-select:not(
      .cf-new-select-field.edit-inline .select-apply-border,
      .common-filter .cf-new-select-field.edit-inline .ant-select
    ),
  .cf-new-currency-input.edit-inline,
  .cf-new-currency-input-field.edit-inline,
  .cf-new-textarea-field.edit-inline .ant-input,
  .cf-new-input-field.edit-inline,
  .cf-new-button-field.edit-inline .ant-btn,
  .cf-new-datepicker-field.edit-inline
    .ant-picker:not(
      .header-date-picker .cf-new-datepicker-field.edit-inline .ant-picker
    ),
  .cf-new-input-mask-field.edit-inline,
  .cf-new-input-mask-field.edit-inline .ant-input-affix-wrapper,
  .cf-new-timepicker-field.edit-inline {
    @apply !bg-[#f4f5f6] dark:!bg-dark-950;
  }

  .wizard-step {
    @apply !flex-row;
  }

  .wizard-step .ant-steps-item-container .ant-steps-item-tail {
    @apply !w-full !h-0.5 !py-0 !px-[17px] !top-1/2 after:!w-full;
  }

  .wizard-step .ant-steps-item .ant-steps-item-content {
    @apply !hidden;
  }

  .wizard-step .ant-steps-item:last-child {
    @apply flex-none;
  }

  .project-select-field .ant-select {
    @apply !pl-[11px];
  }
}

.sidemenu-listbutton .ant-radio-group.ant-radio-group-outline {
  @apply flex;
}

@media (max-height: 700px) {
  .inner-page-select-option {
    @apply !z-[9999];
  }
}

@media (max-height: 652px) {
  .ant-picker-date-panel table tbody tr td {
    @apply !py-0.5 !px-0;
  }
}

@media (max-height: 630px) {
  .chat-message-list {
    @apply h-[calc(100vh-214px)];
  }

  .user-chat-message-inner {
    @apply h-[calc(100vh-211px)];
  }

  .add-group-setting {
    @apply h-[calc(100vh-342px)];
  }

  .responsive-image-size{
    @apply max-w-[180px]
  }

  .edit-group-setting {
    @apply h-[calc(100vh-272px)];
  }
}

@media (max-height: 680px) {
  .ant-picker-date-panel:not(
      .range-calender-picker .ant-picker-date-panel,
      .daily-log-calendar .ant-picker-date-panel
    ) {
    @apply !w-[270px];
  }

  .ant-picker-date-panel
    .ant-picker-body:not(
      .range-calender-picker .ant-picker-date-panel .ant-picker-body
    ) {
    @apply !py-0.5 !px-3;
  }

  .ant-picker-dropdown .ant-picker-content th {
    @apply h-[30px];
  }

  .ant-picker-dropdown .ant-picker-cell {
    @apply py-[3px];
  }

  .ant-picker-dropdown .ant-picker-ranges > li {
    @apply leading-8;
  }

  .responsive-image-size {
    @apply max-w-[200px]
  }
}

@media (max-height: 550px) {
  .responsive-image-size{
    @apply max-w-[150px]
  }
}

@media (max-height: 500px) {
  .select-list-block {
    @apply overflow-y-auto;
  }
  .select-inner-list {
    @apply max-h-fit overflow-hidden;
  }
  .chat-form-body {
    @apply !max-h-full !h-fit !overflow-hidden;
  }
  .chat-form-body {
    @apply min-h-[calc(100dvh-342px)];
  }
  .chat-form-body.edit-group-setting {
    @apply min-h-[calc(100dvh-312px)];
  }
  .chat-form-body.view-group-setting {
    @apply min-h-[calc(100dvh-270px)];
  }
  .group-chat-block {
    @apply max-h-[calc(100dvh-260px)] overflow-y-auto;
  }
  .group-chat-block:has(> .edit-group-setting) {
    @apply max-h-[calc(100dvh-230px)];
  }
  .group-chat-block:has(> .view-group-setting) {
    @apply max-h-[calc(100dvh-190px)];
  }
  .responsive-image-size {
    @apply max-w-[150px]
  }
}

/* ADD NEW CSS */
.type-badge svg {
  @apply mr-1;
}

.custom-radio .ant-checkbox-inner {
  @apply rounded-full;
}

.custom-radio .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #4caf50 !important;
  border-color: #4caf50 !important;
}

/* Start: google-map css */
.google-map .gm-style-iw.gm-style-iw-c {
  @apply !pt-2.5 !pr-3;
}

.google-map .gm-style-iw-ch {
  @apply !pt-0;
}

.google-map .gm-style-iw-ch:empty,
.submittal-approved-via:empty {
  @apply hidden;
}

.google-map .gm-ui-hover-effect {
  @apply !w-6 !h-6 hover:!bg-[#f0f0f0] !flex items-center justify-center !absolute right-1 top-1.5;
}

.google-map .gm-ui-hover-effect span {
  @apply !m-0 !w-[18px] !h-[18px] ease-in-out duration-300 rotate-0;
}

.google-map .gm-ui-hover-effect:hover span {
  @apply rotate-90;
}

/* End: google-map css */

.chart-blank-value .apexcharts-series .apexcharts-bar-area[val="0"] {
  @apply fill-none border-none;
}

@media screen and (max-width: 600px) {
  .header-breadcrumb {
    @apply overflow-x-scroll;
  }
}

.clip-polygon-shape {
  clip-path: polygon(
    0 0,
    20% 80%,
    40% 40%,
    60% 40%,
    80% 0%,
    100% 35%,
    100% 100%,
    0% 100%
  );
}

.shape-size {
  background-size: 1000px 100%;
}

.funnel-shape {
  clip-path: polygon(0% 0, 100% 0, 64% 65%, 64% 100%, 35% 100%, 35% 65%);
}

.multi-avatar-scroll-group-popover .ant-popover-inner-content {
  @apply max-h-[252px] overflow-y-auto;
}

.sidebar-backbutton .ant-btn-icon {
  @apply !m-0;
}

@media (max-width: 767px) {
  .footer-content:not(.chat-view) > .footer-left {
    @apply !hidden;
  }
  .footer-content:not(.chat-view) > .footer-left.hidden {
    @apply !block;
  }
  .footer-content.chat-view > .footer-center,
  .footer-content.chat-view > .footer-right {
    @apply !hidden;
  }
}

.cost-by-type-block .ag-body .ag-row-last {
  @apply bg-[#f4f5f6];
}


.cost-by-type-block .ag-body .ag-pinned-left-cols-container [col-id="name"].ag-cell-value,
.financial-summary-widget .ag-body .ag-pinned-left-cols-container [role="row"]:not([row-id="4"], [row-id="6"]) .ag-cell-value span,
.financial-summary-widget .ag-body [row-id="7"] [col-id="estimated_cost"].ag-cell-value span,
.financial-summary-widget .ag-body [row-id="7"] [col-id="estimated_markup"].ag-cell-value span,
.financial-summary-widget .ag-body [row-id="9"] [col-id="estimated_cost"].ag-cell-value span,
.financial-summary-widget .ag-body [row-id="9"] [col-id="estimated_markup"].ag-cell-value span {
  @apply text-[#777] font-medium !text-xs;
}

.financial-summary-widget .ag-body .ag-pinned-left-cols-container [role="row"] .ag-cell-value {
  @apply !text-xs;
}

.financial-summary-widget .ag-body [row-id="4"],
.financial-summary-widget .ag-body [row-id="6"] {
  @apply border-b border-primary-900/10 bg-[#f4f5f6];
}

.cost-by-type-block .ag-body .ag-row-last .ag-cell-value span,
.cost-by-type-block .ag-body .ag-row-last .ag-cell-value,
.financial-summary-widget .ag-body [row-id="4"] .ag-cell-value span,
.financial-summary-widget .ag-body [row-id="6"] .ag-cell-value span,
.financial-summary-widget .ag-body [row-id="4"] [col-id="name"].ag-cell-value .table-tooltip-text,
.financial-summary-widget .ag-body [row-id="6"] [col-id="name"].ag-cell-value .table-tooltip-text{
  @apply !text-primary-900 !font-semibold;
}

.financial-summary-widget .apexcharts-legend {
  @apply p-0 w-full flex-col;
}

.project-select-field.cf-field.cf-new-select-field.edit-inline
  .ant-select-selector
  .ant-select-selection-item {
  @apply font-semibold text-[15px] leading-7;
}
.static-table.thead-select-field
  .cf-field.cf-new-select-field.edit-inline
  .select-field-block
  .ant-select-selector:not(
    .tax-select-filed
      .cf-field.cf-new-select-field.edit-inline
      .ant-select-selector
  ) {
  @apply !pl-0;
}
.sov-tax-field .ant-select-selection-item {
  @apply !text-sm;
}

.sov-content .ag-floating-bottom {
  @apply border-t border-primary-900/10;
}

.report-field .ant-input-label {
  @apply text-white;
}

.border-select-filed
  .ant-select-selection-search
  .ant-select-selection-search-input {
  @apply !px-[11px];
}

.signature-bg .lg-backdrop.in {
  @apply !bg-[#0b0b0b80];
}

.incident-status ul {
  @apply max-h-[calc(100vh-350px)] overflow-y-auto;
}

.generate-bill tr:nth-child(even) {
  @apply bg-[#f9f9f9] hover:bg-[#22355814];
}

.select-redirect-filed .select-field-block {
  @apply !pr-10;
}

.select-redirect-filed .select-field-block .ant-select-arrow {
  @apply right-[35px];
}

.item-pre-next-button.ant-btn[disabled] svg {
  @apply !text-primary-900/30;
}

.item-pre-next-button.ant-btn svg {
  @apply !text-primary-900;
}

.input-number-right-icon .ant-input-number-wrapper,
.input-number-right-icon .ant-input-number-affix-wrapper {
  @apply !h-[34px] !pl-0;
}

.input-number-right-icon .ant-input-number-group-addon {
  @apply pr-0 invisible transition-none;
}

.input-number-right-icon:hover .ant-input-number-group-addon {
  @apply visible;
}

.input-number-right-icon .ant-input-number-disabled {
  color: inherit;
}

.st-mapview .dhx_cal_tab {
  @apply !border !border-[#212529] active:!bg-[#eceff1] active:text-[#747473];
}

.top-cross-button .ant-btn-icon {
  @apply h-3 flex items-center justify-center;
}

@supports (-moz-appearance: none) {
  .est-by-status {
    padding-right: 14px;
  }
}

/* .est-item-table .ant-collapse-header {
  @apply !items-center;
} */

.select_template_dropdown .ant-dropdown-menu {
  @apply max-h-fit;
}

.multiple-table-combine {
  @apply overflow-x-auto;
}

.multiple-table-combine td {
  @apply px-2.5 py-[7px] text-13 text-[#212529];
}

.multiple-table-combine td .ant-typography {
  @apply w-fit max-w-full;
}

.multiple-table-combine tbody tr:nth-child(even) {
  @apply bg-[#f9f9f9];
}

.multiple-table-combine tbody tr:hover {
  @apply bg-[rgba(34,53,88,0.08)];
}

.multiple-table-combine thead,
.bidders-submission-table tbody tr:nth-child(2) {
  @apply bg-[#f8f8f8] border-b border-[#e8e8e8] font-semibold;
}

.multiple-table-combine thead th,
.bidders-submission-table tbody tr:nth-child(2) td {
  @apply px-2.5 py-[5px] text-13 text-black;
}

.bidders-submission-table td {
  @apply whitespace-nowrap;
}

.bidders-submission-table td:not(:first-child, :nth-child(2)),
.bidders-submission-table thead th:not(:first-child, :nth-child(2)),
.supplier-pricing-table thead th:not(:first-child, :nth-child(2)),
.supplier-pricing-table td:not(:first-child, :nth-child(2)) {
  @apply min-w-[180px] max-w-[180px] w-[180px] text-right;
}

.bidders-submission-table td:first-child,
.supplier-pricing-table td:first-child {
  @apply max-w-[150px] min-w-[150px];
}

.bidders-submission-table td:nth-child(2),
.bidders-submission-table th:nth-child(2),
.supplier-pricing-table td:nth-child(2),
.supplier-pricing-table th:nth-child(2) {
  @apply w-[100px] min-w-[100px] text-right;
}

.ag-select-edit-field .ant-select {
  @apply !border-0;
}

.leads-header .input-filter {
  @apply max-w-[178px];
}

.project-items-chart .apexcharts-canvas {
  @apply mx-auto;
}

.cf-new-select-field .ant-select:not(.cf-new-select-field .ant-select-open, .cf-new-select-field .header-select-status-dropdown, .cf-new-select-field.edit-inline .ant-select) .ant-select-arrow .anticon-down{
  @apply text-primary-900/80;
}

.fr-view,
.froala-editor,
.froala-editor .editor-text,
.word-wrap-break {
  word-break: break-word;
}

.froala-editor img.fr-dib {
  @apply inline;
}

.fr-view a {
  @apply text-black underline hover:text-deep-orange-500;
}

.update-release {
  color: #000000e6;
  font-size: 14px;
  line-height: 24px;
}

.update-release h1 {
  @apply text-4xl;
}

.update-release h2 {
  @apply text-3xl;
}

.update-release h3 {
  @apply text-2xl;
}

.update-release h4 {
  @apply text-xl;
}

.update-release h5 {
  @apply text-lg;
}

.update-release h6 {
  @apply text-base;
}

.update-release p:not(:last-child) {
  margin-bottom: 8px;
}

.update-release ul {
  padding-left: 27px;
  list-style-type: disc;
  margin: 15px 0;
}

.update-release ul ol ul {
  list-style-type: square;
}

.update-release ol {
  padding-left: 27px;
  list-style-type: decimal;
}

.update-release ol ul {
  list-style-type: circle;
}

.filter-filed-block {
  @apply min-w-[300px] max-w-[300px];
}

/* animation */
.animation {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animation-shimmer-line {
  height: 10px;
  width: 100%;
  margin: 10px 0px;
}

.animation-shimmer-paragraph {
  height: 10px;
  width: 50%;
  margin: 10px 0px;
}

.animation-shimmer-title {
  height: 15px;
  width: 40%;
  margin: 15px 0px;
}

.animation-shimmer-paragraph-list {
  height: 10px;
  width: 100%;
  margin: 15px 0px;
}

.paragraph-list {
  padding-left: 50px;
  margin-top: 15px;
}

.animation-bg {
  background-color: #e2e8f0;
  border-radius: 5px;
}

.ant-picker .ant-picker-suffix,
.ant-picker .ant-picker-clear {
  margin-inline-end: 4px;
}

.ant-picker .ant-picker-clear .anticon-close-circle {
  vertical-align: -0.125em;
}

.ant-picker .ant-picker-clear .anticon-close-circle svg {
  @apply w-3 h-3;
}

.field-before-none .cf-field {
  @apply before:hidden;
}

@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}

@keyframes bg-toggle {
  0%,
  100% {
    background-position: 0 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.report-list-table.ag-theme-alpine {
  --ag-header-height: 30px;
  --ag-header-foreground-color: white;
  --ag-header-background-color: #2c2c2c;
}

.report-list-table.ag-theme-alpine .ag-header .ag-header-cell-text {
  @apply text-white;
}

.report-list-table.ag-theme-alpine .ag-pinned-left-cols-container {
  border-right: 1px solid #ced4da;
}

.report-list-table.ag-theme-alpine .ag-root-wrapper {
  border-width: 1px !important;
  border-radius: 4px;
}

.report-list-table.ag-theme-alpine
  .ag-cell-wrapper
  > *:not(.ag-cell-value):not(.ag-group-value) {
  height: var(--ag-line-height, calc(var(--ag-row-height) - 2px));
}

.ag-row-footer {
  @apply !bg-primary-8;
}

.ag-row-footer .ag-cell span {
  @apply font-semibold text-primary-900;
}

.ag-row-footer .ag-cell-value {
  @apply font-semibold text-primary-900;
}

.selectfield-medium .ant-select-selection-item {
  @apply font-medium text-base;
}

.froala-editor .fr-box .fr-toolbar.fr-sticky-on,
.editor-table-border .fr-box .fr-toolbar.fr-sticky-on {
  @apply relative;
}

.froala-editor .fr-box .fr-sticky-dummy,
.editor-table-border .fr-box .fr-sticky-dummy {
  @apply hidden;
}

.editor-table-border .fr-view table th,
.editor-table-border .fr-view table td {
  border: none;
}

.editor-table-border .fr-view table td img.fr-dib {
  margin: 0;
}

.fr-popup .fr-buttons .fr-btn[data-cmd="linkStyle"] {
  @apply hidden;
}

.header-date-picker .ant-picker .ant-picker-clear,
.header-date-picker .ant-picker .ant-picker-suffix {
  margin-inline-end: 0px;
  margin-left: 0;
}

.header-date-picker .ant-picker .ant-picker-clear .anticon-close-circle {
  vertical-align: -1px;
}

.ant-collapse-item-active .btn-collapse {
  @apply !bg-primary-900;
}

.ant-collapse-item-active .btn-collapse .ant-typography {
  @apply !text-white;
}

.editor-list {
  @apply text-sm leading-[1.6];
}

.editor-list ul {
  @apply list-disc pl-5;
}

.editor-list ol {
  @apply list-decimal pl-5;
}

.gantt-wrapper
  .gantt_cell[style*="justify-content: center;"]
  .gantt_tree_content,
.gantt-wrapper
  .gantt_cell[style*="justify-content:center;"]
  .gantt_tree_content {
  @apply justify-center;
}

.dhx_cal_light .dhx_cal_ltitle {
  @apply !bg-white;
}

.dhx_cal_light .dhx_cal_larea {
  @apply pb-3;
}

.dhx_cal_light.dhx_cal_light_wide .dhx_cal_larea .dhx_wrap_section {
  @apply sm:flex-nowrap flex-wrap
}

.dhx_cal_light .dhx_cal_larea .dhx_cal_lsection {
  @apply pl-0 sm:w-[120px] w-full;
}

.dhx_cal_light .dhx_cal_larea .dhx_cal_lsection_title label{
  @apply break-words text-sm text-[#4B4B4B] font-semibold capitalize;
}

.dhx_cal_light .dhx_cal_larea .dhx_cal_lsection label input[type="checkbox"] {
  @apply w-4 h-4 accent-primary-900 !mt-0;
}

.dhx_cal_light .dhx_cal_larea .dhx_cal_ltext textarea,
.dhx_cal_light .dhx_cal_larea .dhx_section_time select {
  @apply py-1 px-1.5 !border !rounded !text-primary-900 !font-normal disabled:!bg-[#f4f5f6];
}

.dhx_cal_light .dhx_cal_larea .dhx_section_time .dhx_section_time_spacer {
  @apply h-auto visible sm:basis-auto;
}

.dhx_cal_light .dhx_cal_lcontrols .dhx_btn_set {
  @apply my-0;
}

#opensesame-cell-widget {
  @apply !z-[1001];
}

#opensesame-cell-widget .bottom-6 {
  @apply !bottom-[3px] !h-auto 2xl:!w-[40%] !w-[30%];
}

#opensesame-cell-widget .copilot-shadow.p-2 {
  @apply !py-0;
}

#opensesame-cell-widget .copilot-shadow.h-14 {
  @apply !h-auto;
}

#opensesame-cell-widget .copilot-shadow div:has(>img.w-8),
#opensesame-cell-widget .copilot-shadow img[alt="Logo"].w-8 {
  @apply !w-6;
}

#opensesame-cell-widget .copilot-shadow div:has(>img.h-8),
#opensesame-cell-widget .copilot-shadow img[alt="Logo"].h-8 {
  @apply !h-6;
}

#opensesame-cell-widget .copilot-shadow textarea.py-2 {
  @apply py-0 placeholder:!text-[#223558] placeholder:!font-semibold placeholder:!opacity-100;
}

#opensesame-cell-widget .copilot-shadow button svg {
  @apply w-5 h-5 my-0.5 mx-0 align-middle;
}

#opensesame-cell-widget > div:not(.fixed) {
  @apply !hidden;
}

#opensesame-cell-widget .z-50:not(.bottom-6)[style*="height: 700px;"] {
  @apply max-h-[700px] !h-[90vh] !top-auto bottom-[3px];
}

@media (min-width: 1700px) {
  .status-dropdown-block {
    @apply max-w-[200px];
  }
}

@media (min-width: 1500px) and (max-width: 1699px) {
  .status-dropdown-block {
    @apply max-w-[150px];
  }
}

@media (min-width: 1350px) and (max-width: 1499px) {
  .status-dropdown-block {
    @apply max-w-[120px];
  }
}

@media (min-width: 1350px) and (max-width: 1499px) {
  .status-dropdown-block {
    @apply max-w-[120px];
  }
}

@media (min-width: 1280px) and (max-width: 1349px) {
  .status-dropdown-block {
    @apply max-w-[100px];
  }
}

@media (max-width: 1350px) {
  #opensesame-cell-widget .bottom-6.left-1\/2 {
    @apply left-[45%]
  }
}

@media (max-width: 1279px) {
  .status-dropdown-block {
    @apply max-w-[250px];
  }
}

@media (max-width: 1100px) {
  #opensesame-cell-widget .bottom-6.left-1\/2 {
    @apply left-[44%] max-w-[140px];
  }

  #opensesame-cell-widget .copilot-shadow.px-6 {
    @apply !px-3;
  }
}

@media (max-width: 1023px) {
  .status-dropdown-block {
    @apply max-w-[150px];
  }
}

@media (max-width: 900px) {
  .filter-filed-block {
    @apply min-w-[250px] max-w-[250px];
  }
}

@media (max-width: 639px) {
  .dhx_cal_light.dhx_cal_light_wide {
    @apply w-full;
  }
}

@media (max-width: 479px) {
  .status-dropdown-block {
    @apply max-w-[100px];
  }
  .wizard-refresh-icon {
    @apply absolute top-2 right-[15px];
  }
}

.tooltip-wrapper {
  @apply relative cursor-pointer hover:z-50
}

.tooltip-wrapper::after {
  content: attr(data-tooltip);
  @apply absolute bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap z-50 transition-opacity duration-200;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  pointer-events: none;
}

.tooltip-wrapper::before {
  content: "";
  @apply absolute w-0 h-0 border-x-4 border-x-transparent border-t-4 border-t-black;
  bottom: 117%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  pointer-events: none;
}

.tooltip-wrapper:hover::after,
.tooltip-wrapper:hover::before {
  @apply opacity-100;
}

.sent-email-preview img{
  @apply inline-block
}

.dhx_mini_calendar .dhx_cal_month_cell.dhx_now {
  @apply !bg-transparent
}

.dhx_mini_calendar .dhx_month_head {
  @apply !rounded
}

.dhx_mini_calendar .dhx_cal_month_cell.dhx_now .dhx_month_head {
  @apply bg-[#d5d5d5]
}

.dhx_mini_calendar div.dhx_month_head.dhx_year_event {
  @apply bg-primary-900 text-white
}