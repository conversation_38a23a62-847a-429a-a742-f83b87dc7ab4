/*
Product Name: dhtmlxMenu 
Version: 4.0.3 
Edition: Standard 
License: content of this file is covered by GPL. Usage outside GPL terms is prohibited. To obtain Commercial or Enterprise <NAME_EMAIL>
Copyright UAB Dinamenta http://www.dhtmlx.com
*/

/* skin detected, extra file: skins/dhx_terrace.less */

.dhxmenu_skin_detect {
  position: absolute;
  left: 0px;
  top: -100px;
  margin: 0;
  padding: 0;
  border: 0px solid white;
  width: 30px;
  height: 10px;
  overflow: hidden;
}
.dhtmlxMenu_dhx_terrace_Middle {
  position: relative;
  height: 34px;
  padding: 0px 5px;
  border: none;
  overflow: hidden;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
}
.dhtmlxMenu_dhx_terrace_Middle div.top_sep {
  float: left;
  position: relative;
  height: 20px;
  margin: 5px 6px 0px 5px;
  line-height: 1px;
  font-size: 1px;
  overflow: hidden;
  cursor: default;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
}
.dhtmlxMenu_dhx_terrace_Middle img.dhtmlxMenu_TopLevel_Item_Icon {
  float: left;
  margin: 2px 3px 0px 3px;
  width: 18px;
  height: 18px;
}
.dhtmlxMenu_dhx_terrace_Middle div.dhtmlxMenu_TopLevel_Text_right {
  position: absolute;
  top: 10px;
  left: none;
  right: 8px;
  font-family: Arial;
  font-size: 13px;
  color: #333333;
  cursor: default;
}
.dhtmlxMenu_dhx_terrace_Middle div.dhtmlxMenu_TopLevel_Text_left {
  position: absolute;
  top: 10px;
  right: none;
  left: 8px;
  font-family: Arial;
  font-size: 13px;
  color: #333333;
  cursor: default;
}
.dhtmlxMenu_dhx_terrace_Middle.dir_left div.align_left {
  float: left;
}
.dhtmlxMenu_dhx_terrace_Middle.dir_left div.align_right {
  float: right;
}
div.dhtmlxMenu_dhx_terrace_TopLevel_Item_Normal,
div.dhtmlxMenu_dhx_terrace_TopLevel_Item_Disabled,
div.dhtmlxMenu_dhx_terrace_TopLevel_Item_Selected {
  position: relative;
  float: left;
  font-family: Arial;
  font-size: 13px;
  color: #333333;
  border: 1px solid #cccccc;
  background-color: #f5f5f5;
  cursor: default;
  white-space: nowrap;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
  height: 22px;
  line-height: 22px;
  vertical-align: middle;
  margin-top: 2px;
  padding: 3px 5px;
}
div.dhtmlxMenu_dhx_terrace_TopLevel_Item_Normal div.top_level_text,
div.dhtmlxMenu_dhx_terrace_TopLevel_Item_Disabled div.top_level_text,
div.dhtmlxMenu_dhx_terrace_TopLevel_Item_Selected div.top_level_text {
  float: left;
  margin: 0px 5px;
}
div.dhtmlxMenu_dhx_terrace_TopLevel_Item_Disabled {
  color: #d1d1d1;
  background-color: #ededed;
}
div.dhtmlxMenu_dhx_terrace_TopLevel_Item_Selected {
  color: #2e2e2e;
  background-color: #ebebeb;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon {
  position: absolute;
  border: 1px solid #cccccc;
  /* *border: 1px solid #c7c7c7; */
  box-shadow: 0 0 5px rgba(127, 127, 127, 0.35);
  padding: 3px 0px;
  border-bottom-left-radius: 1px;
  border-bottom-right-radius: 1px;
  border-top-right-radius: 1px;
  background-color: #f5f5f5;
  overflow: hidden;
  cursor: default;
  line-height: normal;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  td {
  font-family: Arial;
  font-size: 13px;
  color: #333333;
  line-height: normal;
  padding: 0px 5px;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  tr.sub_item_selected
  td {
  background-color: #fff3a1;
  color: black;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  td.sub_item_icon {
  width: 18px;
  text-align: center;
  vertical-align: middle;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  td.sub_item_icon
  img.sub_icon {
  float: left;
  margin: 0px;
  width: 18px;
  height: 18px;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  div.sub_item_text {
  padding: 0px 16px 0px 1px;
  height: 26px;
  line-height: 25px;
  white-space: nowrap;
  text-align: left;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  tr.sub_item_dis
  div.sub_item_text {
  color: #bbbbbb;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  td.sub_item_hk {
  padding-left: 8px;
  padding-right: 8px;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  td.sub_item_hk
  div.sub_item_hk {
  font-family: Arial;
  font-size: 13px;
  color: #737373;
  text-align: right;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  tr.sub_item_dis
  td.sub_item_hk
  div.sub_item_hk {
  color: #c8c8c8 !important;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  div.complex_arrow {
  width: 4px;
  margin: 0px 2px 0px 5px;
  height: 22px;
  background-image: url("/imgs/dhxmenu_terrace/dhxmenu_subar.gif");
  background-repeat: no-repeat;
  background-position: 0px 0px;
  overflow: hidden;
  font-size: 1px;
  float: right;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  tr.sub_item_selected
  div.complex_arrow {
  background-position: -4px 0px !important;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  tr.sub_item_dis
  div.complex_arrow {
  background-position: -8px 0px !important;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  div.complex_arrow_loading {
  width: 11px;
  height: 22px;
  background-position: center center;
  background-repeat: no-repeat;
  background-image: url("/imgs/dhxmenu_terrace/dhxmenu_loader.gif");
  float: right;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  div.sub_icon {
  float: left;
  margin: 0px;
  width: 18px;
  height: 18px;
  background-position: top right;
  background-repeat: no-repeat;
  background-image: url("/imgs/dhxmenu_terrace/dhxmenu_chrd.gif");
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  div.sub_icon.chbx_0 {
  background-position: 0px 0px !important;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  div.sub_icon.chbx_1 {
  background-position: -18px 0px !important;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  tr.sub_item_dis
  div.sub_icon.chbx_0 {
  background-position: -36px 0px !important;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  tr.sub_item_dis
  div.sub_icon.chbx_1 {
  background-position: -54px 0px !important;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  div.sub_icon.rdbt_0 {
  background-position: -72px 0px !important;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  div.sub_icon.rdbt_1 {
  background-position: -90px 0px !important;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  tr.sub_item_dis
  div.sub_icon.rdbt_0 {
  background-position: -108px 0px !important;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon
  table.dhtmlxMebu_SubLevelArea_Tbl
  tr.sub_item_dis
  div.sub_icon.rdbt_1 {
  background-position: -126px 0px !important;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon tr.sub_sep td {
  padding: 2px 0px;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_Polygon div.sub_sep {
  position: static;
  font-size: 1px;
  line-height: 1px;
  height: 1px;
  width: 100%;
  border-top: 1px solid #e8e8e8;
}
iframe.dhtmlxMenu_IE6CoverFix_dhx_terrace {
  position: absolute;
  border: none;
  background: #000000;
  filter: progid:DXImageTransform.Microsoft.Alpha(opacity=100);
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_ArrowUp,
div.dhtmlxMenu_dhx_terrace_SubLevelArea_ArrowUp_Over,
div.dhtmlxMenu_dhx_terrace_SubLevelArea_ArrowUp_Disabled {
  position: relative;
  font-size: 1px;
  border-bottom: 1px solid #cccccc;
  background-image: url("/imgs/dhxmenu_terrace/dhxmenu_arrow_up.gif");
  background-repeat: no-repeat;
  background-position: center center;
  padding: 8px 0px;
  margin-bottom: 3px;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_ArrowUp_Disabled {
  background-image: url("/imgs/dhxmenu_terrace/dhxmenu_arrow_up_dis.gif");
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_ArrowDown,
div.dhtmlxMenu_dhx_terrace_SubLevelArea_ArrowDown_Over,
div.dhtmlxMenu_dhx_terrace_SubLevelArea_ArrowDown_Disabled {
  position: relative;
  font-size: 1px;
  border-top: 1px solid #cccccc;
  background-image: url("/imgs/dhxmenu_terrace/dhxmenu_arrow_down.gif");
  background-repeat: no-repeat;
  background-position: center center;
  padding: 8px 0px;
  margin-top: 3px;
}
div.dhtmlxMenu_dhx_terrace_SubLevelArea_ArrowDown_Disabled {
  background-image: url("/imgs/dhxmenu_terrace/dhxmenu_arrow_down_dis.gif");
}
