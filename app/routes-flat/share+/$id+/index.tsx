import {
  use<PERSON><PERSON><PERSON>,
  use<PERSON><PERSON>der<PERSON><PERSON>,
  Links,
  ScrollRestoration,
  Scripts,
  LiveReload,
} from "@remix-run/react";
import { json, LoaderFunction } from "@remix-run/node";
import { useEffect, useState } from "react";
// css
import tailwind from "~/assets/minify/tailwind.style.css";
import authStyle from "~/assets/minify/auth.style.css";
// Atoms
import Logo from "~/shared/components/atoms/logo/Logo";
import { Typography } from "~/shared/components/atoms/typography";
import { TypographyLink } from "~/shared/components/atoms/typographyLink";

export const links: TLinksFunction = () => [
  { rel: "stylesheet", href: tailwind },
  { rel: "stylesheet", href: authStyle },
];

function detectDevice(userAgent: string | null) {
  const ua = userAgent?.toLowerCase() || "";
  const isAndroid = /android/.test(ua);
  const isIOS = /iphone|ipad|ipod/.test(ua);
  const isMobile = isAndroid || isIOS || /mobile/.test(ua);

  let deviceType: "android" | "ios" | "web" = "web";
  if (isAndroid) deviceType = "android";
  else if (isIOS) deviceType = "ios";

  return {
    isMobile,
    isAndroid,
    isIOS,
    deviceType,
  };
}

export const loader: LoaderFunction = async ({ params, request }) => {
  const userAgent = request.headers.get("user-agent");
  const deviceInfo = detectDevice(userAgent);

  return json({
    id: params.id,
    ...deviceInfo,
  });
};

export default function SharePage() {
  const { id } = useParams();
  const data = useLoaderData<{
    id: string;
    isMobile: boolean;
    isAndroid: boolean;
    isIOS: boolean;
    deviceType: string;
  }>();

  const [appUrl, setAppUrl] = useState<string | null>(null);
  const [fallbackUrl, setFallbackUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [isOpening, setIsOpening] = useState(false);

  useEffect(() => {
    if (!id || !data.deviceType) return;

    const fetchLinkData = async () => {
      try {
        const res = await fetch(
          `https://api3-dev.contractorforeman.net/api/share/${id}?platform=${data.deviceType}`
        );
        if (!res.ok) throw new Error(`API error: ${res.status}`);

        const { data: apiData } = await res.json();

        if (!apiData) throw new Error("Link data missing");

        // Web: redirect immediately
        if (data.deviceType === "web" && apiData.originalUrl) {
          const parsedUrl = new URL(
            apiData.originalUrl || "",
            window.location.origin
          );
          const finalUrl =
            window.location.origin + parsedUrl.pathname + parsedUrl.hash;
          window.location.href = finalUrl;
          return;
        }

        // iOS: build scheme:// link
        if (
          data.deviceType === "ios" &&
          apiData.scheme &&
          apiData.phpOriginalUrl
        ) {
          const schemeUrl = `${apiData.scheme}://${apiData.phpOriginalUrl}`;
          setAppUrl(schemeUrl);
          setFallbackUrl(apiData.fallbackUrl);
        }

        // Android: build intent:// link
        // if (
        //   data.deviceType === "android" &&
        //   apiData.scheme &&
        //   apiData.phpOriginalUrl &&
        //   apiData.package
        // ) {
        //   const stripped = apiData.phpOriginalUrl.replace(/^https?:\/\//, "");
        //   const intentUrl = `intent://${stripped}#Intent;scheme=${
        //     apiData.scheme
        //   };package=${
        //     apiData.package
        //   };S.browser_fallback_url=${encodeURIComponent(
        //     apiData.fallbackUrl
        //   )};end`;
        //   setAppUrl(intentUrl);
        //   setFallbackUrl(apiData.fallbackUrl);
        // }
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchLinkData();
  }, [id, data.deviceType]);

  const logAction = async (action: string) => {
    try {
      await fetch("/api/share/log", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ id, deviceType: data.deviceType, action }),
      });
    } catch (e) {
      console.warn("Logging failed", e);
    }
  };

  // Only open app when user clicks (no auto-fallback)
  const handleOpenApp = () => {
    if (!appUrl) return;

    setIsOpening(true);
    logAction("attempt_open");

    let opened = false;
    const onVisibilityChange = () => {
      if (document.hidden) opened = true;
    };

    document.addEventListener("visibilitychange", onVisibilityChange);

    window.location.href = appUrl;

    setTimeout(() => {
      document.removeEventListener("visibilitychange", onVisibilityChange);
      setIsOpening(false);

      // Removed the automatic fallback redirect
    }, 1500);
  };

  const handleManualDownload = () => {
    if (fallbackUrl) {
      logAction("manual_fallback");
      window.location.href = fallbackUrl;
    }
  };

  return (
    <html lang="en">
      <head>
        <title>Contractor Foreman Share</title>
        <meta name="apple-itunes-app" content="app-id=1239787613" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, user-scalable=no"
        />
        <Links />
      </head>
      <body className="bg-[#f8f8f8] max-h-[calc(100vh-31px)] overflow-y-auto overflow-hidden">
        <div className="bg-gray-100 bg-gradient-to-br from-orange-100/40 to-primary-900/20">
          <div className="h-screen flex items-center justify-center min-h-[550px]">
            {loading && !error && <p>Loading...</p>}

            {!loading && error && (
              <p style={{ color: "red" }}>Error: {error}</p>
            )}

            {!loading && !error && appUrl && (
              <div className="auth-page-block">
                <div className="auth-page-inner">
                  <div className="shadow-md rounded-md bg-gray-50 h-auto lg:!w-[35rem] md:!w-[30rem] sm:!w-[26rem] !w-full relative ">
                    <Typography className="auth-header-border"></Typography>
                    <div className="auth-logo-block">
                      <Logo />
                    </div>
                    <div className="flex justify-center flex-col text-center px-5 md:px-8 pb-5">
                      <div className="text-[15px] text-[#4b5563] font-openSans max-h-[200px] overflow-auto text-left leading-normal my-2">
                        <p>
                          Try it FREE for 30 days! Over 35 powerful features.
                          Price Lock + Unlimited Projects means that your price
                          never goes up. Use one module or use them all &ndash;
                          it's still 1/3 the price of our competitors.
                        </p>
                      </div>

                      <div className="flex justify-center text-center gap-3 mt-3 flex-wrap">
                        <button
                          className="bg-primary-900 text-white border-none px-3 py-2 rounded-md md:text-lg text-md leading-normal cursor-pointer hover:bg-primary-800 transition-colors duration-200"
                          style={{
                            cursor: isOpening ? "not-allowed" : "pointer",
                          }}
                          onClick={handleOpenApp}
                          disabled={isOpening}
                        >
                          {isOpening ? "Opening..." : "Open in App"}
                        </button>

                        <Typography className="w-full text-[15px] !text-[#223558] font-openSans max-h-[200px] overflow-auto text-center leading-normal pt-1">
                          If the app didn't open,{" "}
                          <TypographyLink
                            onClick={handleManualDownload}
                            className="text-[15px] !text-[#223558] hover:!text-[#ff5400] !underline hover:!underline-none duration-200 ml-1 cursor-pointer"
                          >
                            download it from the{" "}
                            {data.deviceType === "ios"
                              ? "App Store"
                              : "Play Store"}
                          </TypographyLink>
                          .
                        </Typography>
                      </div>
                    </div>
                  </div>
                  <div>
                    <Typography className="flex justify-center !mb-0 !text-sm !text-slate-500 opacity-75 py-6">
                      &copy; Copyright{" "}
                      {new Date().toLocaleString("en-GB", {
                        year: "numeric",
                      })}
                      . All Rights Reserved.
                    </Typography>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <ScrollRestoration />
        <Scripts />
        <LiveReload />
      </body>
    </html>
  );
}
