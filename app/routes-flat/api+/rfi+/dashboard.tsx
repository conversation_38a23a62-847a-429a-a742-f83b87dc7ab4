import { json } from "@remix-run/server-runtime";
import { apiCall } from "~/shared/services/apiService/apiService.server";
import { authorizeAction } from "~/shared/middlewares/authorizeApi.server";

export const action: TActionFunction = authorizeAction(
  async ({ request, context }) => {
    const { form, user, token } = context as unknown as IAuthorizeActionContext;
    const result = await apiCall({
      url: "rfi/dashboard",
      data: form,
      method: "post",
      Authorization: token,
      request,
    });
    return json(result);
  }
);
