import { faLock<PERSON>eyholeOpen } from "@fortawesome/pro-regular-svg-icons";
import { useNavigate, useOutletContext, useParams } from "@remix-run/react";
import delay from "lodash/delay";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Number, replaceDOMParams, sanitizeString } from "~/helpers/helper";

import {
  ClientAccessTab,
  ContactsTab,
  DetailsTab,
  DocumentsTab,
  FilesPhotosTab,
  FinancialTab,
  ProcurementTab,
  ProjectDetailsTopBar,
  ReportTab,
  ScheduleTab,
  SovTab,
  SummaryTab,
  TimeTab,
} from "~/modules/projectManagement/pages/project/components/tab";
import ProjectStatusbar from "~/modules/projectManagement/pages/project/components/tab/projectStatusbar";
import { updateProjectApi } from "~/modules/projectManagement/pages/project/redux/action/proDashAction";
import ProjectStoreProvider from "~/modules/projectManagement/pages/project/redux/projectStoreProvider";
import {
  setNoDetailsAvail,
  updateProjectDetail,
} from "~/modules/projectManagement/pages/project/redux/slices/proDetailsSlice";
import {
  useAppProDispatch,
  useAppProSelector,
} from "~/modules/projectManagement/pages/project/redux/store";
import {
  ProjectDetailsField,
  ProjectfieldStatus,
  ProjectStatusIconMap,
} from "~/modules/projectManagement/pages/project/utils/constants";
import { apiRoutes, routes } from "~/route-services/routes";
// molecules
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
// Organisms
import { TimeLineFooter } from "~/shared/components/organisms/timeLine/timeLineFooter";
// Other
import {
  getCommonSidebarCollapse,
  getGModuleDashboard,
  getGSettings,
} from "~/zustand";
import { fetchProjectType } from "~/modules/projectManagement/pages/project/redux/action/addProjectAction";
import { getTaxDetails } from "~/redux/action/getTaxDetailsAction";
import { Spin } from "~/shared/components/atoms/spin";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { fetchProjectFinancialModules } from "~/modules/projectManagement/pages/project/redux/action/ProjectFinancialAction";
import { fetchProjectDocumentsModules } from "~/modules/projectManagement/pages/project/redux/action/ProjectDocumentAction";
import {
  fetchProjectSOVItemsApi,
  fetchSovSummaryApi,
  fetchUnitsApi,
} from "~/modules/projectManagement/pages/project/redux/action/projectSovAction";
import { getItemTypes } from "~/redux/action/getItemTypes";
import { getCostCode } from "~/redux/action/getCostCodeAction";
import { fetchMarkupWithItemTypes } from "~/redux/action/commonMarkupWithItemTypesAction";
import {
  resetSovStates,
  setCurrentFilter,
  setIsSovItemsLoading,
} from "~/modules/projectManagement/pages/project/redux/slices/proSovSlice";
import {
  fetchProjectTimecards,
  fetchProjectTimesModules,
} from "~/modules/projectManagement/pages/project/redux/action/ProjectTimeAction";
import { getTimeCostCode } from "~/modules/projectManagement/pages/project/redux/action/getCostCodeAction";
import { fetchProjectReportModules } from "~/modules/projectManagement/pages/project/redux/action/ProjectReportAction";
import {
  collapseAllFolders,
  setSelectedFolder,
} from "~/modules/projectManagement/pages/project/redux/slices/projectFilePhotoLeftSlice";
import { callAgainFileList } from "~/modules/projectManagement/pages/project/redux/slices/projectFilePhotoRightSlice";
import { fetchProjectContactsApi } from "~/modules/projectManagement/pages/project/redux/action/projectDetailsAction";
// import { fetchGanttScheduleTasks } from "~/modules/projectManagement/pages/project/redux/action/scehduleAction";
import {
  fetchGanttScheduleTasks,
  fetchProjectListApi,
} from "~/redux/action/scehdulerAction";
import { useModuleAccess } from "~/modules/projectManagement/pages/project/hook/useModuleAcces";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { fetchDashData } from "~/modules/people/safetymeetings/redux/action";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { getCommonNotes } from "~/redux/action/commonNotesAction";

type TManageProjectTab = {
  fetchProDetailApis: () => void;
};

const ManageProjectTab = (props: TManageProjectTab) => {
  const { tab, id }: RouteParams = useParams(); // This type already declare.
  const sidebarCollapse: boolean | undefined = getCommonSidebarCollapse();
  const {
    details,
    isDetailLoading,
    noDetailsAvailable,
  }: IProjectDetailsInitialState = useAppProSelector(
    (state) => state.proDetails
  );
  const [apiLoading, setApiLoading] = useState<boolean>(false);
  const navigate = useNavigate();
  const dispatch = useAppProDispatch();
  const detailsRef = useRef<HTMLDivElement>(null);

  const context = useOutletContext<{ fetchProDetailApis: () => void } | null>();

  const fetchDetailApis =
    props.fetchProDetailApis || context?.fetchProDetailApis;

  const currentModule = getCurrentMenuModule();

  const {
    module_key = 0,
    module_id = 0,
    module_access = "",
    singular_name,
  } = currentModule || {};

  const gModuleDashboard: Partial<GModuleDashboard> = getGModuleDashboard();

  const {
    enoughProcurementAccess,
    enoughGanttModuleAccess,
    enoughClientModuleAccess,
    enoughReportModuleAccess,
    enoughTabScheduleAccess,
    enoughDocumentTabAccess,
    enoughTabFileAccess,
    hasEnoughAccessofProjectFinanceTabModule,
    timeTabNoAccess,
  } = useModuleAccess();
  const gSettings: GSettings = getGSettings();

  const StatusbarOption: Array<ModuleStatus> | undefined =
    gModuleDashboard?.module_setting?.module_status;

  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(ProjectfieldStatus);

  const [inputValues, setInputValues] =
    useState<Partial<IProjectDetails>>(ProjectDetailsField);

  const isReadOnly = useMemo(() => module_access === "read_only", [module_key]);
  const { isInitialLoad } = useAppProSelector((state) => state.proDashDocument);
  const [selectedPeriod, setSelectedPeriod] = useState("this_week");
  const [selectedHrsMoneyTab, setSelectedHrsMoneyTab] =
    useState<string>("amount");
  const [laborCostHrsTab, setLaborCostHrsTab] = useState<string>("cost");
  const [laborRate, setLaborRate] = useState(gSettings?.job_cost_wage_field);
  const [markup, setMarkup] = useState("excluded");
  const [generateFrom, setGenerateFrom] = useState(details?.budget_is);
  const [selectedReport, setSelectedReport] = useState("job_cost_actual");
  const { getGlobalModuleByKey } = useGlobalModule();
  const ProjectModule = useMemo(
    () => getGlobalModuleByKey(CFConfig.project_module),
    [getGlobalModuleByKey]
  );
  const { module_key: project_module_key = "" } = ProjectModule || {};

  useEffect(() => {
    if (details?.budget_is) {
      setGenerateFrom(details.budget_is);
    }
  }, [details?.budget_is]);

  const navigateToDetail = useCallback(() => {
    navigate(`${routes.MANAGE_PROJECT.url}/${id}/details`);
  }, []);

  useEffect(() => {
    // IMPORTANT: Don't navigate while details are still loading to prevent premature access checks
    // The access variables (enoughProcurementAccess, enoughClientModuleAccess, etc.) depend on
    // details data from Redux. During API fetching, details might be in initial state causing
    // access checks to return false and trigger unwanted navigation to detail page.
    // Wait until all dependencies are resolved before performing access checks.
    if (
      tab &&
      ["procurement", "client_access"].includes(tab) &&
      isDetailLoading
    ) {
      return;
    }

    const tabAccessMap: Record<string, boolean> = {
      procurement: Boolean(enoughProcurementAccess),
      schedule: Boolean(enoughGanttModuleAccess),
      client_access: Boolean(enoughClientModuleAccess),
      reports: Boolean(enoughReportModuleAccess),
      schedule_of_values: Boolean(enoughTabScheduleAccess),
      documents: Boolean(enoughDocumentTabAccess),
      files_photos: Boolean(enoughTabFileAccess),
      financial: Boolean(hasEnoughAccessofProjectFinanceTabModule),
      time: !timeTabNoAccess,
    };

    if (tab && tab in tabAccessMap && !tabAccessMap[tab]) {
      navigateToDetail();
    }
  }, [
    tab,
    isDetailLoading,
    enoughProcurementAccess,
    enoughGanttModuleAccess,
    enoughClientModuleAccess,
    enoughReportModuleAccess,
    enoughTabScheduleAccess,
    enoughDocumentTabAccess,
    enoughTabFileAccess,
    hasEnoughAccessofProjectFinanceTabModule,
    timeTabNoAccess,
  ]);

  useEffect(() => {
    setInputValues(details);
  }, [details]);

  useEffect(() => {
    if (tab === "reports") {
      dispatch(
        fetchProjectReportModules({
          project_id: id || "",
          filter_by: selectedReport,
          generate_from: generateFrom,
          factor_by: laborRate,
          show_all: 0,
        })
      );
    }
  }, [tab, selectedReport, generateFrom, laborRate, markup]);

  useEffect(() => {
    if (noDetailsAvailable) {
      notification.error({
        description: "No data found.",
      });
      dispatch(setNoDetailsAvail(false));
      setTimeout(() => {
        navigate(`${routes.MANAGE_PROJECT.url}`);
      }, 500);
    }
  }, [noDetailsAvailable]);

  const fetchData = useCallback(async () => {
    setApiLoading(true);
    if (tab === "financial") {
      const keys = [
        "bills",
        "change_orders",
        "estimate",
        "expenses",
        "invoices",
        "payments",
        "purchase_order",
        "sub_contarct",
        "work_orders",
      ];

      await dispatch(
        fetchProjectFinancialModules({
          id: Number(id),
          // module_key: keys,
        })
      );
    }

    if (tab === "documents") {
      const keys = [
        "bills",
        "change_orders",
        "estimate",
        "expenses",
        "invoices",
        "payments",
        "purchase_order",
        "sub_contarct",
        "work_orders",
      ];

      await dispatch(
        fetchProjectDocumentsModules({
          id: Number(id),
          // module_key: keys,
        })
      );

      dispatch(fetchDashData());

      dispatch(
        getCommonNotes({
          module_key: project_module_key,
          record_id: Number(id),
          order: gSettings?.comment_order,
        })
      );
    } else if (tab === "schedule_of_values") {
      dispatch(setCurrentFilter("all"));

      await dispatch(fetchSovSummaryApi({ project_id: Number(id) }));

      await dispatch(
        fetchProjectSOVItemsApi({
          id: Number(id),
          module_key: ["budget_items"],
        })
      );
    }
    if (tab === "files_photos") {
      await dispatch(setSelectedFolder({}));
      await dispatch(collapseAllFolders());
      await dispatch(callAgainFileList());
    }
    if (tab === "time") {
      const keys = [
        "time_cards",
        "counts",
        "timecard_status",
        "estimatedAry",
        "timecardAry",
      ];

      await dispatch(
        fetchProjectTimesModules({
          id: Number(id),
          module_key: keys,
        })
      );

      await dispatch(
        getTimeCostCode({
          // project_id: id ? Number(id) : undefined,
          timecard_cost_code: 1,
          has_parent_code: 1,
          //if time card list sorting
          // timecard_sort_cost_codes_order
        })
      );
    }

    if (tab === "contacts") {
      dispatch(
        fetchProjectContactsApi({
          project_id: Number(id),
          record_type: "project",
        })
      );
    }
    if (tab === "schedule") {
      dispatch(
        fetchGanttScheduleTasks({
          project: id?.toString() || "",
          module_key: module_key?.toString() || "",
        })
      );

      dispatch(
        fetchProjectListApi({
          global_call: true,
          is_completed: true,
          limit: 500,
          record_type: "project,opportunity",
        })
      );
    }
    setApiLoading(false);
  }, [tab]);

  const isCustomstatus = useMemo(() => {
    const statusList =
      StatusbarOption?.filter(
        (itm) => itm?.show_in_progress_bar !== "1" && itm.is_status === "1"
      ) || [];

    const statusExists = statusList.some(
      (item) => item.key === details.project_status
    );

    return statusExists;
  }, [details.project_status, StatusbarOption]);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );
    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) =>
        prevState.map((item) =>
          item.field === field ? { ...item, status: status } : item
        )
      );
    }
  };

  const handleUpdateField = async (data: Partial<IProjectDetails>) => {
    const field = Object.keys(data)[0] as keyof IProjectDetails;
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updateProjectApi({
      ...data,
      module_key: module_key?.toString(),
      id: id,
    })) as IProjectDetailUpdateApiRes;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      dispatch(updateProjectDetail(data));
    } else {
      notification.error({
        description: updateRes.message,
      });
      if (field === "project_status") {
        setInputValues((prev) => {
          return {
            ...prev,
            project_status: details.project_status,
            project_status_name: details.project_status_name,
          };
        });
      }
    }
    delay(() => {
      handleChangeFieldStatus({
        field: field,
        status: "button",
        action: "API",
      });
    }, 3000);
  };

  const projectStatusList: IProjectStatusList[] = useMemo(
    () =>
      (
        StatusbarOption?.filter(
          (itm) => itm?.show_in_progress_bar === "1" && itm.is_status === "1"
        ) || []
      )
        .map((item, index): IProjectStatusList | null => {
          if (item && item.name) {
            return {
              label: replaceDOMParams(sanitizeString(item.name)) || "",
              value: item.key?.toString() || "",
              default_color: item.status_color || "",
              icon: item.key
                ? ProjectStatusIconMap[item.key]
                : faLockKeyholeOpen,
              index,
            };
          }
          return null;
        })
        .filter((item): item is IProjectStatusList => item !== null),
    [StatusbarOption]
  );

  useEffect(() => {
    dispatch(
      fetchProjectType({
        types: [CFConfig.project_type_key_id, CFConfig.project_status_type_id],
        module_id: [module_id],
      })
    );
    dispatch(getTaxDetails({ status: 0, start: 0, limit: -1 }));
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData, id]);

  useEffect(() => {
    if (id) {
      dispatch(resetSovStates());
    }
  }, [id]);

  useEffect(() => {
    if (id) {
      dispatch(getItemTypes({ project_id: Number(id) }));
      dispatch(getCostCode({}));
      dispatch(fetchUnitsApi());
    }
  }, [id]);

  const handleReloadDetails = () => {
    if (tab === "reports") {
      setLaborRate("burden_rate");
      setMarkup("excluded");
      setGenerateFrom("estimate");
      setSelectedReport("job_cost_actual");
    }
    if (
      tab?.toLowerCase() === "summary" ||
      tab === "details" ||
      tab === undefined
    ) {
      fetchDetailApis();
    } else {
      dispatch(setIsSovItemsLoading(true));
      fetchDetailApis();
      fetchData();
    }
  };

  useEffect(() => {
    if (id) {
      dispatch(
        fetchMarkupWithItemTypes({
          project_id: Number(id),
        })
      );
    }
  }, [id]);

  useEffect(() => {
    if (detailsRef.current) {
      detailsRef.current.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }
  }, [tab, detailsRef.current]);

  return (
    <>
      <ProjectStoreProvider>
        <div
          id="project-tab-scroll"
          className={`ease-in-out duration-300 w-full overflow-y-auto ${
            sidebarCollapse
              ? "lg:w-[calc(100%-75px)]"
              : "xl:w-[calc(100%-225px)]"
          }`}
          ref={detailsRef}
        >
          <ProjectDetailsTopBar
            sidebarCollapse={sidebarCollapse}
            inputValues={inputValues}
            setInputValues={setInputValues}
            onReloadDetails={handleReloadDetails}
            handleChangeFieldStatus={handleChangeFieldStatus}
            loadingStatus={loadingStatus}
          />
          {!isDetailLoading && (
            <ReadOnlyPermissionMsg view={isReadOnly} className="pt-0 pb-4" />
          )}
          <div className={window.ENV.PAGE_IS_IFRAME ? "" : "sm:pb-0 pb-10"}>
            <div
              className={`px-[15px] pb-[15px] ${
                module_access === "read_only"
                  ? window.ENV.PAGE_IS_IFRAME
                    ? "md:min-h-[calc(100dvh-198px)] min-h-[calc(100dvh-256px)]"
                    : "md:min-h-[calc(100dvh-335px)] min-h-[calc(100dvh-408px)]"
                  : window.ENV.PAGE_IS_IFRAME
                  ? "md:min-h-[calc(100dvh-164px)] min-h-[calc(100dvh-210px)]"
                  : "md:min-h-[calc(100dvh-297px)] min-h-[calc(100dvh-340px)]"
              }`}
            >
              {!isDetailLoading && !isCustomstatus && (
                <ul className="items-center justify-center xl:w-[calc(100%-30px)] w-[calc(100%-0px)] xl:hidden md:flex hidden pb-[15px]">
                  <ProjectStatusbar
                    inputValues={inputValues}
                    setInputValues={setInputValues}
                    handleUpdateField={handleUpdateField}
                    handleChangeFieldStatus={handleChangeFieldStatus}
                    projectStatusList={projectStatusList}
                    isReadOnly={isReadOnly}
                    loadingStatus={loadingStatus}
                  />
                </ul>
              )}
              {isDetailLoading || apiLoading ? (
                <Spin
                  className={`flex items-center justify-center ${
                    window.ENV.PAGE_IS_IFRAME
                      ? "md:h-[calc(100dvh-161px)] h-[calc(100dvh-205px)]"
                      : "md:h-[calc(100dvh-304px)] h-[calc(100dvh-357px)]"
                  }`}
                />
              ) : tab === "summary" || tab === undefined ? (
                <SummaryTab />
              ) : tab === "details" ? (
                <DetailsTab />
              ) : tab === "financial" ? (
                <FinancialTab />
              ) : tab === "schedule_of_values" ? (
                <SovTab />
              ) : tab === "documents" ? (
                <DocumentsTab isInitialLoad={isInitialLoad ?? false} />
              ) : tab === "time" ? (
                <TimeTab
                  selectedPeriod={selectedPeriod}
                  setSelectedPeriod={setSelectedPeriod}
                  selectedHrsMoneyTab={selectedHrsMoneyTab}
                  setSelectedHrsMoneyTab={setSelectedHrsMoneyTab}
                  laborCostHrsTab={laborCostHrsTab}
                  setLaborCostHrsTab={setLaborCostHrsTab}
                  fetchData={fetchData}
                />
              ) : tab === "files_photos" ? (
                <FilesPhotosTab />
              ) : tab === "contacts" ? (
                <ContactsTab />
              ) : tab === "schedule" ? (
                <ScheduleTab />
              ) : tab === "procurement" ? (
                <ProcurementTab />
              ) : tab === "client_access" ? (
                <ClientAccessTab />
              ) : (
                tab === "reports" && (
                  <ReportTab
                    laborRate={laborRate}
                    setLaborRate={setLaborRate}
                    markup={markup}
                    setMarkup={setMarkup}
                    generateFrom={generateFrom}
                    setGenerateFrom={setGenerateFrom}
                    selectedReport={selectedReport}
                    setSelectedReport={setSelectedReport}
                  />
                )
              )}
            </div>
            <TimeLineFooter
              data={{
                addedDate: details?.date_added || "",
                addedTime: details?.time_added || "",
                addedBy: details?.added_by_name || "",
                moduleId: module_id,
                typeKey: "projects",
                recordId: Number(id),
                qbDateAdded: details.qb_date_added || "",
                qbTimeAdded: details.qb_time_added || "",
                quickbookUserId: Number(details.quickbook_project_id) || 0,
                moduleName: singular_name || "Project",
                companyCamProjectId: Number(details.companycam_project_id) || 0,
                ccDateAdded: details?.companycam_date_added?.toString() || "",
                ccTimeAdded: details?.companycam_time_added?.toString() || "",
              }}
              isSyncedWithCompanyCam={
                Number(details?.companycam_project_id) !== 0 &&
                !!details?.companycam_project_id &&
                !!details?.companycam_date_added &&
                !!details?.companycam_time_added
                  ? `#${details?.companycam_project_id}`
                  : ""
              }
              syncCompanyCamFunction={async (type) => {
                try {
                  const response = (await webWorkerApi({
                    url:
                      type === "map_existing_cc_project"
                        ? apiRoutes.COMMON.map_existing_cc_project
                        : apiRoutes.COMMON.map_existing_cc_project,
                    data: {
                      project_id: details?.project_id,
                      ...(type === "map_existing_cc_project"
                        ? {
                            companycam_project_id:
                              details?.companycam_project_id,
                          }
                        : { record_type: "project" }),
                    },
                  })) as IApiCallResponse;

                  if (response.success) {
                    window.location.reload();
                  } else {
                    notification.error({ description: response.message });
                  }
                } catch (error) {
                  notification.error({
                    description: (error as Error).message,
                  });
                }
              }}
              isSynced={
                details?.quickbook_project_id !== 0 &&
                !!details?.quickbook_project_id
                  ? `#${details?.quickbook_project_id}`
                  : ""
              }
              sidebarCollapse={sidebarCollapse}
              hrefLinkDetail={"customerdetail"}
              nameId={"nameId"}
              isLoading={isDetailLoading}
            />
          </div>
        </div>
      </ProjectStoreProvider>
    </>
  );
};

export default ManageProjectTab;

export { ErrorBoundary };
