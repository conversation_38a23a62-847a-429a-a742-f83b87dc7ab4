import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useTranslation } from "~/hook";
import {
  getGConfig,
  getGModuleFilters,
  setIsFilterBeingApplied,
  setIsFilterUpdated,
  useGModules,
} from "~/zustand";
import isEmpty from "lodash/isEmpty";

// molecules
import { AddButton } from "~/shared/components/molecules/addButton";
import { AccordionButton } from "~/shared/components/molecules/accordionButton";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { DashboardHeader } from "~/shared/components/molecules/dashboardHeader";

// Organisms
import { KanbanList } from "~/shared/components/organisms/kanbanView";

// Other
import InspectionsByStatus from "~/modules/projectManagement/pages/inspections/components/dashboard/InspectionsByStatus";
import InspectionsAgency from "~/modules/projectManagement/pages/inspections/components/dashboard/InspectionsAgency";
import RecentlyFailedInspections from "~/modules/projectManagement/pages/inspections/components/dashboard/RecentlyFailedInspections";
import InspectionsWithTasksToComplete from "~/modules/projectManagement/pages/inspections/components/dashboard/InspectionsWithTasksToComplete";
import InspectionsByProject from "~/modules/projectManagement/pages/inspections/components/dashboard/InspectionsByProject";
import { InspectionList } from "~/modules/projectManagement/pages/inspections/components/dashboard";
import { INSPECTIONS_OPTIONS } from "~/modules/projectManagement/pages/inspections/utils/constants";
import { AddInspection } from "~/modules/projectManagement/pages/inspections/components/sidebar";
import { Popover } from "~/shared/components/atoms/popover";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { InspectionsDashboardRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/inspections/dashboard/regular";
import UpcomingDeliveries from "~/modules/projectManagement/pages/inspections/components/dashboard/UpcomingDeliveries";
import Sortable from "sortablejs";
import {
  fetchInspectionsDashboardApi,
  getInspectionKanbanListApi,
  updateInspectionDetailApi,
} from "~/modules/projectManagement/pages/inspections/redux/action/inspectionDashAction";
import {
  useInAppDispatch,
  useInAppSelector,
} from "~/modules/projectManagement/pages/inspections/redux/store";
import { useNavigate, useSearchParams } from "@remix-run/react";
import InspectionStoreProvider from "~/modules/projectManagement/pages/inspections/redux/inspectionStoreProvider";
import PageDashboardHeader from "~/components/page/common/page-dashboard-header";
import { useModuleDataSearch } from "~/zustand/global-module-filter/hook";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import InspectionFilter from "~/modules/projectManagement/pages/inspections/components/dashboard/inspectionFilter";
import { useAppIVSelector } from "~/modules/financials/pages/invoice/redux/store";
import { getCustomStatusList } from "~/redux/action/getCustomStatusAction";
import { isKanbanViewApi } from "~/redux/action/iskanbanView";
import debounce from "lodash/debounce";
import isEqual from "lodash/isEqual";
import uniq from "lodash/uniq";
import {
  updateKanbanSettingApi,
  updateKanbanSortingApi,
} from "~/redux/action/kanbanSettings";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import { routes } from "~/route-services/routes";
import { InspectionsTableDropdownItems } from "~/modules/projectManagement/pages/inspections/components/dashboard/InspectionsTableDropdownItems";
import { setSearchValueAct } from "~/modules/projectManagement/pages/inspections/redux/slices/inspectionDashSlice";
import { sanitizeString } from "~/helpers/helper";

// Fort Awesome Library Add icons
InspectionsDashboardRegularIconAdd();

const ManageInspectionsCom = () => {
  const { _t } = useTranslation();
  const [fullScreenTable, setFullScreenTable] = useState(false);
  const [viewSearch, setViewSearch] = useState<boolean>(false);
  const [addInspectionOpen, setAddInspectionOpen] = useState<boolean>(false);
  const { checkModuleAccessByKey } = useGModules();
  const [open, setOpen] = useState<boolean>(false);
  const dispatch = useInAppDispatch();
  const [searchParams] = useSearchParams();

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [loadingChild, setLoadingChild] = useState<boolean>(false);
  const [isDefaultViewKanbanLoading, setIsDefaultViewKanbanLoading] =
    useState<boolean>(false);
  const [kanbanSelected, setKanbanSelected] = useState<string[]>([]);
  const [isKanbanLoading, setIsKanbanLoading] = useState<boolean>(false);
  const [kanbanSetting, setKanbanSetting] = useState<
    IKanbanSetting | IInspectionKanBanSettings
  >();
  const [kanbanListData, setKanbanListData] = useState<
    IInspectionKanBanColumnList[]
  >([]);

  const [hasMoreBySection, setHasMoreBySection] = useState<
    Record<string, boolean>
  >({});
  const [pageBySection, setPageBySection] = useState<{ [key: string]: number }>(
    {}
  );
  const [loadingBySection, setLoadingBySection] = useState<{
    [key: string]: boolean;
  }>({});
  const [isKanbanEnabled, setIsKanbanEnabled] = useState<boolean>(false);
  const [inspectionToBeUpdate, setInspectionToBeUpdate] =
    useState<IInspectionKanBanData | null>(null);
  const [selectedData, setSelectedData] =
    useState<IInspectionKanBanData | null>();

  const { sLFetchedModuleId }: ICustomStatusListInitialState = useAppIVSelector(
    (state) => state.customStatusListData
  );
  const navigate = useNavigate();

  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();

  const filterSrv: Partial<InspectionsFilter> | undefined =
    getGModuleFilters() as Partial<InspectionsFilter> | undefined;

  const { page_is_iframe }: GConfig = getGConfig();

  const { searchValue } = useInAppSelector(
    (state) => state.inspectionsDashboard
  );

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(currentModule?.module_key) === "read_only",
    [currentModule?.module_key]
  );

  useEffect(() => {
    dispatch(fetchInspectionsDashboardApi());
  }, []);

  useEffect(() => {
    if (searchParams?.get("action")?.trim() === "new") {
      setAddInspectionOpen(true);
    }
  }, [searchParams.get("action")]);

  useEffect(() => {
    if (
      currentModule?.module_id &&
      currentModule?.module_id?.toString() != sLFetchedModuleId
    ) {
      dispatch(
        getCustomStatusList({
          is_deleted: 0,
          module_id: [currentModule?.module_id],
        })
      );
    }
  }, [currentModule?.module_id, sLFetchedModuleId]);

  const beforeFilterUpdateCallback = () => {
    setIsFilterUpdated(false);
    setIsFilterBeingApplied(true);
  };

  const kanbanView = useMemo(() => isKanbanEnabled, [isKanbanEnabled]);

  useEffect(() => {
    const fetchKanbanView = async () => {
      setIsKanbanLoading(true);
      try {
        const response = (await isKanbanViewApi({
          key: CFConfig.inspection_module,
        })) as IIsKanbanEnableApiRes;

        if (response?.success) {
          setIsKanbanEnabled(response?.data?.is_kanban === 1); // Set the result based on API response
        } else {
          setIsKanbanEnabled(false); // Set false if API response indicates failure
        }
      } catch (error) {
        notification.error({
          description: (error as Error).message || "Something went wrong!",
        });
      } finally {
        setIsKanbanLoading(false);
      }
    };
    fetchKanbanView();
  }, []);

  const fetchKanbanInspectionList = async (
    type: string,
    isLoad: boolean = true
  ) => {
    type === "" ? setIsLoading(true) : setLoadingChild(isLoad);
    const pageForType = pageBySection[type] || 0;

    const limit = 20;

    const tempFil: IInspectionTempFil = {
      inspection_status_kanban: filterSrv?.inspection_status_kanban ?? "",
    };

    if (!filterSrv) {
      return;
    }
    if (filterSrv?.project) {
      tempFil.project = filterSrv.project;
    }
    if (filterSrv?.inspection_status_kanban) {
      tempFil.inspection_status_kanban = filterSrv.inspection_status_kanban;
    }
    if (tempFil.inspection_status_kanban?.toString() === "") {
      delete tempFil.inspection_status_kanban;
    }
    if (filterSrv?.start_date) {
      tempFil.start_date = filterSrv.start_date || "";
    }
    if (filterSrv?.end_date) {
      tempFil.end_date = filterSrv.end_date || "";
    }

    let dataParams: IInspectionKanBanListParmas = {
      filter: tempFil,
      page: pageForType,
      limit: limit,
      is_kanban: true,
      ignore_filter: 1,
      any_status: !!type ? type : undefined,
      search: HTMLEntities.encode(sanitizeString(searchValue || "")),
    };
    if (searchValue === "") {
      delete dataParams.search;
    }

    if (tempFil.inspection_status_kanban?.toString() === "") {
      delete tempFil.inspection_status_kanban;
    }

    try {
      const resData = (await getInspectionKanbanListApi(
        dataParams
      )) as IInspectionKanBanListApiRes;
      if (resData?.success) {
        let newTypes = resData?.data;

        newTypes = newTypes
          .filter((item) => item !== null)
          .map((item) => {
            item.kanban_data = item?.kanban_data?.map((kanbanData) => {
              kanbanData.company_inspection_id = `${_t("Insp. #")}${
                kanbanData.company_inspection_id ?? ""
              }`;
              return kanbanData;
            });
            return item;
          });

        if (pageForType === 0) {
          const data = newTypes.filter((item) => item !== null);
          setKanbanListData(data);
        } else {
          setKanbanListData((prevData) => {
            if (prevData.length === 0) {
              return newTypes;
            }

            const updateData = prevData.map((prevSection) => {
              const newSection = newTypes.find((d) => {
                if (d !== null) {
                  return d.item_id === prevSection.item_id;
                }
              });
              if (newSection) {
                const updatedSection = { ...prevSection };

                const newKanbanData = newSection.kanban_data.filter(
                  (newItem) =>
                    !updatedSection.kanban_data.some(
                      (existingItem) =>
                        existingItem.inspection_id === newItem.inspection_id
                    )
                );

                updatedSection.kanban_data.push(...newKanbanData);
                return updatedSection;
              }
              return prevSection;
            });
            return updateData;
          });
        }
        const newHasMoreBySection = newTypes.reduce(
          (
            acc: Record<number, boolean>,
            section: IInspectionKanBanColumnList
          ) => {
            const sectionDataLength = section.kanban_data.length;
            acc[section.item_id] = sectionDataLength >= limit;
            return acc;
          },
          {}
        );
        setHasMoreBySection((prev) => ({
          ...prev,
          ...newHasMoreBySection,
        }));
        setKanbanSelected(
          resData?.kanban_estimate_type_selected.map((item) => item.toString())
        );
        setKanbanSetting(resData?.kanban_setting);
      } else {
        setKanbanListData([]);
        setKanbanSetting(undefined);
        notification.error({
          description: resData?.message || "Something went wrong!",
        });
      }
      if (type !== "") {
        setLoadingBySection((prev) => ({
          ...prev,
          [type]: false,
        }));
      }
    } catch (err) {
      setIsLoading(false);
      notification.error({
        description: (err as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsLoading(false);
      setLoadingChild(false);
    }
  };

  const previousValues = useRef({
    searchValue,
  });

  const debouncedFetch = debounce(() => {
    fetchKanbanInspectionList("");
  }, 100);

  useEffect(() => {
    const currentValues = {
      filter: JSON.stringify(filterSrv),
      searchValue,
    };

    if (
      !isEmpty(filterSrv) &&
      !isEqual(previousValues.current, currentValues) &&
      kanbanView
    ) {
      debouncedFetch();
      previousValues.current = { ...currentValues };
      setPageBySection({});
    }
  }, [JSON.stringify(filterSrv), searchValue, kanbanView]);

  const debouncedOnSearchChange = useCallback(
    debounce(({ target: { value } }) => {
      dispatch(setSearchValueAct(value));
    }, 500),
    []
  );

  const handleCollapse = async (columnId: string, isCollapseCard: string) => {
    let updatedKanbanSelected = uniq(kanbanSelected);
    if (updatedKanbanSelected.includes(columnId)) {
      updatedKanbanSelected = updatedKanbanSelected.filter(
        (value) => value !== columnId
      );
    } else {
      updatedKanbanSelected.push(columnId);
    }

    setKanbanSelected(updatedKanbanSelected);

    try {
      const requestKanbanSetting = {
        module_field_id: updatedKanbanSelected,
        default_view: kanbanSetting?.default_view?.toString() ?? "0",
        module_id: currentModule?.module_id,
      };

      const responseKanbanSetting = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;

      if (responseKanbanSetting.success) {
        setKanbanSetting(responseKanbanSetting?.data);
        setKanbanSelected(responseKanbanSetting?.kanban_project_selected);
      } else {
        setKanbanSelected((prevState) => kanbanSelected);
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      // Revert changes if an error occurs
      setKanbanSelected((prevState) => kanbanSelected);
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsDefaultViewKanbanLoading(false);
    }
  };

  const handleLoadMore = (val: number) => {
    const isSectionLoading = loadingBySection[val];
    if (isSectionLoading) return;

    const hasMoreForSection = hasMoreBySection[val];
    const currentSectionPage = pageBySection[val] || 0;
    if (hasMoreForSection) {
      const nextPage = currentSectionPage + 1;
      setPageBySection((prev) => ({
        ...prev,
        [val]: nextPage,
      }));

      if (currentSectionPage !== 0) {
        setLoadingBySection((prev) => ({
          ...prev,
          [val]: true,
        }));
        fetchKanbanInspectionList(val.toString());
      }
    }
  };

  const handleDragDropEnd = async (event: Sortable.SortableEvent) => {
    const currentArray = kanbanListData?.map((data) => ({
      column_id: data.item_id,
      sort_order: Number(data.sort_order),
      sorting_id: data.sorting_id.toString(),
      column_name: data.name,
      type_id: data.item_id.toString(),
    }));

    const kanban_sorting: IKanbanSortingArray[] = currentArray.map(
      (data, index) => ({
        ...data,
        sort_order: index,
      })
    );

    try {
      const requestKanbanSetting: ISubmittalKanbanSorting = {
        kanban_sorting: kanban_sorting,
        module_id: currentModule?.module_id || 0,
      };
      const responseKanbanSetting = (await updateKanbanSortingApi(
        requestKanbanSetting
      )) as IKanbanSortingApiRes;
      if (!responseKanbanSetting.success) {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "Something went wrong!",
      });
    }
  };

  const handleDefaultViewKanabanChange = async (val: boolean) => {
    setIsDefaultViewKanbanLoading(true);
    try {
      const requestKanbanSetting = {
        default_view: val ? 1 : 0,
        module_id: currentModule?.module_id,
        module_field_id: kanbanSelected,
      };
      const responseKanbanSetting = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;
      if (responseKanbanSetting.success) {
        setKanbanSetting(responseKanbanSetting?.data);
        // setKanbanSelected(responseKanbanSetting?.kanban_project_selected);
      } else {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsDefaultViewKanbanLoading(false);
    }
  };

  const handleCardDragDropEnd = async (event: Sortable.SortableEvent) => {
    const { from, to, clone } = event;
    const fromColumnId =
      from?.closest("[data-id]")?.getAttribute("data-id") ?? "";
    const toColumnId = to?.closest("[data-id]")?.getAttribute("data-id") ?? "";
    const recordId = clone?.getAttribute("data-record-id") ?? "";

    if (fromColumnId !== toColumnId && !!selectedData?.inspection_id) {
      const data = {
        inspection_status: toColumnId,
      };
      try {
        const updateRes = (await updateInspectionDetailApi({
          id: selectedData?.inspection_id,
          ...data,
        })) as ApiCallResponse;

        if (updateRes.success) {
          setKanbanListData((prevData) =>
            prevData.map((item) => {
              if (item.item_id == Number(toColumnId)) {
                return {
                  ...item,
                  kanban_data: item.kanban_data.map((kanban) => {
                    if (
                      kanban.inspection_id.toString() ===
                      selectedData?.inspection_id.toString()
                    ) {
                      return {
                        ...kanban,
                        status: toColumnId,
                      };
                    } else {
                      return {
                        ...kanban,
                      };
                    }
                  }),
                  total_count: Number(item.total_count) + 1,
                };
              } else if (item.item_id == Number(fromColumnId)) {
                return {
                  ...item,
                  total_count: Number(item.total_count) - 1,
                };
              }
              return item;
            })
          );
        } else {
          fetchKanbanInspectionList("", false);
          notification.error({
            description: updateRes?.message || "Something went wrong!",
          });
        }
      } catch (error) {
        notification.error({
          description: (error as Error)?.message,
        });
      }
    }
  };

  return (
    <>
      <DashboardHeader
        searchPlaceHolder={`Search for ${currentModule?.name || "Inspections"}`}
        searchVal={searchValue}
        onSearchChange={debouncedOnSearchChange}
        viewSearch={viewSearch}
        setViewSearch={setViewSearch}
        leftComponent={
          <>
            {!kanbanView ? (
              <li>
                <ButtonWithTooltip
                  tooltipTitle={_t("Card View")}
                  tooltipPlacement="top"
                  icon="fa-brands fa-trello"
                  iconClassName="h-5 w-5"
                  className="!w-7 !h-7"
                  onClick={() => setIsKanbanEnabled(true)}
                />
              </li>
            ) : (
              <li>
                <ButtonWithTooltip
                  tooltipTitle={_t("Return to Dashboard")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-square-list"
                  iconClassName="h-5 w-5"
                  className="!w-7 !h-7"
                  onClick={() => setIsKanbanEnabled(false)}
                />
              </li>
            )}
            <InspectionFilter
              kanbanView={kanbanView}
              beforeFilterUpdateCallback={beforeFilterUpdateCallback}
              onClearSearch={() => {
                dispatch(setSearchValueAct(""));
              }}
            />
          </>
        }
        rightComponent={
          <div className="flex flex-row sm:items-center items-start sm:gap-5 gap-2">
            <div className="md:flex hidden items-center">
              {kanbanView && (
                <>
                  <CustomCheckBox
                    className="gap-1.5 !font-medium text-primary-900 dark:text-white/90 relative sm:before:absolute sm:before:w-0.5 sm:before:h-11 sm:before:-right-1.5 sm:before:top-1/2 sm:before:-translate-y-1/2 sm:before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)]"
                    checked={kanbanSetting?.default_view?.toString() == "1"}
                    disabled={isDefaultViewKanbanLoading}
                    onChange={(e: CheckboxChangeEvent) =>
                      handleDefaultViewKanabanChange(e.target.checked)
                    }
                    loadingProps={{
                      isLoading: isDefaultViewKanbanLoading,
                      className: "bg-[#ffffff]",
                    }}
                  >
                    {_t("Set as Default View")}
                  </CustomCheckBox>
                </>
              )}
            </div>
            {kanbanView && (
              <Popover
                placement="bottomRight"
                content={
                  <div className="dark:bg-dark-900 min-w-[155px]">
                    <ul className="py-2 px-1 grid gap-0.5">
                      <li className={`rounded bg-blue-50 dark:bg-dark-800`}>
                        <div
                          className="flex items-center justify-between cursor-pointer px-2 py-0.5 gap-1"
                          onClick={() =>
                            handleDefaultViewKanabanChange(
                              kanbanSetting?.default_view?.toString() == "1"
                                ? false
                                : true
                            )
                          }
                        >
                          <Typography
                            className={`text-primary-900 dark:text-white/90`}
                          >
                            {_t("Set as Default View")}
                          </Typography>
                          {kanbanSetting?.default_view?.toString() == "1" && (
                            <FontAwesomeIcon
                              className={`text-base w-4 h-4 text-primary-900 opacity-100 `}
                              icon="fa-regular fa-check"
                            />
                          )}
                        </div>
                      </li>
                    </ul>
                  </div>
                }
                trigger="click"
                open={open}
                className="flex md:hidden"
                onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
              >
                <div className="flex relative items-center justify-center">
                  <ButtonWithTooltip
                    icon="fa-regular fa-gear"
                    tooltipTitle=""
                    tooltipPlacement="top"
                    iconClassName="h-5 w-5"
                    className="!w-7 !h-7"
                    onClick={() => {}}
                  />
                </div>
              </Popover>
            )}
            {currentModule?.module_access === "full_access" ||
            currentModule?.module_access === "own_data_access" ? (
              <li>
                <AddButton onClick={() => setAddInspectionOpen(true)}>
                  {_t(currentModule?.singular_name ?? "Inspections")}
                </AddButton>
              </li>
            ) : null}
          </div>
        }
      />
      {!kanbanView && !isKanbanLoading && (
        <div
          className={`pt-[41px] overflow-y-auto overflow-hidden ${
            !page_is_iframe
              ? "md:h-[calc(100dvh-143px)] h-[calc(100dvh-112px)]"
              : "h-screen"
          }`}
        >
          <ReadOnlyPermissionMsg
            view={currentModule?.module_access === "read_only"}
          />
          <div className="p-4">
            <div
              className={`grid xl:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-2.5 transition-all ease-in-out duration-300 ${
                fullScreenTable || viewSearch
                  ? "max-h-0 overflow-hidden"
                  : "xl:max-h-[600px] md:max-h-[900px] max-h-[1750px]"
              }`}
            >
              <div className="w-full common-card h-[266px]">
                <InspectionsByStatus />
              </div>
              <div className="w-full common-card h-[266px]">
                <InspectionsAgency />
              </div>
              <div className="w-full common-card h-[266px]">
                <RecentlyFailedInspections />
              </div>
              <div className="w-full common-card h-[266px]">
                <InspectionsWithTasksToComplete />
              </div>
              <div className="w-full common-card h-[266px]">
                <UpcomingDeliveries />
              </div>
              <div className="w-full common-card h-[266px]">
                <InspectionsByProject />
              </div>
            </div>
            <div
              className={`w-full ${
                fullScreenTable || viewSearch ? "mt-0" : "mt-4"
              }`}
            >
              <div className="relative h-7 z-[999]">
                <AccordionButton
                  onClick={() => setFullScreenTable((prev: boolean) => !prev)}
                  fullScreenTable={fullScreenTable}
                />
              </div>
              <div className="p-2 bg-white dark:bg-dark-600 common-list-table rounded">
                {!isKanbanLoading && (
                  <InspectionList
                    setAddInspectionOpen={setAddInspectionOpen}
                    search={searchValue || ""}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      )}
      {kanbanView && !isKanbanLoading && (
        <div className="pt-[41px] md:h-[calc(100dvh-116px)] h-[calc(100dvh-84px)] overflow-y-auto overflow-hidden">
          <ReadOnlyPermissionMsg
            view={isReadOnly}
            className="px-4 sm:pt-4 pt-1"
            textClassName="sm:text-13 text-xs flex justify-center items-end sm:h-auto h-7 sm:leading-auto leading-[15px]"
          />
          <div className="flex pt-4 pb-2.5 px-1.5 transition-all ease-in-out duration-300 overflow-x-auto">
            <KanbanList
              className="!border-l"
              list={kanbanListData} //  kanban data
              setList={setKanbanListData}
              loading={!kanbanListData.length || isLoading}
              collapseClick={(submittalColumn) => {
                handleCollapse(
                  submittalColumn?.item_id?.toString(),
                  submittalColumn?.is_collapse_card?.toString()
                );
              }}
              cardDetailsClick={(inspection) => {
                setInspectionToBeUpdate(inspection);
                if (!!inspection.inspection_id) {
                  navigate(
                    `${routes.MANAGE_INSPECTION.url}/${inspection?.inspection_id}`
                  );
                }
              }}
              childLoader={loadingChild}
              loadMore={(val) => {
                handleLoadMore(val);
              }}
              colum={{
                headerName: "name",
                parentId: "item_id",
                count: "total_count",
                collapse: "is_collpase_card",
                color: "status_color",
                child: "kanban_data",
                childCard: {
                  cardId: "inspection_id", // child card id pass
                  cardFirstFirst: "project_name",
                  cardMiddleFirst: "inspection_type",
                  cardLastFirst: "inspection_date",
                  cardMiddleSecond: "company_inspection_id",
                },
              }}
              kanbanSelected={kanbanSelected}
              handleCardDragDropEnd={handleCardDragDropEnd}
              handleColumnDragDropEnd={handleDragDropEnd}
              isReadOnly={isReadOnly}
              handleMouseMove={(data) => {
                setSelectedData(data);
              }}
              onActionClick={(inspection) => {
                setInspectionToBeUpdate(inspection);
              }}
            >
              {(currentModule?.module_access === "full_access" ||
                currentModule?.module_access === "own_data_access") && (
                <InspectionsTableDropdownItems
                  data={inspectionToBeUpdate}
                  icon="fa-solid fa-ellipsis-h"
                  refreshTable={() => fetchKanbanInspectionList("")}
                  className="m-0 hover:!bg-[#0000000f] invisible group-hover/kanbanitem:visible"
                  iconClassName="text-primary-900/80 group-hover/buttonHover:text-primary-900"
                />
              )}
            </KanbanList>
          </div>
        </div>
      )}
      {addInspectionOpen && (
        <AddInspection
          setAddInspectionOpen={setAddInspectionOpen}
          addInspectionOpen={addInspectionOpen}
        />
      )}
    </>
  );
};

const ManageInspections = () => {
  return (
    <InspectionStoreProvider>
      {" "}
      <ManageInspectionsCom />{" "}
    </InspectionStoreProvider>
  );
};

export default React.memo(ManageInspections);

export { ErrorBoundary };
