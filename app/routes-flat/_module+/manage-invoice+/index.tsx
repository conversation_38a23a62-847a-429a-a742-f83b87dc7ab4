import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { type RadioChangeEvent } from "antd";
import { Popover } from "~/shared/components/atoms/popover";
import debounce from "lodash/debounce";

// Hook and redux
import { useTranslation } from "~/hook";
import {
  getGConfig,
  getGModuleByKey,
  getGModuleFilters,
  getGSettings,
  updateModuleFilter,
  useGModules,
} from "~/zustand";
import type { CheckboxProps } from "antd/lib";
import { json, useLoaderData, useSearchParams } from "@remix-run/react";
import { getInvoiceTerms } from "~/redux/action/invoiceTermsAction";
import {
  setSearchValueAct,
  updatePaymentAlertAct,
} from "~/modules/financials/pages/invoice/redux/slices/dashboardSlice";
import { updateKanbanSettingApi } from "~/redux/action/kanbanSettings";
import {
  useAppIVDispatch,
  useAppIVSelector,
} from "~/modules/financials/pages/invoice/redux/store";
import {
  fetchDashData,
  updatePaymentAlert,
} from "~/modules/financials/pages/invoice/redux/action/dashboardAction";
import { getCustomStatusList } from "~/redux/action/getCustomStatusAction";
import { isKanbanViewApi } from "~/redux/action/iskanbanView";
import IVStoreProvider from "~/modules/financials/pages/invoice/redux/IVStoreProvider";

// Atoms
import { Image } from "~/shared/components/atoms/image";
import { Typography } from "~/shared/components/atoms/typography";
import { TypographyLink } from "~/shared/components/atoms/typographyLink";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { AddButton } from "~/shared/components/molecules/addButton";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { AccordionButton } from "~/shared/components/molecules/accordionButton";
import { DashboardHeader } from "~/shared/components/molecules/dashboardHeader";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";

// Other
import SubmittedInvoices from "~/modules/financials/pages/invoice/components/dashboard/SubmittedInvoices";
import ReceivedPayments from "~/modules/financials/pages/invoice/components/dashboard/ReceivedPayments";
import UnpaidInvoices from "~/modules/financials/pages/invoice/components/dashboard/UnpaidInvoices";
import SalesByMonth from "~/modules/financials/pages/invoice/components/dashboard/SalesByMonth";
import BalanceByProject from "~/modules/financials/pages/invoice/components/dashboard/BalanceByProject";
import AccountsReceivable from "~/modules/financials/pages/invoice/components/dashboard/AccountsReceivable";
import InvoicesByStatus from "~/modules/financials/pages/invoice/components/dashboard/InvoicesByStatus";
import RecentPaymnets from "~/modules/financials/pages/invoice/components/dashboard/RecentPaymnets";
import {
  GRID_BUTTON_TAB,
  invoiceChargedApprovalType,
  invoiceExlStatus,
} from "~/modules/financials/pages/invoice/utils/constants";
import { InvoiceList } from "~/modules/financials/pages/invoice/components/dashboard";
import { AddInvoice } from "~/modules/financials/pages/invoice/components";
import InvoicesComingDue from "~/modules/financials/pages/invoice/components/dashboard/InvoicesComingDue";
import InvoiceKanban from "~/modules/financials/pages/invoice/components/dashboard/InvoiceKanban";
import InvoiceFilter from "~/modules/financials/pages/invoice/components/dashboard/InvoiceFilter";

// FontAwesome File
import { IVDashboardRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/invoice/dashboard/regular";
import { fetchIVStatusListApi } from "~/modules/financials/pages/invoice/redux/action";
import { defaultConfig } from "~/data";

// Fort Awesome Library Add icons
IVDashboardRegularIconAdd();

export const loader = () => {
  return json({
    PARALLEL_SITE: process.env.PARALLEL_SITE,
    PANEL_URL: process.env.PANEL_URL,
  });
};

const Invoice = () => {
  const { PARALLEL_SITE, PANEL_URL } = useLoaderData<typeof loader>();

  const { _t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const {
    module_name,
    page_is_iframe,
    module_access,
    module_singular_name,
    module_key,
    module_id,
  }: GConfig = getGConfig();
  const {
    stripe_activated,
    wepay_activated,
    quickbook_desktop_sync,
    quickbook_sync,
  }: GSettings = getGSettings();
  const { checkModuleAccessByKey } = useGModules();

  const ivModule: GModule | undefined = getGModuleByKey(
    defaultConfig.invoice_merge_module_key
  );

  const dispatch = useAppIVDispatch();

  const { sLFetchedModuleId }: ICustomStatusListInitialState = useAppIVSelector(
    (state) => state.customStatusListData
  );

  const { searchValue, isDataFetched }: IInvoiceDashState = useAppIVSelector(
    (state) => state.dashboard
  );
  const { payment_alert } = useAppIVSelector((state) => state.dashboard);
  const { isStatusListDataFetched } = useAppIVSelector(
    (state) => state.statusList
  );
  const filterSrv =
    (getGModuleFilters() as Partial<IInvoiceModuleFilter> | undefined) || {};
  const { tab } = filterSrv;

  const [fullScreenTable, setFullScreenTable] = useState(false);
  const [kanbanSetting, setKanbanSetting] = useState<IKanbanSetting>();
  const [kanbanSelected, setKanbanSelected] = useState<string[]>([]);
  const [isKanbanLoading, setIsKanbanLoading] = useState<boolean>(false);
  const [isKanbanEnabled, setIsKanbanEnabled] = useState<boolean>(false);
  const [loaderSetDefalutViewKanban, setLoaderSetDefalutViewKanban] =
    useState<boolean>(false);
  const [viewSearch, setViewSearch] = useState<boolean>(false);
  const [isAddInvoiceOpen, setIsAddInvoiceOpen] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);

  useEffect(() => {
    dispatch(getInvoiceTerms({ is_deleted: 0 }));
  }, []);

  useEffect(() => {
    if (!isDataFetched) {
      dispatch(fetchDashData());
    }
  }, [isDataFetched]);

  useEffect(() => {
    if (!isStatusListDataFetched) {
      dispatch(
        fetchIVStatusListApi({
          quickbook_sync: quickbook_sync?.toString() || "0",
          quickbook_desktop_sync: quickbook_desktop_sync?.toString() || "0",
        })
      );
    }
  }, [isStatusListDataFetched]);

  useEffect(() => {
    if (
      ivModule?.module_id &&
      ivModule?.module_id?.toString() != sLFetchedModuleId
    ) {
      dispatch(
        getCustomStatusList({
          is_deleted: 0,
          module_id: [ivModule?.module_id],
        })
      );
    }
  }, [ivModule?.module_id, sLFetchedModuleId]);
  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );
  useEffect(() => {
    if (searchParams?.get("action")?.trim() === "new") {
      setIsAddInvoiceOpen(true);
      if (isReadOnly) {
        setSearchParams({});
      }
    }
  }, [searchParams.get("action"), isReadOnly]);

  const removeQueryParamsRemix = () => {
    searchParams.delete("action");
    searchParams.delete("project");
    searchParams.delete("customer_id");
    setSearchParams(searchParams);
  };

  useEffect(() => {
    const fetchKanbanView = async () => {
      setIsKanbanLoading(true);
      try {
        const response = (await isKanbanViewApi({
          key: CFConfig.invoice_merge_module_key,
        })) as IIsKanbanEnableApiRes;
        if (response?.success) {
          setIsKanbanEnabled(response?.data?.is_kanban === 1);
        } else {
          setIsKanbanEnabled(false);
        }
      } catch (error) {
        setIsKanbanEnabled(false);
      } finally {
        setIsKanbanLoading(false);
      }
    };

    fetchKanbanView();
  }, []);

  const handleDefaultViewKanabanChange: CheckboxProps["onChange"] = async (
    e
  ) => {
    setLoaderSetDefalutViewKanban(true);
    try {
      const requestKanbanSetting = {
        module_field_id: kanbanSelected.length
          ? kanbanSelected.filter((data) => data.trim() !== "")
          : undefined,
        default_view: e.target.checked ? 1 : 0,
        module_id: module_id,
      };
      const responseKanbanSetting = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;
      if (responseKanbanSetting.success) {
        setKanbanSetting(responseKanbanSetting?.data);
        setKanbanSelected(responseKanbanSetting?.kanban_project_selected);
      } else {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setLoaderSetDefalutViewKanban(false);
    }
  };

  const onTabButtonChange = (e: RadioChangeEvent) => {
    try {
      const listValue = e.target.value as string;
      let updateStatus: string[] = [];

      if (listValue === "all") {
        updateFilter({
          tab: listValue,
          billing_status: "",
          billing_status_names: "",
        });
        return;
      } else if (listValue === "paid" || listValue === "unpaid") {
        updateStatus =
          listValue === "paid"
            ? ["invoice_paid", "invoice_writeoff"]
            : ["invoice_submitted"];

        const filteredData =
          customStatusList?.filter((data) =>
            updateStatus.includes(data?.key || "")
          ) || [];
        const billingStatus = filteredData
          .map((data) => data.item_id)
          .join(",");
        const billingStatusNames = filteredData
          .map((data) => data.name)
          .join(", ");

        updateFilter({
          tab: listValue,
          billing_status: billingStatus,
          billing_status_names: billingStatusNames,
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    }
  };

  // Memoize the final boolean value
  const defaultKanbanView = useMemo(() => isKanbanEnabled, [isKanbanEnabled]);

  const debouncedOnSearchChange = useCallback(
    debounce(({ target: { value } }) => {
      dispatch(setSearchValueAct(value));
    }, 500),
    []
  );

  // use callbacks
  const updateFilter = useCallback(
    (filter: Partial<IInvoiceModuleFilter>) => {
      if (module_id) {
        updateModuleFilter({
          filter,
          moduleId: module_id.toString(),
          onError: (response: IApiCallResponse) => {
            notification.error({
              description: response.message,
            });
          },
        });
      }
    },
    [module_id]
  );

  const { customStatusList }: ICustomStatusListInitialState = useAppIVSelector(
    (state) => state.customStatusListData
  );

  const handlePaymentAlertClick = async () => {
    const response = await updatePaymentAlert();
    if (response?.success) {
      dispatch(updatePaymentAlertAct());
    }
  };
  const handleDefaultViewKanabanChangeSmall = async (checked: boolean) => {
    setLoaderSetDefalutViewKanban(true);
    try {
      const requestKanbanSetting = {
        module_field_id: kanbanSelected.length
          ? kanbanSelected.filter((data) => data.trim() !== "")
          : undefined,
        default_view: checked ? 1 : 0,
        module_id: module_id,
      };
      const responseKanbanSetting = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;
      if (responseKanbanSetting.success) {
        setKanbanSetting(responseKanbanSetting?.data);
        setKanbanSelected(responseKanbanSetting?.kanban_project_selected);
      } else {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setLoaderSetDefalutViewKanban(false);
    }
  };
  const CARD_VIEW_CHECK_BOX = [
    {
      title: "Set as Default View",
      isChecked: kanbanSetting?.default_view?.toString() == "1",
      onClick: () => {
        const currentKan = kanbanSetting?.default_view?.toString() === "1";
        handleDefaultViewKanabanChangeSmall(!currentKan);
      },
    },
  ];
  return (
    <>
      <DashboardHeader
        searchPlaceHolder={module_name ? _t(`Search for ${module_name}`) : ""}
        viewSearch={viewSearch}
        searchVal={searchValue}
        setViewSearch={setViewSearch}
        onSearchChange={debouncedOnSearchChange}
        filterComponent={
          <InvoiceFilter
            kanbanView={defaultKanbanView}
            onClearSearch={() => {
              dispatch(setSearchValueAct(""));
            }}
          />
        }
        leftComponent={
          <>
            {!defaultKanbanView ? (
              <li>
                <ButtonWithTooltip
                  tooltipTitle={_t("Card View")}
                  tooltipPlacement="top"
                  icon="fa-brands fa-trello"
                  iconClassName="h-5 w-5"
                  className="!w-7 !h-7"
                  onClick={() => setIsKanbanEnabled(true)}
                />
              </li>
            ) : (
              <li>
                <ButtonWithTooltip
                  tooltipTitle={_t("Return to Dashboard")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-square-list"
                  iconClassName="h-5 w-5"
                  className="!w-7 !h-7"
                  onClick={() => setIsKanbanEnabled(false)}
                />
              </li>
            )}
          </>
        }
        rightComponent={
          <div className="flex flex-row items-center sm:gap-5 gap-2">
            {PARALLEL_SITE === "1" ? (
              <li>
                <TypographyLink
                  href={PANEL_URL + "manage_invoice.php?debug=5"}
                  target="_blank"
                  className="text-13 !text-white !bg-deep-orange-500 font-semibold sm:!px-2 !px-1 !py-1 rounded !inline-block whitespace-nowrap align-middle"
                >
                  <Typography className="!text-white sm:block hidden">
                    {_t("Back to Classic")}
                  </Typography>
                  <FontAwesomeIcon
                    className="text-base w-5 h-5 !text-white sm:hidden block"
                    icon="fa-regular fa-computer-classic"
                  />
                </TypographyLink>
              </li>
            ) : null}
            {defaultKanbanView ? (
              <div className="md:flex hidden items-center">
                <li>
                  <CustomCheckBox
                    className="gap-1.5 !font-medium text-primary-900 dark:text-white/90 relative sm:before:absolute sm:before:w-0.5 sm:before:h-11 sm:before:-right-1.5 sm:before:top-1/2 sm:before:-translate-y-1/2 sm:before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)] whitespace-nowrap"
                    checked={kanbanSetting?.default_view?.toString() == "1"}
                    onChange={handleDefaultViewKanabanChange}
                    disabled={loaderSetDefalutViewKanban}
                    loadingProps={{
                      isLoading: loaderSetDefalutViewKanban,
                      className: "bg-[#ffffff]",
                    }}
                  >
                    {_t("Set as Default View")}
                  </CustomCheckBox>
                </li>
              </div>
            ) : (
              <></>
            )}
            {defaultKanbanView && (
              <Popover
                placement="bottomRight"
                content={
                  <div className="dark:bg-dark-900 min-w-[155px]">
                    <ul className="py-2 px-1 grid gap-0.5">
                      {CARD_VIEW_CHECK_BOX.map((item: any, i) => {
                        return (
                          <li
                            className={`rounded bg-blue-50 dark:bg-dark-800`}
                            key={i}
                          >
                            <div
                              className="flex items-center justify-between cursor-pointer px-2 py-0.5 gap-1"
                              onClick={item.onClick}
                            >
                              <Typography className="text-primary-900 dark:text-white/90">
                                {item.title}
                              </Typography>
                              {item?.isChecked && (
                                <FontAwesomeIcon
                                  className={`text-base w-4 h-4 text-primary-900 opacity-100 `}
                                  icon="fa-regular fa-check"
                                />
                              )}
                            </div>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                }
                trigger="click"
                open={open}
                className="flex md:hidden"
                onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
              >
                <div className="flex relative items-center justify-center">
                  <ButtonWithTooltip
                    icon="fa-regular fa-gear"
                    tooltipTitle=""
                    tooltipPlacement="top"
                    iconClassName="h-5 w-5"
                    className="!w-7 !h-7"
                    onClick={() => {}}
                  />
                </div>
              </Popover>
            )}
            {module_access === "full_access" ||
            module_access === "own_data_access" ? (
              <li>
                <AddButton onClick={() => setIsAddInvoiceOpen(true)}>
                  {_t(module_singular_name ?? "Invoice")}
                </AddButton>
              </li>
            ) : null}
          </div>
        }
      ></DashboardHeader>
      {!defaultKanbanView && !isKanbanLoading && (
        <div
          className={`pt-[41px] overflow-y-auto overflow-hidden ${
            !page_is_iframe
              ? "md:h-[calc(100dvh-143px)] h-[calc(100dvh-112px)]"
              : "h-screen"
          }`}
        >
          <ReadOnlyPermissionMsg view={module_access === "read_only"} />
          <div className="p-4">
            <div
              className={`grid xl:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-2.5 transition-all ease-in-out duration-300 ${
                fullScreenTable || viewSearch
                  ? "max-h-0 overflow-hidden"
                  : "xl:max-h-[850px] md:max-h-[1400px] max-h-[3000px]"
              }`}
            >
              {wepay_activated == "0" &&
                stripe_activated == "0" &&
                payment_alert == 1 && (
                  <div className="bg-[#FFFFBB] flex items-center justify-between gap-2 py-1.5 px-[15px] rounded-md xl:col-span-3 md:col-span-2">
                    <Typography className="block text-black sm:text-13 text-xs max-w-[calc(100%-40px)]">
                      {_t("Get paid faster by enabling online payments.")}{" "}
                      <TypographyLink
                        onClick={() => {
                          if (window && window.ENV && window.ENV.PANEL_URL) {
                            window.open(
                              `${window.ENV.PANEL_URL}manage_settings.php#integration`,
                              "_blank"
                            );
                          }
                        }}
                        className="!text-black !underline underline-offset-1 sm:text-13 text-xs"
                      >
                        {_t("Click here")}
                      </TypographyLink>{" "}
                      {_t(
                        "or configure within Settings > Integration > Online Payments"
                      )}
                    </Typography>
                    <CloseButton
                      onClick={handlePaymentAlertClick}
                      className="!bg-transparent"
                    />
                  </div>
                )}
              <div className="common-card min-h-[182px]">
                <SubmittedInvoices />
              </div>
              <div className="common-card min-h-[182px]">
                <ReceivedPayments />
              </div>
              <div className="common-card min-h-[182px]">
                <UnpaidInvoices />
              </div>
              <div className="common-card h-[266px]">
                <SalesByMonth />
              </div>
              <div className="common-card h-[266px]">
                <BalanceByProject
                  filterByProject={(data) => {
                    updateFilter({
                      project: `${data?.project_id}`,
                      project_names: `${data?.project_name}`,
                      tab: "all",
                      key: "project_id",
                      value: `${data?.project_id}`,
                    });
                  }}
                />
              </div>
              <div className="common-card h-[266px]">
                <AccountsReceivable />
              </div>
              <div className="common-card h-[266px]">
                <InvoicesByStatus
                  filterByInvoiceId={(statusId) => {
                    const invoiceStatusIds = customStatusList
                      ?.filter(
                        (i) =>
                          !invoiceExlStatus.includes(i.key || "") &&
                          i?.item_id?.toString() != invoiceChargedApprovalType
                      )
                      ?.map((data) => data.item_id.toString());
                    updateFilter({
                      tab: "all",
                      key: "approval_type",
                      value: statusId,
                      billing_status: invoiceStatusIds.includes(statusId)
                        ? statusId
                        : undefined,
                      billing_status_names: invoiceStatusIds.includes(statusId)
                        ? customStatusList
                            ?.filter((data) => data?.key == statusId)
                            ?.map((data) => data.name)
                            .join(", ")
                        : undefined,
                    });
                  }}
                />
              </div>
              <div className="common-card h-[266px]">
                <RecentPaymnets />
              </div>
              <div className="common-card h-[266px]">
                <InvoicesComingDue />
              </div>
            </div>

            <div
              className={`w-full ${
                fullScreenTable || viewSearch ? "mt-0" : "mt-2"
              }`}
            >
              <div
                className={`flex items-center justify-between min-h-[34px] md:flex-row flex-col gap-1 ${
                  fullScreenTable || viewSearch ? "-mt-2.5" : ""
                }`}
              >
                <div className="flex justify-start items-center md:w-[calc(100%-52%)] w-full">
                  <Image
                    preview={false}
                    rootClassName={`overflow-hidden ${
                      stripe_activated == "1"
                        ? "sm:w-24 w-12 sm:h-10 h-5"
                        : "sm:w-[170px] w-24"
                    }`}
                    src={
                      stripe_activated == "1"
                        ? "https://cdn.contractorforeman.net/assets/images/stripe.png"
                        : "https://cdn.contractorforeman.net/images/credit-card-logos.png"
                    }
                    alt=""
                    className=""
                  />
                  <Typography
                    className={`pl-2 block text-black sm:text-13 text-xs w-full ${
                      stripe_activated == "1"
                        ? "sm:max-w-[calc(100%-100px)] max-w-[calc(100%-48px)]"
                        : "sm:max-w-[calc(100%-170px)] max-w-[calc(100%-98px)]"
                    }`}
                  >
                    {_t(
                      "Allow your clients to securely submit ACH and/or credit card payments online. "
                    )}
                    <TypographyLink
                      className="!text-black !underline hover:!text-deep-orange-500 transition-all underline-offset-1 sm:text-13 text-xs"
                      onClick={() => {
                        if (window && window.ENV && window.ENV.PANEL_URL) {
                          window.open(
                            `${window.ENV.PANEL_URL}manage_settings.php#integration`,
                            "_blank"
                          );
                        }
                      }}
                    >
                      {_t("See rates and details here.")}
                    </TypographyLink>
                  </Typography>
                </div>
                <div className="ml-auto">
                  <ListTabButton
                    value={tab ? tab : GRID_BUTTON_TAB[2].value}
                    options={GRID_BUTTON_TAB}
                    className="min-w-[80px]"
                    onChange={onTabButtonChange}
                    activeclassName="!bg-[#F1F4F9]"
                  />
                </div>
              </div>
              <div className="relative md:h-[5px] h-7 md:mt-0 mt-2.5 w-12 mx-auto md:-top-3 z-[999] flex items-center justify-end">
                <AccordionButton
                  onClick={() => setFullScreenTable((prev: boolean) => !prev)}
                  fullScreenTable={fullScreenTable}
                />
              </div>
              <div className="p-2 bg-white dark:bg-dark-600 common-list-table rounded">
                <div className="mb-2">
                  <TypographyLink
                    target="_blank"
                    href="https://fbx.bz/contractorforeman"
                    className="italic w-fit mx-auto 2xl:text-13 text-xs !text-primary-900 transition-all hover:!text-deep-orange-500 block !leading-[14px]"
                  >
                    <FontAwesomeIcon
                      className="2xl:w-[13px] xl:h-[13px] w-3 h-3 mr-1  "
                      icon="fa-regular fa-circle-info"
                    />
                    {_t(
                      "Overwhelmed by Accounts Receivables? Explore a Fundbox Line of Credit!"
                    )}
                  </TypographyLink>
                </div>
                <InvoiceList
                  setIsAddInvoiceOpen={setIsAddInvoiceOpen}
                  search={searchValue || ""}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {defaultKanbanView && !isKanbanLoading && (
        <>
          <div className="md:pt-[41px] pt-10 md:h-[calc(100vh-143px)] h-[calc(100vh-112px)] overflow-y-auto overflow-hidden">
            <ReadOnlyPermissionMsg
              view={module_access === "read_only"}
              className="px-4 sm:pt-4 pt-1"
              textClassName="sm:text-13 text-xs flex justify-center items-end sm:h-auto h-7 sm:leading-auto leading-[15px]"
            />
            <div className="flex pt-4 pb-2.5 px-1.5 transition-all ease-in-out duration-300 overflow-x-auto">
              <InvoiceKanban
                kanbanSetting={kanbanSetting}
                setKanbanSetting={setKanbanSetting}
                kanbanSelected={kanbanSelected}
                setKanbanSelected={setKanbanSelected}
                search={searchValue || ""}
              />
            </div>
          </div>
        </>
      )}

      {isAddInvoiceOpen && !isReadOnly && (
        <AddInvoice
          isOpen={isAddInvoiceOpen}
          onClose={() => {
            removeQueryParamsRemix();
            setIsAddInvoiceOpen(false);
          }}
        />
      )}
    </>
  );
};

const ManageInvoice = () => {
  return (
    <IVStoreProvider>
      <Invoice />
    </IVStoreProvider>
  );
};

export default ManageInvoice;

export { ErrorBoundary };
