import React from "react";
import { getGConfig } from "~/zustand";
// Hook
import { useTranslation } from "~/hook";
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
// FontAwesome File
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { Tabs } from "~/shared/components/atoms/tabs";
import ScheduleStoreProvider from "~/modules/projectManagement/pages/schedule/redux/scheduleStoreProvider";
import {
  useScheduleAppDispatch,
  useScheduleAppSelector,
} from "~/modules/projectManagement/pages/schedule/redux/store";
import { setActiveTab } from "~/modules/projectManagement/pages/schedule/redux/slices/scheduleSlice";
import ScheduleGanttTab from "~/modules/projectManagement/pages/schedule/components/gantt/ScheduleGanttTab";
import ScheduleListTab from "~/modules/projectManagement/pages/schedule/components/list/ScheduleListTab";
import ScheduleCalendarTab from "~/modules/projectManagement/pages/schedule/components/calendar/ScheduleCalendarTab";
import { SchduleRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/schedule/dashboard/regular";
import { ScheduleSolidIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/schedule/dashboard/solid";

// Fort Awesome Library Add icons
SchduleRegularIconAdd();
ScheduleSolidIconAdd();

const ManageSchedule = () => {
  const { _t } = useTranslation();

  const { page_is_iframe }: GConfig = getGConfig();

  const currentModule = getCurrentMenuModule();

  const { module_access = "read_only" } = currentModule || {};

  const { activeTab } = useScheduleAppSelector((state) => state.scheduleDash);

  const dispatch = useScheduleAppDispatch();

  const SCHEDULE_TABS = [
    {
      label: _t("Gantt"),
      value: "gantt",
      key: "gantt",
    },
    {
      label: _t("List View"),
      value: "list",
      key: "list",
    },
    {
      label: _t("Calendar"),
      value: "calendar",
      key: "calendar",
    },
  ];

  return (
    <>
      <div
        className={`overflow-y-auto overflow-hidden p-4 ${
          !page_is_iframe
            ? "md:h-[calc(100vh-143px)] h-[calc(100vh-112px)]"
            : "h-screen"
        }`}
      >
        <ReadOnlyPermissionMsg view={module_access === "read_only"} />
        <div className="common-card h-full !overflow-y-auto">
          <Tabs
            className="notification-btn"
            activeKey={activeTab}
            items={SCHEDULE_TABS}
            onChange={(activeKey) => {
              dispatch(setActiveTab(activeKey));
            }}
          />

          <div className="px-4 py-0">
            {activeTab === "gantt" && <ScheduleGanttTab />}
            {activeTab === "list" && <ScheduleListTab />}
            {activeTab === "calendar" && <ScheduleCalendarTab />}
          </div>
        </div>
      </div>
    </>
  );
};

const ManageScheduleIndex = () => {
  return (
    <ScheduleStoreProvider>
      <ManageSchedule />
    </ScheduleStoreProvider>
  );
};

export default React.memo(ManageScheduleIndex);

export { ErrorBoundary };
