import { useState, memo, useCallback, useEffect, useMemo } from "react";
import { useTranslation } from "~/hook";
import { useSearchParams } from "@remix-run/react";
import { type RadioChangeEvent } from "antd";
import { CheckboxProps } from "antd/lib";
import debounce from "lodash/debounce";
import {
  getGConfig,
  updateModuleFilter,
  getGModuleFilters,
  getGSettings,
  getGModuleByKey,
} from "~/zustand";
// Atoms
import { Popover } from "~/shared/components/atoms/popover";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { AddButton } from "~/shared/components/molecules/addButton";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { AccordionButton } from "~/shared/components/molecules/accordionButton";
import { DashboardHeader } from "~/shared/components/molecules/dashboardHeader";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
// Redux
import { getCustomStatusList } from "~/redux/action/getCustomStatusAction";
import PaymentStoreProvider from "~/modules/financials/pages/payment/redux/PaymentStoreProvider";
import {
  useAppPaymentDispatch,
  useAppPaymentSelector,
} from "~/modules/financials/pages/payment/redux/store";
import { isKanbanViewApi } from "~/redux/action/iskanbanView";
import { setSearchValueAct } from "~/modules/financials/pages/payment/redux/slices/dashboardSlice";
import {
  fetchDashWidgetData,
  fetchPaymentStatusListApi,
} from "~/modules/financials/pages/payment/redux/action";
import { updateKanbanSettingApi } from "~/redux/action/kanbanSettings";
import { getCustomData } from "~/redux/action/customDataAction";
// FontAwesome File
import { PaymentsDashboardDuotoneIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/payments/dashboard/duotone";
import { PaymentsDashboardRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/payments/dashboard/regular";
// Other
import PaymentMethod from "~/modules/financials/pages/payment/components/dashboard/PaymentMethod";
import ReceivedPayment from "~/modules/financials/pages/payment/components/dashboard/ReceivedPayment";
import Stripe from "~/modules/financials/pages/payment/components/dashboard/Stripe";
import PaymentVerify from "~/modules/financials/pages/payment/components/dashboard/PaymentVerify";
import RecentPayment from "~/modules/financials/pages/payment/components/dashboard/RecentPayment";
import PaymentMonth from "~/modules/financials/pages/payment/components/dashboard/PaymentMonth";
import PaymentList from "~/modules/financials/pages/payment/components/dashboard/PaymentList";
import { GRID_BUTTON_TAB } from "~/modules/financials/pages/payment/utils/constants";
import { AddPayment } from "~/modules/financials/pages/payment/components";
import PaymentKanban from "~/modules/financials/pages/payment/components/dashboard/PaymentKanban";
import PaymentFilter from "~/modules/financials/pages/payment/components/dashboard/PaymentFilter";
import { defaultConfig } from "~/data";
import Fundbox from "~/modules/financials/pages/payment/components/dashboard/Fundbox";

// Fort Awesome Library Add icons
PaymentsDashboardRegularIconAdd();
PaymentsDashboardDuotoneIconAdd();

const ManagePaymentsComponent = () => {
  const { _t } = useTranslation();
  const dispatch = useAppPaymentDispatch();
  const [searchParams, setSearchParams] = useSearchParams();
  const { searchValue, isDataFetched }: IPaymentDashState =
    useAppPaymentSelector((state) => state.dashboard);
  const { customStatusList, isSLDataFetched }: ICustomStatusListInitialState =
    useAppPaymentSelector((state) => state.customStatusListData);
  const { isStatusListDataFetched } = useAppPaymentSelector(
    (state) => state.statusList
  );
  const { isCLDataFetched }: ICustomDataInitialState = useAppPaymentSelector(
    (state) => state.customData
  );
  const paymentModule: GModule | undefined = getGModuleByKey(
    defaultConfig.payment_module
  );
  const filterSrv =
    (getGModuleFilters() as Partial<IPaymentModuleFilter> | undefined) || {};
  const { tab } = filterSrv;
  const [fullScreenTable, setFullScreenTable] = useState<boolean>(false);
  const [addPayment, setAddPayment] = useState<boolean>(false);
  const [viewSearch, setViewSearch] = useState<boolean>(false);

  const [isKanbanLoading, setIsKanbanLoading] = useState<boolean>(false);
  const [isKanbanEnabled, setIsKanbanEnabled] = useState<boolean>(false);
  const [kanbanSetting, setKanbanSetting] = useState<IKanbanSetting>();
  const [isDefaultViewKanban, setIsDefaultViewKanban] =
    useState<boolean>(false);
  const [kanbanSelected, setKanbanSelected] = useState<string[]>([]);
  const [open, setOpen] = useState<boolean>(false);
  const {
    module_name,
    page_is_iframe,
    module_access,
    module_singular_name,
    module_id,
  }: GConfig = getGConfig();
  const { quickbook_desktop_sync, quickbook_sync }: GSettings = getGSettings();

  const onTabButtonChange = (e: RadioChangeEvent) => {
    try {
      const listValue = e.target.value as string;
      const tab_key =
        GRID_BUTTON_TAB.find((item) => item.value === listValue)?.key ?? "";
      const statusCode =
        customStatusList.find((item) => item.key === tab_key)?.item_id ?? "";
      if (listValue) {
        updateFilter({
          tab: listValue,
          payment_status: statusCode?.toString(),
        });
        return;
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    }
  };

  useEffect(() => {
    if (!isDataFetched) {
      dispatch(fetchDashWidgetData());
    }
  }, [isDataFetched]);

  // Memoize the final boolean value
  const defaultKanbanView = useMemo(() => isKanbanEnabled, [isKanbanEnabled]);

  // use callbacks
  const updateFilter = useCallback(
    (filter: Partial<IPaymentModuleFilter>) => {
      if (module_id) {
        updateModuleFilter({
          filter,
          moduleId: module_id.toString(),
          onError: (response: IApiCallResponse) => {
            notification.error({
              description: response.message,
            });
          },
        });
      }
    },
    [module_id]
  );

  const debouncedOnSearchChange = useCallback(
    debounce(({ target: { value } }) => {
      dispatch(setSearchValueAct(value));
    }, 500),
    []
  );

  // use effects
  useEffect(() => {
    if (paymentModule?.module_id && !isSLDataFetched) {
      dispatch(
        getCustomStatusList({
          module_id: [paymentModule?.module_id],
        })
      );
    }
  }, [paymentModule?.module_id, isSLDataFetched]);

  useEffect(() => {
    if (!isStatusListDataFetched) {
      dispatch(
        fetchPaymentStatusListApi({
          quickbook_sync: quickbook_sync?.toString() || "0",
          quickbook_desktop_sync: quickbook_desktop_sync?.toString() || "0",
        })
      );
    }
  }, [isStatusListDataFetched]);

  useEffect(() => {
    if (paymentModule?.module_id && !isCLDataFetched) {
      dispatch(
        getCustomData({
          types: [242],
          moduleId: paymentModule?.module_id,
        })
      );
    }
  }, [paymentModule?.module_id, isCLDataFetched]);

  useEffect(() => {
    const fetchKanbanView = async () => {
      setIsKanbanLoading(true);
      try {
        const response = (await isKanbanViewApi({
          key: CFConfig.payment_module,
        })) as IIsKanbanEnableApiRes;
        if (response?.success) {
          setIsKanbanEnabled(response?.data?.is_kanban === 1);
        } else {
          setIsKanbanEnabled(false);
        }
      } catch (error) {
        setIsKanbanEnabled(false);
      } finally {
        setIsKanbanLoading(false);
      }
    };
    fetchKanbanView();
  }, []);

  useEffect(() => {
    if (searchParams?.get("action")?.trim() === "new") {
      setAddPayment(true);
    }
  }, [searchParams.get("action")]);

  const handleDefaultViewKanabanChange: CheckboxProps["onChange"] = async (
    e
  ) => {
    setIsDefaultViewKanban(true);
    try {
      const requestKanbanSetting = {
        module_field_id: kanbanSelected.length
          ? kanbanSelected.filter((data) => data.trim() !== "")
          : undefined,
        default_view: e.target.checked ? 1 : 0,
        module_id: module_id,
      };
      const responseKanbanSetting = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;
      if (responseKanbanSetting.success) {
        setKanbanSetting(responseKanbanSetting?.data);
        let responseKanbanSelected = customStatusList
          .filter((data) =>
            responseKanbanSetting?.kanban_project_selected.includes(
              data.item_id.toString()
            )
          )
          .map((data) => data.item_id.toString());
        setKanbanSelected(responseKanbanSelected);
      } else {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsDefaultViewKanban(false);
    }
  };

  const removeQueryParamsPMT = () => {
    searchParams.delete("action");
    searchParams.delete("project");
    setSearchParams(searchParams);
  };
  const toggleFullScreenTable = useCallback(() => {
    setFullScreenTable((prev) => !prev);
  }, []);
  const handleDefaultViewKanabanChangeSmallScreen = async (
    checked: boolean
  ) => {
    setIsDefaultViewKanban(true);
    try {
      const requestKanbanSetting = {
        module_field_id: kanbanSelected.length
          ? kanbanSelected.filter((data) => data.trim() !== "")
          : undefined,
        default_view: checked ? 1 : 0,
        module_id: module_id,
      };
      const responseKanbanSetting = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;
      if (responseKanbanSetting.success) {
        setKanbanSetting(responseKanbanSetting?.data);
        let responseKanbanSelected = customStatusList
          .filter((data) =>
            responseKanbanSetting?.kanban_project_selected.includes(
              data.item_id.toString()
            )
          )
          .map((data) => data.item_id.toString());
        setKanbanSelected(responseKanbanSelected);
      } else {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsDefaultViewKanban(false);
    }
  };
  const CARD_VIEW_CHECK_BOX = [
    {
      title: "Set as Default View",
      isChecked: kanbanSetting?.default_view?.toString() == "1",
      onClick: () => {
        const currentKan = kanbanSetting?.default_view?.toString() === "1";
        handleDefaultViewKanabanChangeSmallScreen(!currentKan);
      },
    },
  ];
  return (
    <>
      <DashboardHeader
        viewSearch={viewSearch}
        searchVal={searchValue}
        setViewSearch={setViewSearch}
        onSearchChange={debouncedOnSearchChange}
        searchPlaceHolder={module_name ? _t(`Search for ${module_name}`) : ""}
        filterComponent={
          <PaymentFilter
            onClearSearch={() => {
              dispatch(setSearchValueAct(""));
            }}
          />
        }
        leftComponent={
          <>
            {!defaultKanbanView ? (
              <li>
                <ButtonWithTooltip
                  tooltipTitle={_t("Card View")}
                  tooltipPlacement="top"
                  icon="fa-brands fa-trello"
                  iconClassName="h-5 w-5"
                  className="!w-7 !h-7"
                  onClick={() => setIsKanbanEnabled(true)}
                />
              </li>
            ) : (
              <li>
                <ButtonWithTooltip
                  tooltipTitle={_t("Return to Dashboard")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-square-list"
                  iconClassName="h-5 w-5"
                  className="!w-7 !h-7"
                  onClick={() => setIsKanbanEnabled(false)}
                />
              </li>
            )}
          </>
        }
        rightComponent={
          <div className="flex flex-row items-center sm:gap-5 gap-2">
            {defaultKanbanView ? (
              <>
                <div className="md:flex hidden items-center sm:gap-5 gap-2">
                  <CustomCheckBox
                    className="gap-1.5 !font-medium text-primary-900 dark:text-white/90 relative sm:before:absolute sm:before:w-0.5 sm:before:h-11 sm:before:-right-1.5 sm:before:top-1/2 sm:before:-translate-y-1/2 sm:before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)] whitespace-nowrap"
                    checked={kanbanSetting?.default_view?.toString() == "1"}
                    onChange={handleDefaultViewKanabanChange}
                    disabled={isDefaultViewKanban}
                    loadingProps={{
                      isLoading: isDefaultViewKanban,
                      className: "bg-[#ffffff]",
                    }}
                  >
                    {_t("Set as Default View")}
                  </CustomCheckBox>
                </div>
                <Popover
                  placement="bottomRight"
                  content={
                    <div className="dark:bg-dark-900 min-w-[155px]">
                      <ul className="py-2 px-1 grid gap-0.5">
                        {CARD_VIEW_CHECK_BOX.map((item, i) => {
                          return (
                            <li
                              className={`rounded bg-blue-50 dark:bg-dark-800`}
                              key={i}
                            >
                              <div
                                className="flex items-center justify-between cursor-pointer px-2 py-0.5 gap-1"
                                onClick={item.onClick}
                              >
                                <Typography className="text-primary-900 dark:text-white/90">
                                  {item.title}
                                </Typography>
                                {item?.isChecked && (
                                  <FontAwesomeIcon
                                    className={`text-base w-4 h-4 text-primary-900 opacity-100 `}
                                    icon="fa-regular fa-check"
                                  />
                                )}
                              </div>
                            </li>
                          );
                        })}
                      </ul>
                    </div>
                  }
                  trigger="click"
                  open={open}
                  className="flex md:hidden"
                  onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
                >
                  <div className="flex relative items-center justify-center">
                    <ButtonWithTooltip
                      icon="fa-regular fa-gear"
                      tooltipTitle=""
                      tooltipPlacement="top"
                      iconClassName="h-5 w-5"
                      className="!w-7 !h-7"
                      onClick={() => {}}
                    />
                  </div>
                </Popover>
              </>
            ) : (
              <></>
            )}
            {module_access === "full_access" ||
            module_access === "own_data_access" ? (
              <li>
                <AddButton
                  onClick={() => {
                    setAddPayment(true);
                  }}
                >
                  {_t(module_singular_name ?? "Payment")}
                </AddButton>
              </li>
            ) : null}
          </div>
        }
      />
      {!defaultKanbanView && !isKanbanLoading && (
        <div
          className={`pt-[41px] overflow-y-auto overflow-hidden ${
            !page_is_iframe
              ? "md:h-[calc(100vh-143px)] h-[calc(100vh-112px)]"
              : "h-screen"
          }`}
        >
          <ReadOnlyPermissionMsg view={module_access === "read_only"} />
          <div className="p-4">
            <div
              className={`grid xl:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-2.5 transition-all ease-in-out duration-300 ${
                fullScreenTable || viewSearch
                  ? "max-h-0 overflow-hidden"
                  : "min-[1536px]:max-h-[850px] md:max-h-[1400px] max-h-[4000px]"
              }`}
            >
              <div className="common-card min-h-[180px]">
                <ReceivedPayment />
              </div>
              <div className="common-card min-h-[180px]">
                <PaymentMethod />
              </div>
              <div className="grid sm:grid-cols-2 gap-2.5 xl:col-span-1 md:col-span-2">
                <div className="common-card min-h-[180px]">
                  <Stripe />
                </div>
                <div className="common-card sm:min-h-[180px]">
                  <Fundbox />
                </div>
              </div>
              <div className="common-card h-[266px]">
                <PaymentVerify />
              </div>
              <div className="common-card h-[266px]">
                <RecentPayment />
              </div>
              <div className="common-card h-[266px]">
                <PaymentMonth />
              </div>
            </div>
            <div
              className={`w-full transition-all duration-300 ${
                fullScreenTable || viewSearch
                  ? "md:mt-2.5 mt-8"
                  : "md:mt-[25px] mt-[55px]"
              }`}
            >
              <div className="relative h-7 z-[999] flex items-center justify-end">
                <AccordionButton
                  onClick={toggleFullScreenTable}
                  fullScreenTable={fullScreenTable}
                />
                <div className="flex justify-between items-center md:mb-5 w-full mb-[70px] flex-wrap md:flex-nowrap">
                  <div className="w-full flex justify-end">
                    <ListTabButton
                      value={tab ? tab : GRID_BUTTON_TAB[3].value}
                      options={GRID_BUTTON_TAB}
                      className="lg:min-w-[100px] md:min-w-[80px] min-w-[70px]"
                      onChange={onTabButtonChange}
                      activeclassName="!bg-[#F1F4F9]"
                    />
                  </div>
                </div>
              </div>
              <div className="p-2 bg-white dark:bg-dark-600 common-list-table rounded">
                <PaymentList
                  setAddPayment={setAddPayment}
                  search={searchValue || ""}
                />
              </div>
            </div>
          </div>
        </div>
      )}
      {defaultKanbanView && !isKanbanLoading && (
        <PaymentKanban
          kanbanSelected={kanbanSelected}
          setKanbanSelected={setKanbanSelected}
          kanbanSetting={kanbanSetting}
          setKanbanSetting={setKanbanSetting}
        />
      )}
      {addPayment &&
        (module_access === "full_access" ||
          module_access === "own_data_access") && (
          <AddPayment
            setAddPayment={setAddPayment}
            addPayment={addPayment}
            removeQueryParamsPMT={removeQueryParamsPMT}
          />
        )}
    </>
  );
};

const ManagePayments = () => {
  return (
    <PaymentStoreProvider>
      <ManagePaymentsComponent />
    </PaymentStoreProvider>
  );
};

export default memo(ManagePayments);

export { ErrorBoundary };
