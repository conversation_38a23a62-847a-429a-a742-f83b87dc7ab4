import { lazy, Suspense, useEffect, useRef, useState } from "react";
import { faMemoPad } from "@fortawesome/pro-regular-svg-icons";
// Hook + Redux
import { useTranslation } from "~/hook";
import DashStoreProvider from "~/modules/main-dashboard/redux/DashStoreProvider";
import {
  useAppDashDispatch,
  useAppDashSelector,
} from "~/modules/main-dashboard/redux/store";
import { fetchDashData } from "~/modules/main-dashboard/redux/action";
// Atoms
import { Button } from "~/shared/components/atoms/button";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Popover } from "~/shared/components/atoms/popover";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { IconButton } from "~/shared/components/molecules/iconButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
// FontAwesome File
import { IndexDashboardRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/main-dashboard/regular";
import { IndexDashboardSolidIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/main-dashboard/solid";
// Other

// import WeatherLogs from "~/modules/main-dashboard/WeatherLogs";
// const WeatherLogs = lazy(() => import("~/modules/main-dashboard/WeatherLogs"));
import Calender from "~/modules/main-dashboard/components/widget/Calender";
import ClockTimeCard from "~/modules/main-dashboard/components/widget/ClockTimeCard";
import TrainingSupport from "~/modules/main-dashboard/components/widget/TrainingSupport";
import MyTimeCards from "~/modules/main-dashboard/components/widget/MyTimeCards";
import ProjectsSchedule from "~/modules/main-dashboard/components/widget/ProjectsSchedule";
import DashGridStack from "~/modules/main-dashboard/DashGridStack.client";
import MyAppointments from "~/modules/main-dashboard/components/widget/MyAppointments";
import WeatherLogs from "~/modules/main-dashboard/components/widget/WeatherLogs";
import ProjectsTasksSchedule from "~/modules/main-dashboard/components/widget/ProjectsTasksSchedule";
import DailyLogs from "~/modules/main-dashboard/components/widget/DailyLogs";
import MyDayStatic from "~/modules/main-dashboard/components/widget/MyDayStatic";
import EquipmentLogs from "~/modules/main-dashboard/components/widget/EquipmentLogs";
import Estimates from "~/modules/main-dashboard/components/widget/Estimates";
import Invoices from "~/modules/main-dashboard/components/widget/Invoices";
import Leads from "~/modules/main-dashboard/components/widget/Leads";
import Incidents from "~/modules/main-dashboard/components/widget/Incidents";

import RecentNotes from "~/modules/main-dashboard/components/widget/RecentNotes";
import OpportunitiesStats from "~/modules/main-dashboard/components/widget/OpportunitiesStats";
import Punchlists from "~/modules/main-dashboard/components/widget/Punchlists";
import SafetyMeetings from "~/modules/main-dashboard/components/widget/SafetyMeetings";
import ProjectsScheduleList from "~/modules/main-dashboard/components/widget/ProjectsScheduleList";
import ServiceTickets from "~/modules/main-dashboard/components/widget/ServiceTickets";

const WhoseClockIn = lazy(
  () => import("~/modules/main-dashboard/components/widget/WhoseClockIn")
);
const Inspections = lazy(
  () => import("~/modules/main-dashboard/components/widget/Inspections")
);

// export const loader: TLoaderFunction = async () => {
//   return redirect(new URL("index.php", process.env.PANEL_URL).toString());
// };

// Fort Awesome Library Add icons
IndexDashboardRegularIconAdd();
IndexDashboardSolidIconAdd();

const IndexCom = () => {
  const { _t } = useTranslation();
  const dispatch = useAppDashDispatch();

  const { isDashLoading, dashWidgetGridStack }: IDashIntlState =
    useAppDashSelector((state) => state.dashboard);
  const [gridEditable, setGridEditable] = useState<boolean>(false);
  const gridDashRef = useRef<IDashGridStackHandle>(null);

  useEffect(() => {
    dispatch(
      fetchDashData({
        is_refresh: 1,
        iframe_call: 0,
        global_project: 0,
        status: 1,
        from: "panel",
      })
    );
  }, []);

  const handleManageWidgets = (data: string | string[]) => {
    console.log("data", data);
    if (data?.length) {
      gridDashRef.current?.addUpdateWidget("statics-daily-logs", "add");
    } else {
      gridDashRef.current?.addUpdateWidget("statics-daily-logs", "remove");
    }
  };

  const widgetList = [
    {
      id: "who-is-clocked-user",
      content: <WhoseClockIn />,
    },
    {
      id: "statics-inspections",
      content: <Inspections />,
    },
    {
      id: "statics-equipment-logs",
      content: <EquipmentLogs />,
    },
    {
      id: "statics-notes",
      content: <RecentNotes />,
    },
    {
      id: "statics-daily-logs",
      content: <DailyLogs />,
    },
  ];

  if (isDashLoading) {
    return <>Loading...</>;
  }
  return (
    <>
      {gridEditable ? (
        <div className="fixed z-[101] md:top-28 top-20 w-full bg-[#f8f8f8] dark:bg-dark-800 py-2.5 px-4 border-b border-gray-300 flex md:flex-row flex-col gap-2.5 md:items-center items-start justify-between transition-all ease-in-out duration-300">
          {/* Manage Widgets */}
          <SelectField
            labelPlacement="top"
            applyBorder={true}
            formInputClassName="w-[190px] overflow-visible"
            containerClassName="overflow-visible"
            fieldClassName="before:hidden"
            className="border-select-filed rounded h-8"
            placeholder="Manage Widgets"
            mode={"multiple"}
            options={[
              {
                label: "Clocked User",
                value: "who-is-clocked-user",
              },
              {
                label: "Inspections",
                value: "statics-inspections",
              },
              {
                label: "Notes",
                value: "statics-notes",
              },
            ]}
            name="manage_widgets"
            popupClassName="popup-select-option-header"
            onChange={handleManageWidgets}
          />

          <div className="flex items-center gap-2.5">
            <PrimaryButton
              buttonText="Add New"
              onClick={() => {
                gridDashRef.current?.addUpdateWidget(
                  "statics-daily-logs",
                  "add"
                );
              }}
            />
            <PrimaryButton
              buttonText="remove"
              onClick={() => {
                gridDashRef.current?.addUpdateWidget(
                  "statics-daily-logs",
                  "remove"
                );
              }}
            />

            <PrimaryButton
              buttonText="Auto Arrange"
              onClick={() => gridDashRef.current?.autoArrange()}
            />
            <PrimaryButton
              onClick={() => {
                gridDashRef.current?.saveLayout();
                // setGridEditable(false)
              }}
            />
            <Button
              onClick={() => setGridEditable(false)}
              className="min-w-[75px] hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900"
            >
              {_t("Cancel")}
            </Button>
          </div>
        </div>
      ) : (
        <></>
      )}
      <div className={`p-3 ${gridEditable ? "md:mt-[55px] mt-24" : ""}`}>
        <div className="flex md:flex-row flex-col md:items-center items-start gap-2.5 px-1">
          <div className="flex group items-center justify-between py-2 px-2.5 gap-1.5 border border-gray-300 dark:border-white/10 bg-white dark:bg-dark-800 rounded w-full">
            <div className="text-gray-600 md:pr-0 pr-2">
              <FontAwesomeIcon
                className=" w-[13px] h-[13px] mr-2 inline-block"
                icon={faMemoPad}
              />
              <Typography className="!text-13 dark:text-white">
                {_t(
                  "Don't forget... clean out all trucks every Friday please. Daily Logs and To Do's have been saving us a lot of time, lets find ways to use them more."
                )}
              </Typography>
            </div>
            <div className="flex items-center gap-3 md:opacity-0 group-hover:opacity-100 group-focus-within:opacity-100">
              <ButtonWithTooltip
                tooltipTitle={_t("Click to edit Corporate Note")}
                tooltipPlacement="top"
                icon="fa-regular fa-pen-to-square"
                onClick={() => {}}
              />
              <ButtonWithTooltip
                tooltipTitle={_t("Click to Save Corporate Note")}
                tooltipPlacement="top"
                icon="fa-regular fa-save"
                onClick={() => {}}
              />
              <Popover
                content={
                  <div className="min-w-[272px]">
                    <Typography className="block text-sm py-2 px-3.5 rounded-t-lg bg-gray-50 relative before:w-full before:h-px before:bg-[linear-gradient(177deg,#a5a5a53d_24%,#fafafa_100%)] before:bottom-0 before:left-1/2 before:-translate-x-1/2 before:absolute">
                      {_t(`Announcement`)}
                    </Typography>
                    <div className="py-2 px-3.5">
                      <Typography className="flex items-start text-13">
                        {_t("Send the push notification to all users?")}
                      </Typography>
                      <div className="flex gap-2 justify-center mt-3">
                        <Button type="primary" className="w-fit">
                          {_t("Yes")}
                        </Button>
                        <Button>{_t("No")}</Button>
                      </div>
                    </div>
                  </div>
                }
                placement="top"
                trigger="click"
              >
                <ButtonWithTooltip
                  tooltipTitle={_t("Send Announcement to All Users")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-bullhorn"
                  onClick={() => {}}
                />
              </Popover>
            </div>
          </div>
          <div className="flex gap-2.5">
            {!gridEditable && (
              <Button
                className="min-w-[75px] hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900"
                onClick={() => setGridEditable(true)}
              >
                <FontAwesomeIcon
                  icon="fa-regular fa-pen"
                  className="w-3.5 h-3.5"
                />
                {_t("Customize")}
              </Button>
            )}
            <Button className="min-w-[75px] hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900">
              {_t("Remove Demo Data")}
            </Button>
          </div>
        </div>
        <div className="mt-2.5 mb-7">
          {/* <DailyLogs />
          <EquipmentLogs />
          <Inspections />
          <RecentNotes />
          <Punchlists /> */}
          <Suspense
            fallback={<div className="h-[calc(100vh-144px)]">Loading...</div>}
          >
            <DashGridStack ref={gridDashRef} isGridEditable={gridEditable}>
              {dashWidgetGridStack.map((item) => {
                const widget = widgetList?.find((i) => i.id == item?.id);
                if (!widget?.id) return;
                return (
                  <div
                    key={widget?.id}
                    className={`grid-stack-item`}
                    gs-id={item?.id}
                    gs-x={item?.x}
                    gs-y={item?.y}
                    gs-w={item?.w}
                    gs-h={item?.h}
                    // gs-min-w={2}
                    // gs-max-w={2}
                    // gs-min-h={2}
                    // gs-max-h={3}
                    // gs-no-resize="true"
                    // gs-no-move="true".
                  >
                    <div className="grid-stack-item-content common-card !overflow-hidden !inset-2.5">
                      {widget?.content}
                    </div>
                  </div>
                );
              })}
            </DashGridStack>
          </Suspense>

          {/* <WhoseClockIn />
          <WeatherLogs />
          <Calender />
          <ClockTimeCard />
          <TrainingSupport />
          <MyTimeCards />
          <ProjectsSchedule />
          <MyAppointments />
          <ProjectsTasksSchedule />
          <MyDayStatic />
          {/* <EquipmentLogs /> */}
          <Estimates />
          <Invoices />
          <Leads />
          <Incidents />
          {/* <Inspections /> */}
          {/* <RecentNotes /> */}
          <OpportunitiesStats />
          {/* <Punchlists /> */}
          <SafetyMeetings />
          <ProjectsScheduleList />
          <ServiceTickets />
          {/* 
          <Files />
          <Projects />
          <RFINotice />
          <Submittals />
          <ToDo />
          <VehicleLogs />
          <WorkOrders /> */}
        </div>
      </div>
    </>
  );
};

const Index = () => {
  return (
    <DashStoreProvider>
      <IndexCom />
    </DashStoreProvider>
  );
};

export default Index;

export { ErrorBoundary };
