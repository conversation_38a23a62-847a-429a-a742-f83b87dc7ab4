import { useParams } from "@remix-run/react";
import { useMemo } from "react";
import { CustomFieldForm } from "~/shared/components/organisms/customField";
import DetailsCard from "../../../../modules/document/rfi-and-notices/components/tab/details/DetailsCard";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import Response from "../../../../modules/document/rfi-and-notices/components/tab/details/Response";
import Requested from "../../../../modules/document/rfi-and-notices/components/tab/details/Requested";
import ReferencedQuestion from "../../../../modules/document/rfi-and-notices/components/tab/details/ReferencedQuestion";
import Resolution from "../../../../modules/document/rfi-and-notices/components/tab/details/Resolution";
import Description from "../../../../modules/document/rfi-and-notices/components/tab/details/Description";
import { useAppRFISelector } from "~/modules/document/rfi-and-notices/redux/store";
import getCustomFieldAccess from "~/shared/utils/helper/getCustomFieldAccess";

const RfiNoticeDetailsTab = () => {
  const { id }: RouteParams = useParams();
  const currentModule = getCurrentMenuModule();
  const { module_access = "no-access", module_id } = currentModule || {};
  const { isRFIDetailLoading, rfiDetail }: IRFIInitialState = useAppRFISelector(
    (state) => state.rfiDetail
  );

  const isReadOnly = useMemo(
    () => module_access === "read_only",
    [module_access]
  );

  const requestBody = useMemo(() => {
    return {
      moduleId: Number(module_id),
      recordId: id,
    };
  }, [module_id, id]);

  const { isReadOnlyCustomField, isNoAccessCustomField }: ICustomFieldAccess =
    getCustomFieldAccess();
  return (
    !isRFIDetailLoading && (
      <div className="grid lg:grid-cols-2 gap-2.5">
        <div className="flex flex-col gap-2.5">
          <div className="common-card py-3 px-[15px]">
            <DetailsCard isReadOnly={isReadOnly} />
          </div>
          {rfiDetail.correspondence_key ===
            "correspondence_complience_notice" && (
            <div className="py-3 px-[15px] common-card">
              <Resolution isReadOnly={isReadOnly} />
            </div>
          )}
          {rfiDetail.correspondence_key === "correspondence_rfi" && (
            <div className="py-3 px-[15px] common-card">
              <ReferencedQuestion isReadOnly={isReadOnly} />
            </div>
          )}
          {!isNoAccessCustomField &&
            rfiDetail.correspondence_key === "correspondence_rfi" && (
              <div className="py-3 px-[15px] common-card">
                <CustomFieldForm
                  isReadOnly={isReadOnly || isReadOnlyCustomField}
                  spinClassName="h-[156px]"
                  requestBody={requestBody}
                />
              </div>
            )}
        </div>
        <div className="flex flex-col gap-2">
          {rfiDetail.correspondence_key ===
            "correspondence_complience_notice" && (
            <div className="py-3 px-[15px] common-card">
              <Description isReadOnly={isReadOnly} />
            </div>
          )}
          {rfiDetail.correspondence_key === "correspondence_rfi" && (
            <>
              <div className="py-3 px-[15px] common-card">
                <Requested isReadOnly={isReadOnly} />
              </div>
              <div className="py-3 px-[15px] common-card">
                <Response isReadOnly={isReadOnly} />{" "}
              </div>
            </>
          )}
          {!isNoAccessCustomField &&
            (rfiDetail.correspondence_key ===
              "correspondence_complience_notice" ||
              rfiDetail.correspondence_key ===
                "correspondence_schedule_notice") && (
              <div className="common-card ease-in-out duration-300 py-3 px-[15px] h-fit">
                <CustomFieldForm
                  isReadOnly={isReadOnly || isReadOnlyCustomField}
                  spinClassName="h-[156px]"
                  requestBody={requestBody}
                />
              </div>
            )}
        </div>
      </div>
    )
  );
};

export default RfiNoticeDetailsTab;
