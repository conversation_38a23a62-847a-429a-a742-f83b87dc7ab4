import { useParams } from "@remix-run/react";
import { getGConfig, getGSettings, useGModules } from "~/zustand";
// Organisms
import { NoteList } from "~/shared/components/organisms/notes/noteList";
// Other
import { useEffect, useMemo, useState } from "react";
import { defaultConfig } from "~/data";
import {
  useAppRFIDispatch,
  useAppRFISelector,
} from "~/modules/document/rfi-and-notices/redux/store";
import { Spin } from "~/shared/components/atoms/spin";
import {
  addUpNoteAct,
  addUpNoteFileAct,
  deleteNotesAct,
} from "~/modules/document/rfi-and-notices/redux/slices/rfiNotesSlice";
import { fetchRFINotes } from "~/modules/document/rfi-and-notices/redux/action/rfiNotes";

const NotesTab = () => {
  const { id }: RouteParams = useParams();
  const { module_id, module_access, module_key }: GConfig = getGConfig();
  const { checkModuleAccessByKey } = useGModules();
  const { date_format, image_resolution, comment_order }: GSettings =
    getGSettings();

  const dispatch = useAppRFIDispatch();
  const { rfiDetail, isRFIDetailLoading }: IRFIInitialState = useAppRFISelector(
    (state) => state.rfiDetail
  );
  const { notesData, isNoteTabLoading } = useAppRFISelector(
    (state) => state.rfiNotesNotes
  ) as IRFINotesInitialState;

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );
  useEffect(() => {
    dispatch(
      fetchRFINotes({
        record_id: Number(id),
        module_key: module_key,
        order: comment_order,
      })
    );
  }, []);

  return (
    !isRFIDetailLoading && (
      <div className="grid grid-cols-1 gap-2.5">
        <div className="py-3 px-[15px] common-card">
          <NoteList
            projectid={rfiDetail?.project_id || 0}
            isAddAttachAllow={!isReadOnly}
            isAttachReadOnly={isReadOnly}
            notesData={notesData?.length > 0 ? notesData : []}
            moduleData={{
              moduleKey: module_key || "",
              recordId: id || "",
            }}
            onAddUpNote={(data) => {
              dispatch(addUpNoteAct(data));
            }}
            onUpNoteFile={(data) => {
              dispatch(addUpNoteFileAct(data));
            }}
            isNotesLoading={isNoteTabLoading}
            onNoteDeleted={(id) => {
              dispatch(deleteNotesAct({ note_id: id }));
            }}
            validationParams={{
              date_format,
              file_support_module_access: checkModuleAccessByKey(
                defaultConfig.file_support_key
              ),
              image_resolution,
              module_key,
              module_id,
              module_access,
            }}
          />
        </div>
      </div>
    )
  );
};

export default NotesTab;
