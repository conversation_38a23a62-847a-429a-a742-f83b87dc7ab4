import {
  useState,
  use<PERSON>em<PERSON>,
  useC<PERSON>back,
  useEffect,
  use<PERSON><PERSON>,
  <PERSON><PERSON>vent<PERSON><PERSON><PERSON>,
} from "react";
import { type RadioChangeEvent } from "antd";
import debounce from "lodash/debounce";
import isEqual from "lodash/isEqual";
import { CheckboxProps } from "antd/lib";
// Redux + Store + Helpers + Hook
import { useTranslation } from "~/hook";
import { removeQueryParams, sanitizeString } from "~/helpers/helper";
import { directoryTypeId } from "~/constants/directory";
import {
  getGConfig,
  getGFilterModuleId,
  getGModuleByKey,
  getGModuleFilters,
  getGSettings,
  updateModuleFilter,
  useGModules,
} from "~/zustand";
import { AddDirectory } from "~/modules/people/directory/components";
import { customDataTypesByKey } from "~/utils/constasnts";
import DirStoreProvider from "~/modules/people/directory/redux/DirStoreProvider";
import {
  useAppDispatch,
  useAppSelector,
} from "~/modules/people/directory/redux/store";
import { fetchLeadDashData } from "~/modules/people/directory/redux/action/leadDashAction";
import { getCustomStatusList } from "~/redux/action/getCustomStatusAction";
import { getCustomData } from "~/redux/action/customDataAction";
import { isKanbanViewApi } from "~/redux/action/iskanbanView";
import { updateKanbanSettingApi } from "~/redux/action/kanbanSettings";
// atoms
import { Popover } from "~/shared/components/atoms/popover";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { AddButton } from "~/shared/components/molecules/addButton";
import { DashboardHeader } from "~/shared/components/molecules/dashboardHeader";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
import { AccordionButton } from "~/shared/components/molecules/accordionButton";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
// FontAwesome File
import { LeadDashboardRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/lead/dashboard/regular";
// Other
import LeadsByMonthSource from "~/modules/people/directory/components/leads/dashboard/LeadsByMonthSource";
import NewLeadContact from "~/modules/people/directory/components/leads/dashboard/NewLeadContact";
import LeadsByStage from "~/modules/people/directory/components/leads/dashboard/LeadsByStage";
import UpcomingLeadTasks from "~/modules/people/directory/components/leads/dashboard/UpcomingLeadTasks";
import LeadList from "~/modules/people/directory/components/leads/dashboard/LeadList";
import LeadsFilter from "~/modules/people/directory/components/leads/dashboard/LeadsFilter";
import { defaultConfig } from "~/data";
import { setSearchValueAct } from "~/modules/people/directory/redux/slices/leadDashSlice";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { STATUS_CODE } from "~/shared/constants";
import LeadKanban from "~/modules/people/directory/components/leads/dashboard/LeadKanban";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { useSearchParams } from "@remix-run/react";

// Font awesome icon
LeadDashboardRegularIconAdd();

const ManageLeadCom = () => {
  const { _t } = useTranslation();
  const dispatch = useAppDispatch();
  const [searchParams, setSearchParams] = useSearchParams();
  const {
    module_id,
    module_name,
    module_access,
    module_singular_name,
    page_is_iframe,
    module_key,
  }: GConfig = getGConfig();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { checkModuleAccessByKey } = useGModules();
  const { user_id = 0 } = user || {};
  const { temperature_scale }: GSettings = getGSettings();
  const { searchValue, isDataChanged, isDataFetched }: ILeadIntlState =
    useAppSelector((state) => state.leadDash);

  const filterSrv =
    (getGModuleFilters() as Partial<LeadsFilter> | undefined) || {};
  const filterMId = getGFilterModuleId();

  const { filter_my_list } = filterSrv;
  const modulePLName =
    _t(HTMLEntities.decode(sanitizeString(module_name))) || _t("Leads");

  const proModule: GModule | undefined = getGModuleByKey(
    defaultConfig.project_module
  );
  const dirModule: GModule | undefined = getGModuleByKey(
    defaultConfig.directory_module
  );
  const { customStatusList }: ICustomStatusListInitialState = useAppSelector(
    (state) => state.customStatusListData
  );

  const previousValues = useRef({
    filterSrv: JSON.stringify(filterSrv),
  });
  const pageReloadRef = useRef<boolean>(false);

  // States
  const [addLeads, setAddLeads] = useState<boolean>(false);
  const [fullScreenTable, setFullScreenTable] = useState<boolean>(false);
  const [isKanbanEnabled, setIsKanbanEnabled] = useState<boolean>(false);
  const [viewSearch, setViewSearch] = useState<boolean>(false);
  const [isLeadLoading, setIsLeadLoading] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const [isCloseLeadAlert, setIsCloseLeadAlert] = useState<boolean>(false);
  const [kanbanSetting, setKanbanSetting] = useState<IKanbanSetting>();
  const [kanbanSelected, setKanbanSelected] = useState<string[]>([]);
  const [isKanbanLoading, setIsKanbanLoading] = useState<boolean>(false);
  const [isUpcomingTableChange, setIsUpComingTableChange] =
    useState<boolean>(false);
  const [isDefaultViewKanban, setIsDefaultViewKanban] =
    useState<boolean>(false);
  // Memoize the final boolean value
  const defaultKanbanView = useMemo(() => isKanbanEnabled, [isKanbanEnabled]);
  const [isMyLeadKanban, setIsMyLeadKanban] = useState<boolean>(false);

  // const filter = getGModuleFilters() as Partial<LeadsFilter> | undefined;

  const LEAD_BUTTON_TAB = [
    {
      label: (
        <div className="flex gap-1.5 items-center font-medium">
          <FontAwesomeIcon icon="fa-duotone fa-solid fa-people-group" />
          {`All ${modulePLName}`}
        </div>
      ),
      value: "0",
    },
    {
      label: (
        <div className="flex gap-1.5 items-center font-medium">
          <FontAwesomeIcon icon="fa-duotone fa-solid fa-user" />
          {`My ${modulePLName}`}
        </div>
      ),
      value: "1",
    },
  ];
  const CARD_VIEW_CHECK_BOX = [
    { title: `My ${modulePLName}` },
    { title: "Set as Default View" },
  ];
  useEffect(() => {
    if (dirModule?.module_id && dirModule?.module_id != 0) {
      dispatch(
        getCustomData({
          // types: customDataTypes,
          types: [customDataTypesByKey.leadReferralSourceID],
          moduleId: dirModule?.module_id,
        })
      );
    }
  }, [dirModule?.module_id]);

  useEffect(() => {
    if (
      proModule?.module_id &&
      dirModule?.module_id &&
      dirModule?.module_id != 0
    ) {
      dispatch(
        getCustomStatusList({
          is_deleted: 0,
          module_id: [proModule?.module_id, dirModule?.module_id],
        })
      );
    }
  }, [proModule?.module_id, dirModule?.module_id]);

  const createTempFil = (filterSrv: Partial<LeadsFilter>) => {
    let tempFil: Partial<LeadsFilter> = {
      status: STATUS_CODE.ACTIVE,
      // filter_my_list: "",
      ...filterSrv,
    };

    if (filterSrv?.status === STATUS_CODE.ALL) {
      delete tempFil.status;
    }

    return getValuableObj(tempFil);
  };

  const tempFil = useMemo(() => createTempFil(filterSrv), [filterSrv]);

  const fetchDashboardWidget = async () => {
    await dispatch(
      fetchLeadDashData({ filter: tempFil, is_new_filter_applied: true })
    );
    pageReloadRef.current = false;
  };

  useEffect(() => {
    if (filterMId != module_id) {
      return;
    }

    const currentValues = {
      filterSrv: JSON.stringify(filterSrv),
    };
    if (
      !isEqual(previousValues.current, currentValues) ||
      isDataChanged ||
      !isDataFetched
    ) {
      previousValues.current = currentValues;
      fetchDashboardWidget();
    }
  }, [filterSrv, module_id, filterMId, isDataChanged]);

  const isReadOnly = useMemo(
    () => module_access === "read_only",
    [module_access]
  );

  const updateFilter = useCallback(
    (filter: Partial<LeadsFilter>) => {
      if (module_id) {
        updateModuleFilter({
          filter,
          moduleId: module_id.toString(),
          onError: (response: IApiCallResponse) => {
            notification.error({
              description: response.message,
            });
          },
        });
      }
    },
    [module_id]
  );

  const debouncedOnSearchChange = useCallback(
    debounce(({ target: { value } }) => {
      dispatch(setSearchValueAct(value));
    }, 500),
    []
  );

  const onMyLeadTabButtonChange: CheckboxProps["onChange"] = async (e) => {
    setIsMyLeadKanban(true);
    try {
      updateFilter({
        filter_my_list: e.target.checked ? 1 : 0,
      });
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsMyLeadKanban(false);
    }
  };

  const onMyLeadTabListButtonChange = (e: RadioChangeEvent) => {
    try {
      const listValue = e.target.value as string;
      updateFilter({
        filter_my_list: listValue == "1" ? "1" : "",
      });
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    }
  };

  useEffect(() => {
    return () => {
      const newUrl = window?.location?.pathname?.includes("manage-leads");
      if (!newUrl) {
        dispatch(setSearchValueAct(""));
      }
    };
  }, []);

  useEffect(() => {
    const fetchKanbanView = async () => {
      setIsKanbanLoading(true);
      try {
        const response = (await isKanbanViewApi({
          key: CFConfig.leads_module,
        })) as IIsKanbanEnableApiRes;
        if (response?.success) {
          setIsKanbanEnabled(response?.data?.is_kanban === 1);
        } else {
          setIsKanbanEnabled(false);
        }
      } catch (error) {
        setIsKanbanEnabled(false);
      } finally {
        setIsKanbanLoading(false);
      }
    };
    fetchKanbanView();
  }, []);

  const handleDefaultViewKanabanChange: CheckboxProps["onChange"] = async (
    e
  ) => {
    setIsDefaultViewKanban(true);
    try {
      const requestKanbanSetting = {
        module_field_id: kanbanSelected.length
          ? kanbanSelected.filter((data) => data.trim() !== "")
          : undefined,
        default_view: e.target.checked ? 1 : 0,
        module_id: dirModule?.module_id,
      };
      const responseKanbanSetting = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;
      if (responseKanbanSetting.success) {
        setKanbanSetting(responseKanbanSetting?.data);
        let responseKanbanSelected = customStatusList
          .filter((data) =>
            responseKanbanSetting?.kanban_project_selected.includes(
              data.item_id.toString()
            )
          )
          .map((data) => data.item_id.toString());
        setKanbanSelected(responseKanbanSelected);
      } else {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsDefaultViewKanban(false);
    }
  };

  // Check localStorage on component mount
  useEffect(() => {
    const warningDismissed = localStorage.getItem("LeadDashWarningDismissed");
    if (warningDismissed === "yes") {
      setIsCloseLeadAlert(true);
    }
  }, [localStorage]);

  useEffect(() => {
    if (searchParams?.get("action")?.trim() === "new") {
      setAddLeads(true);
      if (isReadOnly) {
        setSearchParams({});
      }
    }
  }, [searchParams.get("action"), isReadOnly]);

  const removeQueryParamsLead = () => {
    searchParams.delete("action");
    setSearchParams(searchParams);
  };
  const simulateCheckboxChangeEvent = (
    checked: boolean
  ): TCheckboxChangeEvent => {
    return {
      target: { checked },
      stopPropagation: () => {},
      preventDefault: () => {},
      nativeEvent: {} as any,
    };
  };
  return (
    <>
      <DashboardHeader
        searchPlaceHolder={module_name ? _t(`Search for ${module_name}`) : ""}
        onSearchChange={debouncedOnSearchChange}
        searchVal={searchValue}
        viewSearch={viewSearch}
        className="leads-header"
        setViewSearch={setViewSearch}
        // -- Wizard to refresh full screen (If needed in future) --

        // refreshWizard={
        //   <WizardRefreshIcon
        //     refreshIconTooltip={_t("Refreshed just now")}
        //     onClick={() => {
        //       fetchDashboardWidget();
        //       pageReloadRef.current = true;
        //     }}
        //     isLoading={pageReloadRef.current}
        //   />
        // }
        filterComponent={
          <LeadsFilter
            kanbanView={defaultKanbanView}
            onClearSearch={() => {
              dispatch(setSearchValueAct(""));
            }}
          />
        }
        leftComponent={
          <>
            {!defaultKanbanView ? (
              <li>
                <ButtonWithTooltip
                  tooltipTitle={_t("Card View")}
                  tooltipPlacement="top"
                  icon="fa-brands fa-trello"
                  iconClassName="h-5 w-5"
                  className="!w-7 !h-7"
                  onClick={() => setIsKanbanEnabled(true)}
                />
              </li>
            ) : (
              <li>
                <ButtonWithTooltip
                  tooltipTitle={_t("Return to Dashboard")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-square-list"
                  iconClassName="h-5 w-5"
                  className="!w-7 !h-7"
                  onClick={() => setIsKanbanEnabled(false)}
                />
              </li>
            )}
            {/* Add Filter */}
          </>
        }
        rightComponent={
          <div className="flex flex-row sm:items-center items-start sm:gap-2.5 gap-2">
            {defaultKanbanView && (
              <>
                <div className="md:flex hidden items-center sm:gap-5 gap-2">
                  {module_access !== "own_data_access" && (
                    <CustomCheckBox
                      className="gap-1.5 !font-medium text-primary-900 dark:text-white/90 relative sm:before:absolute sm:before:w-0.5 sm:before:h-11 sm:before:-right-1.5 sm:before:top-1/2 sm:before:-translate-y-1/2 sm:before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)] whitespace-nowrap"
                      checked={filter_my_list?.toString() == "1"}
                      onChange={onMyLeadTabButtonChange}
                      disabled={isMyLeadKanban}
                      loadingProps={{
                        isLoading: isMyLeadKanban,
                        className: "bg-[#ffffff]",
                      }}
                    >
                      {_t(`My ${module_name}`)}
                    </CustomCheckBox>
                  )}
                  <CustomCheckBox
                    className="gap-1.5 !font-medium text-primary-900 dark:text-white/90 relative sm:before:absolute sm:before:w-0.5 sm:before:h-11 sm:before:-right-1.5 sm:before:top-1/2 sm:before:-translate-y-1/2 sm:before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)] whitespace-nowrap"
                    checked={kanbanSetting?.default_view?.toString() == "1"}
                    onChange={handleDefaultViewKanabanChange}
                    disabled={isDefaultViewKanban}
                    loadingProps={{
                      isLoading: isDefaultViewKanban,
                      className: "bg-[#ffffff]",
                    }}
                  >
                    {_t("Set as Default View")}
                  </CustomCheckBox>
                </div>
                <>
                  <Popover
                    placement="bottomRight"
                    content={
                      <div className="dark:bg-dark-900 min-w-[155px]">
                        <ul className="py-2 px-1 grid gap-0.5">
                          {CARD_VIEW_CHECK_BOX?.filter((item: any) => {
                            if (item?.title?.includes(`My ${modulePLName}`)) {
                              return (
                                checkModuleAccessByKey(module_key) !==
                                "own_data_access"
                              );
                            }
                            return true;
                          }).map((item: any, i) => {
                            const isChecked = item?.title?.includes(
                              "Set as Default View"
                            )
                              ? kanbanSetting?.default_view?.toString() == "1"
                              : filter_my_list?.toString() == "1";
                            return (
                              <li
                                className={`rounded bg-blue-50 dark:bg-dark-800`}
                              >
                                <div
                                  className="flex items-center justify-between cursor-pointer px-2 py-0.5"
                                  onClick={() => {
                                    if (
                                      item.title.includes("Set as Default View")
                                    ) {
                                      handleDefaultViewKanabanChange(
                                        simulateCheckboxChangeEvent(
                                          !kanbanSetting?.default_view
                                        )
                                      );
                                    } else {
                                      onMyLeadTabButtonChange(
                                        simulateCheckboxChangeEvent(
                                          !filter_my_list
                                        )
                                      );
                                    }
                                  }}
                                >
                                  <Typography
                                    className={`text-primary-900 dark:text-white/90`}
                                  >
                                    {item.title}
                                  </Typography>
                                  {isChecked && (
                                    <FontAwesomeIcon
                                      className={`text-base w-4 h-4 text-primary-900 opacity-100 `}
                                      icon="fa-regular fa-check"
                                    />
                                  )}
                                </div>
                              </li>
                            );
                          })}
                        </ul>
                      </div>
                    }
                    trigger="click"
                    open={open}
                    className="flex md:hidden"
                    onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
                  >
                    <div className="flex relative items-center justify-center">
                      <ButtonWithTooltip
                        icon="fa-regular fa-gear"
                        tooltipTitle=""
                        tooltipPlacement="top"
                        iconClassName="h-5 w-5"
                        className="!w-7 !h-7"
                        onClick={() => {}}
                      />
                    </div>
                  </Popover>
                </>
              </>
            )}

            {module_access === "full_access" ||
            module_access === "own_data_access" ? (
              <li>
                <AddButton
                  onClick={() => {
                    setAddLeads(true);
                  }}
                >
                  {_t(module_singular_name ?? "Lead")}
                </AddButton>
              </li>
            ) : null}
          </div>
        }
      />
      {!defaultKanbanView && !isKanbanLoading ? (
        <div
          className={`pt-[41px] overflow-y-auto overflow-hidden ${
            !page_is_iframe
              ? "md:h-[calc(100vh-143px)] h-[calc(100vh-112px)]"
              : "h-screen"
          }`}
        >
          <ReadOnlyPermissionMsg view={isReadOnly} />
          <div className="p-4">
            <div
              className={`grid xl:grid-cols-4 lg:grid-cols-2 grid-cols-1 gap-2.5 transition-all ease-in-out duration-300 ${
                fullScreenTable || viewSearch
                  ? "max-h-0 overflow-hidden"
                  : "min-[1536px]:max-h-[650px] lg:max-h-[900px] max-h-[2000px]"
              }`}
            >
              {!isCloseLeadAlert && (
                <div className="bg-[#FFFFBB] flex items-center justify-between gap-2 py-1.5 px-[15px] rounded-md xl:col-span-4 lg:col-span-2 col-span-1">
                  <Typography className="block text-black sm:text-13 text-xs max-w-[calc(100%-40px)]">
                    {_t(
                      "Collect more Leads: Add the lead capture form to your website and automatically have your leads sync to your Contractor Foreman account. Configure the form within Leads > Settings."
                    )}
                  </Typography>
                  <CloseButton
                    onClick={() => {
                      localStorage.setItem("LeadDashWarningDismissed", "yes");
                      setIsCloseLeadAlert(true);
                    }}
                    className="!bg-transparent"
                  />
                </div>
              )}
              <div className="common-card h-[266px]">
                <LeadsByMonthSource
                  onClickMonthSource={({ key, name }) => {
                    updateFilter({
                      referral_source: key ? key : "",
                      referral_source_names: name || "",
                    });
                  }}
                  tempFil={tempFil}
                />
              </div>
              <div className="common-card h-[266px]">
                <NewLeadContact tempFil={tempFil} />
              </div>
              <div className="common-card h-[266px]">
                <LeadsByStage
                  onClickLeadsStage={({ key, name }) => {
                    updateFilter({
                      stage: key ? key : "",
                      stage_names: name || "",
                    });
                  }}
                  tempFil={tempFil}
                />
              </div>
              <div className="common-card h-[266px]">
                <UpcomingLeadTasks
                  tempFil={tempFil}
                  setIsUpComingTableChange={setIsUpComingTableChange}
                />
              </div>
            </div>

            <div
              className={`w-full ${
                fullScreenTable || viewSearch
                  ? "sm:mt-2.5 mt-10"
                  : "sm:mt-7 mt-14"
              }`}
            >
              <div className="relative h-7 z-[999] flex items-center justify-end">
                <AccordionButton
                  onClick={() => setFullScreenTable((prev: boolean) => !prev)}
                  fullScreenTable={fullScreenTable}
                />
                <div className="flex justify-between items-center w-full sm:mb-7 mb-20 flex-wrap md:flex-nowrap">
                  <div className="w-fit p-1 bg-[#EEEFF0] rounded dark:bg-dark-800 ml-auto md:ml-0">
                    {module_access !== "own_data_access" && (
                      <ListTabButton
                        // RadioGroupClassName="sm:block"
                        value={filter_my_list == "1" ? "1" : "0"}
                        options={LEAD_BUTTON_TAB}
                        activeclassName="active:bg-[#ffffff] !text-primary-900 !rounded-md"
                        className="sm:min-w-[100px] min-w-fit sm:px-1.5 !text-[#868D8D] px-2 !border-transparent bg-[#EEEFF0]"
                        onChange={onMyLeadTabListButtonChange}
                      />
                    )}
                  </div>
                </div>
              </div>
              <div className="p-2 bg-white dark:bg-dark-600 common-list-table rounded">
                <LeadList
                  onIsDataLoading={setIsLeadLoading}
                  search={searchValue || ""}
                  onContactAction={setAddLeads}
                  pageReload={pageReloadRef.current}
                  isUpcomingTableChange={isUpcomingTableChange}
                  setIsUpComingTableChange={setIsUpComingTableChange}
                />
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="pt-[41px] md:h-[calc(100dvh-143px)] h-[calc(100dvh-112px)] overflow-y-auto overflow-hidden">
          <ReadOnlyPermissionMsg
            view={module_access === "read_only"}
            className="px-4 sm:pt-4 pt-1"
            textClassName="sm:text-13 text-xs flex justify-center items-end sm:h-auto h-7 sm:leading-auto leading-[15px]"
          />
          <div className="flex pt-4 pb-2.5 px-1.5 transition-all ease-in-out duration-300 overflow-x-auto">
            {defaultKanbanView && !isKanbanLoading && (
              <LeadKanban
                kanbanSetting={kanbanSetting}
                setKanbanSetting={setKanbanSetting}
                kanbanSelected={kanbanSelected}
                setKanbanSelected={setKanbanSelected}
                search={searchValue || ""}
              />
            )}
          </div>
        </div>
      )}

      {/* <AddLeads setAddLeads={setAddLeads} addLeads={addLeads} /> */}
      {addLeads && !isReadOnly && (
        <AddDirectory
          addDirectory={addLeads}
          setAddDirectory={(data) => {
            removeQueryParams();
            removeQueryParamsLead();
            setAddLeads(data);
          }}
          contactData={{
            key: module_key,
            label: module_name,
            labelSingular: module_singular_name,
            value: directoryTypeId.leads?.toString(),
          }}
          temperatureScale={Number(temperature_scale || 0)}
          saveButtonText={_t("Create Lead")}
        />
      )}
    </>
  );
};

const ManageLead = () => {
  return (
    <DirStoreProvider>
      <ManageLeadCom />
    </DirStoreProvider>
  );
};

export default ManageLead;

export { ErrorBoundary };
