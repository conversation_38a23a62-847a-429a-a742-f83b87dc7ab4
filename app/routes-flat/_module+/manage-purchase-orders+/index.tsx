import { RadioChangeEvent } from "antd";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "~/hook";
import { getGConfig, getGModuleFilters, updateModuleFilter } from "~/zustand";
// molecules
import { AccordionButton } from "~/shared/components/molecules/accordionButton";
import { AddButton } from "~/shared/components/molecules/addButton";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { DashboardHeader } from "~/shared/components/molecules/dashboardHeader";
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
// FontAwesome File
import { PODashboardRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/purchase-order/dashboard/regular";
// Other
import { CheckboxProps } from "antd/lib";
import debounce from "lodash/debounce";
import isEmpty from "lodash/isEmpty";
import { AddPurchaseOrders } from "~/modules/financials/pages/purchaseOrders/components";
import { PurchaseOrdersList } from "~/modules/financials/pages/purchaseOrders/components/dashboard";
import DeliveredPurchaseOrders from "~/modules/financials/pages/purchaseOrders/components/dashboard/DeliveredPurchaseOrders";
import DeliveriesRunningLate from "~/modules/financials/pages/purchaseOrders/components/dashboard/DeliveriesRunningLate";
import PurchaseOrderKanban from "~/modules/financials/pages/purchaseOrders/components/dashboard/PurchaseOrderKanban";
import PurchaseOrdersFilter from "~/modules/financials/pages/purchaseOrders/components/dashboard/PurchaseOrdersFilter";
import PurchaseOrdersStats from "~/modules/financials/pages/purchaseOrders/components/dashboard/PurchaseOrdersStats";
import RecentlyDeliveredItems from "~/modules/financials/pages/purchaseOrders/components/dashboard/RecentlyDeliveredItems";
import RecentPricingRequest from "~/modules/financials/pages/purchaseOrders/components/dashboard/RecentPricingRequest";
import UpcomingDeliveries from "~/modules/financials/pages/purchaseOrders/components/dashboard/UpcomingDeliveries";
import { fetchDashData } from "~/modules/financials/pages/purchaseOrders/redux/action/dashboardAction";
import POStoreProvider from "~/modules/financials/pages/purchaseOrders/redux/POStoreProvider";
import {
  useAppPODispatch,
  useAppPOSelector,
} from "~/modules/financials/pages/purchaseOrders/redux/store";
import { PO_RIGHT_BUTTON } from "~/modules/financials/pages/purchaseOrders/utils/constants";
import { isKanbanViewApi } from "~/redux/action/iskanbanView";
import { updateKanbanSettingApi } from "~/redux/action/kanbanSettings";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Popover } from "~/shared/components/atoms/popover";
import { Typography } from "~/shared/components/atoms/typography";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { getCustomStatusList } from "~/redux/action/getCustomStatusAction";
import { useSearchParams } from "@remix-run/react";
import { setSearchValueAct } from "~/modules/financials/pages/purchaseOrders/redux/slices/dashboardSlice";

// Fort Awesome Library Add icons
PODashboardRegularIconAdd();

const PurchaseOrders = () => {
  const dispatch = useAppPODispatch();
  const { _t } = useTranslation();
  const [fullScreenTable, setFullScreenTable] = useState<boolean>(false);
  const [addPurchaseOrders, setAddPurchaseOrders] = useState<boolean>(false);
  const [isKanbanEnabled, setIsKanbanEnabled] = useState<boolean>(false);
  const [viewSearch, setViewSearch] = useState<boolean>(false);
  const defaultKanbanView = useMemo(() => isKanbanEnabled, [isKanbanEnabled]);
  const [open, setOpen] = useState<boolean>(false);
  const [kanbanSetting, setKanbanSetting] = useState<IKanbanSetting>();
  const [kanbanSelected, setKanbanSelected] = useState<string[]>([]);
  const [isKanbanLoading, setIsKanbanLoading] = useState<boolean>(false);
  const [loaderSetDefalutViewKanban, setLoaderSetDefalutViewKanban] =
    useState<boolean>(false);
  const { searchValue } = useAppPOSelector((state) => state.dashboard);
  const [searchParams, setSearchParams] = useSearchParams();

  const debouncedOnSearchChange = useCallback(
    debounce(({ target: { value } }) => {
      dispatch(setSearchValueAct(value));
    }, 500),
    []
  );

  const filterSrv =
    (getGModuleFilters() as Partial<PurchaseOrdersFilter> | undefined) || {};
  const { tab, billing_status, status } = filterSrv;
  const {
    module_id,
    module_name,
    page_is_iframe,
    module_access,
    module_singular_name,
    module_key,
  }: GConfig = getGConfig();

  useEffect(() => {
    if (!module_id) return;
    dispatch(
      getCustomStatusList({
        module_id: [module_id],
      })
    );
  }, [module_id]);
  const updateFilter = useCallback(
    (filter: Partial<PurchaseOrdersFilter>) => {
      if (module_id) {
        updateModuleFilter({
          filter,
          moduleId: module_id.toString(),
          onError: (response: IApiCallResponse) => {
            notification.error({
              description: response.message,
            });
          },
        });
      }
    },
    [module_id]
  );
  const isReadOnly = useMemo(
    () => module_access === "read_only",
    [module_access]
  );
  const onTabButtonChange = (e: RadioChangeEvent) => {
    const listValue = e.target.value as string;
    if (listValue === "all") {
      updateFilter({
        tab: listValue,
        billing_status: "",
        // status: "0",
      });
    } else if (listValue === "open" || listValue === "closed") {
      updateFilter({
        tab: listValue,
        billing_status: "",
        // billing_status: PO_RIGHT_BUTTON.find((item) => item.value === listValue)
        //   ?.billing_status,
        // status: "0",
      });
    }
  };

  const previousValues = useRef({});

  const filter = useMemo(() => {
    return {
      tab,
      billing_status,
      status,
    };
  }, [tab, billing_status, status]);

  useEffect(() => {
    const currentValues = {
      filter: JSON.stringify(filter),
    };

    if (!isEmpty(filterSrv)) {
      previousValues.current = { ...currentValues };
    }
  }, [defaultKanbanView, JSON.stringify(filterSrv)]);

  useEffect(() => {
    dispatch(fetchDashData());
  }, []);

  useEffect(() => {
    const fetchKanbanView = async () => {
      setIsKanbanLoading(true);
      try {
        const response = (await isKanbanViewApi({
          key: CFConfig.purchase_order_module,
        })) as IIsKanbanEnableApiRes;
        if (response?.success) {
          setIsKanbanEnabled(response?.data?.is_kanban === 1);
        } else {
          setIsKanbanEnabled(false);
        }
      } catch (error) {
        setIsKanbanEnabled(false);
      } finally {
        setIsKanbanLoading(false);
      }
    };

    fetchKanbanView();
  }, []);

  const handleDefaultViewKanabanChange: CheckboxProps["onChange"] = async (
    e
  ) => {
    setLoaderSetDefalutViewKanban(true);
    try {
      const requestKanbanSetting = {
        module_field_id: kanbanSelected.length
          ? kanbanSelected.filter((data) => data.trim() !== "")
          : undefined,
        default_view: e.target.checked ? 1 : 0,
        module_id: module_id,
      };
      const responseKanbanSetting = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;
      if (responseKanbanSetting.success) {
        setKanbanSetting(responseKanbanSetting?.data);
        setKanbanSelected(responseKanbanSetting?.kanban_project_selected);
      } else {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setLoaderSetDefalutViewKanban(false);
    }
  };

  useEffect(() => {
    if (searchParams?.get("action")?.trim() === "new") {
      setAddPurchaseOrders(true);
    }
  }, [searchParams.get("action")]);

  const handleDefaultViewKanabanChangeSmall = async (checked: boolean) => {
    setLoaderSetDefalutViewKanban(true);
    try {
      const requestKanbanSetting = {
        default_view: checked ? 1 : 0,
        module_id: module_id,
      };
      const responseKanbanSetting = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;
      if (responseKanbanSetting.success) {
        setKanbanSetting(responseKanbanSetting?.data);
      } else {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setLoaderSetDefalutViewKanban(false);
    }
  };
  const CARD_VIEW_CHECK_BOX = [
    {
      title: "Set as Default View",
      isChecked: kanbanSetting?.default_view?.toString() == "1",
      onClick: () => {
        const currentKan = kanbanSetting?.default_view?.toString() === "1";
        handleDefaultViewKanabanChangeSmall(!currentKan);
      },
    },
  ];
  return (
    <>
      <DashboardHeader
        searchPlaceHolder={module_name ? _t(`Search for ${module_name}`) : ""}
        viewSearch={viewSearch}
        searchVal={searchValue}
        setViewSearch={setViewSearch}
        onSearchChange={debouncedOnSearchChange}
        leftComponent={
          <>
            {!isKanbanEnabled ? (
              <li>
                <ButtonWithTooltip
                  tooltipTitle={_t("Card View")}
                  tooltipPlacement="top"
                  icon="fa-brands fa-trello"
                  iconClassName="h-5 w-5"
                  className="!w-7 !h-7"
                  onClick={() => setIsKanbanEnabled(true)}
                />
              </li>
            ) : (
              <li>
                <ButtonWithTooltip
                  tooltipTitle={_t("Return to Dashboard")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-square-list"
                  iconClassName="h-5 w-5"
                  className="!w-7 !h-7"
                  onClick={() => setIsKanbanEnabled(false)}
                />
              </li>
            )}
            <PurchaseOrdersFilter
              kanbanView={isKanbanEnabled}
              onClearSearch={() => {}}
            />
            {/* <li>
              <ButtonWithTooltip
                tooltipTitle={_t(`List of All ${module_name}`)}
                tooltipPlacement="top"
                icon="fa-regular fa-file-invoice"
                iconClassName="h-5 w-5"
                className="!w-7 !h-7"
                onClick={() => {}}
              />
            </li> */}
          </>
        }
        rightComponent={
          <>
            {isKanbanEnabled && (
              <li className="md:flex hidden items-center">
                <CustomCheckBox
                  className="gap-1.5 !font-medium text-primary-900 dark:text-white/90 relative sm:before:absolute sm:before:w-0.5 sm:before:h-11 sm:before:-right-1.5 sm:before:top-1/2 sm:before:-translate-y-1/2 sm:before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)] justify-end"
                  checked={kanbanSetting?.default_view?.toString() == "1"}
                  onChange={handleDefaultViewKanabanChange}
                  disabled={loaderSetDefalutViewKanban}
                  loadingProps={{
                    isLoading: loaderSetDefalutViewKanban,
                    className: "bg-[#ffffff]",
                  }}
                >
                  {_t("Set as Default View")}
                </CustomCheckBox>
              </li>
            )}
            {/* NOTE FOR DEVLOER: this popover for responsive view */}
            {isKanbanEnabled && (
              <Popover
                placement="bottomRight"
                content={
                  <div className="dark:bg-dark-900 min-w-[155px]">
                    <ul className="py-2 px-1 grid gap-0.5">
                      {CARD_VIEW_CHECK_BOX.map((item, i) => {
                        return (
                          <li
                            className={`rounded bg-blue-50 dark:bg-dark-800`}
                            key={i}
                          >
                            <div
                              className="flex items-center justify-between cursor-pointer px-2 py-0.5 gap-1"
                              onClick={item.onClick}
                            >
                              <Typography
                                className={`text-primary-900 dark:text-white/90`}
                              >
                                {item.title}
                              </Typography>
                              {item?.isChecked && (
                                <FontAwesomeIcon
                                  className={`text-base w-4 h-4 text-primary-900 opacity-100 `}
                                  icon="fa-regular fa-check"
                                />
                              )}
                            </div>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                }
                trigger="click"
                open={open}
                className="flex md:hidden"
                onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
              >
                <div className="flex relative items-center justify-center">
                  <ButtonWithTooltip
                    icon="fa-regular fa-gear"
                    tooltipTitle=""
                    tooltipPlacement="top"
                    iconClassName="h-5 w-5"
                    className="!w-7 !h-7"
                    onClick={() => {}}
                  />
                </div>
              </Popover>
            )}
            {module_access === "full_access" ||
            module_access === "own_data_access" ? (
              <li>
                <AddButton onClick={() => setAddPurchaseOrders(true)}>
                  {_t(module_singular_name ?? "Purchase Order")}
                </AddButton>
              </li>
            ) : null}
          </>
        }
      />
      {!defaultKanbanView && !isKanbanLoading && (
        <div
          className={`pt-[41px] overflow-y-auto overflow-hidden ${
            !page_is_iframe
              ? "md:h-[calc(100vh-143px)] h-[calc(100vh-112px)]"
              : "h-screen"
          }`}
        >
          <ReadOnlyPermissionMsg view={module_access === "read_only"} />
          <div className="p-4">
            <div
              className={`grid xl:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-2.5 transition-all ease-in-out duration-300 ${
                fullScreenTable
                  ? "max-h-0 overflow-hidden"
                  : "min-[1536px]:max-h-[470px] md:max-h-[900px] max-h-[1670px]"
              }`}
            >
              <div className="common-card min-h-[193px]">
                <DeliveredPurchaseOrders />
              </div>
              <div className="common-card min-h-[193px]">
                <PurchaseOrdersStats />
              </div>
              <div className="common-card min-h-[193px]">
                <RecentPricingRequest />
              </div>
              <div className="common-card min-h-[266px]">
                <RecentlyDeliveredItems />
              </div>
              <div className="common-card min-h-[266px]">
                <UpcomingDeliveries />
              </div>
              <div className="common-card min-h-[266px]">
                <DeliveriesRunningLate />
              </div>
            </div>
            <div
              className={`w-full ${
                fullScreenTable || viewSearch
                  ? "md:mt-2.5 mt-[37px]"
                  : "md:mt-5 mt-[55px]"
              }`}
            >
              <div className="relative h-7 z-[999] flex items-center justify-end">
                <AccordionButton
                  onClick={() => setFullScreenTable((prev: boolean) => !prev)}
                  fullScreenTable={fullScreenTable}
                />
                <div className="w-full flex text-end justify-end md:mb-5 mb-[85px]">
                  <ListTabButton
                    value={tab ? tab : PO_RIGHT_BUTTON[0].label}
                    options={PO_RIGHT_BUTTON}
                    className="sm:min-w-[100px] min-w-[70px]"
                    onChange={onTabButtonChange}
                    activeclassName="!bg-[#F1F4F9]"
                  />
                </div>
              </div>
              <div className="p-2 bg-white dark:bg-dark-600 common-list-table rounded">
                <PurchaseOrdersList
                  filterParams={{}}
                  setAddPOOpen={setAddPurchaseOrders}
                  search={searchValue}
                />
              </div>
            </div>
          </div>
        </div>
      )}
      {defaultKanbanView && !isKanbanLoading && (
        <PurchaseOrderKanban
          kanbanSetting={kanbanSetting}
          setKanbanSetting={setKanbanSetting}
          kanbanSelected={kanbanSelected}
          setKanbanSelected={setKanbanSelected}
          search={searchValue || ""}
          isReadOnly={isReadOnly}
        />
      )}
      {addPurchaseOrders && !isReadOnly && (
        <AddPurchaseOrders
          setAddPurchaseOrders={setAddPurchaseOrders}
          addPurchaseOrders={addPurchaseOrders}
          onClose={() => {
            searchParams.delete("action");
            setSearchParams(searchParams);
          }}
        />
      )}
    </>
  );
};

const ManagePurchaseOrders = () => {
  return (
    <POStoreProvider>
      <PurchaseOrders />
    </POStoreProvider>
  );
};

export default ManagePurchaseOrders;

export { ErrorBoundary };
