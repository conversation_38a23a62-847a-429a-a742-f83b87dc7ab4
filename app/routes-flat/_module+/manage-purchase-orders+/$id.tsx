import { Outlet, useNavigate, useParams } from "@remix-run/react";
import {
  getCommonSidebarCollapse,
  getGConfig,
  getGModuleByKey,
  setCommonSidebarCollapse,
  useGModules,
} from "~/zustand";
import ManagePurchaseOrdersTab from "./$id+/$tab";
// molecules
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
// ModuleSidebar
import { ModuleSidebar } from "~/shared/components/moduleSidebar";
// Other
import { SELECT_TAB_OPTIONS } from "~/modules/financials/pages/purchaseOrders/utils/constants";

// FontAwesome File
import { PurchaseOrderDetailSolidIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/purchase-order/detail/solid";
import { PurchaseOrderDetailRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/purchase-order/detail/regular";
import { PurchaseOrderDetailLightIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/purchase-order/detail/light";
import { useEffect, useMemo, useState } from "react";
import { defaultConfig } from "~/data";
import { routes } from "~/route-services/routes";
import {
  useAppPODispatch,
  useAppPOSelector,
} from "~/modules/financials/pages/purchaseOrders/redux/store";
import {
  getPOBillHistoryDetail,
  getPODetail,
} from "~/modules/financials/pages/purchaseOrders/redux/action/PODetailAction";
import POStoreProvider from "~/modules/financials/pages/purchaseOrders/redux/POStoreProvider";
import { getInvoiceTerms } from "~/redux/action/invoiceTermsAction";
import { getCommonNotes } from "~/redux/action/commonNotesAction";
import {
  getPOItems,
  getPOPricingItems,
} from "~/modules/financials/pages/purchaseOrders/redux/action/POItemAction";
import { getCustomStatusList } from "~/redux/action/getCustomStatusAction";
import { getCommonAttachments } from "~/redux/action/commonAttachmentSection";
import { fetchMarkupWithItemTypes } from "~/redux/action/commonMarkupWithItemTypesAction";
import { useGlobalModule } from "~/zustand/global/modules/slice";

// Fort Awesome Library Add icons
PurchaseOrderDetailSolidIconAdd();
PurchaseOrderDetailRegularIconAdd();
PurchaseOrderDetailLightIconAdd();

const ManagePurchaseOrdersPage = () => {
  const { tab, id: purchase_order_id }: RouteParams = useParams(); // This type already declare.
  const sidebarCollapse: boolean | undefined = getCommonSidebarCollapse();
  const navigate = useNavigate();
  const { purchaseOrderDetail } = useAppPOSelector(
    (state) => state?.purchaseOrderDetail
  );
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useAppPODispatch();
  const { module_id, page_is_iframe, module_key }: GConfig = getGConfig();
  const { checkModuleAccessByKey } = useGModules();
  const isReadOnly = useMemo(
    () => Boolean(Boolean(checkModuleAccessByKey(module_key) === "read_only")),
    [module_key]
  );
  const { poPricingDetail = [], isPOPricingLoading } = useAppPOSelector(
    (state) => state.purchaseOrderPricingDetail
  );
  const module: GModule | undefined = getGModuleByKey(
    defaultConfig.project_module
  );
  const { getGlobalModuleByKey } = useGlobalModule();
  const poModule = useMemo(
    () => getGlobalModuleByKey(CFConfig.purchase_order_module),
    [getGlobalModuleByKey] // Ensures memoization based on function reference
  );
  const { module_id: po_module_id = 0 } = poModule || {};

  useEffect(() => {
    if (!po_module_id) return;
    dispatch(
      getCustomStatusList({
        module_id: [po_module_id],
      })
    );
  }, [po_module_id]);

  const SELECT_TAB_OPTION = useMemo(
    () =>
      SELECT_TAB_OPTIONS.filter(
        (item) =>
          !(
            item?.value === "supplier_pricing_details" &&
            (poPricingDetail?.length == 0 ||
              !poPricingDetail?.some(
                ({ pricing_ved_items }) => pricing_ved_items?.length
              ))
          )
      ),
    [poPricingDetail, isPOPricingLoading]
  );

  const fetchEstimateDetail = async () => {
    setIsLoading(true);
    const response = (await dispatch(getPODetail({ purchase_order_id }))) as {
      payload: { statusCode: number; message: string };
    };
    if ([404, 400].includes(response?.payload?.statusCode)) {
      notification.error({
        description: response?.payload?.message || "Purchase order not found",
      });
      navigate(`${routes.MANAGE_PURCHASE_ORDERS.url}`);
      return;
    }
    dispatch(getInvoiceTerms({ is_deleted: 0 }));
    dispatch(
      getCommonNotes({
        module_key: module_key,
        record_id: Number(purchase_order_id),
      })
    );
    dispatch(
      getCommonAttachments({
        record_id: Number(purchase_order_id),
        module_key: module_key,
      })
    );
    dispatch(getPOBillHistoryDetail({ purchase_order_id }));
    dispatch(getPOItems({ purchase_order_id, need_section: 1 }));
    dispatch(getPOPricingItems({ purchase_order_id }));
    setIsLoading(false);
  };
  useEffect(() => {
    if (purchase_order_id && module_id && !!module?.module_id) {
      fetchEstimateDetail();
    }
  }, [purchase_order_id, module_id, module?.module_id]);
  useEffect(() => {
    if (purchaseOrderDetail?.purchase_order_id) {
      dispatch(
        fetchMarkupWithItemTypes({
          project_id: Number(purchaseOrderDetail?.pro_id),
        })
      );
    }
  }, [purchaseOrderDetail?.purchase_order_id, purchaseOrderDetail?.pro_id]);

  return (
    <div
      className={`flex overflow-hidden ${
        !page_is_iframe
          ? "md:h-[calc(100vh-143px)] h-[calc(100vh-112px)]"
          : "h-screen"
      }`}
    >
      {!sidebarCollapse && (
        <div
          className={`xl:hidden block absolute bg-black/20 dark:bg-white/10 z-[100] ease-in-out duration-300 ${
            sidebarCollapse
              ? "w-0 h-0"
              : window.ENV.PAGE_IS_IFRAME
              ? "w-full h-full"
              : "w-full h-[calc(100dvh-112px)]"
          }`}
          onClick={() => setCommonSidebarCollapse(true)}
        ></div>
      )}
      <ModuleSidebar
        sidebarCollapse={sidebarCollapse}
        onSidebarCollapse={setCommonSidebarCollapse}
        selectOptions={SELECT_TAB_OPTION}
        onSelectedOption={(value: string) => {
          navigate(value);
        }}
        selectedOption={tab ?? "details"}
      />

      {tab ? <Outlet /> : <ManagePurchaseOrdersTab />}
    </div>
  );
};

const ManagePurchaseOrders = () => {
  return (
    <POStoreProvider>
      <ManagePurchaseOrdersPage />
    </POStoreProvider>
  );
};

export default ManagePurchaseOrders;
export { ErrorBoundary };
