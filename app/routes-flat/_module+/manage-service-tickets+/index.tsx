import {
  defer,
  useLoaderD<PERSON>,
  useNavigate,
  useRevalidator,
  useSearchParams,
} from "@remix-run/react";
import { type RadioChangeEvent } from "antd";
import { CheckboxProps } from "antd/lib";
import debounce from "lodash/debounce";
import isEmpty from "lodash/isEmpty";
import uniq from "lodash/uniq";
import React, {
  MouseEventHandler,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import Sortable from "sortablejs";
import {
  escapeHtmlEntities,
  getApiDefaultParams,
  sanitizeString,
} from "~/helpers/helper";
import { useTranslation } from "~/hook";
import ServiceTicketFilter from "~/modules/projectManagement/pages/servicetickets/components/dashboard/ServiceTicketFilter";
import ServiceTicketsList from "~/modules/projectManagement/pages/servicetickets/components/dashboard/ServiceTicketsList";
import TicketsStatus from "~/modules/projectManagement/pages/servicetickets/components/dashboard/TicketsStatus";
import TicketStats from "~/modules/projectManagement/pages/servicetickets/components/dashboard/TicketStats";
import TodayServices from "~/modules/projectManagement/pages/servicetickets/components/dashboard/TodayServices";
import { AddServicesTickets } from "~/modules/projectManagement/pages/servicetickets/components/sidebar";
import {
  copyServiceTicketOnCompleteAPI,
  downloadPdfServiceTicketApi,
  fetchDashData,
  getSTKanbanListApi,
  sendEnRouteStatusMailApi,
  setCreateModifyInvoiceApi,
  setServiceNotificationApi,
  setServiceReminderApi,
  updateCalendarEventsApi,
} from "~/modules/projectManagement/pages/servicetickets/redux/action/dashboardAction";
import {
  disableAutoScheduleAppoinment,
  fetchServiceTicketDetails,
  sendCompleteStatusMail,
  updateSTDetailApi,
} from "~/modules/projectManagement/pages/servicetickets/redux/action/serviceTicketDetailsAction";
import {
  useAppSTDispatch,
  useAppSTSelector,
} from "~/modules/projectManagement/pages/servicetickets/redux/store";
import STStoreProvider from "~/modules/projectManagement/pages/servicetickets/redux/STStoreProvider";
import {
  JOB_STATUS_BUTTON_TAB,
  SERVICE_TICKET_OPTIONS,
} from "~/modules/projectManagement/pages/servicetickets/utils/constants";
import {
  updateKanbanSettingApi,
  updateKanbanSortingApi,
} from "~/redux/action/kanbanSettings";

// Atoms
import { Button } from "~/shared/components/atoms/button";
import { Popover } from "~/shared/components/atoms/popover";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// molecules
import { AddButton } from "~/shared/components/molecules/addButton";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { PDFFilePreview } from "~/shared/components/molecules/pdfFilePreview";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { AccordionButton } from "~/shared/components/molecules/accordionButton";
import { DashboardHeader } from "~/shared/components/molecules/dashboardHeader";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
import { ShareInternalLinkModal } from "~/shared/components/molecules/shareInternalLinkModal";

// Organisms
import { KanbanList } from "~/shared/components/organisms/kanbanView";

// Other
import {
  getGConfig,
  getGModuleByKey,
  getGSettings,
  setGSettingsUpdateValue,
  useGModules,
} from "~/zustand";

import { getApiData } from "~/helpers/axios-api-helper";
import DirSendEmail from "~/modules/people/directory/components/DirSendEmail";
import Scheduler from "~/modules/projectManagement/pages/servicetickets/components/dashboard/Schedulers";
import { sendCommonEmailApi } from "~/redux/action/sendEmailAction";
import { apiRoutes, routes } from "~/route-services/routes";
import { removeFirstSlash } from "~/shared/utils/helper/common";
import {
  backendDateFormat,
  backendTimeFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";

import { setActiveField } from "~/redux/slices/sendEmailSlice";
import { resetAllSlices } from "~/modules/projectManagement/pages/servicetickets/redux/action/resetActions";
import { setSearchValueAct } from "~/modules/projectManagement/pages/servicetickets/redux/slices/dashboardSlice";

import { sendMessageKeys } from "~/components/page/$url/data";
import { ScheduleService } from "~/modules/projectManagement/pages/servicetickets/components/tab/modal/scheduleService";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";
import dayjs from "dayjs";
import {
  getFilterIndex,
  getSTFilter,
} from "~/modules/projectManagement/pages/servicetickets/zustand/filter/slice";
import {
  setSTFilter,
  updateGlobalProject,
  updateSTFilter,
} from "~/modules/projectManagement/pages/servicetickets/zustand/filter/actions";

// Controllers
import { isKanbanEnabledApi } from "~/shared/controllers/commonController.server";

import { authenticator } from "~/services/auth.server";
import delay from "lodash/delay";
import { initialSTData } from "~/modules/projectManagement/pages/servicetickets/zustand/constants";
import {
  getGlobalProject,
  isExpiredAuthorization,
  isGlobalProjectSet,
} from "~/zustand/global/config/slice";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { getDirectaryIdByName } from "~/components/sidebars/multi-select/customer/zustand/action";
import { getModuleFilter } from "~/shared/controllers/filterController.server";
import { getModuleByKeyAPI } from "~/shared/controllers/modulesController.server";
// FontAwesome File
import { STDashboardRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/service-ticket/dashboard/regular";
import { STDashboardSolidIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/service-ticket/dashboard/solid";
import { defaultConfig } from "~/data";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import CustomContextMenu from "~/shared/components/molecules/CustomContextMenu";
import { faArrowUpRightFromSquare } from "@fortawesome/pro-regular-svg-icons";

// Fort Awesome Library Add icons
STDashboardRegularIconAdd();
STDashboardSolidIconAdd();

export const loader = async ({ request }: TLoaderFunctionArgs) => {
  const authSession: string = (await authenticator.isAuthenticated(
    request
  )) as unknown as string;

  const menuModule = getModuleByKeyAPI({
    Authorization: authSession,
    request,
    data: {
      module_key: CFConfig.service_ticket_module,
    },
  });

  const isKanbanEnabledApiCall = async () => {
    const form = await getWebWorkerApiParams({
      otherParams: {
        key: CFConfig.service_ticket_module,
      },
    });

    return isKanbanEnabledApi(form, authSession, request);
  };

  const getModuleFilterAPICall = async () => {
    const response = await menuModule;
    const module =
      response.data && response.data.length ? response.data.shift() : undefined;
    if (module) {
      return getModuleFilter(
        module.module_id,
        authSession,
        request
      ) as unknown as Promise<ISTGetFilterApiResponse>;
    }
    return (await {
      success: false,
      message: "Not found module",
      statusCode: 404,
    }) as unknown as Promise<ISTGetFilterApiResponse>;
  };

  return defer({
    isKanbanEnabledApi: isKanbanEnabledApiCall(),
    getModuleFilterAPI: getModuleFilterAPICall(),
  });
};

const ServiceTicket = () => {
  const { isKanbanEnabledApi, getModuleFilterAPI } =
    useLoaderData<typeof loader>();
  const revalidator = useRevalidator();

  const globalProject = getGlobalProject();
  const isGlobalProjectGutted = isGlobalProjectSet();

  const navigate = useNavigate();
  const dispatch = useAppSTDispatch();

  const { searchValue }: ISTDashState = useAppSTSelector(
    (state) => state.dashboard
  );

  const { details }: IServiceInitialState = useAppSTSelector(
    (state) => state.serviceDetails
  );

  const { module_name: paymentModuleName = "Payment" } =
    (getGModuleByKey(CFConfig.payment_module) as GModule) || {};
  const { module_name: invoiceModuleName = "Invoice" } =
    (getGModuleByKey(CFConfig.invoice_merge_module_key) as GModule) || {};
  const { getGlobalModuleByKey } = useGlobalModule();
  const woModuleName: IModule | undefined = getGlobalModuleByKey(
    CFConfig.work_order_module || "Work Orders"
  );

  const { date_format }: GSettings = getGSettings();
  const { checkModuleAccessByKey } = useGModules();
  const [fullScreenTable, setFullScreenTable] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [loadingChild, setLoadingChild] = useState<boolean>(false);
  const { _t } = useTranslation();
  const [drowerOpen, setDrowerOpen] = useState<boolean>(false);
  const [viewSearch, setViewSearch] = useState<boolean>(false);
  const [calendar, setCalendar] = useState<boolean>(false);
  const [mapsView, setMapsView] = useState<boolean>(false);
  const [isKanbanEnabled, setIsKanbanEnabled] = useState<boolean>(false);
  const [isDefaultViewKanban, setIsDefaultViewKanban] =
    useState<boolean>(false);
  const [isMySTKanban, setIsMySTKanban] = useState<boolean>(false);
  const [selectedData, setSelectedData] =
    useState<ISTKanbanRowDataList | null>();
  const [pdfTempId, setPdfTempId] = useState<string>("");
  const [scheduledServiceDialogOpen, setScheduledServiceDialogOpen] =
    useState<boolean>(false);
  const [statusModalLoading, setStatusModalLoading] = useState<boolean>(false);
  const [addSTLoading, setAddSTLoading] = useState<boolean>(false);
  const [selectedCustomer, setSelectedCustomer] = useState<any[]>([]);
  const [open, setOpen] = useState<boolean>(false);
  const initJobStatus = {
    from: "",
    to: "",
  };
  const [jobStatus, setJobStatus] = useState(initJobStatus);
  const limit = 15;
  const [hasMoreBySection, setHasMoreBySection] = useState<
    Record<string, boolean>
  >({});
  const [pageBySection, setPageBySection] = useState<{ [key: string]: number }>(
    {}
  );
  const [loadingBySection, setLoadingBySection] = useState<{
    [key: string]: boolean;
  }>({});
  const [STTabvalue, setSTTabValue] = useState<number>();
  const [tab, setTab] = useState<string>("");
  const [kanbanSelected, setKanbanSelected] = useState<string[]>([]);
  const [kanbanSetting, setKanbanSetting] = useState<IKanbanSetting>();
  const [kanbanListData, setKanbanListData] = useState<IKanbanColumnDataList[]>(
    []
  );
  // select employee for map
  const [customerOptions, setCustomerOptions] = useState<any>([]);
  const [isOpenSelectCustomer, setIsOpenSelectCustomer] =
    useState<boolean>(false);
  const [calendarEmployees, setCalendarEmployees] = useState([]);
  const [updatedEmployee, setUpdatedEmployee] = useState(false);

  // 3dots action
  const [billsDetailPdfViewOpen, setBillsDetailPdfViewOpen] =
    useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [sendNotificationLoader, setSendNotificationLoader] = useState(false);
  const [notifyTechnicians, setNotifyTechnicians] = useState<{
    id: string | number;
    flag: boolean;
  }>({ id: "", flag: false });
  const [iframeData, setIframeData] = useState<{
    url: string;
    title: string;
  }>({ url: "", title: "" });
  const [shareLink, setShareLink] = useState<string>("");
  const [isShareOpen, setIsShareOpen] = useState<boolean>(false);
  const [delArchConfirmOpen, setDelArchConfirmOpen] = useState<string>("");
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const options: CustomerEmailTab[] = [
    CFConfig.employee_key,
    "my_crew",
    CFConfig.customer_key,
    CFConfig.contractor_key,
    CFConfig.vendor_key,
    CFConfig.misc_contact_key,
    "by_service",
    "my_project",
  ];
  const {
    module_id,
    module_name,
    page_is_iframe,
    module_access,
    module_singular_name,
    module_key,
    authorization,
  }: GConfig = getGConfig();

  // const moduleSLName = HTMLEntities.decode(sanitizeString(module_singular_name)) || "Service Ticket";
  const modulePLName =
    _t(HTMLEntities.decode(sanitizeString(module_name))) ||
    _t("Service Ticket's");

  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { user_id, allow_delete_module_items } = user || {};
  const filterIndex = getFilterIndex();

  const filterSrv = getSTFilter();
  const filterFields = [
    "start_date",
    "end_date",
    "project",
    "employee",
    "priority",
    "job_status",
    "job_status_kanban",
  ];
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    visible: boolean;
  }>({ x: 0, y: 0, visible: false });

  const isFilterApply =
    filterFields.some((field) => !isEmpty(filterSrv?.[field])) ||
    filterSrv?.status !== initialSTData.filter.status;
  const stageFilterKeys = (
    filterSrv?.job_status_kanban?.split(",") || []
  ).filter((key) => key.trim() !== "");

  const SERVICE_TICKETLEFT_TAB = [
    {
      label: (
        <div className="flex gap-1.5 items-center">
          <FontAwesomeIcon icon="fa-duotone fa-solid fa-people-group" />
          {`All ${modulePLName}`}
        </div>
      ),
      value: 0,
    },
    {
      label: (
        <div className="flex gap-1.5 items-center">
          <FontAwesomeIcon icon="fa-duotone fa-solid fa-user" />
          {`My ${modulePLName}`}
        </div>
      ),
      value: 1,
    },
  ];

  const [searchParams, setSearchParams] = useSearchParams();

  const [queryParamsProject, setQueryParamsProject] = useState<IProject>();
  let projectCallApiCalled = false;
  useEffect(() => {
    if (!projectCallApiCalled) {
      projectCallApiCalled = true;
      const projectID = searchParams.get("project");
      if (!projectID) {
        setQueryParamsProject(undefined);
        return;
      } else if (!window.location.search.includes("action=new")) {
        setQueryParamsProject(undefined);
        searchParams.delete("project");
        setSearchParams(searchParams);
        return;
      }
      const fetchData = async () => {
        const fetchProject = async (
          is_completed: boolean | undefined = false
        ) => {
          try {
            const apiParams = await getWebWorkerApiParams<SetProjectsParams>({
              otherParams: {
                start: 0,
                limit: 1,
                is_completed,
                record_type: "project",
                projects: projectID,
                need_all_projects: 0,
                global_call: true,
                filter: { status: "0" },
              },
            });
            const response = (await webWorkerApi({
              url: apiRoutes.GET_PROJECTS.url,
              method: "post",
              data: apiParams,
            })) as IGetProjectsApiResponse;
            if (
              response &&
              response.data &&
              response.data.projects &&
              response.data.projects.length
            ) {
              return response.data.projects.find((a) => a);
            }
            if (!is_completed) {
              return await fetchProject(true);
            }
          } catch (error) {
            console.error(
              `\n File: #manage-service-tickets+/index.tsx -> Line: #395 ->  `,
              (error as Error).message
            );
          }
        };
        const project = await fetchProject();
        if (project) {
          setQueryParamsProject({
            ...project,
            view_in_calendar: Number(project.view_in_calendar),
            view_in_schedule: Number(project.view_in_schedule),
            allow_overbilling: Number(project.allow_overbilling),
            is_assigned_project: Number(project.is_assigned_project),
            show_client_access: Number(project.show_client_access),
            start_date: project.start_date?.toString(),
            end_date: project.end_date || undefined,
            customer_name: project.customer_name || undefined,
            customer_name_only: project.customer_name_only || undefined,
            cust_image: project.cust_image || undefined,
          });
        } else {
          setQueryParamsProject(undefined);
        }
      };
      fetchData();
    }
  }, [searchParams.get("project")]);

  const updateFilter = useCallback(
    (filter: Partial<IInitialSTData["filter"]>) => {
      if (module_id) {
        updateSTFilter(filter, module_id.toString());
      }
    },
    [module_id]
  );
  const handleInvoiceMiddleClick = async (data: any) => {
    try {
      const resData: Partial<ISTCreateModifyApiRes> =
        await setCreateModifyInvoiceApi(Number(data?.service_ticket_id));
      if (resData?.statusCode === 200 && resData.data) {
        const newURL = new URL(
          routes.MANAGE_INVOICE.url +
            "/" +
            (resData?.data?.id?.toString() || ""),
          window.location.origin
        );
        window.open(newURL.toString(), "_blank");
      }
    } catch (err) {
      console.error(
        `\n File: ServiceTicketTableDropdownItems.tsx -> Line: Error -> `,
        err
      );
    }
  };
  useEffect(() => {
    const apiCalls = async () => {
      try {
        const isKanbanEnabledApiResponse = await isKanbanEnabledApi;

        let isKanbanEnabled = false;
        if (isKanbanEnabledApiResponse.statusCode === 401) {
          delay(() => {
            revalidator.revalidate();
          }, 500);
        } else {
          if (isKanbanEnabledApiResponse.success) {
            isKanbanEnabled = isKanbanEnabledApiResponse.data?.is_kanban === 1;
          }
          setIsKanbanEnabled(isKanbanEnabled);
          const getModuleFilterAPIResponse = await getModuleFilterAPI;
          if (getModuleFilterAPIResponse?.success) {
            const filter = {
              ...initialSTData.filter,
              ...(getModuleFilterAPIResponse.data.filter_data ?? {}),
              ...(getModuleFilterAPIResponse.data.global_project.project_id?.toString() !==
              "0"
                ? {
                    project:
                      getModuleFilterAPIResponse.data.global_project.project_id.toString(),
                    project_names:
                      getModuleFilterAPIResponse.data.global_project
                        .project_name,
                  }
                : {}),
            };
            setSTFilter(filter);

            if (isKanbanEnabled) {
              fetchKanbanSTList({
                type: "",
                filter,
                search: "",
                isLoad: false,
              });
            }
          }
        }
      } catch (error) {
        setSTFilter(initialSTData.filter);
        setIsKanbanEnabled(false); // Set false in case of an error
      }
    };

    apiCalls();
  }, []);

  useEffect(() => {
    if (isGlobalProjectGutted && Number(globalProject?.project_id)) {
      updateGlobalProject(globalProject);
    } else {
      updateGlobalProject(filterSrv);
    }
  }, [JSON.stringify(globalProject), isGlobalProjectGutted]);
  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );
  useEffect(() => {
    if (searchParams?.get("action")?.trim() === "new") {
      setDrowerOpen(true);
      if (isReadOnly) {
        setSearchParams({});
      }
    }
  }, [searchParams.get("action"), module_access]);

  useEffect(() => {
    dispatch(resetAllSlices());
    dispatch(fetchDashData());
    return () => {
      const newUrl = window?.location?.pathname?.includes(
        "manage-service-tickets"
      );
      if (!newUrl) {
        dispatch(setSearchValueAct(""));
      }
    };
  }, [mapsView, calendar]);

  const onTabButtonChange = (e: RadioChangeEvent, type: number | string) => {
    let listValue;
    if (type === "ticket") {
      listValue = e.target.value as number;
      setSTTabValue(Number(listValue));
      updateFilter({
        is_own_data: listValue,
        is_my_ST: listValue,
      });
    }
    if (type === "flag") {
      listValue = e.target.value as string;
      setTab(listValue);
      updateFilter({
        tab: listValue,
      });
    }
  };

  const debouncedOnSearchChange = useCallback(
    debounce(({ target: { value } }) => {
      dispatch(setSearchValueAct(value));
      fetchKanbanSTList({
        type: "",
        isLoad: false,
        filter: filterSrv,
        search: value || "",
      });
    }, 500),
    [filterSrv]
  );

  // fetch kanban to do list
  const fetchKanbanSTList = async ({
    type,
    isLoad = true,
    filter,
    search,
  }: {
    type: string;
    isLoad?: boolean;
    filter: IInitialSTData["filter"];
    search: string;
  }) => {
    type === "" ? setIsLoading(true) : setLoadingChild(isLoad);

    const {
      is_my_ST,
      start_date,
      end_date,
      project,
      project_names,
      employee,
      employee_names,
      priority,
      priority_names,
      job_status,
      job_status_kanban,
      job_status_names,
      job_status_kanban_names,
      status,
    } = filter;
    const tempFil: ISTTempFil = {
      is_my_ST: is_my_ST || 0,
      status: status || "",
    };

    if (start_date && end_date) {
      tempFil.start_date = start_date;
      tempFil.end_date = end_date;
    }

    if (project) {
      tempFil.project = project;
      tempFil.project_names = project_names;
    }

    if (employee) {
      tempFil.employee = employee
        .toString()
        .split(",")
        .filter((user_id: string) => user_id !== "0" && user_id !== "")
        .map((user_id: string) => Number(user_id));
      tempFil.employee_names = employee_names;
    }

    if (priority) {
      tempFil.priority = priority;
    }
    if (priority_names) {
      tempFil.priority_names = priority_names;
    }

    if (job_status) {
      tempFil.job_status = job_status;
    }
    if (job_status_names) {
      tempFil.job_status_names = job_status_names;
    }
    if (job_status_kanban) {
      tempFil.job_status_kanban = job_status_kanban;
    }
    if (job_status_kanban_names) {
      tempFil.job_status_kanban_names = job_status_kanban_names;
    }
    const pageForType = pageBySection[type] || 0;
    let dataParams: ISTKanbanListParams = {
      limit: limit,
      start: pageForType,
      is_kanban: 1,
      ignore_filter: 1,
      any_pagination_status: !!type ? type : undefined,
      is_own_data: filter?.is_my_ST?.toString() === "0" ? "0" : "1",
      search: escapeHtmlEntities(search || ""),
      user_id: user_id,
      filter: tempFil,
    };
    if (search === "") {
      delete dataParams.search;
    }
    if (isEmpty(dataParams.filter)) {
      delete dataParams.filter;
    }

    try {
      const resData = (await getSTKanbanListApi(dataParams)) as ISTKanbanApiRes;
      if (resData?.success) {
        setIsLoading(false);
        const newTypes = resData?.data?.filter(
          (item) => Number(item?.is_deleted) != 1
        );

        const newHasMoreBySection = newTypes.reduce(
          (acc: Record<number, boolean>, section: IKanbanColumnDataList) => {
            const sectionDataLength = section.kanban_data.length;
            acc[section.item_id] = sectionDataLength >= limit;
            return acc;
          },
          {}
        );

        setHasMoreBySection((prev) => ({
          ...prev,
          ...newHasMoreBySection,
        }));

        if (pageForType == 0) {
          const data = newTypes.filter((item) => item !== null);
          setKanbanListData(data);
        } else {
          setKanbanListData((prevData) => {
            if (prevData.length === 0) {
              return newTypes;
            }
            const updateData = prevData.map((prevSection) => {
              const newSection = newTypes.find((d) => {
                if (d !== null) {
                  return d.item_id === prevSection.item_id;
                }
              });
              if (newSection) {
                const updatedSection = { ...prevSection };

                const newKanbanData = newSection.kanban_data.filter(
                  (newItem) =>
                    !updatedSection.kanban_data.some((existingItem) => {
                      return (
                        existingItem.service_ticket_id ===
                        newItem.service_ticket_id
                      );
                    })
                );
                updatedSection.kanban_data.push(...newKanbanData);
                return updatedSection;
              }
              return prevSection;
            });
            return updateData;
          });
        }
        setKanbanSelected(
          stageFilterKeys.length > 0
            ? stageFilterKeys
            : resData?.kanban_estimate_type_selected || []
        );
        setKanbanSetting(resData?.kanban_setting);
      } else {
        setIsLoading(false);
        if (type === "") {
          setKanbanListData([]);
          setKanbanSetting(undefined);
        } else {
          // this code work is to stop api call on scroll
          setHasMoreBySection((prev) => {
            return Object.keys(prev).reduce(
              (acc: Record<string, boolean>, key) => {
                acc[key] = false;
                return acc;
              },
              {}
            );
          });
        }
      }
      if (type !== "") {
        setLoadingBySection((prev) => ({
          ...prev,
          [type]: false,
        }));
      }
    } catch (err) {
      setIsLoading(false);
    } finally {
      setLoadingChild(false);
    }
  };

  const handleColspan = async (
    columnId: string,
    isCollapseCard: string,
    key: string
  ) => {
    // Optimistically update the kanbanSelected state
    const updatedKanbanSelected = uniq(
      stageFilterKeys?.length > 0
        ? !kanbanSelected.includes(columnId) && !kanbanSelected.includes(key)
          ? [...kanbanSelected, columnId]
          : kanbanSelected.filter(
              (value) => value !== columnId && value !== key
            )
        : kanbanSelected.includes(columnId)
        ? kanbanSelected.filter((value) => value !== columnId)
        : [...kanbanSelected, columnId]
    ).filter((data) => data.trim() !== ""); // Ensure no empty strings

    setKanbanSelected(updatedKanbanSelected);
    if (stageFilterKeys.length > 0) {
      return;
    }
    const requestKanbanSetting = {
      module_field_id: updatedKanbanSelected.length
        ? updatedKanbanSelected
        : undefined,
      default_view: kanbanSetting?.default_view?.toString() ?? "0",
      module_id,
    };

    try {
      const response = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;

      if (response.success) {
        setKanbanSetting(response.data);
        setKanbanSelected(response.kanban_project_selected || []);
      } else {
        // Revert the optimistic update on failure
        notification.error({
          description: response.message || "Something went wrong!",
        });
        setKanbanSelected(kanbanSelected); // Restore previous state
      }
    } catch (error) {
      // Revert the optimistic update on error
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
      setKanbanSelected(kanbanSelected); // Restore previous state
    } finally {
      setIsDefaultViewKanban(false);
    }
  };

  const handleMySTKanabanChange: CheckboxProps["onChange"] = async (e) => {
    setIsMySTKanban(true);
    try {
      updateFilter({
        is_my_ST: e.target.checked ? 1 : 0,
      });
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsMySTKanban(false);
    }
  };

  const handleDefaultViewKanabanChange: CheckboxProps["onChange"] = async (
    e
  ) => {
    setIsDefaultViewKanban(true);
    try {
      const requestKanbanSetting = {
        default_view: e.target.checked ? 1 : 0,
        module_id: module_id,
      };
      const responseKanbanSetting = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;
      if (responseKanbanSetting.success) {
        setKanbanSetting(responseKanbanSetting?.data);
        // setKanbanSelected(responseKanbanSetting?.kanban_project_selected);
      } else {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsDefaultViewKanban(false);
    }
  };

  const handleUpdateSTJobStatus = async (
    updateddata: any,
    jobStatusFrom: string,
    isSetLoading: boolean | undefined = true
  ) => {
    const requestKanbanPriorityUpdate = {
      ...updateddata,
      module_key: module_key,
    };
    try {
      if (isSetLoading) {
        setStatusModalLoading(true);
      }
      const responseKanbanStatusUpdate = (await updateSTDetailApi(
        requestKanbanPriorityUpdate
      )) as IKanbanPriorityUpdateApiRes;
      setStatusModalLoading(false);
      if (responseKanbanStatusUpdate?.success) {
        let newRecord: Partial<{
          job_status: number;
          service_date_only: string;
          service_date: string;
          service_time: string;
        }> = {
          job_status: Number(updateddata?.job_status),
        };

        if (updateddata?.service_date_only) {
          newRecord = {
            ...newRecord,
            service_date_only: updateddata.service_date_only,
            service_date: updateddata.service_date,
          };
        }
        if (updateddata?.service_time) {
          newRecord = {
            ...newRecord,
            service_time: updateddata?.service_time,
          };
        }
        setKanbanListData((prevData) =>
          prevData.map((item) => {
            if (item.item_id == Number(updateddata?.job_status)) {
              return {
                ...item,
                kanban_data: item.kanban_data.map((ticket) => {
                  if (
                    ticket.service_ticket_id === updateddata?.service_ticket_id
                  ) {
                    return {
                      ...ticket,
                      job_status_key: item.key,
                      job_status_name: item.name,
                      ...newRecord,
                    };
                  }
                  return ticket;
                }),
                total_count: (parseInt(item.total_count) + 1).toString(),
              };
            } else if (item.item_id == Number(jobStatusFrom)) {
              return {
                ...item,
                total_count: (parseInt(item.total_count) - 1).toString(),
              };
            }
            return item;
          })
        );

        setSelectedData((prev) =>
          prev
            ? {
                ...prev,
                ...newRecord,
              }
            : prev
        );
      } else {
        fetchKanbanSTList({
          type: "",
          isLoad: false,
          filter: filterSrv,
          search: searchValue || "",
        });
        notification.error({
          description:
            responseKanbanStatusUpdate.message || "Something went wrong!",
        });
      }
    } catch (error) {
      setStatusModalLoading(false);
      notification.error({
        description: (error as Error).message || "Something went wrong!",
      });
    }
  };

  const completedStatusMail = () => {
    dispatch(
      sendCompleteStatusMail({
        id: selectedData?.service_ticket_id || "",
      })
    );
  };
  const handleEnd = async (event: Sortable.SortableEvent) => {
    const currentArray = kanbanListData?.map((data) => ({
      column_id: data.item_id,
      sort_order: Number(data.sort_order),
      sorting_id: data.sorting_id.toString(),
      column_name: data.name,
      type_id: data.item_id.toString(),
    }));

    const kanban_sorting: IKanbanSortingArray[] = currentArray.map(
      (data, index) => ({
        ...data,
        sort_order: index,
      })
    );

    try {
      const requestKanbanSetting: IKanbanSorting = {
        kanban_sorting: kanban_sorting,
        module_id: module_id,
      };
      const responseKanbanSetting = (await updateKanbanSortingApi(
        requestKanbanSetting
      )) as IKanbanSortingApiRes;
      if (!responseKanbanSetting.success) {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "Something went wrong!",
      });
    }
  };

  const handleLoadMore = (val: number) => {
    const isSectionLoading = loadingBySection[val];
    if (isSectionLoading) return;

    const hasMoreForSection = hasMoreBySection[val];
    const currentSectionPage = pageBySection[val] || 0;
    if (hasMoreForSection) {
      const nextPage = currentSectionPage + 1;
      setPageBySection((prev) => ({
        ...prev,
        [val]: nextPage,
      }));
      if (currentSectionPage !== 0) {
        setLoadingBySection((prev) => ({
          ...prev,
          [val]: true,
        }));
        fetchKanbanSTList({
          type: val.toString(),
          filter: filterSrv,
          search: searchValue || "",
        });
      }
    }
  };

  // Memoize the final boolean value
  const defaultKanbanView = useMemo(() => isKanbanEnabled, [isKanbanEnabled]);

  const debouncedFetch = debounce(() => {
    fetchKanbanSTList({
      type: "",
      filter: filterSrv,
      search: searchValue || "",
    });
  }, 100);

  useEffect(() => {
    if (defaultKanbanView && filterIndex) {
      debouncedFetch();
      setPageBySection({});
    }
    if (filterSrv?.tab) {
      setTab(filterSrv?.tab);
    }
    if (filterSrv?.is_my_ST != undefined) {
      setSTTabValue(Number(filterSrv?.is_my_ST));
    }
  }, [defaultKanbanView, filterIndex, filterSrv]);

  const paymentModuleNoAccess = useMemo(
    () =>
      checkModuleAccessByKey(defaultConfig.payment_module) === "read_only" ||
      checkModuleAccessByKey(defaultConfig.payment_module) === "no_access",
    []
  );
  const invoiceModuleNoAccess = useMemo(
    () =>
      checkModuleAccessByKey(defaultConfig.invoice_merge_module_key) ===
        "read_only" ||
      checkModuleAccessByKey(defaultConfig.invoice_merge_module_key) ===
        "no_access",
    []
  ); // 3 dots functionality
  const getOptions = () => {
    return SERVICE_TICKET_OPTIONS.filter((option) => {
      if (!selectedData) {
        return false;
      }
      switch (option.key) {
        case "service-reminder":
          if (
            !selectedData.service_format_date ||
            !["149", "263"].includes(selectedData.job_status?.toString() || "")
          ) {
            return false;
          }
          break;
        case "archive":
          if (selectedData.is_deleted === 1) {
            return false;
          }
          break;
        case "active":
          if (selectedData.is_deleted === 0) {
            return false;
          }
          break;
      }
      return true;
    }).map((option) => {
      return {
        ...option,
        label:
          option.key === "post-payment"
            ? `Post ${paymentModuleName}`
            : option.key === "invoice"
            ? `Create/Modify ${invoiceModuleName}`
            : option.key === "work-order"
            ? `${modulePLName} vs ${woModuleName?.plural_name}`
            : option.label,
        disabled:
          (option.key === "delete" &&
            (allow_delete_module_items === "0" || page_is_iframe)) ||
          (option.key === "post-payment" && paymentModuleNoAccess) ||
          (option.key === "invoice" && invoiceModuleNoAccess),
        onClick: async (e: { domEvent: any }) => {
          switch (option.key) {
            case "email-pdf":
              setBillsDetailPdfViewOpen(true);
              (await dispatch(
                fetchServiceTicketDetails({
                  id: Number(selectedData?.service_ticket_id),
                })
              ).unwrap()) as IServiceTicketDetailApiRes;
              break;
            case "post-payment":
              let tempAuthorization = authorization;
              const isExpired = isExpiredAuthorization();
              if (isExpired) {
                const response = (await webWorkerApi({
                  url: "/api/auth/token",
                })) as IGetTokenFromNode;
                if (response.success) {
                  tempAuthorization = response.data.accessToken;
                  setAuthorizationExpired(response.data.accessTokenExpired);
                }
              }
              const baseUrl = `${window?.location?.protocol}//${window?.location?.host}`;
              const pathname = routes.MANAGE_PAYMENT.url;
              const newURL = new URL(baseUrl + pathname, window.ENV.PANEL_URL);
              newURL.searchParams.set("authorize_token", tempAuthorization);
              newURL.searchParams.set("action", "new");
              newURL.searchParams.set("iframecall", "1");
              newURL.searchParams.set("from_remix", "1");
              newURL.searchParams.set("module_key", "service_tickets");
              newURL.searchParams.set(
                "customer_id",
                selectedData?.customer_id.toString() || ""
              );
              newURL.searchParams.set("iframe_close", "1");
              setIframeData({ url: newURL.toString(), title: "Post Payment" });
              break;
            case "invoice":
              try {
                const resData: Partial<ISTCreateModifyApiRes> =
                  await setCreateModifyInvoiceApi(
                    Number(selectedData?.service_ticket_id)
                  );
                if (resData?.statusCode === 200) {
                  let tempAuthorization = authorization;
                  const isExpired = isExpiredAuthorization();
                  if (isExpired) {
                    const response = (await webWorkerApi({
                      url: "/api/auth/token",
                    })) as IGetTokenFromNode;
                    if (response.success) {
                      tempAuthorization = response.data.accessToken;
                      setAuthorizationExpired(response.data.accessTokenExpired);
                    }
                  }

                  const newURL = new URL(
                    routes.MANAGE_INVOICE.url +
                      "/" +
                      (resData?.data?.id?.toString() || ""),
                    window.location.origin
                  );
                  const event = e?.domEvent;

                  if (
                    Number(+window.ENV.ENABLE_ALL_CLICK) &&
                    (event?.ctrlKey || event?.metaKey)
                  ) {
                    window.open(newURL.toString(), "_blank");
                  } else {
                    newURL.searchParams.set(
                      "authorize_token",
                      tempAuthorization
                    );
                    newURL.searchParams.set("iframecall", "1");
                    newURL.searchParams.set("from_remix", "1");
                    setIframeData({
                      url: newURL.toString(),
                      title: "Create Invoice",
                    });
                  }
                }
              } catch (err) {
                // setIsDataFetching(false);
              }
              break;
            case "service-reminder":
              try {
                const resData: Partial<ISTReminderApiRes> =
                  await setServiceReminderApi(
                    Number(selectedData?.service_ticket_id)
                  );
                fetchKanbanSTList({
                  type: "",
                  filter: filterSrv,
                  search: searchValue || "",
                });
                // setIsDataFetching(false);
                if (!resData.success) {
                  notification.error({
                    description: resData.message || "Something went wrong.",
                  });
                }
              } catch (err) {
                // setIsDataFetching(false);
              }
              break;
            case "notify":
              setNotifyTechnicians({
                id: Number(selectedData?.service_ticket_id),
                flag: true,
              });
              break;
            // Service Ticket vs Work Order modal iframe
            case "work-order":
              window.open("https://vimeo.com/399295862", "_blank");
              break;
            case "share":
              setIsShareOpen(true);
              break;
            case "archive":
              setDelArchConfirmOpen("archive");
              break;
            case "active":
              setDelArchConfirmOpen("active");
              break;
            case "delete":
              setDelArchConfirmOpen("delete");
              break;
            default:
              break;
          }
        },
        ...(Number(+window.ENV.ENABLE_ALL_CLICK)
          ? {
              onAuxClick: (e: any) => {
                if (option.key === "invoice" && e.button === 1) {
                  e.preventDefault();
                  e.stopPropagation();
                  handleInvoiceMiddleClick(selectedData);
                }
              },
              onContextMenu: (e: React.MouseEvent<any>) => {
                if (option.key === "invoice") {
                  e.preventDefault();
                  e.stopPropagation();
                  setContextMenu({ x: e.clientX, y: e.clientY, visible: true });
                }
              },
            }
          : {}),
      };
    });
  };
  const contextMenuItems = [
    {
      label: "Open in new tab",
      icon: faArrowUpRightFromSquare,
      onClick: () => {
        handleInvoiceMiddleClick(selectedData);
      },
    },
  ];
  // Archive functionality
  const handleDirDelArch = async (isAssocDel = false) => {
    try {
      setIsDeleting(true);
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "update_module_item_status",
          user,
          otherParams: {
            primary_key: selectedData?.service_ticket_id,
            module_key: "service_tickets",
            status: selectedData?.is_deleted?.toString() === "0" ? 1 : 0,
          },
        }),
        success: (response: { message: string }) => {
          setIsDeleting(false);
          fetchKanbanSTList({
            type: "",
            filter: filterSrv,
            search: searchValue || "",
          });
          dispatch(fetchDashData());
          onCloseDelModal();
        },
        callComplete: () => {
          setIsDeleting(false);
          onCloseDelModal();
        },
        error: (description) => {
          setIsDeleting(false);
          notification.error({
            description,
          });
        },
      });
    } catch (error) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const handleDirDelActive = async () => {
    try {
      setIsDeleting(true);
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "delete_module_record",
          user,
          otherParams: {
            primary_key: selectedData?.service_ticket_id,
            module_key: "service_tickets",
            remove_associated_data: 0,
          },
        }),
        success: (response: { message: string }) => {
          setIsDeleting(false);
          fetchKanbanSTList({
            type: "",
            filter: filterSrv,
            search: searchValue || "",
          });
          dispatch(fetchDashData());
          onCloseDelModal();
          // refreshAgGrid();
        },
        error: (description) => {
          notification.error({
            description,
          });
        },
        callComplete: () => {
          setIsDeleting(false);
          onCloseDelModal();
        },
      });
    } catch (error: unknown) {
      setIsDeleting(false);
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const onCloseDelModal = () => {
    setSelectedData(null);
    setDelArchConfirmOpen("");
  };

  const handleNotificationData = async () => {
    setSendNotificationLoader(true);
    try {
      const resData: Partial<ISTNotificationApiRes> =
        await setServiceNotificationApi(Number(notifyTechnicians?.id));

      // QA : https://app.clickup.com/t/86cx9w399
      // fetchKanbanSTList({
      //   type: "",
      //   filter: filterSrv,
      //   search: searchValue || "",
      // });
      setNotifyTechnicians({ id: "", flag: false });

      if (resData.statusCode !== 200) {
        // notification.success({
        //   description: resData.message,
        // });
        notification.error({
          description: "Please select service technician.",
        });
      } else {
        // notification.error({
        //   description: resData.message || "Something went wrong.",
        // });
      }
    } catch (err) {}
    setSendNotificationLoader(false);
  };
  const emailApiCall = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean
  ) => {
    const formData: IServiceSendEmailForm = {
      ...tempFormData,
      send_me_copy: ccMailCopy ? 1 : 0,
      send_custom_email: 0,
      record_id: Number(selectedData?.service_ticket_id),
      module_id: module_id,
      module_key: module_key,
      t_id: pdfTempId,
      service_ticket_id: Number(selectedData?.service_ticket_id),
      action: "send",
      op: "get_master_pdf_template",
    };
    try {
      const res = (await sendCommonEmailApi(formData)) as ISendEmailCommonRes;
      if (res) {
        if (res.success) {
          closeSendMailSidebar();
        } else {
          notification.error({
            description: res.message,
          });
        }
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    }
  };

  const downloadPdf = async (tId: string) => {
    const res = (await downloadPdfServiceTicketApi({
      service_ticket_id: Number(selectedData?.service_ticket_id),
      action: "download",
      t_id: tId,
      op: "pdf_service_ticket",
    })) as IDownloadSericeTicketRes;
    if (res) {
      if (res.success) {
        const link = document.createElement("a");
        link.href = res.base64_encode_pdf ?? "";
        link.download =
          res.data.pdf_name.toString() || res.base64_encode_pdfUrl || "";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        notification.error({
          description: res.message,
        });
      }
    }
  };

  // select employee
  const updateEventCalendar = async (data: any) => {
    try {
      const resData: any = await updateCalendarEventsApi(data);
      if (resData.statusCode === 200) {
        setUpdatedEmployee(true);
      } else {
        setUpdatedEmployee(false);
      }
    } catch (err) {
      console.error(
        `\n File: #manage-service-tickets+/index.tsx -> Line: #1369 ->  `,
        err
      );
    }
  };
  const handleSelectCustomer = (data: Partial<ISTCustomerDetails[]>) => {
    const userIds = data.map((item) => item?.user_id);
    userIds?.length ? userIds.join(",") : "";
    const cData = {
      setting_option: "service_ticket_employee_filter",
      service_ticket_employee_filter: userIds?.length ? userIds.join(",") : "",
    };
    updateEventCalendar(cData);
  };

  useEffect(() => {
    if (details) {
      const formikCustomer = {
        user_id: details.customer_id,
        display_name: details.customer_name,
        type_name: getDirectaryIdByName(Number(details?.dir_type)),
        contact_id: details.contact_id || 0,
        email: details.cust_email
          ? details.cust_email
          : details.customer_data?.length && details.customer_data[0].email,
        image: details.contact_id
          ? ""
          : details.customer_data?.length && details.customer_data[0].image,
        type_key:
          details.customer_data?.length && details.customer_data?.length > 0
            ? details.customer_data[0].type_key
            : "",
      };

      const formikInvoiceTo = {
        user_id: details.billed_to,
        display_name: details.billed_to_display_name,
        billed_to_dir_type: details.billed_to_dir_type,
        contact_id: details.billed_to_contact,
        email:
          details.invoiced_to_data?.length && details.invoiced_to_data[0].email,
        type_name:
          details.invoiced_to_data?.length &&
          details.invoiced_to_data[0].type_name,
        image: details.billed_to_contact
          ? ""
          : details.invoiced_to_data?.length &&
            details.invoiced_to_data[0].image,
        type_key:
          details.invoiced_to_data?.length &&
          details.invoiced_to_data[0].type_key,
      };

      const selectedCustomers = [];
      if (details.customer_id && details.customer_data?.length) {
        selectedCustomers.push(formikCustomer);
      }
      if (details.billed_to && details.invoiced_to_data?.length) {
        selectedCustomers.push(formikInvoiceTo);
      }

      setSelectedCustomer(selectedCustomers);
    }
  }, [details]);

  const handleMySTKanabanChangeSmall = async (checked: boolean) => {
    setIsMySTKanban(true);
    try {
      updateFilter({
        is_my_ST: checked ? 1 : 0,
      });
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsMySTKanban(false);
    }
  };
  const handleDefaultViewKanabanChangeSmall = async (checked: boolean) => {
    setIsDefaultViewKanban(true);
    try {
      const requestKanbanSetting = {
        default_view: checked ? 1 : 0,
        module_id: module_id,
      };
      const responseKanbanSetting = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;
      if (responseKanbanSetting.success) {
        setKanbanSetting(responseKanbanSetting?.data);
      } else {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsDefaultViewKanban(false);
    }
  };
  const CARD_VIEW_CHECK_BOX = [
    {
      title: `My ${module_name}`,
      isChecked: filterSrv?.is_my_ST?.toString() == "1",
      onClick: () => {
        const current = filterSrv?.is_my_ST?.toString() === "1";
        handleMySTKanabanChangeSmall(!current);
      },
    },
    {
      title: "Set as Default View",
      isChecked: kanbanSetting?.default_view?.toString() == "1",
      onClick: () => {
        const currentKan = kanbanSetting?.default_view?.toString() === "1";
        handleDefaultViewKanabanChangeSmall(!currentKan);
      },
    },
  ];
  return (
    <>
      {/* Calendar */}
      {calendar && (
        <div
          className={`p-4 md:h-[calc(100vh-143px)] h-[calc(100vh-112px)] overflow-y-auto overflow-hidden ${
            !calendar && "hidden"
          }`}
        >
          <div className="flex mb-3.5 gap-2.5 items-center">
            <Button
              icon={
                <FontAwesomeIcon
                  className="text-base w-3.5 h-3.5 text-primary-900 dark:text-white/80"
                  icon="fa-regular fa-arrow-left"
                />
              }
              className="px-2.5 hover:!text-primary-900 hover:!border-primary-900 transition-all"
              onClick={() => setCalendar(false)}
            >
              {_t("Back")}
            </Button>
            <Button
              className="px-2.5 hover:!text-primary-900 hover:!border-primary-900 transition-all"
              onClick={() => {
                setCustomerOptions([CFConfig.employee_key, "my_crew"]);
                setIsOpenSelectCustomer(true);
                setUpdatedEmployee(false);
              }}
            >
              {_t("Employees")}
            </Button>
            {isFilterApply && (
              <Typography className="text-primary-900">
                {_t("Listed records are based on applied Filter.")}
              </Typography>
            )}
          </div>
          <div className="w-full min-h-[250px] scheduler_ui relative">
            <Scheduler
              updatedEmployee={updatedEmployee}
              setCalendarEmployees={setCalendarEmployees}
              type="calendar"
            />
          </div>
        </div>
      )}

      {/* Maps */}
      {mapsView && (
        <div
          className={`p-4 md:h-[calc(100vh-143px)] h-[calc(100vh-112px)] overflow-y-auto overflow-hidden ${
            !mapsView && "hidden"
          }`}
        >
          <div className="flex gap-2.5 items-center">
            <Button
              icon={
                <FontAwesomeIcon
                  className="text-base w-3.5 h-3.5 text-primary-900 dark:text-white/80"
                  icon="fa-regular fa-arrow-left"
                />
              }
              className="px-2.5 hover:!text-primary-900 hover:!border-primary-900 transition-all"
              onClick={() => setMapsView(false)}
            >
              {_t("Back")}
            </Button>
            <Button
              className="px-2.5 hover:!text-primary-900 hover:!border-primary-900 transition-all"
              onClick={() => {
                setCustomerOptions([CFConfig.employee_key, "my_crew"]);
                setIsOpenSelectCustomer(true);
              }}
            >
              {_t("Employees")}
            </Button>
            {isFilterApply && (
              <Typography className="text-primary-900">
                {_t("Listed records are based on applied Filter.")}
              </Typography>
            )}
          </div>
          <div className="w-full scheduler_ui relative">
            <Scheduler
              updatedEmployee={updatedEmployee}
              type="map"
              setCalendarEmployees={setCalendarEmployees}
            />
          </div>
        </div>
      )}

      <div
        className={`pt-[41px] overflow-y-auto overflow-hidden ${
          !page_is_iframe
            ? "md:h-[calc(100vh-143px)] h-[calc(100vh-107px)]"
            : "h-screen"
        } ${(calendar || mapsView) && "hidden"}`}
      >
        <DashboardHeader
          searchPlaceHolder={module_name ? _t(`Search for ${module_name}`) : ""}
          onSearchChange={debouncedOnSearchChange}
          viewSearch={viewSearch}
          searchVal={searchValue}
          setViewSearch={setViewSearch}
          filterComponent={
            <ServiceTicketFilter
              onClearSearch={() => {
                dispatch(setSearchValueAct(""));
              }}
              kanbanView={defaultKanbanView}
            />
          }
          leftComponent={
            <>
              {!defaultKanbanView ? (
                <>
                  <li>
                    <ButtonWithTooltip
                      tooltipTitle={_t("Card View")}
                      tooltipPlacement="top"
                      icon="fa-brands fa-trello"
                      iconClassName="h-5 w-5"
                      className="!w-7 !h-7"
                      onClick={() => {
                        setIsKanbanEnabled(true);
                        if (!filterIndex) {
                          fetchKanbanSTList({
                            type: "",
                            isLoad: false,
                            filter: filterSrv,
                            search: searchValue || "",
                          });
                        }
                      }}
                    />
                  </li>
                  <li>
                    <ButtonWithTooltip
                      tooltipTitle={_t("Map View")}
                      tooltipPlacement="top"
                      icon="fa-regular fa-location-dot"
                      iconClassName="h-5 w-5"
                      className="!w-7 !h-7"
                      onClick={() => {
                        setMapsView(true);
                      }}
                    />
                  </li>
                  <li>
                    <ButtonWithTooltip
                      tooltipTitle={_t(`${modulePLName} Calendar`)}
                      tooltipPlacement="top"
                      icon="fa-regular fa-calendar-days"
                      iconClassName="h-5 w-5"
                      className="!w-7 !h-7"
                      onClick={() => setCalendar(true)}
                    />
                  </li>
                </>
              ) : (
                <li>
                  <ButtonWithTooltip
                    tooltipTitle={_t("Return to Dashboard")}
                    tooltipPlacement="top"
                    icon="fa-regular fa-square-list"
                    iconClassName="h-5 w-5"
                    className="!w-7 !h-7"
                    onClick={() => setIsKanbanEnabled(false)}
                  />
                </li>
              )}
            </>
          }
          rightComponent={
            <div className="flex flex-row items-center sm:gap-5 gap-2">
              {defaultKanbanView ? (
                <>
                  <li className="lg:flex hidden">
                    {checkModuleAccessByKey(module_key) !==
                      "own_data_access" && (
                      <CustomCheckBox
                        className="gap-1.5 !font-medium text-primary-900 dark:text-white/90 relative sm:before:absolute sm:before:w-0.5 sm:before:h-11 sm:before:-right-1.5 sm:before:top-1/2 sm:before:-translate-y-1/2 sm:before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)]"
                        checked={filterSrv?.is_my_ST?.toString() == "1"}
                        onChange={handleMySTKanabanChange}
                        disabled={isMySTKanban}
                        loadingProps={{
                          isLoading: isMySTKanban,
                          className: "bg-[#ffffff]",
                        }}
                      >
                        {_t(`My ${modulePLName}`)}
                      </CustomCheckBox>
                    )}
                  </li>
                  <li className="lg:flex hidden">
                    <CustomCheckBox
                      className="gap-1.5 !font-medium text-primary-900 dark:text-white/90 relative sm:before:absolute sm:before:w-0.5 sm:before:h-11 sm:before:-right-1.5 sm:before:top-1/2 sm:before:-translate-y-1/2 sm:before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)]"
                      checked={kanbanSetting?.default_view?.toString() == "1"}
                      onChange={handleDefaultViewKanabanChange}
                      disabled={isDefaultViewKanban}
                      loadingProps={{
                        isLoading: isDefaultViewKanban,
                        className: "bg-[#ffffff]",
                      }}
                    >
                      {_t("Set as Default View")}
                    </CustomCheckBox>
                  </li>
                  <Popover
                    placement="bottomRight"
                    content={
                      <div className="dark:bg-dark-900 min-w-[155px]">
                        <ul className="py-2 px-1 grid gap-0.5">
                          {CARD_VIEW_CHECK_BOX.map((item, i) => {
                            return (
                              <li
                                className={`rounded bg-blue-50 dark:bg-dark-800`}
                                key={i}
                              >
                                <div
                                  className="flex items-center justify-between cursor-pointer px-2 py-0.5"
                                  onClick={item.onClick}
                                >
                                  <Typography
                                    className={`text-primary-900 dark:text-white/90`}
                                  >
                                    {item.title}
                                  </Typography>
                                  {item?.isChecked && (
                                    <FontAwesomeIcon
                                      className={`text-base w-4 h-4 text-primary-900 opacity-100 `}
                                      icon="fa-regular fa-check"
                                    />
                                  )}
                                </div>
                              </li>
                            );
                          })}
                        </ul>
                      </div>
                    }
                    trigger="click"
                    open={open}
                    className="flex lg:hidden"
                    onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
                  >
                    <div className="flex relative items-center justify-center">
                      <ButtonWithTooltip
                        icon="fa-regular fa-gear"
                        tooltipTitle=""
                        tooltipPlacement="top"
                        iconClassName="h-5 w-5"
                        className="!w-7 !h-7"
                        onClick={() => {}}
                      />
                    </div>
                  </Popover>
                </>
              ) : (
                <></>
              )}
              {module_access === "full_access" ||
              module_access === "own_data_access" ? (
                <li>
                  <AddButton onClick={() => setDrowerOpen(true)}>
                    {_t(module_singular_name ?? "Service Ticket")}
                  </AddButton>
                </li>
              ) : null}
            </div>
          }
        />
        {!defaultKanbanView ? (
          <>
            <ReadOnlyPermissionMsg view={module_access === "read_only"} />
            <div className="p-4">
              <div
                className={`grid xl:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-2.5 transition-all ease-in-out duration-300 ${
                  fullScreenTable || viewSearch
                    ? "max-h-0 overflow-hidden"
                    : "min-[1536px]:max-h-[650px] md:max-h-[900px] max-h-[2000px]"
                }`}
              >
                <div className="common-card h-[266px] ">
                  <TodayServices />
                </div>
                <div className="common-card sm:h-[266px] max-xl:order-3 max-xl:col-span-2 max-md:col-span-1 max-md:order-2">
                  <TicketStats />
                </div>
                <div className="common-card h-[266px] max-xl:order-2 max-md:order-3">
                  <TicketsStatus />
                </div>
              </div>

              <div
                className={`w-full ${
                  fullScreenTable || viewSearch
                    ? "md:mt-2.5 mt-[80px]"
                    : "md:mt-[25px] mt-[100px]"
                }`}
              >
                <div className="relative h-7 z-[999] flex items-center justify-end">
                  <AccordionButton
                    onClick={() => setFullScreenTable((prev: boolean) => !prev)}
                    fullScreenTable={fullScreenTable}
                  />
                  <div className="flex justify-between items-center md:mb-5 w-full mb-[130px] flex-wrap md:flex-nowrap">
                    {checkModuleAccessByKey(module_key) !==
                      "own_data_access" && (
                      <div className="w-full mb-2 md:mb-0">
                        <div className="w-fit p-1 bg-[#EEEFF0] rounded dark:bg-dark-800 ml-auto md:ml-0">
                          <ListTabButton
                            value={STTabvalue}
                            options={SERVICE_TICKETLEFT_TAB}
                            className="sm:min-w-[100px] min-w-fit sm:px-1.5 !text-[#868D8D] px-2 !border-transparent bg-[#EEEFF0]"
                            activeclassName="active:bg-[#ffffff] !text-primary-900 !rounded-md"
                            onChange={(e) => onTabButtonChange(e, "ticket")}
                          />
                        </div>
                      </div>
                    )}
                    <div className="w-full flex text-end justify-end">
                      <ListTabButton
                        value={tab}
                        options={JOB_STATUS_BUTTON_TAB}
                        className="sm:min-w-[100px] min-w-[60px]"
                        onChange={(e) => onTabButtonChange(e, "flag")}
                        activeclassName="!bg-[#F1F4F9]"
                      />
                    </div>
                  </div>
                </div>
                <div className="p-2 bg-white dark:bg-dark-600 common-list-table rounded">
                  <ServiceTicketsList
                    onIsDataLoading={(data: boolean) => {}}
                    search={searchValue || ""}
                  />
                </div>
              </div>
            </div>
          </>
        ) : (
          <>
            <ReadOnlyPermissionMsg
              view={module_access === "read_only"}
              className="px-4 sm:pt-4 pt-1"
              textClassName="sm:text-13 text-xs flex justify-center items-end sm:h-auto h-7 sm:leading-auto leading-[15px]"
            />
            <div className="flex pt-4 pb-2.5 px-1.5 transition-all ease-in-out duration-300 overflow-x-auto">
              <KanbanList
                list={kanbanListData} //  kanban data
                setList={setKanbanListData}
                loading={isLoading}
                childLoader={loadingChild}
                collapseClick={(STColumn) => {
                  handleColspan(
                    STColumn?.item_id?.toString(),
                    STColumn?.is_collapse_card?.toString(),
                    STColumn?.key?.toString()
                  );
                }}
                cardDetailsClick={(STItem) => {
                  if (!!STItem?.service_ticket_id) {
                    navigate(`${STItem.service_ticket_id}`);
                  }
                  setSelectedData(STItem);
                }}
                kanbanSelected={kanbanSelected}
                loadMore={(val) => {
                  handleLoadMore(val);
                }}
                iconShow={true}
                colum={{
                  headerName: "name",
                  parentId: "item_id",
                  count: "total_count",
                  collapse: "is_collpase_card",
                  color: "status_color",
                  child: "kanban_data",
                  childCard: {
                    cardId: "service_ticket_id", // child card id pass
                    cardFirstFirst: "customer_name",
                    cardFirstSecond: "company_ticket_id",
                    cardFirstSecondPrefix: "ST #",
                    cardMiddleFirst: "title",
                    cardLastFirst: "service_tech_name",
                    cardLastSecond: "service_date_only",
                    cardLastSecondTime: "service_time",
                    cardImg: "user_profile_image",
                    imgName: "assignee",
                  },
                }}
                handleCardDragDropEnd={(event: Sortable.SortableEvent) => {
                  const { from, to, clone } = event;

                  const fromColumnId =
                    from?.closest("[data-id]")?.getAttribute("data-id") ?? "";
                  const toColumnId =
                    to?.closest("[data-id]")?.getAttribute("data-id") ?? "";
                  if (fromColumnId === toColumnId) {
                    return;
                  }

                  const fromStatusRecords = kanbanListData.find(
                    (item) => item.item_id == Number(toColumnId)
                  );
                  if (!fromStatusRecords) {
                    return;
                  }
                  const recordId =
                    clone
                      ?.querySelector("[data-record-id]")
                      ?.getAttribute("data-record-id") ?? "";

                  const currentRecord = fromStatusRecords.kanban_data.find(
                    (kanban) =>
                      kanban.service_ticket_id?.toString() === recordId
                  );
                  if (!currentRecord) {
                    return;
                  }

                  setSelectedData(currentRecord);

                  if (
                    toColumnId === "261" ||
                    (currentRecord.service_date_only &&
                      (toColumnId === "152" ||
                        toColumnId === "149" ||
                        toColumnId === "151" ||
                        toColumnId === "263" ||
                        toColumnId === "153"))
                  ) {
                    /**
                     * "Unscheduled" value is 261
                     * "Cancelled" value is 152
                     * "Requires Follow up" value is 153
                     * "Scheduleded" value is 149
                     * "Completed" value is 151
                     * "En Route" value is 263
                     */
                    handleUpdateSTJobStatus(
                      {
                        job_status: toColumnId,
                        service_ticket_id: currentRecord.service_ticket_id,
                      },
                      fromColumnId,
                      false
                    );

                    if (toColumnId === "263") {
                      setJobStatus({
                        from: fromColumnId,
                        to: toColumnId,
                      });
                    }
                    if (toColumnId === "151") {
                      setJobStatus({
                        from: fromColumnId,
                        to: toColumnId,
                      });
                      setScheduledServiceDialogOpen(true);
                    }

                    return;
                  } else if (toColumnId === "150") {
                    // "Checked-In" value is 150
                    let otherData: ISTDetailFieldsBoolean = {};
                    if (!currentRecord.service_date_only) {
                      const date = dayjs;
                      let newDate = date();
                      try {
                        newDate = date
                          .utc()
                          .tz(window.TIMEZONE?.timezone_utc_tz_id);
                      } catch (error) {
                        console.error(
                          `Invalid timezone:`,
                          window.TIMEZONE?.timezone_utc_tz_id
                        );
                      }
                      const service_date = backendDateFormat(
                        newDate.format("YYYY-MM-DD"),
                        "YYYY-MM-DD"
                      );
                      otherData = {
                        service_date_only: newDate.format(date_format),
                        service_date,
                        service_time: backendTimeFormat(
                          newDate.format("hh:mm A")
                        ),
                      };
                    }
                    handleUpdateSTJobStatus(
                      {
                        job_status: toColumnId,
                        service_ticket_id: currentRecord.service_ticket_id,
                        ...otherData,
                      },
                      fromColumnId
                    );
                    return;
                  }
                  // other all status handle from modals

                  setJobStatus({
                    from: fromColumnId,
                    to: toColumnId,
                  });
                }}
                handleColumnDragDropEnd={handleEnd}
                isReadOnly={isReadOnly}
                handleMouseMove={(ST) => {
                  // setSelectedData(ST);
                }}
                onActionClick={(ST) => {
                  setSelectedData(ST);
                }}
              >
                <div className="flex items-center gap-1.5 invisible group-hover/kanbanitem:visible">
                  {(module_access === "full_access" ||
                    module_access === "own_data_access") && (
                    <DropdownMenu
                      options={getOptions()}
                      buttonClass="dropdown-menu-button m-0 hover:!bg-[#0000000f]"
                      icon="fa-solid fa-ellipsis-h"
                      iconClassName="text-primary-900/80 group-hover/buttonHover:text-primary-900"
                      {...((paymentModuleNoAccess ||
                        invoiceModuleNoAccess ||
                        allow_delete_module_items === "0") && {
                        footerText: _t(
                          "Some actions might be unavailable depending on your privilege."
                        ),
                      })}
                      // onClick={(e: React.MouseEvent<HTMLButtonElement>) =>
                      //   e.stopPropagation()
                      // }
                    />
                  )}
                </div>
              </KanbanList>
            </div>
          </>
        )}
        {/* </div> */}
        {drowerOpen && !isReadOnly && (
          <AddServicesTickets
            drowerOpen={drowerOpen}
            setDrowerOpen={setDrowerOpen}
            editView={false}
            project={queryParamsProject}
          />
        )}

        {/* 3 dots options */}
        {/* View/Email PDF */}
        {billsDetailPdfViewOpen && (
          <PDFFilePreview
            projectId={selectedData?.project_id}
            isOpen={billsDetailPdfViewOpen}
            onCloseModal={() => setBillsDetailPdfViewOpen(false)}
            moduleId={module_id}
            op="pdf_service_ticket"
            idName="service_ticket_id"
            isLoading={false}
            id={selectedData?.service_ticket_id?.toString() || ""}
            options={options}
            emailSubject={selectedData?.email_subject || ""}
            handleEmailApiCall={emailApiCall}
            handleDownload={downloadPdf}
            isViewAttachment={false}
            moduleName={module_singular_name}
            setPdfTempId={setPdfTempId}
            selectedCustomer={selectedCustomer ?? []}
          />
        )}

        {/* ! Delete POpup */}
        {delArchConfirmOpen !== "" && (
          <ConfirmModal
            isOpen={delArchConfirmOpen !== ""}
            modaltitle={_t(
              delArchConfirmOpen === "delete"
                ? "Delete"
                : delArchConfirmOpen === "active"
                ? "Active"
                : "Archive"
            )}
            description={_t(
              delArchConfirmOpen === "delete"
                ? "Are you sure you want to delete this Item?"
                : delArchConfirmOpen === "archive"
                ? "Are you sure you want to Archive this item? To view it or Activate it later, set the filter to show Archived items."
                : "Are you sure you want to Activate this data?"
            )}
            withConfirmText={delArchConfirmOpen === "delete"}
            modalIcon={
              delArchConfirmOpen === "delete"
                ? "fa-regular fa-trash-can"
                : delArchConfirmOpen === "archive"
                ? "fa-regular fa-box-archive"
                : "fa-regular fa-regular-active"
            }
            isLoading={isDeleting}
            onAccept={(data) => {
              if (delArchConfirmOpen === "delete") {
                handleDirDelActive();
              } else {
                handleDirDelArch();
              }
            }}
            onDecline={onCloseDelModal}
            onCloseModal={onCloseDelModal}
          />
        )}

        {/* ! Share link */}
        {isShareOpen && (
          <ShareInternalLinkModal
            isOpen={isShareOpen}
            shareLinkParams={{
              record_id: Number(selectedData?.service_ticket_id),
              module_key: module_key,
              module_page: removeFirstSlash(
                routes.MANAGE_SERVICE_TICKETS.url || ""
              ),
            }}
            onEmailLinkClick={(data) => {
              setIsSendEmailSidebarOpen(true);
              setShareLink(data);
              setIsShareOpen(false);
            }}
            onCloseModal={() => {
              setSelectedData(null);
              setIsShareOpen(false);
              setShareLink("");
            }}
          />
        )}

        {/* ! Email send popup */}
        {isSendEmailSidebarOpen && (
          <DirSendEmail
            isOpen={isSendEmailSidebarOpen}
            canWrite={shareLink ? false : true}
            options={
              shareLink
                ? undefined
                : [
                    CFConfig.employee_key,
                    "my_crew",
                    CFConfig.customer_key,
                    CFConfig.contractor_key,
                    CFConfig.vendor_key,
                    CFConfig.misc_contact_key,
                    "by_service",
                  ]
            }
            emailData={{
              subject: shareLink ? "Shared Link" : "",
              body: shareLink
                ? `A link to a record within Contractor Foreman has been shared with you. <a href="${shareLink}">View Details.</a>`
                : "",
            }}
            onSendResponse={() => {
              setSelectedData(null);
              setShareLink("");
            }}
            onClose={() => {
              setIsSendEmailSidebarOpen(false);
              setSelectedData(null);
              setShareLink("");
            }}
            appUsers={true}
          />
        )}

        {/* !  */}
        {isConfirmDialogOpen && (
          <ConfirmModal
            isOpen={isConfirmDialogOpen}
            modalIcon="fa-regular fa-clipboard-list-check"
            modaltitle={_t("Add Option To List")}
            description={_t(
              `This will add to the list. Do you want to add it?`
            )}
            // isLoading={isAddingCustomData}
            onCloseModal={() => setIsConfirmDialogOpen(false)}
            onAccept={() => {
              // handleAddCustomData();
            }}
            onDecline={() => setIsConfirmDialogOpen(false)}
          />
        )}

        {/* ! NOtify Technicians  */}
        {notifyTechnicians && (
          <ConfirmModal
            isOpen={notifyTechnicians.flag}
            modalIcon="fa-regular fa-bell"
            modaltitle={_t("Notify Technicians")}
            description={_t(
              `Do you want to send a notification to the assigned contacts? Notifications will be sent based on the preferences within the user's account.                                                        `
            )}
            isLoading={sendNotificationLoader}
            onCloseModal={() => setNotifyTechnicians({ id: "", flag: false })}
            onAccept={() => {
              handleNotificationData();
            }}
            onDecline={() => setNotifyTechnicians({ id: "", flag: false })}
          />
        )}

        {iframeData?.url && (
          <IframeModal
            isOpen={iframeData?.url ? true : false}
            widthSize="100vw"
            onCloseModal={() => {
              setIframeData({ url: "", title: "" });
            }}
            modalBodyClass="p-0"
            header={{
              // icon,
              closeIcon: true,
            }}
            iframeProps={{
              src: iframeData.url,
              id: iframeData.title,
            }}
            messageListener={(key, data) => {
              if (key === sendMessageKeys.modal_change) {
                fetchKanbanSTList({
                  type: "",
                  isLoad: false,
                  filter: filterSrv,
                  search: searchValue || "",
                });
                setIframeData({ url: "", title: "" });
              }
            }}
          />
        )}
        {/* 3 dots options */}
      </div>

      {/* select employee */}
      {isOpenSelectCustomer && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectCustomer}
          closeDrawer={() => {
            dispatch(setActiveField(customerOptions[0]));
            setIsOpenSelectCustomer(false);
            setUpdatedEmployee(false);
            // setCustomerFieldType("");
            setCustomerOptions([]);
          }}
          singleSelecte={false}
          options={customerOptions?.length > 0 ? customerOptions : []}
          setCustomer={(data) => {
            handleSelectCustomer(
              data as unknown as Partial<ISTCustomerDetails[]>
            );
          }}
          selectedCustomer={calendarEmployees}
          groupCheckBox={true}
          additionalContactDetails={0}
        />
      )}

      {Boolean(jobStatus.to) && selectedData && (
        <ConfirmModal
          isOpen={Boolean(jobStatus.to)}
          isLoading={statusModalLoading}
          modalIcon={
            !selectedData?.service_date_only
              ? "fa-regular fa-file-check"
              : "fa-regular fa-clipboard-list-check"
          }
          modaltitle={
            !selectedData?.service_date_only
              ? "Confirmation"
              : _t("Update Service Ticket Status")
          }
          description={
            !selectedData?.service_date_only
              ? _t(
                  "A service date is required to assign any status other than 'Unscheduled'. Without a service date, the status will remain 'Unscheduled'. Are you sure you want to proceed?"
                )
              : _t(
                  `Do you want to send an update to the customer notifying them that the service has been En Route ?`
                )
          }
          onCloseModal={async () => {
            if (!selectedData.service_date_only) {
              setStatusModalLoading(true);
              await fetchKanbanSTList({
                type: "",
                isLoad: false,
                filter: filterSrv,
                search: searchValue || "",
              });
              setStatusModalLoading(false);
            }
            setJobStatus(initJobStatus);
          }}
          onAccept={async () => {
            if (jobStatus.to === "152") {
              // "Cancelled" value is 152
              await handleUpdateSTJobStatus(
                {
                  job_status: jobStatus.to,
                  service_ticket_id: selectedData?.service_ticket_id,
                },
                jobStatus.from
              );
              setJobStatus(initJobStatus);
              return;
              // https://app.clickup.com/t/86cx5z875 => remove because of this CU
              // } else if (jobStatus.to === "153") {
              //   // "Requires Follow up" value is 153 and "Unscheduled" value is 261
              //   if (selectedData?.job_status?.toString() !== "261") {
              //     await handleUpdateSTJobStatus(
              //       {
              //         job_status: "261",
              //         service_ticket_id: selectedData?.service_ticket_id,
              //       },
              //       jobStatus.from
              //     );
              //   } else {
              //     setStatusModalLoading(true);
              //     await fetchKanbanSTList({
              //       type: "",
              //       isLoad: false,
              //       filter: filterSrv,
              //       search: searchValue || "",
              //     });
              //     setStatusModalLoading(false);
              //   }
              //   setJobStatus(initJobStatus);
            } else if (!selectedData?.service_date_only) {
              setScheduledServiceDialogOpen(true);
            } else {
              // send notify
              const sendEnRouteStatusMail = async () => {
                try {
                  setStatusModalLoading(true);
                  const res = (await sendEnRouteStatusMailApi({
                    service_ticket_id: Number(selectedData?.service_ticket_id),
                  })) as IDownloadSericeTicketRes;
                  setJobStatus(initJobStatus);
                  setStatusModalLoading(false);
                  if (res?.success) {
                    // Successfully sent notification
                  } else {
                    notification.error({ description: res?.message });
                  }
                } catch (error) {
                  setJobStatus(initJobStatus);
                  setStatusModalLoading(false);
                  notification.error({
                    description: "Error sending notification.",
                  });
                }
              };
              sendEnRouteStatusMail();
            }
          }}
          onDecline={async () => {
            if (!selectedData.service_date_only) {
              setStatusModalLoading(true);
              await fetchKanbanSTList({
                type: "",
                isLoad: false,
                filter: filterSrv,
                search: searchValue || "",
              });
              setStatusModalLoading(false);
            }
            setJobStatus(initJobStatus);
          }}
        />
      )}

      {scheduledServiceDialogOpen && selectedData && Boolean(jobStatus.to) && (
        <ScheduleService
          isOpen={scheduledServiceDialogOpen}
          handleUpdateServiceTicket={async (data) => {
            const serviceDateOnly = data.date
              ? backendDateFormat(data.date as string, date_format) || null
              : null;
            if (!selectedData.service_date_only) {
              await handleUpdateSTJobStatus(
                {
                  job_status: jobStatus.to,
                  service_date_only: data.date,
                  service_date: serviceDateOnly,
                  service_time: data.time
                    ? backendTimeFormat(data.time) || ""
                    : "",
                  service_ticket_id: selectedData?.service_ticket_id,
                },
                jobStatus.from
              );
              if (jobStatus.to === "149" || jobStatus.to === "153") {
                setScheduledServiceDialogOpen(false);
                setJobStatus(initJobStatus);
              } else if (jobStatus.to === "263") {
                setScheduledServiceDialogOpen(false);
              }
            } else if (data.nextAppoinment === "1") {
              const serviceTicketData = {
                ...data,
                service_date: serviceDateOnly,
                service_time: data.time1,
                service_end_time: data.time2,
                service_date_only: data.date,
                job_status: "149",
              };
              delete serviceTicketData.date;

              const addServiceTicket = async () => {
                setAddSTLoading(true);
                const response = await copyServiceTicketOnCompleteAPI(
                  getValuableObj(serviceTicketData),
                  selectedData.service_ticket_id?.toString()
                );
                setAddSTLoading(false);
                if (response?.success) {
                  setScheduledServiceDialogOpen(false);
                  setJobStatus(initJobStatus);
                  dispatch(resetAllSlices());
                  setTimeout(() => {
                    navigate(
                      `/manage-service-tickets/${response.data?.service_ticket_id}`
                    );
                  }, 100);
                } else {
                  notification.error({ description: response?.message });
                }
                if (data?.completed === "1") {
                  completedStatusMail();
                }
              };
              addServiceTicket();
            } else if (jobStatus.to === "151") {
              if (data?.completed === "1") {
                completedStatusMail();
              }
              if (data?.notShowAgain) {
                dispatch(disableAutoScheduleAppoinment()).then(() =>
                  setGSettingsUpdateValue("auto_schedule_appointment", 0)
                );
              }
              setScheduledServiceDialogOpen(false);
              setJobStatus(initJobStatus);
            }
          }}
          isLoading={
            (statusModalLoading && !selectedData.service_date_only) ||
            addSTLoading
          }
          onCloseModal={async () => {
            if (!selectedData.service_date_only) {
              setAddSTLoading(true);
              await fetchKanbanSTList({
                type: "",
                isLoad: false,
                filter: filterSrv,
                search: searchValue || "",
              });
              setAddSTLoading(false);
            }
            setScheduledServiceDialogOpen(false);
            setJobStatus(initJobStatus);
          }}
          isShowDate={!selectedData.service_date_only}
          details={selectedData}
        />
      )}
      <CustomContextMenu
        position={{ x: contextMenu.x, y: contextMenu.y }}
        visible={contextMenu.visible}
        items={contextMenuItems}
        onClose={() => setContextMenu((c) => ({ ...c, visible: false }))}
      />
    </>
  );
};

const ManageServiceTicket = () => {
  return (
    <STStoreProvider>
      <ServiceTicket />
    </STStoreProvider>
  );
};

export default React.memo(ManageServiceTicket);

export { ErrorBoundary };
