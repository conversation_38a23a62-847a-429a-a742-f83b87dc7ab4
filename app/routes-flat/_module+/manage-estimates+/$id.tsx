// Atoms
import { But<PERSON> } from "~/shared/components/atoms/button";
import { Toolt<PERSON> } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// molecules
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";

// ModuleSidebar
import { ModuleSidebar } from "~/shared/components/moduleSidebar";

// Other
import { useNavigate, useParams, useSearchParams } from "@remix-run/react";
import { ESTIMATES_EDIT_VIEW_SIDE_MENU } from "~/modules/financials/pages/estimates/utils/constants";

import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "~/hook";
import { getEstBiddingStatus } from "~/modules/financials/pages/estimates/redux/action/ESBiddingAction";
import {
  getEstimateCoverSheet,
  getEstimateCoverSheetList,
  getEstimateDetail,
  getEstimateScopeDetail,
} from "~/modules/financials/pages/estimates/redux/action/ESDetailAction";
import {
  getCustomProjectTypeList,
  getEstimateItems,
} from "~/modules/financials/pages/estimates/redux/action/ESItemAction";
import { getOneBuildSetting } from "~/modules/financials/pages/estimates/redux/action/ESOneBuildAction";
import ESStoreProvider from "~/modules/financials/pages/estimates/redux/ESStoreProvider";
import {
  useAppESDispatch,
  useAppESSelector,
} from "~/modules/financials/pages/estimates/redux/store";
import { approvalHideStatus } from "~/modules/financials/pages/estimates/utils/common";
import { getCustomStatusList } from "~/redux/action/getCustomStatusAction";
import { getTaxDetails } from "~/redux/action/getTaxDetailsAction";
import { routes } from "~/route-services/routes";

import {
  getCommonSidebarCollapse,
  getGConfig,
  getGModuleByKey,
  setCommonSidebarCollapse,
} from "~/zustand";
import ManageEstimatesTab from "./$id+/$tab";

// FontAwesome File
import { ESDetailRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/estimates/detail/regular";
import { ESDetailSolidIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/estimates/detail/solid";
import { ESDetailLightIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/estimates/detail/light";
import { defaultConfig } from "~/data";
import { getCostCode } from "~/redux/action/getCostCodeAction";
import {
  changeTab,
  disableComponent,
  initiateComponent,
} from "~/modules/financials/pages/estimates/redux/slices/frameComponentSlice";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { getModuleAccess } from "~/shared/utils/helper/module";
import { fetchMarkupWithItemTypes } from "~/redux/action/commonMarkupWithItemTypesAction";
import { useGlobalMenuModule } from "~/zustand/global/menuModules/slice";

// Fort Awesome Library Add icons
ESDetailRegularIconAdd();
ESDetailSolidIconAdd();
ESDetailLightIconAdd();
interface ManageEstimatesTabProps {
  id?: string;
  tab?: string;
}
const EstimatesDetailsPage = (props: ManageEstimatesTabProps) => {
  const { _t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppESDispatch();
  const [serachParams, setSearchParams] = useSearchParams();
  const { tab: routeTab, id: routeId }: RouteParams = useParams();

  const { getGlobalModuleByKey } = useGlobalModule();

  const EstimateModule = useMemo(
    () => getGlobalModuleByKey(CFConfig.estimate_module),
    [getGlobalModuleByKey] // Ensures memoization based on function reference
  );

  const { module_id: estimate_module_id = 0 } = EstimateModule || {};

  const module_access = useMemo(
    () => getModuleAccess(EstimateModule),
    [EstimateModule]
  );

  const {
    isComponent,
    tab: componentTab,
    isInitiated,
  } = useAppESSelector((state) => state.frameComponent);

  const sidebarCollapse: boolean | undefined = getCommonSidebarCollapse();
  const ProjectModule: GModule | undefined = getGModuleByKey(
    defaultConfig.project_module
  );
  // Compute tab and estimate_id using useMemo
  const { tab, estimate_id } = useMemo(() => {
    return isComponent
      ? { tab: componentTab || "", estimate_id: props?.id }
      : { tab: routeTab, estimate_id: routeId };
  }, [routeTab, routeId, props?.id, componentTab, isComponent]);

  useEffect(() => {
    if (props?.id) {
      dispatch(initiateComponent({ tab: props?.tab, id: props?.id }));
    } else {
      dispatch(disableComponent());
    }
  }, [props?.id, props?.tab]);

  const [loading, setLoading] = useState<boolean>(false);

  const {
    estimateDetail,
    isEstimateDetailLoading,
    isDashLoading,
  }: IEstimatesDetailState = useAppESSelector((state) => state.estimateDetail);
  const { page_is_iframe }: GConfig = getGConfig();

  const isReadOnly = useMemo(
    () => module_access === "read_only",
    [module_access]
  );

  const { checkGlobalMenuModulePermissionByKey } = useGlobalMenuModule();

  const bidManagerModulePermissionDis = checkGlobalMenuModulePermissionByKey(
    CFConfig.estimate_bidding_module
  );

  const bidManagerDisabled = bidManagerModulePermissionDis === "disabled";

  const tabSideMenu = useMemo(() => {
    let filteredMenu = [...ESTIMATES_EDIT_VIEW_SIDE_MENU];

    if (bidManagerDisabled) {
      filteredMenu = filteredMenu.filter((item) => item.value !== "bidding");
    }

    if (estimateDetail?.hide_batch_coversheet === 1) {
      filteredMenu = filteredMenu.filter(
        (item) => item.value !== "cover_sheet"
      );
    }

    return filteredMenu;
  }, [estimateDetail, bidManagerDisabled]);

  const fetchEstimateDetail = async () => {
    setLoading(true);
    const response = await dispatch(
      getEstimateDetail({ estimate_id, add_event: true })
    );
    const { statusCode, message }: { statusCode?: number; message?: string } =
      response?.payload || {};
    if (statusCode === 404) {
      notification.error({
        description: message || "Estimate not found",
      });
      if (!isComponent) {
        navigate(`${routes.MANAGE_ESTIMATE.url}`);
      } else {
        setSearchParams({});
      }
      return;
    }
    await dispatch(getEstimateItems({ estimate_id }));
    await dispatch(getEstimateScopeDetail({ estimate_id }));
    await dispatch(
      getEstimateCoverSheet({ estimate_id, module_id: estimate_module_id })
    );
    // await dispatch(getEstimateCoverSheet({module_id,estimate_id}));
    await dispatch(
      getEstimateCoverSheetList({ module_id: estimate_module_id })
    );
    await dispatch(
      getCustomProjectTypeList({
        is_deleted: 0,
        module_id: !!ProjectModule?.module_id ? [ProjectModule?.module_id] : [],
      })
    );
    setLoading(false);
  };

  useEffect(() => {
    if (
      estimate_id &&
      estimate_module_id &&
      !!ProjectModule?.module_id &&
      isInitiated
    ) {
      fetchEstimateDetail();
    }
  }, [estimate_id, estimate_module_id, ProjectModule?.module_id, isInitiated]);

  useEffect(() => {
    if (!estimate_module_id) return;
    dispatch(
      getCustomStatusList({
        module_id: [estimate_module_id],
      })
    );
  }, [estimate_module_id]);
  useEffect(() => {
    dispatch(getOneBuildSetting());
    dispatch(getEstBiddingStatus());
    dispatch(getTaxDetails({ status: 0, start: 0, limit: -1 }));
    dispatch(getCostCode({ has_parent_code: 1 }));
  }, []);

  useEffect(() => {
    if (estimateDetail?.estimate_id) {
      dispatch(
        fetchMarkupWithItemTypes({
          project_id: Number(estimateDetail?.project_id),
        })
      );
    }
  }, [estimateDetail?.estimate_id, estimateDetail?.project_id]);

  const handleReviewSubmit = () => {
    if (isComponent) {
      dispatch(changeTab({ tab: "estimate_finalize" }));
    } else {
      const url =
        "estimate_finalize" +
        (window.ENV.PAGE_IS_IFRAME ? window.location.search : "");
      navigate(url);
    }
  };

  return (
    <div
      className={`flex overflow-hidden ${
        window.ENV.PAGE_IS_IFRAME || page_is_iframe || isComponent
          ? "h-screen"
          : "md:h-[calc(100dvh-143px)] h-[calc(100dvh-112px)]"
      }`}
    >
      {!sidebarCollapse && (
        <div
          className={`xl:hidden block absolute bg-black/20 dark:bg-white/10 z-[100] ease-in-out duration-300 ${
            sidebarCollapse
              ? "w-0 h-0"
              : window.ENV.PAGE_IS_IFRAME
              ? "w-full h-full"
              : "w-full h-[calc(100dvh-112px)]"
          }`}
          onClick={() => setCommonSidebarCollapse(true)}
        ></div>
      )}
      <ModuleSidebar
        sidebarCollapse={sidebarCollapse}
        onSidebarCollapse={setCommonSidebarCollapse}
        selectOptions={tabSideMenu}
        enableOpenInNewTab={Number(window.ENV.ENABLE_ALL_CLICK)}
        // onSelectedOption={(value: string) => {
        //   navigate(value);
        // }}
        //  onSelectedOption={(value: string) => {
        // if (isComponent) {
        //   dispatch(changeTab({ tab: value }));
        onSelectedOption={(value: string, e) => {
          if (isComponent) {
            dispatch(changeTab({ tab: value }));
          } else {
            if (e) {
              e.preventDefault();
            }
            const isSpecialTab = value?.includes("bidding");
            const shouldOpenInNewTab =
              Number(window.ENV.ENABLE_ALL_CLICK) &&
              e &&
              (e?.button === 1 || e?.ctrlKey || e?.metaKey);

            if (isSpecialTab && shouldOpenInNewTab) {
              const basePath = `${routes.MANAGE_ESTIMATE.url}/${estimate_id}/${value}`;
              const newTabUrl = `${basePath}`;
              window.open(newTabUrl, "_blank");
            } else {
              navigate(value);
            }
          }
        }}
        allowBack={!Number(props?.id)}
        sidebarClassName="pb-[60px]"
        selectedOption={tab ?? "details"}
        SelecteMenuBottom={
          <div
            className={`fixed bg-primary-900 ${
              window.ENV.PAGE_IS_IFRAME ? "bottom-0" : "md:bottom-8 bottom-28"
            } ${
              sidebarCollapse
                ? "md:w-[75px] w-0 md:p-2.5 md:flex hidden "
                : "w-[225px] p-2.5"
            }`}
          >
            {!isReadOnly &&
              !isEstimateDetailLoading &&
              !approvalHideStatus[
                estimateDetail?.approval_type?.toString() ?? ""
              ] &&
              (sidebarCollapse ? (
                <Tooltip
                  title={_t("Review and Submit")}
                  placement="right"
                  overlayClassName="menu-tooltip"
                >
                  <Button
                    type="primary"
                    htmlType="button"
                    className={`h-[38px] w-[calc(100%-12px)] mx-auto gap-0 bg-[#FF5400] hover:!bg-[#FF5400] active:!bg-[#FF5400] !shadow-none !duration-1000 ease-in-out ${
                      tab == "estimate_finalize"
                        ? "!bg-[linear-gradient(90deg,#34496F_0%,#34496F_100%)] dark:!bg-[linear-gradient(90deg,#2b323b_0%,#131d27_100%)]"
                        : ""
                    }`}
                    onClick={handleReviewSubmit}
                  >
                    <div className="w-[25px] flex justify-center">
                      <FontAwesomeIcon
                        className="w-5 h-5 text-white"
                        icon="fa-regular fa-paper-plane"
                      />
                    </div>
                  </Button>
                </Tooltip>
              ) : (
                <Button
                  type="primary"
                  htmlType="button"
                  className={`w-full h-[38px] gap-0 bg-[#FF5400] hover:!bg-[#FF5400] active:!bg-[#FF5400] !shadow-none !duration-1000 ease-in-out ${
                    sidebarCollapse ? "md:flex hidden" : ""
                  } ${
                    tab == "estimate_finalize"
                      ? "!bg-[linear-gradient(90deg,#34496F_0%,#34496F_100%)] dark:!bg-[linear-gradient(90deg,#2b323b_0%,#131d27_100%)]"
                      : ""
                  }`}
                  onClick={handleReviewSubmit}
                >
                  <div className="w-[25px] flex justify-center">
                    <FontAwesomeIcon
                      className="w-5 h-5 text-white"
                      icon="fa-regular fa-paper-plane"
                    />
                  </div>
                  <Typography className="ease-in-out whitespace-nowrap duration-75 text-white normal-case font-normal pl-2">
                    {_t("Review and Submit")}
                  </Typography>
                </Button>
              ))}
          </div>
        }
      />
      <ManageEstimatesTab tab={tab} estimate_id={estimate_id} />
      {/* {tab ? <Outlet /> : <ManageEstimatesTab />} */}
    </div>
  );
};

const ManageEstimates = (props: ManageEstimatesTabProps) => {
  return (
    <ESStoreProvider>
      <EstimatesDetailsPage {...props} />
    </ESStoreProvider>
  );
};

export default ManageEstimates;

export { ErrorBoundary };
