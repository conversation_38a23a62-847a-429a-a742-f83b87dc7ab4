import { memo, useEffect, useState } from "react";

// atoms
import { Toolt<PERSON> } from "~/shared/components/atoms/tooltip";
import { <PERSON><PERSON> } from "~/shared/components/atoms/header";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { RadioGroupList } from "~/shared/components/molecules/radioGroupList";
import { CloseButton } from "~/shared/components/molecules/closeButton";
// Other
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";

// Zustand
import { setChatUser } from "~/zustand/global/chat/action";
import { getChatUsers } from "~/zustand/global/chat/slice";

// Other
import UserForm from "./user-form";
import { globalChat } from "~/route-services/chat.routes";
import {
  CHAT_DEFAULT_TAB_IDs,
  CHAT_DEFAULT_TABS,
  CHAT_PROJECT_MESSAGING_TABS,
} from "./constants";
import { useTranslation } from "~/hook";
import { Number, sanitizeString } from "~/helpers/helper";
import MessageList from "./message-list";
import { useGlobalData } from "~/zustand/global/store";
import { set } from "@firebase/database";
import { routes } from "~/route-services/routes";
import { useNavigate } from "@remix-run/react";

const ChatBox = ({
  minimize,
  data: activeChatData,
  updateChat,
  groupAdmin,
  currentUserIsAdmin,
  openChat,
  search,
}: IChatBoxProps) => {
  const [isChatUserForm, setIsChatUserForm] = useState<boolean>(false);
  const navigate = useNavigate();
  const [isSubmiting, setIsSubmiting] = useState<boolean>(false);
  const [isChatUserSettingForm, setIsChatUserSettingForm] =
    useState<boolean>(false);
  const [notificationType, setNotificationType] = useState<string>("1");

  const chartUsers =
    getChatUsers(
      CHAT_DEFAULT_TABS.group as keyof IInitialGlobalData["chat"]["users"]
    ) || [];
  const { _t } = useTranslation();

  useEffect(() => {
    setNotificationType(activeChatData.notification_type?.toString() || "1");
  }, [activeChatData.notification_type]);
  const status_color = activeChatData.status_color;
  const tipe = activeChatData.tipe;
  return (
    <div
      className={`user-chat-message relative inline-block xl:w-[21vw] md:w-80 w-full bg-white dark:bg-dark-600 overflow-hidden mt-auto ${
        minimize
          ? `-ml-[1px] max-w-[120px] h-[30px] !border-t-0`
          : "md:ml-2.5 md:max-w-[340px] h-full rounded-t-lg shadow-[0_0_15px_0]"
      } shadow-[#00000040] border !border-b-0 border-[#ccc] dark:border-white/10`}
    >
      <div
        className={`flex items-center justify-between border-b-[#e8e8e8] dark:border-b-white/10 ${
          minimize
            ? "py-0.5 px-0.5 h-[30px] text-primary-900 dark:text-white/90 bg-white dark:bg-dark-900"
            : "h-[39px] py-2 px-3 border-b rounded-t-lg text-white dark:text-white/90 bg-primary-900 dark:bg-dark-900"
        }`}
      >
        <Tooltip
          overlayClassName="min-w-[350px] max-w-[350px] chat-custom-tooltip"
          color="#fff"
          title={
            <div className="text-[15px] font-bold flex items-center">
              <div className="w-[88px] h-[88px] rounded-md overflow-hidden">
                <AvatarProfile
                  user={{
                    image: activeChatData.avatar,
                    name: HTMLEntities.decode(
                      sanitizeString(activeChatData.name)
                    ),
                    status_color: status_color,
                  }}
                  className="w-[88px] h-[88px]"
                  style={
                    tipe === "project"
                      ? {
                          color: status_color || "#000000",
                          background: status_color || "#000000",
                        }
                      : {}
                  }
                />
              </div>
              <div className="pl-4 text-left w-[calc(100%-96px)]">
                <Header
                  level={5}
                  className="!mb-0 !text-xl font-semibold !text-primary-900 dark:!text-white/90"
                >
                  {activeChatData.tipe === CHAT_PROJECT_MESSAGING_TABS.client
                    ? "Client: "
                    : ""}
                  {HTMLEntities.decode(sanitizeString(activeChatData.name))}
                </Header>
                {Object.values(CHAT_PROJECT_MESSAGING_TABS).includes(
                  activeChatData.tipe
                ) && (
                  <Typography className="text-sm text-[#777] font-medium">
                    {activeChatData.project_customer_name}
                  </Typography>
                )}
              </div>
            </div>
          }
        >
          <div
            className={`text-[15px] font-bold flex items-center ${
              minimize ? "max-w-[calc(100%-25px)]" : "max-w-[calc(100%-90px)]"
            }`}
            onClick={() => {
              updateChat("id_" + activeChatData.user_id.toString(), {
                data: activeChatData,
                minimize: false,
              });
              const { users: chatUsers } = useGlobalData.getState().chat;

              let activeChatTab =
                activeChatData.tipe as keyof IGlobalChat["users"];

              if (activeChatTab === "users") {
                activeChatTab =
                  CHAT_DEFAULT_TABS.persons as keyof IGlobalChat["users"];
              }

              const newChatUsers =
                (chatUsers[activeChatTab] as IChatUsersApiResponse["all"]) ||
                [];
              setChatUser({
                [activeChatTab]: newChatUsers.map((newChatUser) => {
                  if (
                    newChatUser.user_id?.toString() ===
                    activeChatData.user_id?.toString()
                  ) {
                    return { ...newChatUser, unread_count: 0 };
                  }
                  return newChatUser;
                }),
              });

              if (
                [CHAT_DEFAULT_TABS.persons, CHAT_DEFAULT_TABS.group].includes(
                  activeChatTab
                )
              ) {
                const newUsers =
                  (chatUsers[
                    CHAT_DEFAULT_TABS.chats as keyof IGlobalChat["users"]
                  ] as IChatUsersApiResponse["all"]) || [];
                setChatUser({
                  [CHAT_DEFAULT_TABS.chats as keyof IGlobalChat["users"]]:
                    newUsers.map((newChatUser) => {
                      if (
                        newChatUser.user_id?.toString() ===
                        activeChatData.user_id?.toString()
                      ) {
                        return { ...newChatUser, unread_count: 0 };
                      }
                      return newChatUser;
                    }),
                });
              }
            }}
          >
            <div className="w-5 h-5 min-w-[20px] rounded-full overflow-hidden">
              <AvatarProfile
                user={{
                  image: activeChatData.avatar,
                  name: HTMLEntities.decode(
                    sanitizeString(activeChatData.name)
                  ),
                  status_color: status_color,
                }}
                className="w-5 h-5 min-w-[20px]"
                style={
                  tipe === "project"
                    ? {
                        color: status_color || "#000000",
                        background: status_color || "#000000",
                      }
                    : {}
                }
              />
            </div>
            <div
              className={`pl-1.5 text-13 font-semibold truncate ${
                minimize ? "text-primary-900 dark:text-white/90" : "text-white"
              }`}
            >
              <span
                onClick={() => {
                  if (
                    Object.values(CHAT_PROJECT_MESSAGING_TABS).includes(
                      activeChatData.tipe
                    )
                  ) {
                    navigate(
                      `${routes?.MANAGE_PROJECT.url}/${activeChatData?.user_id}`
                    );
                  }
                }}
              >
                {activeChatData.tipe === CHAT_PROJECT_MESSAGING_TABS.client
                  ? "Client: "
                  : ""}
                {HTMLEntities.decode(sanitizeString(activeChatData.name))}
              </span>
            </div>
          </div>
        </Tooltip>
        <ul className="flex items-center gap-0.5">
          <ButtonWithTooltip
            tooltipPlacement="top"
            tooltipTitle={_t("Chat Settings")}
            icon="fa-regular fa-cog"
            className={`w-5 !bg-transparent hover:!bg-black/50 focus:!bg-black/50 rounded-sm !h-5 overflow-hidden ${
              minimize ? "hidden" : "flex"
            }`}
            iconClassName="text-base flex w-3.5 h-3.5 !text-white group-hover/buttonHover:!text-white"
            onClick={() => {
              if (!activeChatData.is_user_deleted) {
                setIsChatUserSettingForm(true);
              }
            }}
          />
          {groupAdmin && (
            <ButtonWithTooltip
              tooltipPlacement="top"
              tooltipTitle={
                currentUserIsAdmin ? _t("Edit Group") : _t("View Group")
              }
              icon={`fa-regular ${
                currentUserIsAdmin ? "fa-pencil" : "fa-info"
              }`}
              className={`w-5 !bg-transparent hover:!bg-black/50 focus:!bg-black/50 rounded-sm !h-5 overflow-hidden ${
                minimize ? "hidden" : "flex"
              }`}
              iconClassName="text-base flex w-3.5 h-3.5 !text-white group-hover/buttonHover:!text-white"
              onClick={() => {
                if (!activeChatData.is_user_deleted) {
                  setIsChatUserForm(true);
                }
              }}
            />
          )}
          <ButtonWithTooltip
            tooltipPlacement="top"
            tooltipTitle={_t("Minimize")}
            icon="fa-regular fa-minus"
            className={`w-5 !bg-transparent hover:!bg-black/50 focus:!bg-black/50 rounded-sm !h-5 overflow-hidden ${
              minimize ? "hidden" : "flex"
            }`}
            iconClassName="text-base flex w-3.5 h-3.5 !text-white group-hover/buttonHover:!text-white"
            onClick={() => {
              updateChat("id_" + activeChatData.user_id.toString(), {
                data: activeChatData,
                minimize: true,
              });
            }}
          />
          <ButtonWithTooltip
            tooltipPlacement="top"
            tooltipTitle={_t("Close")}
            icon="fa-regular fa-close"
            className={`rounded-sm !bg-transparent overflow-hidden w-5 !h-5 ${
              minimize
                ? ""
                : "hover:!bg-black/50 focus:!bg-black/50 !text-white"
            }`}
            iconClassName={`text-base flex w-3.5 h-3.5 group-hover/buttonHover:rotate-90 ease-in-out duration-300 ${
              minimize
                ? "!text-black group-hover/buttonHover:!text-black"
                : "!text-white group-hover/buttonHover:!text-white"
            }`}
            onClick={() => {
              openChat(activeChatData, false);
            }}
          />
        </ul>
      </div>

      <MessageList
        activeChatData={activeChatData}
        search={search}
        minimize={minimize}
      />
      {groupAdmin && (
        <div
          className={`absolute z-10 top-0 overflow-hidden flex items-end shadow rounded-t-lg !pb-0 ${
            isChatUserForm ? "h-full w-full pb-[30px]" : "h-0 w-0 pb-0"
          }`}
        >
          <div
            className={`block absolute bg-black/20 rounded-t-lg ${
              isChatUserForm ? "h-full w-full" : "h-0 w-0"
            }`}
            onClick={() => {
              setIsChatUserForm(false);
            }}
          ></div>
          <div
            className={`relative bg-white h-auto w-full transition-all ease-in-out duration-300 dark:bg-dark-600 ${
              isChatUserForm ? "bottom-0" : "-bottom-[100%]"
            }`}
          >
            {isChatUserForm && (
              <UserForm
                adminData={groupAdmin}
                initialValues={{
                  group_name: HTMLEntities.decode(
                    sanitizeString(activeChatData.name)
                  ),
                  group_image: activeChatData.avatar,
                  group_members:
                    activeChatData.group_user?.filter(
                      (groupUser: IChatUsersApiResponseRecordGroupUser) =>
                        groupUser.user_id.toString() !==
                        groupAdmin.user_id.toString()
                    ) || [],
                }}
                onClickDeleteGroup={async () => {
                  try {
                    const apiParams = await getWebWorkerApiParams({
                      otherParams: {
                        group_id: activeChatData.user_id,
                      },
                    });

                    const chatResponse = (await webWorkerApi({
                      url: globalChat.CHAT_GROUP.delete,
                      method: "post",
                      data: apiParams,
                    })) as IEditChatGroupApiResponse;

                    if (chatResponse.success) {
                      if (FBDatabase.companyMessageRefValueSet) {
                        const { user } = useGlobalData.getState();
                        set(FBDatabase.companyMessageRefValueSet, {
                          data: "group_deleted",
                          group_id: chatResponse.group_id,
                          members: activeChatData.group_user?.map(
                            (groupUser: IChatUsersApiResponseRecordGroupUser) =>
                              groupUser.user_id.toString()
                          ),
                          ke: "Public",
                          tipe: "group",
                          company_id: user?.company_id,
                        });
                      }
                      openChat(activeChatData, false);
                    } else {
                      notification.error({
                        description:
                          chatResponse.message || "Something went wrong!",
                      });
                    }
                  } catch (error) {
                    notification.error({
                      description: (error as Error).message,
                    });
                  } finally {
                    setIsChatUserForm(false);
                  }
                }}
                onClose={() => {
                  setIsChatUserForm(false);
                }}
                onSubmit={async (values) => {
                  try {
                    let groupMembers: {
                      [key: string]: string;
                    } = {
                      [`group_members[0]`]: groupAdmin.user_id.toString(),
                    };

                    values.group_members?.forEach((members, index) => {
                      groupMembers = {
                        ...groupMembers,
                        [`group_members[${index + 1}]`]:
                          members.user_id.toString(),
                      };
                    });

                    let params: {
                      avatar: string;
                      group_name?: string;
                      group_id?: string;
                    } = {
                      avatar: values.group_image,
                      // group_name: HTMLEntities.encode(values.group_name), // in future this add when Node API implemented
                      group_name: values.group_name,
                      group_id: activeChatData.user_id,
                      ...groupMembers,
                    };

                    const apiParams = await getWebWorkerApiParams({
                      otherParams: params,
                    });

                    const chatResponse = (await webWorkerApi({
                      url: globalChat.CHAT_GROUP.update,
                      method: "post",
                      data: apiParams,
                    })) as IEditChatGroupApiResponse;

                    if (chatResponse.success) {
                      if (FBDatabase.companyMessageRefValueSet) {
                        const { user } = useGlobalData.getState();
                        set(FBDatabase.companyMessageRefValueSet, {
                          data: "group_updated",
                          group_id: chatResponse.group_id,
                          avatar: chatResponse.group_data.avatar,
                          name: chatResponse.group_data.name,
                          members:
                            chatResponse.group_data.group_user?.map(
                              (groupUser) => groupUser.user_id?.toString()
                            ) || [],
                          ke: "Public",
                          tipe: "group",
                          company_id: user?.company_id,
                        });
                      }
                    } else {
                      notification.error({
                        description:
                          chatResponse.message || "Something went wrong!",
                      });
                    }
                  } catch (error) {
                    notification.error({
                      description: (error as Error).message,
                    });
                  } finally {
                    setIsChatUserForm(false);
                  }
                }}
                readOnly={!currentUserIsAdmin}
                formBodyClassName={
                  !currentUserIsAdmin
                    ? "max-h-[calc(100vh-273px)] h-[357px] view-group-setting"
                    : "max-h-[calc(100vh-312px)] h-[316px] edit-group-setting"
                }
                chat_id={activeChatData.user_id}
              />
            )}
          </div>
        </div>
      )}

      <div
        className={`absolute top-0 w-full flex items-end rounded-t-lg overflow-hidden z-10 ${
          isChatUserSettingForm ? "h-full" : "h-0"
        }`}
      >
        <div
          className={`block absolute bg-black/20 rounded-t-lg ${
            isChatUserSettingForm ? "h-full w-full" : "h-0 w-0"
          }`}
          onClick={() => {
            setIsChatUserSettingForm(false);
          }}
        ></div>
        <div
          className={`relative bg-white rounded-t-lg h-auto w-full transition-all ease-in-out duration-300 dark:bg-dark-900 ${
            isChatUserSettingForm ? "bottom-0" : "-bottom-[100%]"
          }`}
        >
          <div className="flex items-center justify-between py-2 px-1.5 border-b border-b-[#e8e8e8]">
            <div className="flex items-center">
              <div className="w-6 h-6 bg-[#e4ecf68c] dark:bg-dark-500 dark:text-white/90 text-primary-900 flex items-center justify-center rounded-full">
                <FontAwesomeIcon
                  className="text-base flex w-3.5 h-3.5"
                  icon="fa-regular fa-comments"
                />
              </div>
              <Header level={5} className="pl-1.5 !text-13 !mb-0 font-semibold">
                {_t("Chat Setting")}
              </Header>
            </div>
            <div className="flex items-center">
              <CloseButton
                iconClassName="!w-4 !h-4 "
                onClick={() => {
                  !isSubmiting && setIsChatUserSettingForm(false);
                }}
                isLoading={isSubmiting}
                className={
                  isSubmiting ? "!bg-[#00000040] hover:!bg-[#f0f0f0]" : ""
                }
              />
            </div>
          </div>
          <div className="py-2 px-2.5 overflow-y-auto overflow-hidden">
            <InlineField
              label={_t("Which device does this happen on?")}
              labelPlacement="top"
              isRequired
              field={
                <div className="w-full">
                  <RadioGroupList
                    options={[
                      {
                        value: "1",
                        label: _t("All Messages"),
                      },
                      ...(activeChatData.tipe !== "users"
                        ? [
                            {
                              value: "2",
                              label: _t("@ Mentions"),
                            },
                          ]
                        : ([] as IRadioGroupProps["options"])),
                      {
                        value: "0",
                        label: _t("Mute"),
                      },
                    ]}
                    className="chat-radio-messages "
                    name="platform"
                    formInputClassName="!px-0"
                    onChange={({ target: { value } }) => {
                      setNotificationType(value);
                    }}
                    value={notificationType}
                    disabled={isSubmiting}
                  />
                </div>
              }
            />
          </div>
          <div className="p-1">
            <PrimaryButton
              buttonText={_t("Save")}
              className="w-full primary-btn justify-center"
              isLoading={isSubmiting}
              disabled={isSubmiting}
              onClick={async () => {
                setIsSubmiting(true);
                try {
                  const apiParams = await getWebWorkerApiParams({
                    otherParams: {
                      notification_type: notificationType,
                      type: activeChatData.tipe,
                      chat_id: activeChatData.user_id,
                      type_id:
                        CHAT_DEFAULT_TAB_IDs[
                          (activeChatData.tipe ||
                            "") as keyof typeof CHAT_DEFAULT_TAB_IDs
                        ] ?? "",
                    },
                  });

                  const chatResponse = (await webWorkerApi({
                    url: globalChat.update_setting,
                    method: "post",
                    data: apiParams,
                  })) as IEditChatSettingApiResponse;
                  if (chatResponse.success) {
                    setChatUser({
                      [activeChatData.tipe as keyof IInitialGlobalData["chat"]["users"]]:
                        chartUsers.map((chartUser) => {
                          if (
                            chartUser.user_id.toString() ===
                            activeChatData.user_id.toString()
                          ) {
                            return {
                              ...chartUser,
                              notification_type: notificationType,
                            };
                          }
                          return chartUser;
                        }),
                    });
                    updateChat("id_" + activeChatData.user_id.toString(), {
                      data: {
                        ...activeChatData,
                        notification_type: Number(notificationType),
                      },
                      minimize,
                    });
                    setIsChatUserSettingForm(false);
                  } else {
                    notification.error({
                      description:
                        chatResponse.message || "Something went wrong!",
                    });
                  }
                } catch (error) {
                  notification.error({
                    description: (error as Error).message,
                  });
                }
                setIsSubmiting(false);
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(ChatBox);
