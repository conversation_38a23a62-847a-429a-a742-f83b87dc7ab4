import { memo, useEffect, useState, useRef } from "react";
import { useLoaderData, useNavigate } from "@remix-run/react";

// Shared
import { <PERSON><PERSON> } from "~/shared/components/atoms/button";
import { Typography } from "~/shared/components/atoms/typography";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { Search } from "~/shared/components/atoms/search";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import ActivityNotification from "~/shared/components/organisms/sidebars/activityNotification";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import AddNewTask from "~/shared/components/organisms/sidebars/addNewTask";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { CHAT_PLAN_IDS } from "~/shared/components/organisms/layoutSection/footer/chat/constants";

// Zustand
import { getGlobalUser } from "~/zustand/global/user/slice";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import {
  getCurrentMenuModule,
  isExpiredAuthorization,
} from "~/zustand/global/config/slice";
import { setAuthorizationExpired } from "~/zustand/global/config/action";

// Other
import FooterChat from "./chat";
import { Number } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import { routes } from "~/route-services/routes";
import { loader } from "~/routes-flat/_module";

// const isOpenSesameEnabled = true; // Set this to true if you want to enable Open Sesame Cell Cureently false as for demo purpose by JS.

const MainFooter = () => {
  const { authorization } = useLoaderData<typeof loader>();
  const [viewSearch, setViewSearch] = useState<boolean>(false);
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const {
    user_subscription,
    group_id,
    demo_mode,
    company_id,
    full_name,
    email,
    phone,
    user_id,
  } = user || {};
  const currentModule = getCurrentMenuModule();
  const { module_key } = currentModule || {};
  const { checkGlobalModulePermissionByKey } = useGlobalModule();

  const { _t } = useTranslation();
  const navigate = useNavigate();

  const companyChatModulePermission = checkGlobalModulePermissionByKey(
    CFConfig.company_chat_module
  );

  const directoryModulePermission = checkGlobalModulePermissionByKey(
    CFConfig.directory_module
  );

  const todoModulePermission = checkGlobalModulePermissionByKey(
    CFConfig.todo_module
  );

  const calenderModulePermission = checkGlobalModulePermissionByKey(
    CFConfig.corporate_calendar_module
  );

  const hasAnyWritePermission =
    !["no_access", "read_only"].includes(directoryModulePermission) ||
    !["no_access", "read_only"].includes(todoModulePermission) ||
    !["no_access", "read_only"].includes(calenderModulePermission);

  const isNotChatPlanIdsAvailable = (
    Object.values(CHAT_PLAN_IDS) as (number | undefined)[]
  ).includes(group_id);

  const [calendarOpen, setCalendarOpen] = useState<boolean>(false);

  const [isOpen, setIsOpen] = useState<boolean>(false);

  const [calendarUrl, setCalendarUrl] = useState<string>("");
  const chatboxRef = useRef<HTMLUListElement | null>(null);

  function getFormattedDate() {
    const now = new Date();

    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0"); // Months are zero-based, so we add 1
    const day = String(now.getDate()).padStart(2, "0");

    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");

    return `${year}-${month}-${day}_${hours}:${minutes}:${seconds}`;
  }

  useEffect(() => {
    const setCalendarIframeUrl = async () => {
      if (
        authorization &&
        typeof window !== "undefined" &&
        window.ENV.PANEL_URL
      ) {
        let tempAuthorization = authorization;
        const isExpired = isExpiredAuthorization();
        if (isExpired) {
          const response = (await webWorkerApi({
            url: "/api/auth/token",
          })) as IGetTokenFromNode;
          if (response.success) {
            tempAuthorization = response.data.accessToken;
            setAuthorizationExpired(response.data.accessTokenExpired);
          }
        }
        const pathname = "manage_calendar.php";
        const newURL = new URL("/" + pathname, window.ENV.PANEL_URL);
        newURL.searchParams.set("date_time", getFormattedDate());
        newURL.searchParams.set("authorize_token", tempAuthorization);
        newURL.searchParams.set("iframecall", "1");
        newURL.searchParams.set("from_remix", "1");
        newURL.searchParams.set("web_page", pathname);
        setCalendarUrl(newURL.toString());
        return;
      }
      setCalendarUrl("");
    };
    setCalendarIframeUrl();
  }, [authorization]);

  // const isViewFundBoxLink = [
  //   CFConfig.bill_module,
  //   CFConfig.invoice_module,
  // ].includes(module_key || "");

  return (
    <footer className="fixed w-full bottom-0 border-t border-[#ccc] dark:border-white/10 shadow-[0_-2px_10px_-6px] shadow-primary-900 dark:shadow-white/10 z-[1001] dark:bg-dark-600">
      <div
        className="footer-content flex bg-white dark:bg-dark-600"
        id="footer-content"
      >
        {Number(user_subscription) > 1 &&
          !isNotChatPlanIdsAvailable &&
          companyChatModulePermission !== "no_access" && <FooterChat />}
        <div className="footer-center md:flex-[1_0_0%] border-[#0000001a] border-t dark:border-white/10 md:block">
          <div className="flex justify-between">
            <div className="md:max-w-[220px] w-full pr-2.5 ml-auto md:flex hidden">
              <Search
                placeholder={_t("Search here...")}
                variant="borderless"
                className="search-input-borderless relative dark:bg-white/5 rounded-md border border-solid border-gray-200 focus:!border-primary-900 hover:!border-primary-900 focus-within:!border-primary-900 dark:before:bg-[#696b6e] h-[30px] footer-search"
                allowClear={true}
                onSearch={(value: string) => {
                  const trimmedValue = value.trim();
                  if (!trimmedValue) return;
                  // This logic change when remix main dashboard complete
                  if (window.location.href !== routes.DASHBOARD.url) {
                    const newUrl = new URL("index.php", window.ENV.PANEL_URL);
                    const newUrlParams = new URLSearchParams({
                      q: value,
                    })?.toString();
                    newUrl.search = newUrlParams;
                    window.location.href = newUrl.toString();
                  }
                }}
              />
            </div>
          </div>
        </div>

        <div className="footer-right md:w-auto w-full flex-[0_0_auto] border-l border-[#0000001a] dark:border-white/10 border-t md:block">
          <div className="footer-control flex md:justify-between justify-around px-[5px] gap-[5px]">
            <div className="flex items-center justify-center md:hidden">
              <Button
                htmlType="button"
                className={`w-[30px] !m-0 p-0 group/handle-visible-change max-w-[30px] hover:!bg-[#384a66] max-h-[30px] ${
                  viewSearch ?? ""
                }`}
                type="text"
                onClick={() => {
                  setViewSearch((prev) => !prev);
                }}
              >
                <FontAwesomeIcon
                  className="text-base w-4 h-4 !text-[#878a92] group-hover/handle-visible-change:!text-white dark:!text-white/90"
                  icon="fa-regular fa-magnifying-glass"
                />
              </Button>
              <div
                className={`md:static md:translate-x-0 sm:px-0 px-2.5 absolute w-full z-10 md:!bg-transparent bg-white dark:bg-[#1E2732] -translate-x-full ease-in left-0 ${
                  viewSearch ? "translate-x-0" : ""
                }`}
              >
                <div className="flex items-center">
                  <Button
                    htmlType="button"
                    className="w-6 group/handle-visible-change max-w-6 max-h-6"
                    type="text"
                    onClick={() => {
                      setViewSearch((prev) => !prev);
                    }}
                  >
                    <FontAwesomeIcon
                      className="text-base w-4 h-4 !text-[#878a92] group-hover/handle-visible-change:text-primary-900 dark:!text-white/90"
                      icon="fa-regular fa-angle-left"
                    />
                  </Button>
                  <Search
                    placeholder={_t("Search here...")}
                    variant="borderless"
                    className="search-input-borderless relative dark:bg-white/5 rounded-md border border-solid border-gray-200 focus:!border-primary-900 hover:!border-primary-900 focus-within:!border-primary-900 dark:before:bg-[#696b6e] h-[30px] footer-search"
                    allowClear={true}
                    onSearch={(value: string) => {
                      // This logic change when remix main dashboard complete
                      if (window.location.href !== routes.DASHBOARD.url) {
                        const newUrl = new URL(
                          "index.php",
                          window.ENV.PANEL_URL
                        );
                        const newUrlParams = new URLSearchParams({
                          q: value,
                        })?.toString();
                        newUrl.search = newUrlParams;
                        window.location.href = newUrl.toString();
                      }
                    }}
                  />
                </div>
              </div>
            </div>
            {/* {isOpenSesameEnabled && (
              <Button
                onClick={() => {
                  if (!user_id?.toString()) {
                    return;
                  }
                  window.OpenSesameCellConfig = {
                    config: {
                      logo: "",
                      primaryColor: "#ffffff",
                      secondaryColor: "#223558",
                      placeholderText:
                        "Ask me anything about your account, billing, or support...",
                      suggestedQuestions: [
                        "How do I update my billing information?",
                        "Show me my recent transactions.",
                        "What are the support hours?",
                        "Can I get a summary of my account activity?",
                      ],
                      borderRadius: 32,
                      glowColor: "#223558",
                      authToken: {
                        bearer: authorization,
                      },
                      userId: user_id.toString(),
                      cellId: "790b485a-1764-4670-bd94-cc6de1c08e9f",
                      readOnly: true,
                    },
                  };
                  const s = document.createElement("script");
                  s.src = "https://open-sesame-cell.vercel.app/widget.js";
                  s.async = true;
                  document.head.appendChild(s);
                }}
              >
                Open Sesame Cell
              </Button>
            )} */}
            <Button
              className={`transition-all duration-300 ease-in-out md:w-fit w-[30px] !h-[30px] bg-transparent group/buttonHover hover:!bg-[#384a66] dark:hover:!bg-dark-900 border-0 px-2 gap-1 ${
                isOpen ? "!bg-[#384a66] dark:!bg-dark-900" : ""
              }`}
              onClick={() => setIsOpen(!isOpen)}
            >
              <Typography
                className={`!text-[#878a92] dark:text-[#dcdcdd] group-hover/buttonHover:!text-white md:!inline-block !hidden ${
                  isOpen ? "!text-white" : ""
                }`}
              >
                {_t("Ask Clark")}
              </Typography>
              <FontAwesomeIcon
                className={`text-base flex w-6 h-6 !text-[#878a92] dark:text-[#dcdcdd] group-hover/buttonHover:hidden ${
                  isOpen ? "!hidden" : ""
                } relative top-0.5`}
                icon="fa-regular fa-message-bot"
              />
              <FontAwesomeIcon
                className={`text-base hidden w-6 h-6 text-white group-hover/buttonHover:flex ${
                  isOpen ? "!flex" : ""
                } relative top-0.5`}
                icon="fa-solid fa-message-bot"
              />
            </Button>
            {!["no_access"].includes(calenderModulePermission) && (
              <ButtonWithTooltip
                tooltipPlacement="top"
                tooltipTitle={_t("Calendar")}
                icon="fa-regular fa-calendar-days"
                className={`transition-all duration-300 ease-in-out !w-[30px] !h-[30px] !bg-transparent hover:!bg-[#384a66] dark:hover:!bg-dark-900 focus:!bg-primary-900 focus:!text-white ${
                  calendarOpen ? "!bg-[#384a66] dark:!bg-dark-900" : ""
                }`}
                iconClassName={`text-base flex w-[18px] h-[18px] !text-[#878a92] dark:text-[#dcdcdd] group-hover/buttonHover:!text-white ${
                  calendarOpen ? "!text-white" : ""
                }`}
                onClick={() => setCalendarOpen(true)}
              />
            )}
            {hasAnyWritePermission && <AddNewTask />}
            <ActivityNotification
              enableOpenInNewTab={Number(window.ENV.ENABLE_ALL_CLICK)}
            />
          </div>
        </div>
      </div>
      {calendarOpen && (
        <IframeModal
          isOpen={calendarOpen}
          widthSize="100vw"
          onCloseModal={() => setCalendarOpen(false)}
          modalBodyClass="p-0"
          header={{
            title: _t("Calendar"),
            icon: (
              <FontAwesomeIcon
                className="w-3.5 h-3.5"
                icon="fa-regular fa-calendar-note"
              />
            ),
            closeIcon: true,
          }}
          iframeProps={{
            src: calendarUrl,
          }}
        />
      )}

      {isOpen && (
        <div className="chatbot fixed bottom-[40px] right-4 w-11/12 max-w-[420px] rounded-[15px] shadow-[0_0_128px_0_rgba(0,0,0,0.1),_0_32px_64px_-48px_rgba(0,0,0,0.5)] bg-white z-[99] origin-bottom-right transition-all">
          <header className="bg-primary-900 text-white p-0 rounded-t-[15px] flex items-center justify-between">
            <ButtonWithTooltip
              tooltipPlacement="top"
              tooltipTitle=""
              icon="fa-regular fa-xmark"
              className={`w-6 h-6 flex items-center justify-content-center !bg-transparent hover:!bg-transparent border-0 outline-0 absolute top-1 right-1 z-[1]`}
              iconClassName="text-base w-4 h-4 !text-primary-900"
              onClick={() => setIsOpen(!isOpen)}
            />
          </header>

          <ul
            ref={chatboxRef}
            className="chatbox p-0 space-y-4 h-[610px] max-h-[calc(100vh-100px)] overflow-auto rounded-[15px]"
          >
            <iframe
              src={`https://embed.chatnode.ai/a63182b89a20a55e?data-name=${full_name}&data-email=${email}&data-phone=${phone}`}
              width="100%"
              height="700"
              style={{ visibility: "visible", border: "none" }}
              onLoad={function (e) {
                e.currentTarget.style.visibility = "visible";
              }}
              className="block w-100 p-0 m-0 h-[610px] max-h-[calc(100vh-100px)] rounded-[15px]"
              allow="autoplay; clipboard-read; clipboard-write"
            ></iframe>
          </ul>
        </div>
      )}
    </footer>
  );
};

export default memo(MainFooter);
