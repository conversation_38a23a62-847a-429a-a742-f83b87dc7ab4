import { ThunkDispatch, UnknownAction } from "@reduxjs/toolkit";
import { Dispatch, SetStateAction } from "react";
import { UseSelector } from "react-redux";

declare global {
  type TFileAttachmentTabsValues =
    | "new"
    | "gallery"
    | "google"
    | "dropbox"
    | "drive"
    | "url";
  interface IFileSelectProps {
    selectedTab: FileAttachmentTabsValues | "";
    setSelectedTab: React.Dispatch<
      React.SetStateAction<FileAttachmentTabsValues | "">
    >;
    options?: Array<FileAttachmentTabsValues>;
    setSelectedFileData?: Dispatch<SetStateAction<IAddFileRequestBodyChild[]>>;
    handleSelectImage?: (
      selectedFile: FilePhotoRightDetail,
      is_checked: boolean
    ) => void;
    selectedFiles?: FilePhotoRightDetail[] | undefined;
    selectedAllFiles?: boolean;
    selectedProjects?: IProject | undefined | null;
    selectedCustomer?: IGetGlobalDirectoryData | null | undefined;
  }

  interface IValidationParams {
    module_key: string;
    image_resolution: string;
    file_support_module_access: TModuleAccessStatus;
    module_access: TModuleAccessStatus;
    module_id: number;
    date_format: string;
  }

  interface ICustomFileSelectProps {
    projectid?: string | number;
    selectedTab?: FileAttachmentTabsValues | "";
    setSelectedTab: React.Dispatch<
      React.SetStateAction<FileAttachmentTabsValues | "">
    >;
    options?: Array<FileAttachmentTabsValues>;
    setSelectedFileData?: Dispatch<SetStateAction<IAddFileRequestBodyChild[]>>;
    handleSelectImage?: (selectedFile: IFile, is_checked: boolean) => void;
    selectedFiles?: IFile[] | undefined;
    selectedAllFiles?: boolean;
    selectedProjects?: IProject | undefined | null;
    selectedCustomer?: IGetGlobalDirectoryData | null | undefined;
    useAppSelector?: UseSelector<{
      selectCustomerData: TInitialStateSelectCustomers;
    }>;
    selectedProjectDropbox?: ISelectedFolder | undefined;
    dispatch?: ThunkDispatch<
      { selectCustomerData: TInitialStateSelectCustomers },
      undefined,
      UnknownAction
    > &
      Dispatch<UnknownAction>;
    isSingleSelect?: boolean;
    setSelectedFiles?: Dispatch<SetStateAction<IFile[]>>;
    validationParams: IValidationParams;
    addFilesRes?: IAddDirFilePhotoRes | {};
    load: boolean;
    isLoad: (load: boolean) => void;
    projectInputProps?: ICustomFileTabsSelectProps["projectInputProps"];
    onlyImage?: number;
    multiple?: boolean;
    filesData?: IFile[];
  }

  interface IProjectInputProps {
    placeholder?: IGallaryFilterInputProps["placeholder"];
    checkBoxProps?: TCheckboxProps;
    label?: IGallaryFilterInputProps["label"];
    filterCustomerClassName?: IGallaryFilterInputProps["filterClassName"];
    fieldCustomerClassName?: IGallaryFilterInputProps["fieldClassName"];
    filterProjectClassName?: IGallaryFilterInputProps["filterClassName"];
    fieldProjectClassName?: IGallaryFilterInputProps["fieldClassName"];
    projectSidebarProps?: IProjectSidebarProps;
  }

  interface IProjectSidebarProps {
    genericProjects?: ISelectProjectProps["genericProjects"];
    searchPlcaeHolder?: ISelectProjectProps["searchPlcaeHolder"];
    isShowProjectType?: ISelectProjectProps["isShowProjectType"];
  }
  interface ICustomFileTabsSelectProps {
    projectid?: string | number;
    selectedTab?: FileAttachmentTabsValues | "";
    setSelectedTab: React.Dispatch<
      React.SetStateAction<FileAttachmentTabsValues | "">
    >;
    options?: Array<FileAttachmentTabsValues>;
    setSelectedFileData?: Dispatch<SetStateAction<IAddFileRequestBodyChild[]>>;
    // setSelectedFilesFromNewFilestab: Dispatch<SetStateAction<NewFileObj[]>>;
    handleSelectImage?: (selectedFile: IFile, is_checked: boolean) => void;
    selectedFiles?: IFile[] | undefined;
    selectedAllFiles?: boolean;
    selectedProjects?: IProject | undefined | null;
    selectedCustomer?: IGetGlobalDirectoryData | null | undefined;
    useAppSelector?: UseSelector<{
      selectCustomerData: TInitialStateSelectCustomers;
    }>;
    selectedProjectDropbox?: ISelectedFolder | undefined;
    dispatch?: ThunkDispatch<
      { selectCustomerData: TInitialStateSelectCustomers },
      undefined,
      UnknownAction
    > &
      Dispatch<UnknownAction>;
    isSingleSelect?: boolean;
    setSelectedFiles?: Dispatch<SetStateAction<IFile[]>>;
    validationParams: IValidationParams;
    addFilesRes?: IAddDirFilePhotoRes | {};
    load: boolean;
    isLoad: (load: boolean) => void;
    setAvailableFiles: (availableFiles: boolean) => void;
    projectInputProps?: IProjectInputProps;
    onlyImage?: number;
    multiple?: boolean;
    filesData?: IFile[];
    setSelectedFilesFromNewFilestab?: Dispatch<SetStateAction<INewFileObj[]>>;
    isFileAndPhotoModule?: boolean;
  }

  interface SignedUrls {
    [key: string]: string;
  }

  interface IFileObject {
    file_upload: string | undefined;
    file_url: string;
    signedUrl: string;
    file_name: string | undefined;
    file_type: string | undefined;
  }
  interface IFilePhotoFile {
    image: string;
    iconClassName: string;
    icon: IFontAwesomeIconProps["icon"];
    name: string;
    file_tags: Array<Partial<FilePhotoTag>>;
    date: string;
    is_image: boolean;
    file_path: string;
    is_file_shared: boolean;
    image_id?: string;
  }
  interface ICustomerSelectOption<TData> {
    name: string;
    id?: number;
    value: TData;
    icon?: React.ReactNode;
    displayIcon?: React.ReactNode;
    addButton?: boolean;
    addTooltipContent?: string;
  }
}
