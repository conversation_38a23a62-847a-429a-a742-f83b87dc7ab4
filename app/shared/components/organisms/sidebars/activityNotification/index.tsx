import { memo, useEffect, useMemo, useState } from "react";

// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { LightGalleryModel } from "~/shared/components/atoms/lightGalleryModel";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Tabs } from "~/shared/components/atoms/tabs";
// Molecules
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CloseButton } from "~/shared/components/molecules/closeButton";

// Other
import NotificationTab from "./NotificationTab";
import ActivityTab from "./ActivityTab";
import {
  ACTIVITY_NOTIFICATION_TAB_KEYS,
  ACTIVITY_NOTIFICATION_TABS,
} from "./contant";
import { useTranslation } from "~/hook";
import { useSearchParams } from "@remix-run/react";

const ActivityNotification = ({
  enableOpenInNewTab = 0,
}: ActivityNotificationProps) => {
  const { _t } = useTranslation();
  const [recentActiveButton, setRecentActiveButton] = useState<string>("");

  // Detect if tab is active based on URL query param (for new tab support)
  const [searchParams, setSearchParams] = useSearchParams();

  useEffect(() => {
    const tab = searchParams.get("tab");

    if (
      (tab === ACTIVITY_NOTIFICATION_TAB_KEYS.notification ||
        tab === ACTIVITY_NOTIFICATION_TAB_KEYS.activity) &&
      recentActiveButton !== tab // Prevent re-trigger if already active
    ) {
      setRecentActiveButton(tab);
      const sp = new URLSearchParams(searchParams);
      sp.delete("tab");
      setSearchParams(sp, { replace: true });
    }
  }, []);

  // Close drawer on outside footer click
  useEffect(() => {
    if (Boolean(recentActiveButton.trim())) {
      const onClick = (e: MouseEvent) => {
        const footerTab = document.getElementsByTagName("footer")?.[0] as Node;
        const recentNotificationButton = document.getElementById(
          "recent_notification_button"
        ) as Node;
        const recentHistoryButton = document.getElementById(
          "recent_history_button"
        ) as Node;

        if (
          footerTab?.contains(e.target as Node) &&
          !recentNotificationButton?.contains(e.target as Node) &&
          !recentHistoryButton?.contains(e.target as Node)
        ) {
          setRecentActiveButton("");
        }
      };
      window.addEventListener("click", onClick);
      return () => window.removeEventListener("click", onClick);
    }
  }, [recentActiveButton]);

  const Notification =
    typeof window !== "undefined"
      ? window.Notification ||
        window.mozNotification ||
        window.webkitNotification
      : undefined;

  const { permission: notificationPermission } = Notification || {};

  const browserNotificationStepImageSrc = useMemo(() => {
    const userAgent = navigator.userAgent;
    if (userAgent.indexOf("Edg") > -1) {
      return "https://cdn.contractorforeman.net/assets/images/edg-notification-step.png";
    } else if (userAgent.indexOf("Chrome") > -1) {
      return "https://cdn.contractorforeman.net/assets/images/chrome-notification-step.png";
    } else if (userAgent.indexOf("Firefox") > -1) {
      return "https://cdn.contractorforeman.net/assets/images/firefox-notification-step.png";
    } else if (userAgent.indexOf("Safari") > -1) {
      return "https://cdn.contractorforeman.net/assets/images/safari-notification-step.png";
    } else if (
      userAgent.indexOf("Trident") > -1 ||
      userAgent.indexOf("MSIE") > -1 ||
      userAgent.indexOf("Opera") > -1
    ) {
      return "https://cdn.contractorforeman.net/assets/images/edg-notification-step.png";
    }

    return "https://cdn.contractorforeman.net/assets/images/edg-notification-step.png";
  }, []);

  const isActiveNotification =
    recentActiveButton === ACTIVITY_NOTIFICATION_TAB_KEYS.notification;
  const isActiveActivity =
    recentActiveButton === ACTIVITY_NOTIFICATION_TAB_KEYS.activity;

  return (
    <>
      <a
        href="?tab=notification"
        id="recent_notification_button"
        onClick={(e) => {
          if (
            enableOpenInNewTab &&
            (e.ctrlKey || e.metaKey || e.button === 1 || e.button === 2)
          ) {
            return;
          }
          e.preventDefault();
          setRecentActiveButton(ACTIVITY_NOTIFICATION_TAB_KEYS.notification);
        }}
        onAuxClick={(e) => {
          if (!enableOpenInNewTab) {
            e.preventDefault();
          }
        }}
        onContextMenu={(e) => {
          if (!enableOpenInNewTab) {
            e.preventDefault();
          }
        }}
      >
        <ButtonWithTooltip
          tooltipPlacement="top"
          tooltipTitle={_t("Notifications")}
          icon={
            typeof Notification !== "undefined" &&
            Notification.permission === "denied"
              ? "fa-solid fa-bell-slash"
              : "fa-regular fa-bell"
          }
          className={`transition-all duration-300 ease-in-out !w-[30px] !h-[30px] !bg-transparent hover:!bg-[#384a66] dark:hover:!bg-dark-900 focus:!bg-primary-900 focus:!text-white ${
            isActiveNotification ? "!bg-[#384a66] dark:!bg-dark-900" : ""
          }`}
          iconClassName={`text-base flex w-[18px] h-[18px] !text-[#878a92] dark:!text-[#dcdcdd] group-hover/buttonHover:!text-white ${
            isActiveNotification ? "!text-white" : ""
          }`}
        />
      </a>

      <a
        href="?tab=activity"
        id="recent_history_button"
        onClick={(e) => {
          if (
            enableOpenInNewTab &&
            (e.ctrlKey || e.metaKey || e.button === 1 || e.button === 2)
          ) {
            return;
          }
          e.preventDefault();
          setRecentActiveButton(ACTIVITY_NOTIFICATION_TAB_KEYS.activity);
        }}
        onAuxClick={(e) => {
          if (!enableOpenInNewTab) {
            e.preventDefault();
          }
        }}
        onContextMenu={(e) => {
          if (!enableOpenInNewTab) {
            e.preventDefault();
          }
        }}
      >
        <ButtonWithTooltip
          tooltipPlacement="topRight"
          tooltipTitle={_t("History")}
          icon="fa-regular fa-clock-rotate-left"
          className={`transition-all duration-300 ease-in-out !w-[30px] !h-[30px] !bg-transparent hover:!bg-[#384a66] dark:hover:!bg-dark-900 focus:!bg-primary-900 focus:!text-white ${
            isActiveActivity ? "!bg-[#384a66] dark:!bg-dark-900" : ""
          }`}
          iconClassName={`text-base flex w-[18px] h-[18px] !text-[#878a92] dark:text-[#dcdcdd] group-hover/buttonHover:!text-white ${
            isActiveActivity ? "!text-white" : ""
          }`}
        />
      </a>

      {Boolean(recentActiveButton.trim()) && (
        <Drawer
          open={Boolean(recentActiveButton.trim())}
          className="bottom-[30px] !h-[calc(100vh-31px)] z-[999]"
          width={350}
          maskClosable={true}
          onClose={() => {
            setRecentActiveButton("");
          }}
          rootClassName="drawer-open h-[calc(100vh-31px)]"
          classNames={{
            body: "!p-0",
          }}
          title={
            <div className="flex items-center flex-row-reverse w-full justify-center">
              {notificationPermission !== "granted" && (
                <div className="flex items-center justify-center rounded-full mr-2.5 bg-transparent text-white w-5 h-5">
                  <LightGalleryModel
                    zoom={true}
                    thumbnail={true}
                    backdropDuration={150}
                    showZoomInOutIcons={true}
                    actualSize={false}
                    mode="lg-slide"
                    alignThumbnails="left"
                    mousewheel={true}
                  >
                    <a
                      data-src={browserNotificationStepImageSrc}
                      href={browserNotificationStepImageSrc}
                      className="lightGalleryModel"
                      id={browserNotificationStepImageSrc}
                    >
                      <Tooltip
                        title={_t(
                          "Please click here to enable notification of browser."
                        )}
                        className="h-3.5 w-3.5 cursor-pointer"
                      >
                        <FontAwesomeIcon
                          icon="fa-regular fa-circle-info"
                          className="rounded !text-xs text-primary-900 "
                        />
                      </Tooltip>
                    </a>
                  </LightGalleryModel>
                </div>
              )}

              <Header
                level={5}
                className={`!mb-0 font-semibold !text-xs !text-[#dc3545] ${notificationPermission}`}
              >
                {notificationPermission === "default"
                  ? "Please allow browser Notifications."
                  : notificationPermission === "denied"
                  ? "Please enable browser Notifications."
                  : undefined}
              </Header>
            </div>
          }
          closeIcon={
            <CloseButton
              onClick={() => {
                setRecentActiveButton("");
              }}
            />
          }
        >
          <div className="sidebar-body dark:bg-dark-800 h-full">
            <Tabs
              className="notification-btn"
              activeKey={recentActiveButton}
              items={ACTIVITY_NOTIFICATION_TABS}
              onChange={(activeKey) => {
                setRecentActiveButton(activeKey);
              }}
            />
            <div className="h-[calc(100%-58px)]">
              {isActiveNotification ? (
                <NotificationTab
                  onClick={(isCloseDrawer) =>
                    isCloseDrawer && setRecentActiveButton("")
                  }
                />
              ) : (
                isActiveActivity && (
                  <ActivityTab onClick={() => setRecentActiveButton("")} />
                )
              )}
            </div>
          </div>
        </Drawer>
      )}
    </>
  );
};

export default memo(ActivityNotification);
