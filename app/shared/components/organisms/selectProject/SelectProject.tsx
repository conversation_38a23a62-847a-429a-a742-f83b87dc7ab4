import { useTranslation } from "~/hook";
//Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Button } from "~/shared/components/atoms/button";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Search } from "~/shared/components/atoms/search";
import { Typography } from "~/shared/components/atoms/typography";
// Molecular
import { AddButton } from "~/shared/components/molecules/addButton";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { LoadingItems } from "~/shared/components/molecules/loadingItems";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SelectItem } from "~/shared/components/molecules/selectItem";
import { SelectSingleItems } from "~/shared/components/molecules/selectSingleItems";
import { InfiniteScroll } from "~/shared/components/molecules/InfiniteScroll/InfiniteScroll";

import {
  escapeHtmlEntities,
  replaceDOMParams,
  sanitizeString,
} from "~/helpers/helper";
import { useCallback, useEffect, useState, useRef, useMemo } from "react";
import { getProject } from "~/redux/action/getProjectAction";
import debounce from "lodash/debounce";
import { getProjectModules } from "~/zustand/global-project-complete/store";
import { AddNewProject } from "../../molecules/addNewProject";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { getModuleAccess } from "~/shared/utils/helper/module";

const limit = 20;

const SelectProject = ({
  open,
  setOpen,
  isSingleSelect = false,
  selectedProjects, // multi select
  onProjectSelected,
  isRequired = true,
  genericProjects = "",
  category,
  module_key = "",
  isShowProjectType = false,
  customer_id = "",
  searchPlcaeHolder,
  addNewProjectProps = {},
  onClose,
}: ISelectProjectProps) => {
  const { _t } = useTranslation();
  const [viewSearch, setViewSearch] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [addNewProject, setAddNewProject] = useState<boolean>(false);
  const [showAddNewProject, setShowAddNewProject] = useState<boolean>(false);
  const [projectList, setProjectList] = useState<IProject[]>([]);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [start, setStart] = useState<number>(0);
  const [bottomSheetOpen, setBottomSheetOpen] = useState<boolean>(false);
  const [search, setSearch] = useState<string>("");
  const [isSearchReset, setIsSearchReset] = useState<boolean>(false);
  const [projectSearchDebounce, setProjectSearchDebounce] =
    useState<string>("");
  const [multiSelectedProjects, setMultiSelectedProjects] = useState<
    IProject[]
  >([]);
  const { getGlobalModuleByKey } = useGlobalModule();

  const ProjectModule = useMemo(
    () => getGlobalModuleByKey(CFConfig.project_module),
    [getGlobalModuleByKey]
  );
  const project_module_access = useMemo(
    () => getModuleAccess(ProjectModule),
    [ProjectModule]
  );
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);
  const gProjectModules: IProjectModules = getProjectModules();

  const handleCloseDrawer = () => {
    onClose ? onClose("closeIcon") : setOpen(false);
    setMultiSelectedProjects([]);
  };

  const onSaveProjectDrawerClose = () => {
    if (onClose) {
      onClose("saveBtn");
    } else {
      setMultiSelectedProjects([]);
      setOpen(false);
    }
  };

  const handleProjectClick = (project: IProject) => {
    if (isSingleSelect) {
      const isSelected = multiSelectedProjects.some(
        (selectedProject) => selectedProject.id == project.id
      );
      if (!isSelected) {
        onProjectSelected?.([project]);
        handleCloseDrawer();
      } else {
        onProjectSelected?.([]);
        handleCloseDrawer();
      }
    } else {
      setMultiSelectedProjects((prevProjects) => {
        if (project && prevProjects.find((p) => p.id === project.id)) {
          return prevProjects.filter((p) => p.id !== project.id);
        }
        return project ? [...prevProjects, project] : prevProjects;
      });
    }
  };

  const loadMoreProjects = async () => {
    setIsLoading(true);
    try {
      let currentStart = start;
      const getProjectListParams = {
        search: projectSearchDebounce
          ? escapeHtmlEntities(projectSearchDebounce)
          : undefined,
        start: currentStart,
        limit,
        version: "web",
        from: "panel",
        global_call: true,
        record_type: genericProjects ? genericProjects : "project",
        is_completed: module_key
          ? Number(gProjectModules?.[module_key as keyof IProjectModules]) !== 1
          : true,
        filter: {
          status: 0,
          record_type: "project",
          customer: customer_id?.toString(),
        },
        category: category && category.trim() ? category : undefined,
        ...(selectedProjects.length &&
          selectedProjects[0].id &&
          !isNaN(Number(selectedProjects[0].id)) && {
            top_project_id: selectedProjects[0].id,
          }),
      };

      const result = (await getProject(
        getProjectListParams
      )) as ISelectProjectsRes;

      if (result.success) {
        const genericProject =
          result?.data?.custom_generic_projects?.length > 0
            ? (result.data.custom_generic_projects?.map(
                (obj: { key: string; display_name: string }) => ({
                  id: obj.key,
                  project_name: obj.display_name,
                })
              ) as unknown as IProject[])
            : [];
        const responseProjects =
          currentStart <= 0
            ? genericProject?.length > 0
              ? genericProject.concat(result.data.projects)
              : result?.data.projects || []
            : result?.data.projects || [];

        const filteredData = responseProjects.filter((item1) =>
          multiSelectedProjects.some((item2) => item2.id == item1.id)
        );
        const filteredData2 = responseProjects.filter((item1) =>
          multiSelectedProjects.some((item2) => item2.id != item1.id)
        );
        const concat =
          filteredData.length > 0 &&
          filteredData2.length > 0 &&
          genericProject?.length > 0
            ? [...filteredData, ...filteredData2]
            : responseProjects;

        if (
          responseProjects &&
          !responseProjects.length &&
          customer_id &&
          !projectSearchDebounce?.trim() &&
          addNewProjectProps &&
          module_key === CFConfig.estimate_module &&
          project_module_access !== "read_only"
        ) {
          setShowAddNewProject(true);
        } else {
          setShowAddNewProject(false);
        }

        setProjectList((prevList) => [...prevList, ...concat]);
        setStart((prevStart) => prevStart + limit);

        if (
          responseProjects?.length <
          (currentStart <= 0 ? limit + genericProject?.length : limit)
        ) {
          setHasMore(false);
        } else {
          setHasMore(true);
        }
        // After loading more projects, scroll to the middle of the container
        if (scrollContainerRef.current) {
          const middleScrollPosition =
            scrollContainerRef.current.scrollHeight / 2 -
            scrollContainerRef.current.clientHeight / 2;
          scrollContainerRef.current.scrollTo({
            top: middleScrollPosition,
            behavior: "smooth",
          });
        }
      } else {
        setHasMore(false);
      }
    } catch (error) {
      setHasMore(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearchProject = useCallback(
    debounce((searchProject: string) => {
      setProjectSearchDebounce(searchProject);
    }, 1000),
    []
  );

  useEffect(() => {
    if (open || projectSearchDebounce) {
      setStart(0);
      setProjectList([]);
      setHasMore(true);
      setIsSearchReset(true);
    }
  }, [open, projectSearchDebounce]);

  useEffect(() => {
    if (isSearchReset) {
      loadMoreProjects();
      setIsSearchReset(false);
    }
  }, [isSearchReset]);

  useEffect(() => {
    if (selectedProjects && selectedProjects.length) {
      setMultiSelectedProjects(selectedProjects);
    } else {
      setMultiSelectedProjects([]);
    }
  }, [selectedProjects]);

  // confirmed with dhrumil, will remove once will get required data from BE and handle this from BE
  // useEffect(() => {
  //   if (projectList?.length) {
  //     setMultiSelectedProjects((prevSelected) => {
  //       const updatedSelection = projectList.filter((project) =>
  //         prevSelected.some((selected) => selected.id === project.id)
  //       );
  //       return updatedSelection;
  //     });
  //   }
  // }, [projectList]);

  return (
    <>
      <Drawer
        open={open}
        rootClassName="drawer-open"
        width={970}
        maskClosable={false}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4 text-primary-900"
                icon="fa-light fa-building-circle-arrow-right"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {isSingleSelect
                ? isShowProjectType
                  ? _t("Select Project/Opportunity")
                  : _t("Select Project")
                : isShowProjectType
                ? _t("Select Projects/Opportunity")
                : _t("Select Projects")}
            </Header>
          </div>
        }
        closeIcon={<CloseButton onClick={handleCloseDrawer} />}
      >
        <div className="flex lg:flex-row flex-col h-full">
          <div className="w-full xl:max-w-[630px] md:flex-[1_0_0%] border-r border-gray-300 dark:border-white/10">
            <div className="py-3 px-[15px] flex items-center whitespace-nowrap gap-3">
              <div className="flex items-center justify-center w-7 md:w-full">
                <Button
                  htmlType="button"
                  className={`w-7 !m-0 group/handle-visible-change max-w-[28px] max-h-[28px] block md:hidden ${
                    viewSearch ?? ""
                  }`}
                  type="text"
                  onClick={() => {
                    setViewSearch((prev) => !prev);
                  }}
                >
                  <FontAwesomeIcon
                    className="text-base w-[18px] h-[18px] !text-primary-gray-80 dark:!text-white/90"
                    icon="fa-regular fa-magnifying-glass"
                  />
                </Button>
                <div
                  className={`md:static md:translate-x-0 sm:px-0 px-2.5 absolute w-full z-10 md:!bg-transparent bg-white dark:bg-[#1E2732] -translate-x-full ease-in left-0 ${
                    viewSearch ? "translate-x-0" : ""
                  }`}
                >
                  <div className="flex items-center">
                    <Button
                      htmlType="button"
                      className={`w-6 group/handle-visible-change max-w-[24px] max-h-[24px] md:hidden`}
                      type="text"
                      onClick={() => {
                        setViewSearch((prev) => !prev);
                      }}
                    >
                      <FontAwesomeIcon
                        className="text-base w-[18px] h-[18px] text-primary-900/80 group-hover/handle-visible-change:text-primary-900 dark:!text-white/90"
                        icon="fa-regular fa-angle-left"
                      />
                    </Button>
                    <Search
                      placeholder={
                        searchPlcaeHolder
                          ? searchPlcaeHolder
                          : isSingleSelect
                          ? isShowProjectType
                            ? _t("Select Project/Opportunity")
                            : "Search Project"
                          : isShowProjectType
                          ? _t("Select Projects/Opportunity")
                          : _t("Select Projects")
                      }
                      variant="borderless"
                      className="search-input-borderless relative dark:bg-white/5 rounded-md border border-solid border-gray-200 focus:!border-primary-900 hover:!border-primary-900 focus-within:!border-primary-900 dark:before:bg-[#696b6e]"
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        handleSearchProject(e.target.value);
                        setSearch(e.target.value);
                      }}
                      value={search}
                      allowClear={true}
                    />
                  </div>
                </div>
              </div>
              {showAddNewProject && (
                <AddButton
                  onClick={() => {
                    setAddNewProject(true);
                  }}
                >
                  {_t("Project")}
                </AddButton>
              )}
            </div>
            <>
              <div
                className={`h-screen md:max-h-[calc(100dvh-115px)] max-h-[calc(100dvh-104px)] flex flex-col gap-1.5 overflow-y-auto`}
              >
                {isLoading && projectList.length === 0 ? (
                  <LoadingItems isAvatar={false} skeleton={20} />
                ) : projectList.length === 0 ? (
                  <div className="md:h-[calc(100dvh-120px)] h-[calc(100dvh-200px)] flex justify-center items-center">
                    <NoRecords
                      image={`${window.ENV.CDN_URL}assets/images/no-records-projects.svg`}
                    />
                  </div>
                ) : (
                  <InfiniteScroll
                    loadMore={loadMoreProjects}
                    hasMore={hasMore}
                    isLoading={isLoading}
                    loadingComponent={
                      <LoadingItems isAvatar={false} skeleton={1} />
                    }
                  >
                    {projectList?.map((item: IProject) => (
                      <SelectSingleItems
                        key={item.id}
                        name={replaceDOMParams(
                          sanitizeString(item?.project_name) ?? ""
                        )}
                        subTitle={
                          isShowProjectType && item?.prj_record_type
                            ? item?.prj_record_type === "opportunity"
                              ? "Opportunity"
                              : "Project"
                            : ""
                        }
                        isViewAvatar={false}
                        active={multiSelectedProjects.some(
                          (p) => p.id?.toString() === item.id?.toString()
                        )}
                        {...item}
                        onClick={() => handleProjectClick(item)}
                      />
                    ))}
                  </InfiniteScroll>
                )}
              </div>
            </>
          </div>
          <div
            className={`z-20 xl:w-[340px] xl:min-w-[340px] xl:max-w-[340px] lg:w-[280px] lg:min-w-[280px] lg:max-w-[280px] w-full lg:bg-white bg-primary-gray-20/60 dark:bg-dark-800 lg:dark:bg-[#15202B] flex-[1_0_0%] max-md:overflow-hidden ${
              bottomSheetOpen ? "fixed top-0 h-full" : "lg:h-full h-0"
            }`}
          >
            {bottomSheetOpen && (
              <div
                className="lg:hidden block fixed left-0 bg-black/20 h-full w-full"
                onClick={() => {
                  setBottomSheetOpen(false);
                }}
              ></div>
            )}
            <div
              className={`transition-all ease-in-out duration-300 bg-white lg:rounded-none rounded-t-lg max-lg:fixed w-full z-10 ${
                bottomSheetOpen
                  ? "h-[calc(100vh-150px)] bottom-0 left-0"
                  : "lg:h-full h-0 max-lg:-bottom-[100%]"
              }`}
            >
              <div className="py-[13px] px-[15px] flex items-center relative before:absolute before:left-0 before:bottom-0 before:w-full before:h-px before:bg-[linear-gradient(90deg,#3f4c653d_24%,#3f4c6500_100%)]">
                <div className="flex items-center justify-center rounded-full mr-[7px] w-9 h-9 bg-gray-200/50 dark:bg-dark-500">
                  <FontAwesomeIcon
                    className="w-[18px] h-[18px] text-primary-900 dark:text-white/90"
                    icon="fa-regular fa-box-check"
                  />
                </div>
                <div>
                  <Header
                    level={5}
                    className="!text-[15px] !mb-0 !text-primary-900 dark:!text-white/90"
                  >
                    {_t("Currently Selected")}{" "}
                    {multiSelectedProjects && multiSelectedProjects.length > 0
                      ? ` (${multiSelectedProjects.length})`
                      : ""}
                  </Header>
                  <Typography className="text-[11px] opacity-60 font-normal text-primary-900 dark:text-white/90">
                    {_t("Selected project will appear here!")}
                  </Typography>
                </div>
              </div>
              <div className="py-2 px-[15px] lg:h-[calc(100vh-179px)] h-[65px] overflow-y-auto overflow-hidden">
                <div className="flex flex-col">
                  {multiSelectedProjects.map((project) => (
                    <SelectItem
                      key={project.id}
                      name={replaceDOMParams(
                        sanitizeString(project?.project_name) ?? ""
                      )}
                      subName={
                        isShowProjectType && project?.prj_record_type
                          ? project?.prj_record_type === "opportunity"
                            ? _t("Opportunity")
                            : _t("Project")
                          : isShowProjectType && project?.project_name
                          ? _t("Project")
                          : ""
                      }
                      onClick={() => {
                        handleProjectClick(project);
                        isSingleSelect && handleCloseDrawer();
                      }}
                    />
                  ))}
                </div>
              </div>
              {!isSingleSelect && (
                <div className="sidebar-footer flex items-center justify-center w-full p-[15px]">
                  <PrimaryButton
                    disabled={
                      isRequired
                        ? !multiSelectedProjects?.length
                          ? true
                          : false
                        : false
                    }
                    onClick={() => {
                      onProjectSelected?.(multiSelectedProjects);
                      onSaveProjectDrawerClose();
                    }}
                  >
                    {_t("Save")}
                  </PrimaryButton>
                </div>
              )}
            </div>
          </div>
        </div>
      </Drawer>
      {addNewProject && (
        <AddNewProject
          addNewProject={addNewProject}
          setAddNewProject={setAddNewProject}
          customer_id={customer_id}
          {...addNewProjectProps}
        />
      )}
    </>
  );
};

export default SelectProject;
