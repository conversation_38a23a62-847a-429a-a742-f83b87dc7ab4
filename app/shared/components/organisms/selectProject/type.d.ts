import { Dispatch, SetStateAction } from "react";

declare global {
  type IProjectType = IProject | undefined | null | string;

  interface IAddNewProjectOptions {
    label: string;
    value: string;
  }

  interface ISelectProjectProps {
    open: boolean;
    setOpen: (open: boolean) => void;
    isSingleSelect?: boolean;
    selectedProjects: IProject[];
    onProjectSelected: (project: IProject[]) => void;
    isRequired?: boolean;
    genericProjects?: string;
    category?: string;
    module_key?: string;
    customer_id?: string;
    isShowProjectType?: boolean;
    searchPlcaeHolder?: string;
    addNewProjectStatusOptions?: IAddNewProjectOptions[];
    addNewProjectProjectTypeOptions?: IAddNewProjectOptions[];
    addNewProjectApiCall?: (val: boolean) => void;
    addNewProjectAutoNumber?: string;
    // onNewProjectAdd?: (insertId: string) => void;
    addNewProjectProps?: Omit<
      IAddNewProjectProps,
      "addNewProject" | "setAddNewProject" | "customer_id"
    >;
    onClose?: (context: "closeIcon" | "saveBtn") => void;
  }
}
