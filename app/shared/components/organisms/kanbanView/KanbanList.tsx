import { memo, useCallback, useRef, useState } from "react";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { IconButton } from "~/shared/components/molecules/iconButton";
import { InfiniteScroll } from "~/shared/components/molecules/InfiniteScroll/InfiniteScroll";

import KanbanItem from "./item";

import pkg from "react-sortablejs";
const { ReactSortable } = pkg;

import KanbanViewLoader, { KanbanViewLoaderList } from "./KanbanViewLoader";
import { useTranslation } from "~/hook";
import { NoRecords } from "../../molecules/noRecords";
import { sanitizeString } from "~/helpers/helper";

const KanbanList = ({
  list,
  setList,
  colum,
  loading,
  collapseClick,
  cardDetailsClick,
  firstFiledAction = (val: any) => {}, // change type given by value
  secondFiledAction = (val: any) => {}, // change type given by value
  onActionClick = (val: any) => {}, // change type given by value
  children,
  kanbanSelected,
  iconShow = false,
  loadMore = () => {},
  handleCardDragDropEnd,
  handleColumnDragDropEnd,
  isReadOnly = false,
  handleMouseMove,
  className = "",
  childLoader,
  collapseKey = "item_id",
}: IKanbanListProps) => {
  const { _t } = useTranslation();
  const [hoveredItemId, setHoveredItemId] = useState<string | null>(null);
  const observer = useRef<IntersectionObserver | null>(null);

  const lastElementRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (loading) return;
      if (observer.current) observer.current.disconnect();
      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting) {
          const target = entries[0].target as HTMLDivElement;
          loadMore(target.dataset.laneId);
        }
      });
      if (node) observer.current.observe(node);
    },
    [loading, loadMore]
  );

  const handleHover = (data: any) => {
    setHoveredItemId(data.item_id);
  };
  return (
    <>
      {loading ? (
        <div
          className={`overflow-hidden ${
            isReadOnly
              ? "h-[calc(100dvh-249px)]"
              : window.ENV.PAGE_IS_IFRAME
              ? "h-[calc(100dvh-70px)]"
              : "h-[calc(100dvh-210px)]"
          }`}
        >
          <KanbanViewLoader />
        </div>
      ) : !loading && list && list?.length > 0 ? (
        <>
          <ReactSortable
            list={list}
            setList={setList}
            animation={200}
            group="kanban-columns"
            direction="horizontal"
            onEnd={handleColumnDragDropEnd}
            className="flex"
            disabled={isReadOnly}
          >
            {list?.map((data: any = {}, i: number) => {
              const collapse =
                kanbanSelected.length > 0
                  ? kanbanSelected.includes(data?.[collapseKey]?.toString()) ||
                    kanbanSelected.includes(data?.["key"]?.toString())
                  : false;
              return (
                <div
                  className={`ml-2.5 flex flex-col gap-[15px]  ${
                    isReadOnly
                      ? "md:h-[calc(100dvh-256px)] h-[calc(100dvh-225px)]"
                      : collapse
                      ? window.ENV.PAGE_IS_IFRAME
                        ? "h-[calc(100dvh-70px)]"
                        : "md:h-[calc(100dvh-218px)] h-[calc(100dvh-190px)]"
                      : "h-[calc(100dvh-414px)] xl:min-h-[392px] min-h-[300px]"
                  }`}
                  key={data.item_id} // Changed key to a more unique identifier
                >
                  <div
                    className={`group/kanban-list border flex items-center bg-white rounded p-3 shadow-[0px_0px_3px] shadow-[#0000001a] border-[#bbbbbb] cursor-move ease-in-out duration-300 dark:bg-dark-800 dark:border-white/20 ${
                      !collapse
                        ? "w-[42px] h-[calc(100dvh-414px)] mr-3.5 xl:min-h-[392px] min-h-[300px] [writing-mode:vertical-lr]"
                        : "w-[270px]"
                    }`}
                    style={{ borderTop: `4px solid ${data[colum.color]}` }}
                  >
                    <Tooltip
                      title={HTMLEntities.decode(
                        sanitizeString(data[colum.headerName])
                      )}
                      placement="top"
                    >
                      <Header
                        level={4}
                        className={`!text-sm max-w-[160px] !mb-0 truncate font-bold tracking-wide leading-[26px] dark:text-[#dcdcdd] ${
                          !collapse ? "text-[#2c323f]" : "text-[#777]"
                        }`}
                      >
                        {HTMLEntities.decode(
                          sanitizeString(data[colum.headerName])
                        )}
                      </Header>
                    </Tooltip>
                    <Typography
                      className={`inline-block text-[11px] font-semibold leading-5 text-[#565656] text-center border border-[#CFCFCF] shadow-[0px_0px_3px] shadow-[#0000001a] rounded-sm bg-gray-200 dark:bg-dark-900 dark:text-white/90 dark:border-white/20 ${
                        !collapse ? "mt-3 py-3 min-w-fit" : "ml-3 min-w-[30px]"
                      }`}
                    >
                      {data[colum.count]}
                    </Typography>
                    <Tooltip
                      title={`${
                        !collapse ? _t("Expand Card") : _t("Collapse Card")
                      }`}
                      placement={!collapse ? "left" : "top"}
                    >
                      <IconButton
                        htmlType="button"
                        className={`w-[26px] max-w-[26px] rounded max-h-[26px] hover:bg-primary-gray-20 dark:hover:bg-[#121f36] dark:hover:text-white hover:text-primary-900 active:bg-primary-gray-20 group-hover/kanban-list:opacity-100 ${
                          !collapse
                            ? "opacity-100 rotate-180 mt-1.5"
                            : "md:opacity-0 ml-auto"
                        }`}
                        variant="text"
                        onClick={() => {
                          collapseClick(data);
                        }}
                      >
                        <FontAwesomeIcon
                          className="text-base w-3.5 h-3.5 text-primary-gray-80 dark:text-white/90"
                          icon="fa-regular fa-angle-left"
                        />
                      </IconButton>
                    </Tooltip>
                  </div>
                  {collapse && (
                    <>
                      <ReactSortable
                        list={data[colum.child]}
                        setList={(newState) => {
                          const newList = [...list];
                          newList[i][colum.child] = newState;
                          setList && setList(newList || []);
                        }}
                        group="kanban-items"
                        animation={200}
                        className={`flex flex-col gap-2 pr-1.5 pb-px hover-scroll overflow-hidden ease-in-out duration-300 ${
                          !collapse
                            ? "w-0 h-0"
                            : `w-[280px] overflow-y-auto !overflow-x-hidden ${
                                isReadOnly
                                  ? "md:h-[calc(100dvh-315px)] h-[calc(100dvh-295px)]"
                                  : window.ENV.PAGE_IS_IFRAME
                                  ? "h-[calc(100dvh-142px)]"
                                  : "md:h-[calc(100dvh-284px)] h-[calc(100dvh-260px)]"
                              }`
                        }`}
                        direction="horizontal"
                        onEnd={handleCardDragDropEnd}
                        data-id={data.item_id}
                        disabled={isReadOnly}
                        filter=".kanban-card-loading"
                      >
                        <InfiniteScroll
                          loadMore={() => {
                            loadMore(data?.item_id);
                          }}
                          hasMore={data[colum.count] > data[colum.child].length}
                          isLoading={childLoader}
                          loadingComponent={
                            <div className="flex flex-col gap-2">
                              <KanbanViewLoaderList numbersrow={1} />
                            </div>
                          }
                          loadingComponentParams={{
                            className: "kanban-card-loading",
                          }}
                        >
                          {data[colum.child]?.map((obj: any, index: number) => (
                            <KanbanItem
                              key={index}
                              item={obj}
                              iconShow={iconShow}
                              colum={colum.childCard}
                              color={data[colum.color]}
                              hide={!collapse}
                              laneId={data.item_id}
                              cardDetailsClick={(val) => cardDetailsClick(val)}
                              firstFiledAction={(val) => firstFiledAction(val)}
                              secondFiledAction={(val) =>
                                secondFiledAction(val)
                              }
                              onMouseEnter={() => handleHover(data)}
                              ref={
                                data.item_id === hoveredItemId
                                  ? lastElementRef
                                  : undefined
                              }
                              handleMouseMove={(val) => handleMouseMove(val)}
                              className={className}
                              enableOpenInNewTab={Number(
                                window.ENV.ENABLE_ALL_CLICK
                              )}
                            >
                              <div onClick={() => onActionClick(obj)}>
                                {children}
                              </div>
                            </KanbanItem>
                          ))}
                        </InfiniteScroll>
                      </ReactSortable>
                    </>
                  )}
                </div>
              );
            })}
          </ReactSortable>
        </>
      ) : (
        !loading && (
          <div className="w-full h-[calc(100vh-215px)] flex justify-center items-center">
            <NoRecords
              rootClassName="w-full max-w-[280px]"
              image={`${window.ENV.CDN_URL}assets/images/create-record-list-view.svg`}
              text={
                <Typography className="sm:text-base text-xs text-black font-semibold">
                  {_t("No Record Found")}
                </Typography>
              }
            />
          </div>
        )
      )}
    </>
  );
};

export default memo(KanbanList);
