import { useCallback, useEffect, useState } from "react";
import { removeDuplicatesFile } from "~/shared/utils/helper/removeDuplicatesFile";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Form
import { useFormik } from "formik";
// Molecules
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { InputField } from "~/shared/components/molecules/inputField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
// Organisms
import { AttachmentCard } from "~/shared/components/organisms/attachmentCard";

const AddNoteModal = ({
  isOpen,
  isLoading = false,
  buttonLabel = "Save",
  onSaveNote,
  onCloseModal,
  projectid,
  validationParams,
  projectInputProps,
}: IAddNoteModalProps) => {
  const { _t } = useTranslation();
  const [selectedFiles, setSelectedFiles] = useState<IFile[]>([]);
  const [attachedFile, setAttachedFile] = useState({});
  const [isNotes, setNotes] = useState(true);
  const [isFileDeleted, setIsFileDeleted] = useState<boolean>(false);
  const [deletedFile, setDeletedFile] = useState<string>("");
  const formik = useFormik({
    initialValues: { title: "", description: "" },
    onSubmit: (values) => {
      values.attachFiles = selectedFiles;
      values.title = values?.title.trim();
      values.description = values?.description.trim();
      onSaveNote(values);
    },
  });

  // Memoize the callback functions
  const handleAddAttachment = useCallback((data: IFile[]) => {
    setAttachedFile(data);
    setSelectedFiles((prev) => {
      const updatedFiles = [...prev, ...data];
      return removeDuplicatesFile(updatedFiles);
    });
  }, []);

  useEffect(() => {
    if (isFileDeleted) {
      if (
        Number(deletedFile) >= 0 &&
        Number(deletedFile) <= selectedFiles.length
      ) {
        selectedFiles.splice(Number(deletedFile), 1);
      }
      setSelectedFiles(selectedFiles);
    }
  }, [deletedFile, isFileDeleted]);

  const handleUpdateData = useCallback((data: Partial<IgetUpdatedFileRes>) => {
    const newFile = data?.data;
    if (!newFile) return;
    setSelectedFiles((prevFiles) =>
      prevFiles.some((file) => file.image_id === newFile.image_id)
        ? prevFiles.map((file) =>
            file.image_id === newFile.image_id ? newFile : file
          )
        : [...prevFiles, newFile]
    );
  }, []);

  const handleDeleteFile = useCallback((data: Partial<IFile>) => {
    const key = Object.keys(data)[0] as keyof IFile;
    const value = data[key] as string;
    setSelectedFiles((prevFiles) =>
      prevFiles.filter((file) => !(key in file && file[key] === value))
    );
  }, []);

  const handleSaveNote = () => {
    if (
      formik.values.title.trim() === "" &&
      formik.values.description.trim() === ""
    ) {
      notification.error({
        description: "Please Enter Title or Description.",
      });
    } else {
      formik.submitForm();
    }
  };

  return (
    <CommonModal
      isOpen={isOpen}
      widthSize="800px"
      onCloseModal={onCloseModal}
      modalBodyClass="p-0"
      keyboard={false}
      header={{
        title: _t("Note"),
        icon: (
          <FontAwesomeIcon className="w-3.5 h-3.5" icon="fa-regular fa-memo" />
        ),
        closeIcon: true,
      }}
    >
      <form
        noValidate
        method="post"
        className="py-4"
        onSubmit={formik.handleSubmit}
      >
        <div className="modal-body grid gap-5 overflow-y-auto max-h-[calc(100vh-200px)] px-4">
          <InputField
            label={_t("Title")}
            type="text"
            labelPlacement="top"
            name="title"
            value={formik.values.title}
            maxLength={256}
            onChange={(e) => {
              if (e.target.value.length > 255) {
                notification.error({
                  message: _t("Character Limit Exceeded"),
                  description: _t("Title cannot exceed 255 characters."),
                });
                return;
              }
              formik.handleChange(e);
            }}
            onPressEnter={(e) => e.preventDefault()}
          />
          <TextAreaField
            label={_t("Description")}
            labelPlacement="top"
            name="description"
            value={formik.values.description}
            onChange={formik.handleChange}
          />
          <AttachmentCard
            projectid={projectid}
            isAddAllow
            isReadOnly={false}
            files={selectedFiles}
            notesAttach={true}
            onAddAttachment={handleAddAttachment}
            setUpdatedData={handleUpdateData}
            isShowDeleteMenu={true}
            onDeleteFile={handleDeleteFile}
            validationParams={validationParams}
            addFilesRes={attachedFile}
            isNotes={true}
            setNotes={setNotes}
            setIsFileDeleted={setIsFileDeleted}
            setDeletedFile={setDeletedFile}
            projectInputProps={projectInputProps}
            setSelectedFilesData={setSelectedFiles}
          />
        </div>
        <div className="modal-footer text-center px-4 pt-4">
          <PrimaryButton
            htmlType="button"
            className="!w-fit"
            buttonText={_t(`${buttonLabel}`)}
            onClick={handleSaveNote}
            disabled={isLoading}
            isLoading={isLoading}
          />
        </div>
      </form>
    </CommonModal>
  );
};

export default AddNoteModal;
