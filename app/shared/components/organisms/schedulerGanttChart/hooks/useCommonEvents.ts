import { useCallback } from "react";
import { useGantt } from "~/hook/useGantt";
import { adjustGanttCalendarForBaseline } from "../gantt/helper/helper";
import { useGanttSettings } from "./useGanttSettings";
import { useTranslation } from "~/hook";
import { useGanttDataStore } from "./useGanttDataStore";
import { useSlackVisualization } from "./useSlackVisualization";
import { MessagePopupConfig, MessagePopupObject } from "@dhx/react-gantt";
import { setListGanttTasks } from "~/modules/projectManagement/pages/schedule/redux/slices/scheduleSlice";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";

export const useGanttCommonEvents = () => {
  const { getGanttInstance, dispatch } = useGantt();
  const { baseline } = useGanttSettings();
  const { updateParentProgressIfChildChanged, updatePreviousGanttState } =
    useGanttDataStore();
  const { updateSlack } = useSlackVisualization();

  const currenMenuModule = getCurrentMenuModule();

  const { module_key } = currenMenuModule || {};

  const { _t } = useTranslation();

  const onGanttReady = useCallback(() => {
    const ganttInstance = getGanttInstance();

    if (!ganttInstance) {
      console.warn("Gantt instance not available");
      return;
    }

    const originalMessage = ganttInstance.message;

    const wrappedMessage = function (
      config: string | number | MessagePopupConfig
    ): string | number | HTMLElement | undefined {
      const messageText =
        typeof config === "string"
          ? config
          : typeof config === "object" && "text" in config
          ? config.text
          : "";

      if (
        typeof messageText === "string" &&
        (messageText.includes("Task not found") ||
          messageText.includes("Invalid argument"))
      ) {
        return;
      }

      return originalMessage(config);
    };

    // Attach remaining required properties to the wrapper
    (wrappedMessage as MessagePopupObject).position = originalMessage.position;
    (wrappedMessage as MessagePopupObject).keyboard = originalMessage.keyboard;
    (wrappedMessage as MessagePopupObject).hide = originalMessage.hide;

    // Assign fully-typed replacement
    ganttInstance.message = wrappedMessage as MessagePopupObject;

    setTimeout(() => {
      try {
        adjustGanttCalendarForBaseline(
          ganttInstance,
          ganttInstance.getTaskByTime() as TGanttTaskTypes[],
          baseline
        );

        // Set the fullscreen container BEFORE init
        ganttInstance.ext.fullscreen.getFullscreenElement = () => {
          return document.getElementById("gantt-fullscreen-wrapper")!;
        };

        const state = ganttInstance.getState();
        const maxDate = state.max_date;

        // Ensure scroll goes far enough to the right
        const x = ganttInstance.posFromDate(maxDate);
        ganttInstance.scrollTo(x, 0);
      } catch (error) {
        console.error("Error in onGanttReady:", error);
      }
    }, 100);
  }, [getGanttInstance, baseline]);

  const onCircularLinkError = (link: TGanttLink) => {
    const gantt = getGanttInstance();
    if (!gantt) return;

    let from = gantt.getTask(link.source);
    let to = gantt.getTask(link.target);

    const msg = _t(
      `There is Circular link error for the link between ${from.text} - ${to.text}`
    );

    notification.error({
      description: msg,
    });
  };

  const onParse = useCallback(() => {
    const gantt = getGanttInstance();

    if (!gantt) return;

    updateSlack();
    updatePreviousGanttState();

    const taskIds: string[] = [];
    let allTasks: TGanttTaskTypes[] = [];

    gantt.eachTask((task: TGanttTaskTypes) => {
      task.$open = true;
      taskIds.push(task?.id as string);

      allTasks.push(structuredClone(task)); // To store the tasks in Schedule module redux to show them in list view
    });

    gantt.sort(
      (task1, task2) => (+task1.sortorder - +task2.sortorder) as 0 | 1 | -1,
      false
    );

    if (module_key === "gantt_chart") {
      dispatch(setListGanttTasks(allTasks));
    }

    updateParentProgressIfChildChanged(taskIds);
  }, [getGanttInstance, updatePreviousGanttState, module_key]);

  const onAfterTaskMove = useCallback(
    (id: string | number) => {
      const gantt = getGanttInstance();
      if (!gantt) return;

      const task = gantt.getTask(id);

      task.native_action = "order";

      return true;
    },
    [getGanttInstance]
  );

  return {
    onGanttReady,
    onCircularLinkError,
    onParse,
    onAfterTaskMove,
  };
};
