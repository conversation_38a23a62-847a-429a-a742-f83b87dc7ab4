import { useCallback, useEffect, useRef, useState } from "react";
import { useTranslation } from "~/hook";
import { useGantt } from "~/hook/useGantt";
import { updateScheduleSettings } from "~/redux/action/scehdulerAction";
import { updateSettings } from "~/redux/slices/schedulerSlice";

const columnMapping = {
  wbs: "show_wbs_column",
  text: "show_task_column",
  start_date_only: "show_start_date_column",
  end_date_only: "show_end_date_column",
  progress: "show_progress_column",
  duration: "show_duration_column",
  predecessor: "show_predecessors_column",
  add: "show_new_option_column",
};

export const useGanttContextMenu = () => {
  const menuRef = useRef<any>(null);
  const { getGanttInstance, dispatch } = useGantt();
  const [scriptLoaded, setScriptLoaded] = useState(false);
  const { _t } = useTranslation();

  useEffect(() => {
    const existing = document.querySelector(`script[src="/gantt/dhtmlx.js"]`);
    if (existing) {
      setScriptLoaded(true);
      return;
    }

    const script = document.createElement("script");
    script.src = "/gantt/dhtmlx.js";
    script.onload = () => setScriptLoaded(true);
    script.onerror = () => console.error("Failed to load dhtmlx.js");
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  const updareColumnVisibility = useCallback(
    async (column: keyof typeof columnMapping, visible: boolean) => {
      const gantt = getGanttInstance();

      const payload = {
        option: "column",
        column,
        visibility: visible ? 1 : 0,
      };

      const updateSettingRes = await updateScheduleSettings({
        ...payload,
      });

      let columnToConsider = column;

      if (column?.includes("toggle#")) {
        columnToConsider = column.replace(
          "toggle#",
          ""
        ) as keyof typeof columnMapping;
      }

      if (updateSettingRes.success) {
        const newSettings = {
          [columnMapping[columnToConsider]]: visible,
        };

        dispatch(updateSettings({ newSettings }));
      } else {
        notification.error({
          description: _t(
            updateSettingRes.message || "Failed to update column visibility"
          ),
        });

        var currentColumn = gantt?.getGridColumn(columnToConsider);
        if (currentColumn) {
          currentColumn.hide = visible;
          gantt?.render();
        }
      }
    },
    [getGanttInstance]
  );

  const addColumnsConfig = useCallback(
    (menu: any) => {
      const gantt = getGanttInstance();

      if (!gantt) return;

      menu.addNewChild(null, -1, "show_columns", "Show columns:", false);
      var columns = gantt.config.columns;

      for (var i = 0; i < columns.length; i++) {
        var checked = !columns[i].hide,
          itemLabel = getColumnLabel(columns[i]);
        menu.addCheckbox(
          "child",
          "show_columns",
          i,
          columns[i].name,
          itemLabel,
          checked
        );
      }
    },
    [getGanttInstance]
  );

  const addColumnToggle = useCallback(
    (menu: any, column_name: string) => {
      const gantt = getGanttInstance();

      if (!gantt) return;
      var column = gantt.getGridColumn(column_name);
      var label = getColumnLabel(column);

      // add prefix to distinguish from the same item in 'show columns' menu
      var item_id = "toggle#" + column_name;
      menu.addNewChild(null, -1, item_id, "Hide '" + label + "'", false);
      menu.addNewSeparator(item_id);
    },
    [getGanttInstance]
  );

  const getColumnLabel = useCallback(
    (column: TGanttGridColumn) => {
      const gantt = getGanttInstance();

      if (column == null || !gantt) return "";

      var locale = gantt.locale.labels;
      var text =
        column.label !== undefined
          ? column.label
          : locale["column_" + column.name];

      text = text || column.name;
      return text;
    },
    [getGanttInstance]
  );

  // Initialize context menu when script is ready
  useEffect(() => {
    const gantt = getGanttInstance();

    if (!gantt || !scriptLoaded || !window.dhtmlXMenuObject) return;

    const menu = new window.dhtmlXMenuObject();

    menu.renderAsContextMenu();
    menu.setSkin("dhx_terrace");

    gantt.attachEvent("onContextMenu", (taskId, linkId, event: MouseEvent) => {
      var x =
          event?.clientX +
          document.body.scrollLeft +
          document.documentElement.scrollLeft,
        y =
          event?.clientY +
          document.body.scrollTop +
          document.documentElement.scrollTop;
      var target = event.target || event.srcElement;
      var column_id = (target as Element)?.getAttribute("column_id");

      menu.clearAll();
      addColumnsConfig(menu);
      if (column_id) {
        addColumnToggle(menu, column_id);
      }

      menu.showContextMenu(x, y);
      return false;
    });

    menu.attachEvent("onClick", (id: keyof typeof columnMapping) => {
      var parts = (id + "").split("#");
      var is_toggle = parts[0] == "toggle",
        column_id = parts[1] || id;

      var column = gantt.getGridColumn(column_id);

      if (column) {
        var visible = !is_toggle ? menu.getCheckboxState(id) : false;

        updareColumnVisibility(id, visible);

        column.hide = !visible;

        gantt.render();
      }
      return true;
    });

    menuRef.current = menu;

    return () => {
      //   menu.unload();
    };
  }, [scriptLoaded, getGanttInstance]);
};
