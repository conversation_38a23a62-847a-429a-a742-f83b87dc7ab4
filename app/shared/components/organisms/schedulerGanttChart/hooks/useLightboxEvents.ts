import { useCallback } from "react";
import {
  adjustGanttCalendarForBaseline,
  date_to_str,
  getDefautlValsForLightBox,
  invalidDateForTask,
} from "../gantt/helper/helper";
import { useGantt } from "~/hook/useGantt";
import { useGanttSettings } from "./useGanttSettings";
import { useGanttDataStore } from "./useGanttDataStore";
import { useTranslation } from "~/hook";
import { useSlackVisualization } from "./useSlackVisualization";

export const useLightboxEvents = () => {
  const { getGanttInstance } = useGantt();
  const { company_date_format, baseline, slack } = useGanttSettings();
  const { updatePreviousGanttState } = useGanttDataStore();
  const { updateSlack } = useSlackVisualization();
  const { _t } = useTranslation();

  const onTaskDblClick = useCallback(
    (id: string) => {
      const gantt = getGanttInstance();

      if (!gantt) return;

      const task = gantt.getTask(id) as TGanttTaskTypes;

      const defaultVal = getDefautlValsForLightBox(task);

      task.contractors = defaultVal.contractors;
      task.employees = defaultVal.employees;

      if (
        !!task.planned_start &&
        task.planned_end &&
        task.planned_start !== invalidDateForTask &&
        task.planned_end !== invalidDateForTask
      ) {
        task.planned_duration = gantt.calculateDuration(
          new Date(task.planned_start),
          new Date(task.planned_end)
        );
      }

      // Prevent adding root task directly unless via Project Module
      if (task.$level === 0 && !id.toString().startsWith("prj_")) {
        alert(
          "To add a new Project, first you need to create a Project (Menu > Project) and then from the Schedule you can click on the Project button to choose which projects appear within the list."
        );
        gantt.deleteTask(id);
        return false;
      }

      // Adjust end date for project tasks (subtract 1 day)
      // if (task.type === "project") {
      //   const temp = new Date(task.end_date ?? "");
      //   task.end_date = gantt.date.add(
      //     new Date(temp.getFullYear(), temp.getMonth(), temp.getDate()),
      //     -1,
      //     "day"
      //   );
      // }

      updatePreviousGanttState();

      return true;
    },
    [getGanttInstance, updatePreviousGanttState]
  );

  const onTaskClick = useCallback(
    (id: string) => {
      const gantt = getGanttInstance();
      if (!gantt) return false;

      setTimeout(() => {
        gantt.showTask(id); // Ensure task is brought into view
      }, 10);
      return true;
    },
    [getGanttInstance]
  );

  const onBeforeTaskAdd = useCallback(
    (id: string | number, task: TGanttTaskTypes) => {
      // Restrict top-level task creation
      const gantt = getGanttInstance();
      if (!gantt) return false;

      if (task["!nativeeditor_status"] === "inserted" && task.$level === 0) {
        gantt.deleteTask(id);
        return false;
      }

      // Allow system-defined IDs
      if (
        task.id?.toString().startsWith("prj_") ||
        task.id?.toString().startsWith("task_")
      ) {
        return true;
      }

      // // If not a project, assign project_id
      if (task.type !== "project") {
        let parent_flag = true;
        let parent = id;
        while (parent_flag) {
          if (gantt.getTask(parent)["$level"] == 1) {
            parent_flag = false;
          }
          parent = gantt.getTask(parent)?.parent as string;
        }

        task.project_id = parent?.toString()?.replace("prj_", "");
      }

      task.level = task.$level;
      return true;
    },
    [getGanttInstance]
  );

  const dateHandlerAterTaskUpdation = useCallback(
    (gantt: TGanttStaticTypes, task: TGanttTaskTypes) => {
      let tempStart = new Date(task.start_date as Date);
      let tempEnd = new Date(task.end_date);

      task.start_date_only = date_to_str(
        gantt,
        company_date_format,
        new Date(
          tempStart.getFullYear(),
          tempStart.getMonth(),
          tempStart.getDate()
        )
      );

      task.end_date_only = date_to_str(
        gantt,
        company_date_format,
        gantt.date.add(
          new Date(
            tempEnd.getFullYear(),
            tempEnd.getMonth(),
            tempEnd.getDate()
          ),
          -1,
          "day"
        )
      );
    },
    [date_to_str, company_date_format]
  );

  const onAfterTaskAdd = useCallback(
    (_id: string | number, task: TGanttTaskTypes) => {
      const gantt = getGanttInstance();
      if (!gantt) return false;

      if (slack) {
        updateSlack();
      }

      dateHandlerAterTaskUpdation(gantt, task);
    },
    [getGanttInstance, slack]
  );

  const onBeforeTaskDelete = useCallback(
    (_id: string | number, task: TGanttTaskTypes) => {
      if (task.id?.toString()?.startsWith("prj_")) {
        notification.error({
          description: _t("You are not allowed to delete Main Parent task."),
        });
        return false;
      }

      return true;
    },
    []
  );

  const onAfterTaskDelete = useCallback(
    (_id: string | number, task: TGanttTaskTypes) => {
      const gantt = getGanttInstance();
      if (!gantt) return false;

      if (slack) {
        updateSlack();
      }

      const parentTask = gantt.getTask(task.parent);

      if (
        isNaN(parseInt(task.parent?.toString())) &&
        !gantt.hasChild(task.parent) &&
        (parentTask?.["$level"] ?? 0) > 0
      ) {
        parentTask.type = gantt.config.types.task?.toString();
      }

      return true;
    },
    [slack, getGanttInstance]
  );

  const onAfterTaskUpdate = useCallback(
    (_id: string | number, task: TGanttTaskTypes) => {
      const gantt = getGanttInstance();
      if (!gantt) return false;

      if (slack) {
        updateSlack();
      }

      dateHandlerAterTaskUpdation(gantt, task);

      if (baseline) {
        adjustGanttCalendarForBaseline(
          gantt,
          gantt.getTaskByTime() as TGanttTaskTypes[],
          baseline
        );
      }
    },
    [baseline, slack]
  );

  return {
    onTaskClick,
    onTaskDblClick,
    onBeforeTaskAdd,
    onAfterTaskAdd,
    onBeforeTaskDelete,
    onAfterTaskDelete,
    onAfterTaskUpdate,
  };
};
