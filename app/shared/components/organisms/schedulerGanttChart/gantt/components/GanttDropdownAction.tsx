import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { useTranslation } from "~/hook";
import { useGantt } from "~/hook/useGantt";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Typography } from "~/shared/components/atoms/typography";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import ImportMicrosoftProjectFile from "./action-components/ImportMicrosoftProjectFile";
import { useGanttSettings } from "../../hooks/useGanttSettings";
import SendScheduleReminerModal from "./action-components/SendScheduleReminerModal";
import ImportFromExistingProModal from "./action-components/ImportFromExistingProModal";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { deleteProjectSchedule } from "~/redux/action/scehdulerAction";
import { useGanttDataStore } from "../../hooks/useGanttDataStore";
import { useBoolean } from "~/modules/projectManagement/pages/project/hook/use-boolean";
import { useGanttStyle } from "../../hooks/useGanttStyles";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { useGModules } from "~/zustand";
import { defaultConfig } from "~/data";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import ChangeWorkDaysModal from "./action-components/ChangeWorkDaysModal";

const GanttDropdownAction = () => {
  const { getGanttInstance } = useGantt();
  const { checkModuleAccessByKey } = useGModules();
  const { _t } = useTranslation();
  const { baseline, slack, projectId } = useGanttSettings();
  const { fetchSchduleTasks } = useGanttDataStore();
  const loading = useBoolean();
  const { styleInnerHtml } = useGanttStyle();

  const currenMenuModule = getCurrentMenuModule();

  const { module_key } = currenMenuModule || {};

  const user: IInitialGlobalData["user"] = getGlobalUser();
  const [importPro, setImportPro] = useState<{
    type: string;
  }>({
    type: "",
  });
  const hasProjectAccessShowMessages = useMemo(() => {
    const mAccess = checkModuleAccessByKey(defaultConfig.project_module);
    return mAccess === "no_access" || mAccess === "read_only";
  }, []);
  const getStyleForExporting = useCallback(() => {
    let customStyle = `
      ${styleInnerHtml}
      .gantt_marker {
        display: none;
      }
    `;

    return customStyle;
  }, [styleInnerHtml]);

  const exportToPDF = useCallback(() => {
    let customStyle = getStyleForExporting();

    const gantt = getGanttInstance();
    gantt?.exportToPDF({
      name: "gantt.pdf",
      raw: true,
      header: `<style>${customStyle}</style>`,
    });
  }, [getGanttInstance, getStyleForExporting]);

  const exportToPng = useCallback(() => {
    let customStyle = getStyleForExporting();

    const gantt = getGanttInstance();
    gantt?.exportToPNG({
      name: "gantt.png",
      raw: true,
      header: `<style>${customStyle}</style>`,
    });
  }, [getGanttInstance, getStyleForExporting]);

  const exportToMsProject = useCallback(() => {
    const gantt = getGanttInstance();
    if (!gantt) return;
    gantt.config.columns[6].editor = undefined;
    gantt.exportToMSProject();

    const formatter = gantt?.ext.formatters.durationFormatter({
      format: ["day"],
      store: "day",
      short: true,
    });

    let linksFormatter = gantt?.ext.formatters.linkFormatter({
      durationFormatter: formatter,
    });

    let predecessors = {
      type: "predecessor",
      map_to: "auto",
      formatter: linksFormatter,
    };

    gantt.config.columns[6].editor = predecessors;
    gantt.render();
  }, [getGanttInstance]);

  const addItemToSOVOptions = [
    {
      label: _t("Export to PDF"),
      value: "export_to_pdf",
      onClick: () => {
        exportToPDF();
      },
    },
    {
      label: _t("Export to PNG"),
      value: "export_to_png",
      onClick: () => {
        exportToPng();
      },
    },
    {
      label: _t("Export to Excel"),
      value: "export_to_excel",
      onClick: () => {
        const gantt = getGanttInstance();
        gantt?.exportToExcel();
      },
    },
    {
      label: _t("Export to iCal"),
      value: "export_to_ical",
      onClick: () => {
        const gantt = getGanttInstance();
        gantt?.exportToICal();
      },
    },
    {
      label: _t("Export to Ms Project"),
      value: "export_to_ms_project",
      onClick: () => {
        exportToMsProject();
      },
    },
    {
      label: _t("Import Microsoft Project"),
      value: "import_microsoft_project",
      onClick: () => {
        setImportPro({
          type: "import_microsoft_project",
        });
      },
    },
    {
      label:
        module_key === "gantt_chart"
          ? _t("Copy a Schedule")
          : _t("Import from Existing Project"),
      value: "import_from_existing_project",
      onClick: () => {
        setImportPro({
          type: "import_from_existing_project",
        });
      },
    },
    ...(module_key === "gantt_chart"
      ? [
          {
            label: _t("Change Work Days"),
            value: "change_work_days",
            onClick: () => {
              setImportPro({
                type: "change_work_days",
              });
            },
          },
        ]
      : []),
    {
      label: _t("Send Schedule Notification"),
      value: "send_schedule_notification",
      onClick: () => {
        setImportPro({
          type: "send_schedule_notification",
        });
      },
    },
    {
      label: _t("Delete Schedule"),
      value: "delete_schedule",
      onClick: () => {
        setImportPro({
          type: "delete_schedule",
        });
      },
    },
  ];

  const closeModal = useCallback(() => {
    setImportPro({ type: "" });
  }, []);

  const onDeleteSchedule = useCallback(async () => {
    loading.onTrue();
    const deleteRes = await deleteProjectSchedule({ project_id: projectId });

    if (!deleteRes.success) {
      notification.error({
        description: deleteRes.message ?? "Failed to delete schedule",
      });
    } else {
      fetchSchduleTasks();
      closeModal();
    }
    loading.onFalse();
  }, [projectId, fetchSchduleTasks]);

  return (
    <>
      <DropdownMenu
        options={addItemToSOVOptions}
        buttonClass="w-fit h-auto !mx-0"
        placement="bottomRight"
        contentClassName="menu-h-scroll"
        {...(hasProjectAccessShowMessages && {
          footerText: _t(
            "Some actions might be unavailable depending on your privilege."
          ),
        })}
      >
        <div className="py-1 px-2.5 bg-[#EBF1F9] rounded flex items-center gap-[5px]">
          <Typography className="text-primary-900 text-sm">
            {_t("Actions")}
          </Typography>
          <FontAwesomeIcon
            className="w-3 h-3 text-primary-900"
            icon="fa-regular fa-chevron-down"
          />
        </div>
      </DropdownMenu>

      {importPro.type === "import_microsoft_project" && (
        <ImportMicrosoftProjectFile
          onClose={closeModal}
          isOpen={importPro.type === "import_microsoft_project"}
          gantt={getGanttInstance()}
          module_key={module_key ?? ""}
        />
      )}

      {importPro.type === "send_schedule_notification" && (
        <SendScheduleReminerModal
          isOpen={importPro.type === "send_schedule_notification"}
          onClose={closeModal}
        />
      )}

      {importPro.type === "import_from_existing_project" && (
        <ImportFromExistingProModal
          isOpen={importPro.type === "import_from_existing_project"}
          onClose={closeModal}
          module_key={module_key ?? ""}
        />
      )}

      {importPro.type === "change_work_days" && (
        <ChangeWorkDaysModal
          isOpen={importPro.type === "change_work_days"}
          onClose={closeModal}
        />
      )}

      {importPro.type === "delete_schedule" && (
        <ConfirmModal
          isOpen={importPro.type === "delete_schedule"}
          isLoading={loading.bool}
          modaltitle={_t("Delete")}
          description={_t(
            "Are you sure you want to DELETE/CLEAR the selected Schedule?"
          )}
          modalIcon={"fa-regular fa-trash-can"}
          onAccept={onDeleteSchedule}
          onDecline={closeModal}
          onCloseModal={closeModal}
        />
      )}
    </>
  );
};

export default GanttDropdownAction;
