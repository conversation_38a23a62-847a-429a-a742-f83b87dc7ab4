import { useTranslation } from "~/hook";

// Fortawesome
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
// Other
import { useCallback, useMemo, useRef, useState } from "react";
import { useDropzone } from "react-dropzone";
import { addSchduleMppData } from "~/redux/action/scehdulerAction";
import { Number, sanitizeString } from "~/helpers/helper";
import { useGanttDataStore } from "../../../hooks/useGanttDataStore";
import { useGanttSettings } from "../../../hooks/useGanttSettings";
import { useGantt } from "~/hook/useGantt";
import { useCurrentModuleAccess } from "../../../hooks/useCurrentModuleAccess";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { useBoolean } from "~/modules/projectManagement/pages/project/hook/use-boolean";
import { SelectProject } from "~/shared/components/organisms/selectProject";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";

const ImportMicrosoftProjectFile = ({
  isOpen,
  onClose,
  gantt,
  module_key,
}: TImportMicroProFileProps & { module_key: string }) => {
  const { _t } = useTranslation();
  const [selectedFileData, setSelectedFileData] = useState<File>();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isImporting, setIsImporting] = useState<boolean>(false);
  const [selectedProject, setSelectedProject] = useState<IProjectSelectParams>({
    id: 0,
    project_name: "",
  });

  const { projectId } = useGanttSettings();
  const { getGanttInstance } = useGantt();
  const { fetchSchduleTasks } = useGanttDataStore();
  const { enoughGanttAccess } = useCurrentModuleAccess();

  const isScheduleModule = useMemo(() => {
    return module_key === "gantt_chart";
  }, [module_key]);

  const projectSelector = useBoolean();

  const { open, getRootProps, getInputProps } = useDropzone({
    onDrop: (acceptedFiles, rejectedFiles) => {
      // Check if any files were rejected
      if (rejectedFiles.length > 0) {
        notification.error({
          description: "Please upload a valid xml file",
        });
        return;
      }

      const file = acceptedFiles?.[0];
      if (file) {
        setSelectedFileData(file);
      }
    },
    accept: {
      "text/xml": [".xml"], // MIME type for xml files,
    },
    disabled: !enoughGanttAccess,
    noClick: true,
    multiple: false,
  });

  const scheduleAddMapDataHandler = useCallback(
    async (mppData: IImportFromMSProData) => {
      const payload: IAddSchduleMapDataApiParams = {
        project_id: isScheduleModule
          ? Number(selectedProject?.id)
          : Number(projectId),
        mpp_data: mppData?.data,
        mpp_links: mppData?.links,
      };

      const addMapDataRes = await addSchduleMppData(payload);

      if (!addMapDataRes.success) {
        notification.error({
          description: addMapDataRes.message || "Failed to add mpp data",
        });
      } else {
        fetchSchduleTasks();
        getGanttInstance()?.render();
        onClose();
      }

      setIsImporting(false);
    },
    [
      projectId,
      fetchSchduleTasks,
      getGanttInstance,
      selectedProject,
      isScheduleModule,
    ]
  );

  const handleUploadFileData = () => {
    if (isScheduleModule && selectedProject?.id?.toString() === "0") {
      notification.error({
        description: "Please choose a project to continue.",
      });
      return;
    }

    if (!selectedFileData) {
      notification.error({
        description: "Please choose a file to continue.",
      });
      return;
    }

    const confirmRes = confirm(
      `WARNING: Uploading a new file will delete all existing data for the selected project.`
    );
    if (!confirmRes) {
      return;
    }

    setIsImporting(true);
    gantt?.importFromMSProject({
      data: selectedFileData,
      callback: (project: IImportFromMSPro) => {
        if (project) {
          if (!isScheduleModule) {
            gantt.clearAll();
          }
          scheduleAddMapDataHandler(project?.data);
        } else {
          notification.error({
            description: "No data avaialble in the file you provided!",
          });
          setIsImporting(false);
        }
      },
    });
  };

  const onChangeProject = (projects: IProject[]) => {
    if (projects.length) {
      setSelectedProject({
        id: Number(projects[0].id),
        project_name: projects[0].project_name ?? "",
      });
    } else {
      setSelectedProject({
        id: 0,
        project_name: "",
      });
    }
  };

  return (
    <>
      <CommonModal
        isOpen={isOpen}
        widthSize="600px"
        onCloseModal={onClose}
        modalBodyClass="p-0"
        header={{
          title: _t("Import Microsoft Project File"),
          icon: (
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-file-export"
            />
          ),
          closeIcon: true,
        }}
      >
        <div className="py-4">
          <div className="modal-body grid gap-3.5 overflow-y-auto max-h-[calc(100vh-200px)] px-4">
            {isScheduleModule && (
              <ButtonField
                label={_t("Project")}
                placeholder={_t("Select Project")}
                labelPlacement="top"
                name="project_id"
                isDisabled={!enoughGanttAccess}
                readOnly={!enoughGanttAccess}
                onClick={() => {
                  projectSelector.onTrue();
                }}
                required={true}
                className="h-6 py-0 !w-full gap-0"
                readOnlyClassName="h-6 !font-medium whitespace-nowrap truncate sm:block flex"
                inputClassName="w-full"
                fieldClassName="!w-full !mt-2"
                spanWidthClass="w-fit"
                value={HTMLEntities.decode(
                  sanitizeString(selectedProject?.project_name || "")
                )}
                headerTooltip={`Project: ${HTMLEntities.decode(
                  sanitizeString(selectedProject?.project_name || "")
                )}`}
                rightIcon={
                  selectedProject?.id ? (
                    <ProjectFieldRedirectionIcon
                      projectId={selectedProject?.id?.toString() || ""}
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    />
                  ) : (
                    ""
                  )
                }
              />
            )}
            <InlineField
              label={_t("Item")}
              isRequired={true}
              labelPlacement="top"
              field={
                <div className="mt-1" {...getRootProps()}>
                  <input ref={fileInputRef} {...getInputProps()} />
                  <button
                    type="button"
                    onClick={open}
                    className="px-3 py-1 text-xs border rounded bg-white font-medium border-gray-400"
                  >
                    Choose file
                  </button>
                  <span className="text-gray-600 !ml-2 text-xs">
                    {selectedFileData?.name || "No file chosen"}
                  </span>
                </div>
              }
            />
          </div>
          <div className="modal-footer px-4 pt-4 text-center">
            <PrimaryButton
              htmlType="button"
              className="!w-fit"
              buttonText={_t("Upload")}
              onClick={handleUploadFileData}
              disabled={isImporting}
              isLoading={isImporting}
            />
          </div>
        </div>
      </CommonModal>

      {projectSelector.bool && (
        <SelectProject
          isSingleSelect={true}
          open={projectSelector.bool}
          genericProjects="project,opportunity"
          setOpen={() => projectSelector.onFalse()}
          selectedProjects={selectedProject?.id ? [selectedProject] : []}
          onProjectSelected={(data) => {
            onChangeProject(data);
          }}
          isRequired={true}
        />
      )}
    </>
  );
};

export default ImportMicrosoftProjectFile;
