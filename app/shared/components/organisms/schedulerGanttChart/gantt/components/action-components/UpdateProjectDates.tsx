import { memo, useMemo } from "react";
import { Form } from "@remix-run/react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { Number, sanitizeString } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { displayDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import dayjs from "dayjs";
import { updateProjectStartEndDate } from "~/modules/projectManagement/pages/schedule/redux/action/scehduleDashAction";

const UpdateProjectDates = ({
  setUpdateProjectDateOpen,
  updateProjectDateOpen,
  projectsToChangeDates,
  updateSchedulePros,
}: IUpdateProjectDateProps) => {
  const { _t } = useTranslation();

  const validationSchema = Yup.object().shape({
    projects: Yup.array().of(
      Yup.object().shape({
        start_date: Yup.string().required("Start date is required"),
        end_date: Yup.string().required("End date is required"),
      })
    ),
  });

  const formik = useFormik({
    initialValues: {
      projects:
        projectsToChangeDates?.map((p) => ({
          id: p.id as string,
          project_name: p.project_name || "",
          start_date: p.start_date as string,
          end_date: p.end_date || "",
        })) || [],
    },
    validationSchema,
    onSubmit: async (values) => {
      const pros = values?.projects;

      let errors: string[] = [];

      pros?.forEach((p) => {
        const startDate = dayjs(
          p.start_date || "",
          CFConfig.day_js_date_format
        ).startOf("day");

        const endDate = dayjs(
          p.end_date || "",
          CFConfig.day_js_date_format
        ).endOf("day");

        const isValidOverEndDate = !(
          endDate?.isValid() &&
          !(startDate?.isSame(endDate) || startDate?.isBefore(endDate))
        );

        if (!isValidOverEndDate) {
          errors.push(
            `Start Date must be before or equal to End Date for project ${p.project_name}.`
          );
        }
      });

      if (errors.length > 0) {
        notification.error({
          description: errors[0],
        });
        return;
      }

      const payload = {
        projects: pros?.map((p) => {
          const startDate = dayjs(
            p.start_date || "",
            CFConfig.day_js_date_format
          );
          const endDate = dayjs(p.end_date || "", CFConfig.day_js_date_format);

          return {
            ...p,
            id: p?.id?.toString(),
            start_date: startDate.format("YYYY-MM-DD"),
            end_date: endDate.format("YYYY-MM-DD"),
          };
        }),
      };

      const updateProDatesRes = await updateProjectStartEndDate(payload);

      if (updateProDatesRes.success) {
        await updateSchedulePros(pros?.map((p) => p?.id?.toString()));
        setUpdateProjectDateOpen(false);
      } else {
        notification.error({
          description:
            updateProDatesRes.message || "Failed to update project date.",
        });
      }
    },
  });

  const projectsMap = useMemo(() => {
    return new Map(
      projectsToChangeDates?.map((p) => [
        Number(p.id),
        { hasStartDate: !!p.start_date, hasEndDate: !!p.end_date },
      ])
    );
  }, [projectsToChangeDates]);

  const handleCloseDrawer = () => {
    setUpdateProjectDateOpen(false);
  };

  return (
    <>
      <CommonModal
        isOpen={updateProjectDateOpen}
        widthSize="650px"
        onCloseModal={handleCloseDrawer}
        maskClosable={false}
        modalBodyClass="p-0"
        header={{
          title: _t("Update Project Dates"),
          icon: (
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-address-card"
            />
          ),
          closeIcon: true,
        }}
      >
        <Form method="post" onSubmit={formik.handleSubmit} noValidate>
          <div className="overflow-y-auto p-4 flex flex-col gap-4">
            <SidebarCardBorder addGap={true} className="overflow-visible">
              <div className="grid grid-cols-3 gap-2">
                <div>{_t("Project Name")}</div>
                <FieldLabel labelClass="" required={true}>
                  {_t("Start Date")}
                </FieldLabel>
                <FieldLabel labelClass="" required={true}>
                  {_t("End Date")}
                </FieldLabel>
              </div>
              {projectsToChangeDates?.map((p, index) => {
                return (
                  <div className="grid grid-cols-3 gap-2">
                    <div>
                      {_t(HTMLEntities.decode(sanitizeString(p.project_name)))}
                    </div>
                    <div>
                      <DatePickerField
                        label=""
                        placeholder=""
                        name={`projects[${index}].start_date`}
                        id={`projects[${index}].start_date`}
                        disabled={projectsMap?.get(Number(p.id))?.hasStartDate}
                        value={displayDateFormat(
                          formik.values.projects[index].start_date
                            ?.toString()
                            .trim(),
                          CFConfig.day_js_date_format
                        )}
                        onChange={(date) => {
                          const dateKey = `projects[${index}].start_date`;

                          if (!Array.isArray(date)) {
                            const newDate = !!date
                              ? date?.format(CFConfig.day_js_date_format)
                              : "";
                            formik.setFieldValue(dateKey, newDate);
                          } else {
                            formik.setFieldValue(dateKey, "");
                          }
                        }}
                        format={CFConfig.day_js_date_format}
                        errorMessage={
                          formik.touched.projects?.[index]?.start_date &&
                          !formik.values.projects?.[index]?.start_date
                            ? formik.errors.projects?.[index].start_date
                            : ""
                        }
                      />
                    </div>
                    <div>
                      <DatePickerField
                        label=""
                        placeholder=""
                        name={`projects[${index}].end_date`}
                        id={`projects[${index}].end_date`}
                        disabled={projectsMap?.get(Number(p.id))?.hasEndDate}
                        value={displayDateFormat(
                          formik.values.projects[index].end_date
                            ?.toString()
                            .trim(),
                          CFConfig.day_js_date_format
                        )}
                        onChange={(date) => {
                          const dateKey = `projects[${index}].end_date`;

                          if (!Array.isArray(date)) {
                            const newDate = !!date
                              ? date?.format(CFConfig.day_js_date_format)
                              : "";
                            formik.setFieldValue(dateKey, newDate);
                          } else {
                            formik.setFieldValue(dateKey, "");
                          }
                        }}
                        format={CFConfig.day_js_date_format}
                        errorMessage={
                          formik.touched.projects?.[index]?.end_date &&
                          !formik.values.projects?.[index]?.end_date
                            ? formik.errors.projects?.[index]?.end_date
                            : ""
                        }
                      />
                    </div>
                  </div>
                );
              })}
            </SidebarCardBorder>
          </div>

          <div className="p-5 flex items-center justify-center w-full">
            <PrimaryButton
              type="primary"
              className="w-full justify-center primary-btn"
              htmlType="submit"
              buttonText={_t("Update")}
              isLoading={formik.isSubmitting}
              disabled={formik.isSubmitting}
            />
          </div>
        </Form>
      </CommonModal>
    </>
  );
};

export default memo(UpdateProjectDates);
