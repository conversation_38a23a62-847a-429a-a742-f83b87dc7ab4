import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { useTranslation } from "~/hook";
import { copyExistingSchedule } from "~/redux/action/scehdulerAction";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { useBoolean } from "~/modules/projectManagement/pages/project/hook/use-boolean";
import { Number, sanitizeString } from "~/helpers/helper";
import { useGanttDataStore } from "../../../hooks/useGanttDataStore";
import { useGanttSettings } from "../../../hooks/useGanttSettings";
import { useCurrentModuleAccess } from "../../../hooks/useCurrentModuleAccess";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { SelectProject } from "~/shared/components/organisms/selectProject";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
import { useGantt } from "~/hook/useGantt";

const ImportFromExistingProModal: React.FC<
  Pick<TImportMicroProFileProps, "isOpen" | "onClose"> & { module_key: string }
> = ({ isOpen, onClose, module_key }) => {
  const { _t } = useTranslation();
  const [selectedProject, setSelectedProject] = useState<IProjectSelectParams>({
    id: 0,
    project_name: "",
  });

  const [selectedToProject, setSelectedToProject] =
    useState<IProjectSelectParams>({
      id: 0,
      project_name: "",
    });

  const [activeProSelector, setActiveProSelector] = useState<string>("");

  const loadingBool = useBoolean();
  const projectSelector = useBoolean();
  const { enoughGanttAccess } = useCurrentModuleAccess();
  const { fetchSchduleTasks } = useGanttDataStore();
  const { projectId } = useGanttSettings();
  const { getGanttInstance } = useGantt();

  const isSchedulerModule = useMemo(
    () => module_key === "gantt_chart",
    [module_key]
  );

  const sendReminderHandler = useCallback(async () => {
    const prjId = Number(selectedProject?.id);
    if (!prjId) {
      notification.error({
        description: "Please select project.",
      });
      return;
    } else if (prjId === Number(projectId)) {
      notification.error({
        description: "Project is same. Please Select Different one.",
      });
      return;
    }

    if (isSchedulerModule) {
      if (!selectedToProject?.id) {
        notification.error({
          description: "Please select project to copy to.",
        });
        return;
      } else {
        if (prjId === Number(selectedToProject?.id)) {
          notification.error({
            description: "Project is same. Please Select Different one.",
          });
          return;
        }
      }
    }

    const confirmRes = await confirm(
      _t(
        "Are you 100% sure you want to do this? If existing tasks are assigned to the project schedule that you are copying too, they will be deleted and there is NO way to recover the items."
      )
    );

    if (!confirmRes) {
      return;
    }

    loadingBool.onTrue();

    const copyRes = await copyExistingSchedule({
      project_from: Number(prjId),
      project_to: isSchedulerModule
        ? Number(selectedToProject?.id)
        : Number(projectId),
    });

    if (!copyRes.success) {
      notification.error({
        description: copyRes.message ?? "Failed to copy existing schedule",
      });
    } else {
      fetchSchduleTasks();
      getGanttInstance()?.render();
      onClose();
    }
    loadingBool.onFalse();
  }, [
    selectedProject,
    projectId,
    fetchSchduleTasks,
    getGanttInstance,
    isSchedulerModule,
    selectedToProject,
  ]);

  const onChangeProject = (projects: IProject[]) => {
    const selectorToConsider =
      activeProSelector === "from" ? setSelectedProject : setSelectedToProject;

    if (projects.length) {
      selectorToConsider({
        id: Number(projects[0].id),
        project_name: projects[0].project_name ?? "",
      });
    } else {
      selectorToConsider({
        id: 0,
        project_name: "",
      });
    }
  };

  return (
    <CommonModal
      isOpen={isOpen}
      widthSize="600px"
      onCloseModal={onClose}
      modalBodyClass="p-0"
      header={{
        title: isSchedulerModule
          ? _t("Copy from Existing Project Schedule")
          : _t("Import From Existing Project Schedule"),
        icon: (
          <FontAwesomeIcon className="w-3.5 h-3.5" icon="fa-regular fa-copy" />
        ),
        closeIcon: true,
      }}
    >
      <div className="p-4">
        <div className="modal-body overflow-y-auto max-h-[calc(100vh-200px)]">
          <div className="!mb-[15px]">
            <ButtonField
              label={_t(isSchedulerModule ? "Project From" : "Import From")}
              placeholder={_t("Select Project")}
              labelPlacement="top"
              name="project_id"
              isDisabled={!enoughGanttAccess}
              readOnly={!enoughGanttAccess}
              onClick={() => {
                projectSelector.onTrue();
                setActiveProSelector("from");
              }}
              required={true}
              className="h-6 py-0 !w-full gap-0"
              readOnlyClassName="h-6 !font-medium whitespace-nowrap truncate sm:block flex"
              inputClassName="w-full"
              fieldClassName="!w-full !mt-2"
              spanWidthClass="w-fit"
              value={HTMLEntities.decode(
                sanitizeString(selectedProject?.project_name || "")
              )}
              headerTooltip={`Project: ${HTMLEntities.decode(
                sanitizeString(selectedProject?.project_name || "")
              )}`}
              rightIcon={
                selectedProject?.id ? (
                  <ProjectFieldRedirectionIcon
                    projectId={selectedProject?.id?.toString() || ""}
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                  />
                ) : (
                  ""
                )
              }
            />
          </div>
          {isSchedulerModule && (
            <div>
              <ButtonField
                label={_t("Project To")}
                placeholder={_t("Select Project")}
                labelPlacement="top"
                name="project_id"
                isDisabled={!enoughGanttAccess}
                readOnly={!enoughGanttAccess}
                onClick={() => {
                  projectSelector.onTrue();
                  setActiveProSelector("to");
                }}
                required={true}
                className="h-6 py-0 !w-full gap-0"
                readOnlyClassName="h-6 !font-medium whitespace-nowrap truncate sm:block flex"
                inputClassName="w-full"
                fieldClassName="!w-full !mt-2"
                spanWidthClass="w-fit"
                value={HTMLEntities.decode(
                  sanitizeString(selectedToProject?.project_name || "")
                )}
                headerTooltip={`Project: ${HTMLEntities.decode(
                  sanitizeString(selectedToProject?.project_name || "")
                )}`}
                rightIcon={
                  selectedToProject?.id ? (
                    <ProjectFieldRedirectionIcon
                      projectId={selectedToProject?.id?.toString() || ""}
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    />
                  ) : (
                    ""
                  )
                }
              />
            </div>
          )}
        </div>
        <div className="flex justify-center mt-5">
          <PrimaryButton
            type="primary"
            htmlType="button"
            onClick={sendReminderHandler}
            isLoading={loadingBool.bool}
            disabled={loadingBool.bool}
            buttonText={_t(
              isSchedulerModule ? "Copy Schedule" : "Import Schedule"
            )}
            className="!w-[150px]"
          />
        </div>
      </div>

      {projectSelector.bool && (
        <SelectProject
          isSingleSelect={true}
          open={projectSelector.bool}
          genericProjects="project,opportunity"
          setOpen={() => projectSelector.onFalse()}
          selectedProjects={
            activeProSelector === "from"
              ? selectedProject?.id
                ? [selectedProject]
                : []
              : selectedToProject?.id
              ? [selectedToProject]
              : []
          }
          onProjectSelected={(data) => {
            onChangeProject(data);
          }}
          isRequired={true}
        />
      )}
    </CommonModal>
  );
};

export default ImportFromExistingProModal;
