import React, { useCallback, useEffect, useState } from "react";
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { useTranslation } from "~/hook";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { useBoolean } from "~/modules/projectManagement/pages/project/hook/use-boolean";
import { RadioGroupList } from "~/shared/components/molecules/radioGroupList";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { useGanttSettings } from "../../../hooks/useGanttSettings";
import { updateWeekendDays } from "~/modules/projectManagement/pages/schedule/redux/action/scehduleDashAction";
import { useGanttDataStore } from "../../../hooks/useGanttDataStore";

const ChangeWorkDaysModal: React.FC<
  Pick<TImportMicroProFileProps, "isOpen" | "onClose">
> = ({ isOpen, onClose }) => {
  const { _t } = useTranslation();

  const [selectedWorkDay, setSelectedWorkDay] = useState<string>("");

  const { exclude_weekend_days } = useGanttSettings();
  const { fetchSchduleTasks } = useGanttDataStore();

  const loadingBool = useBoolean();

  useEffect(() => {
    if (exclude_weekend_days !== undefined) {
      setSelectedWorkDay(exclude_weekend_days?.toString());
    }
  }, [exclude_weekend_days]);

  const changeWorkingDaysHandler = useCallback(async () => {
    if (selectedWorkDay?.toString() === exclude_weekend_days?.toString()) {
      return;
    }

    loadingBool.onTrue();
    const updateWeekendRes = await updateWeekendDays({
      option: selectedWorkDay,
    });

    if (!updateWeekendRes.success) {
      notification.error({
        description:
          updateWeekendRes.message || "Failed to update weekend days!",
      });
    } else {
      fetchSchduleTasks();
    }
    loadingBool.onFalse();
  }, [selectedWorkDay]);

  return (
    <CommonModal
      isOpen={isOpen}
      widthSize="600px"
      onCloseModal={onClose}
      modalBodyClass="p-0"
      header={{
        title: _t("Select Your Normal Weekly Work Schedule"),
        icon: (
          <FontAwesomeIcon
            className="w-3.5 h-3.5"
            icon="fa-regular fa-calendar"
          />
        ),
        closeIcon: true,
      }}
    >
      <div className="p-4">
        <div className="modal-body overflow-y-auto max-h-[calc(100vh-200px)] my-2">
          <InlineField
            label={_t("Work Days")}
            labelPlacement="top"
            isRequired={true}
            field={
              <RadioGroupList
                view="col"
                formInputClassName="!px-0 !pt-2 !pb-0"
                value={selectedWorkDay}
                onChange={(e) => setSelectedWorkDay(e.target.value)}
                options={[
                  {
                    label: _t("Monday - Thursday"),
                    value: "3",
                  },
                  {
                    label: _t("Monday - Friday"),
                    value: "2",
                  },
                  {
                    label: _t("Monday - Saturday"),
                    value: "1",
                  },
                  {
                    label: _t("7-Days a Week"),
                    value: "0",
                  },
                ]}
              />
            }
          />
        </div>
        <div className="flex justify-center mt-5">
          <PrimaryButton
            type="primary"
            htmlType="button"
            onClick={changeWorkingDaysHandler}
            isLoading={loadingBool.bool}
            disabled={loadingBool.bool}
            buttonText={_t("Update")}
            className="!w-[150px]"
          />
        </div>
      </div>
    </CommonModal>
  );
};

export default ChangeWorkDaysModal;
