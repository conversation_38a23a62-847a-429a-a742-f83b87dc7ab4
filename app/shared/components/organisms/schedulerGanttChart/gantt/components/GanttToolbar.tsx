import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { But<PERSON> } from "~/shared/components/atoms/button";
import { useGanttScales } from "../../hooks/useGanttScales";
import { adjustGanttCalendarForBaseline } from "../helper/helper";
import { useGanttZoom } from "../../hooks/useGanttZoom";
import { useGanttSettings } from "../../hooks/useGanttSettings";
import { useGantt } from "~/hook/useGantt";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { useTranslation } from "~/hook";
import { useBoolean } from "~/modules/projectManagement/pages/project/hook/use-boolean";
import { SelectProject } from "../../../selectProject";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { updateScheduleProjects } from "~/modules/projectManagement/pages/schedule/redux/action/scehduleDashAction";
import { setProjectId } from "~/redux/slices/schedulerSlice";
import { useGanttDataStore } from "../../hooks/useGanttDataStore";
import { Number } from "~/helpers/helper";
import { fetchGanttScheduleTasks } from "~/redux/action/scehdulerAction";
import UpdateProjectDates from "./action-components/UpdateProjectDates";

const GanttToolbar = () => {
  const { getGanttInstance, dispatch, selector } = useGantt();
  const { _t } = useTranslation();

  const { zoomToFit, restoreDefaultScale, saveDefaultScale } = useGanttScales();
  const { zoomIn, zoomOut, canZoomIn, canZoomOut } = useGanttZoom();
  const { schedule_projects } = selector();

  const projectSelectionDrawer = useBoolean();
  const addProjectDatesDrawer = useBoolean();

  const [projectsToChangeDates, setProjectsToChangeDates] = useState<
    Partial<IProject>[]
  >([]);
  const [currentlySelectedProIds, setCurrentlySelectedProIds] = useState<
    string[]
  >([]);

  const currenMenuModule = getCurrentMenuModule();

  const { module_key } = currenMenuModule || {};

  const {
    zoom_to_fit,
    critical_path,
    auto_scheduling,
    baseline,
    slack,
    updateGanttSettings,
  } = useGanttSettings();

  const ganttReRender = useCallback((g: TGanttStaticTypes) => {
    g.scrollTo(g.getScrollState().x, g.getScrollState().y);
  }, []);

  const handleToggleZoom = useCallback(() => {
    const newVal = !zoom_to_fit;
    if (newVal) {
      saveDefaultScale();
      zoomToFit();
    } else {
      restoreDefaultScale();
    }

    updateGanttSettings({ zoom_to_fit: newVal });
  }, [zoom_to_fit, zoomToFit, saveDefaultScale, restoreDefaultScale]);

  const expandAllTasks = useCallback(() => {
    const g = getGanttInstance();
    if (!g) return;

    g.eachTask((task) => {
      g.open(task.id);
    });
    ganttReRender(g);
  }, [getGanttInstance]);

  const collapseAllTasks = useCallback(() => {
    const g = getGanttInstance();
    if (!g) return;

    g.eachTask((task) => {
      g.close(task.id);
    });
  }, [getGanttInstance]);

  const updateCriticalPath = useCallback(() => {
    const g = getGanttInstance();
    if (!g) return;

    const newPath = !critical_path;

    g.config.highlight_critical_path = !critical_path;

    updateGanttSettings({
      critical_path: newPath,
    });

    ganttReRender(g);
  }, [getGanttInstance, critical_path]);

  const toggleAutoScheduling = useCallback(() => {
    const g = getGanttInstance();
    if (!g) return;

    const newValue = !g.config.auto_scheduling;

    g.config.auto_scheduling = newValue;
    g.config.auto_scheduling_strict = newValue;

    if (newValue) {
      g.autoSchedule(); // enforce links after enabling
    }

    updateGanttSettings({
      auto_scheduling: newValue,
    });

    ganttReRender(g);
  }, [getGanttInstance]);

  const toggleSlack = useCallback(() => {
    const g = getGanttInstance();
    if (!g) return;

    g.config.show_slack = !g.config.show_slack;

    updateGanttSettings({
      slack: g.config.show_slack,
    });

    ganttReRender(g);
  }, [getGanttInstance]);

  const toggleBaseline = useCallback(() => {
    const g = getGanttInstance();
    if (!g) return;

    const newState = !baseline;

    // Update config
    g.config.show_baseline = newState;
    g.config.task_height = newState ? 20 : 28;

    updateGanttSettings({
      baseline: newState,
    });

    // Extend calendar immediately if enabling baseline
    if (newState) {
      adjustGanttCalendarForBaseline(
        g,
        g.getTaskByTime() as TGanttTaskTypes[],
        newState
      );
    }

    ganttReRender(g);
  }, [getGanttInstance, baseline]);

  const selectedPros = useMemo(() => {
    const ids = schedule_projects?.[0]?.value?.split(",");
    const names = schedule_projects?.[0]?.project_names?.split(",");

    return (
      ids?.map((id, index) => ({
        id: Number(id),
        project_name: names?.[index] ?? "",
      })) ?? []
    );
  }, [schedule_projects]);

  const closeDrawer = () => {
    projectSelectionDrawer.onFalse();
    setCurrentlySelectedProIds([]);
    setProjectsToChangeDates([]);
  };

  const updateSchedulePros = useCallback(async (proIds: string[]) => {
    const updateProjectsRes = await updateScheduleProjects({
      projects: proIds,
    });

    if (updateProjectsRes.success) {
      dispatch(setProjectId(proIds?.join(",")));

      dispatch(
        fetchGanttScheduleTasks({
          project: proIds?.join(","),
          module_key: module_key?.toString() || "",
          skipLoading: true,
        })
      );
      closeDrawer();
    } else {
      notification.error({
        description:
          updateProjectsRes.message || "Failed to update schedule projects",
      });
    }
  }, []);

  const onSelectProjects = useCallback(
    async (projects: IProject[]) => {
      const prosToConsider = projects?.filter(
        (p) => !selectedPros?.some((sp) => Number(sp?.id) === Number(p.id))
      );

      const projectsWithNoDate = prosToConsider.filter(
        (project) => !project.start_date || !project.end_date
      );

      const proIds = projects?.map((p) => p?.id?.toString() ?? "");

      if (projectsWithNoDate.length > 0) {
        setCurrentlySelectedProIds(proIds);
        setProjectsToChangeDates(projectsWithNoDate);
        addProjectDatesDrawer.onTrue();
        return;
      }

      closeDrawer();
      updateSchedulePros(proIds);
    },
    [selectedPros, module_key, dispatch]
  );

  return (
    <>
      <div className="flex items-center gap-2 justify-start my-2 flex-wrap">
        {module_key === "gantt_chart" && (
          <Button
            onClick={() => {
              projectSelectionDrawer.onTrue();
            }}
            className="hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900"
          >
            {_t("Projects")}
          </Button>
        )}

        <ButtonWithTooltip
          tooltipPlacement="top"
          tooltipTitle="Expand All"
          onClick={expandAllTasks}
          className="!p-[14px] !border-[1px] !border-gray-200"
          icon="fa-regular fa-angle-down"
          iconClassName="text-base w-3.5 h-3.5 !text-primary-900 dark:!text-white/80"
        />

        <ButtonWithTooltip
          tooltipPlacement="top"
          tooltipTitle="Collapse All"
          onClick={collapseAllTasks}
          className="!p-[14px] !border-[1px] !border-gray-200"
          icon="fa-regular fa-angle-up"
          iconClassName="text-base w-3.5 h-3.5 !text-primary-900 dark:!text-white/80"
        />

        <ButtonWithTooltip
          tooltipPlacement="top"
          tooltipTitle="Undo"
          onClick={() => getGanttInstance()?.undo()}
          className="!p-[14px] !border-[1px] !border-gray-200"
          icon="fa-regular fa-undo"
          iconClassName="text-base w-3.5 h-3.5 !text-primary-900 dark:!text-white/80"
        />

        <ButtonWithTooltip
          tooltipPlacement="top"
          tooltipTitle="Redo"
          onClick={() => getGanttInstance()?.redo()}
          className="!p-[14px] !border-[1px] !border-gray-200"
          icon="fa-regular fa-redo"
          iconClassName="text-base w-3.5 h-3.5 !text-primary-900 dark:!text-white/80"
        />

        <ButtonWithTooltip
          tooltipPlacement="top"
          tooltipTitle="Zoom In"
          onClick={zoomIn}
          disabled={!canZoomIn()}
          className="!p-[14px] !border-[1px] !border-gray-200"
          icon="fa-solid fa-magnifying-glass-plus"
          iconClassName="text-base w-3.5 h-3.5 !text-primary-900 dark:!text-white/80"
        />

        <ButtonWithTooltip
          tooltipPlacement="top"
          tooltipTitle="Zoom Out"
          onClick={zoomOut}
          disabled={!canZoomOut()}
          className="!p-[14px] !border-[1px] !border-gray-200"
          icon="fa-solid fa-magnifying-glass-minus"
          iconClassName="text-base w-3.5 h-3.5 !text-primary-900 dark:!text-white/80"
        />

        <Button
          onClick={handleToggleZoom}
          className="hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900"
        >
          {zoom_to_fit ? "Set Default Scale" : "Zoom to Fit"}
        </Button>

        <Button
          onClick={() => updateCriticalPath()}
          className="hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900"
        >
          {critical_path ? "Hide Critical Path" : "Show Critical Path"}
        </Button>

        <Button
          onClick={toggleAutoScheduling}
          className="hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900"
        >
          {auto_scheduling ? "Disable" : " Enable"} Auto Scheduling
        </Button>

        <Button
          onClick={toggleSlack}
          className="hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900"
        >
          {slack ? "Hide" : "Show"} Slack
        </Button>

        <Button
          onClick={toggleBaseline}
          className="hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900"
        >
          {baseline ? "Hide" : "Show"} Baseline
        </Button>
      </div>

      {projectSelectionDrawer.bool && (
        <SelectProject
          isSingleSelect={false}
          open={projectSelectionDrawer.bool}
          setOpen={() => {}}
          selectedProjects={selectedPros}
          onProjectSelected={(data) => {
            onSelectProjects(data);
          }}
          isRequired={false}
          module_key={module_key}
          genericProjects="project,opportunity"
          onClose={(context) => {
            if (context === "closeIcon") {
              closeDrawer();
            }
          }}
        />
      )}

      {addProjectDatesDrawer.bool && (
        <UpdateProjectDates
          updateProjectDateOpen={addProjectDatesDrawer.bool}
          setUpdateProjectDateOpen={() => addProjectDatesDrawer.onFalse()}
          projectsToChangeDates={projectsToChangeDates}
          updateSchedulePros={() => updateSchedulePros(currentlySelectedProIds)}
        />
      )}
    </>
  );
};

export default GanttToolbar;
