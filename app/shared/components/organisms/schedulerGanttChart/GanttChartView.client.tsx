import React, { use<PERSON>allback, useMemo, useRef } from "react";
import { useWorkTime } from "@dhx/react-gantt";
import moment from "moment";

// Shared
import { ReactGanttChart } from "~/shared/components/molecules/reactGanttview";

// Required components
import GanttToolbar from "./gantt/components/GanttToolbar";
import CustomLightBox from "./gantt/components/CustomLightBox";
import LinkEditModalWrapper from "./gantt/components/LinkEditModalWrapper";
import FullScreenGanttWrapper from "./gantt/components/FullScreenGanttWrapper";

// Action component
import GanttDropdownAction from "./gantt/components/GanttDropdownAction";

// Gantt hooks
import { useGanttDataStore } from "./hooks/useGanttDataStore";
import { useGanttConfig } from "./hooks/useGanttConfig";
import { useGanttStyle } from "./hooks/useGanttStyles";
import { useGanttCommonEvents } from "./hooks/useCommonEvents";
import { useGanttTemplates } from "./hooks/useGanttTemplates";
import { useBaselineLayer } from "./hooks/useBaselineLayer";
import { useLightboxEvents } from "./hooks/useLightboxEvents";
import { useGanttSettings } from "./hooks/useGanttSettings";
import { useGantt } from "~/hook/useGantt";
import { useSlackVisualization } from "./hooks/useSlackVisualization";
import { useCurrentModuleAccess } from "./hooks/useCurrentModuleAccess";
import { useGanttContextMenu } from "./hooks/useGanttContextMenu";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";

const GanttChartView: React.FC<IGanttChartViewProps> = ({
  additionalConfigs = {},
  additionalMarkers = [],
}) => {
  const linkModalRef = useRef<ILinkEditModalRef>(null);
  const fullscreenRef = useRef<IFullScreenGanttWrapperRef>(null);

  const { ganttRef, getGanttInstance } = useGantt();

  const currenMenuModule = getCurrentMenuModule();

  const { module_key } = currenMenuModule || {};

  const { company_date_format, ...currentSettings } = useGanttSettings();

  const { onGanttReady, onCircularLinkError, onParse, onAfterTaskMove } =
    useGanttCommonEvents();

  const { ganttLinks, ganttTasks, taskNLinkCrudHandler, holidays } =
    useGanttDataStore();

  const { enoughGanttAccess } = useCurrentModuleAccess();

  useGanttConfig(); // Adding columns & scales & other configs in gantt
  useGanttStyle(); // Applying Custom styling in gant
  useBaselineLayer(); // Adding Baseline Layer in gantt

  const { updateSlack } = useSlackVisualization(); // Adding Slack Layer in gantt

  const { isWorkTime } = useWorkTime(ganttRef);

  const {
    timeline_cell_class,
    rightside_text,
    task_class,
    ...commonTemplates
  } = useGanttTemplates();

  const {
    onTaskClick,
    onTaskDblClick,
    onBeforeTaskAdd,
    onAfterTaskAdd,
    onBeforeTaskDelete,
    onAfterTaskDelete,
    onAfterTaskUpdate,
  } = useLightboxEvents();

  const checkWidth = useCallback(
    (task: TGanttTaskTypes) => {
      const gantt = getGanttInstance();
      if (!gantt) return false;

      const taskWidth =
        task.end_date && task.start_date
          ? gantt?.posFromDate(task.end_date) -
            gantt?.posFromDate(task.start_date)
          : 100;
      const contentWidth = (task.text?.length || 0) * 7; // assuming average character width
      return contentWidth > taskWidth;
    },
    [getGanttInstance]
  );

  const templates = useMemo(
    () =>
      ({
        ...commonTemplates,
        timeline_cell_class: (_task: TGanttTaskTypes, date: Date) =>
          timeline_cell_class(date, isWorkTime),
        rightside_text: (start: Date, end: Date, task: TGanttTaskTypes) =>
          rightside_text(end, task, checkWidth),
        task_class: (_start: Date, _end: Date, task: TGanttTaskTypes) =>
          task_class(task, checkWidth),
      } as TGanttTemplateTypes),
    [
      commonTemplates,
      checkWidth,
      timeline_cell_class,
      rightside_text,
      task_class,
      isWorkTime,
    ]
  );

  const config = useMemo(
    () =>
      ({
        readonly: !enoughGanttAccess,
        auto_scheduling_compatibility: true,
        auto_scheduling_initial: false,
        auto_scheduling: currentSettings.auto_scheduling,
        auto_scheduling_strict: currentSettings.auto_scheduling,
        show_slack: currentSettings.slack,
        show_baseline: currentSettings.baseline,
        highlight_critical_path: currentSettings.critical_path,
        fit_tasks: true,
        order_branch: "marker",
        touch: true,
        smart_scales: true,
        show_progress: true,
        drag_progress: true,
        drag_project: true,
        autoscroll: true,
        autoscroll_speed: 50,
        branch_loading: true,
        static_background: true,
        work_time: true,
        duration_unit: "day",
        smart_rendering: true,
        scale_height: 60,
        keep_grid_width: false,
        grid_resize: true,
        show_grid: true,
        show_task_cells: true,
        show_tasks_outside_timescale: true,
        open_tree_initially: true,
        task_date: company_date_format,
        multiselect_one_level: true,
        multiselect: false,
        ...additionalConfigs,
      } as TGanttConfigTypes),
    [
      enoughGanttAccess,
      currentSettings.auto_scheduling,
      currentSettings.slack,
      currentSettings.baseline,
      currentSettings.critical_path,
    ]
  );

  const plugins = useMemo(
    () =>
      ({
        tooltip: true,
        click_drag: true,
        auto_scheduling: true,
        critical_path: true,
        drag_timeline: true,
        fullscreen: true,
        grouping: true,
        undo: true,
        marker: true,
        export_api: true,
      } as TGanttPluginsTypes),
    []
  );

  const data: IGanttDataProps = {
    batchSave: (changes) => {
      taskNLinkCrudHandler(changes);
    },
  };

  const handleLinkDblClick = useCallback(
    (id: string) => {
      const gantt = getGanttInstance();
      if (!gantt || gantt.config.readonly) {
        return false;
      }

      linkModalRef.current?.open(id);
    },
    [getGanttInstance]
  );

  const nonWorkingDays = useMemo(() => {
    switch (currentSettings.exclude_weekend_days) {
      case 1:
        return [0];

      case 2:
        return [0, 6];

      case 3:
        return [0, 5, 6];

      default:
        return [];
    }
  }, [currentSettings.exclude_weekend_days]);

  const holidaysDates = useMemo(() => {
    if (holidays.length) {
      return holidays.reduce((acc, h) => {
        const date = moment(h.holiday).format("YYYY-MM-DD");
        acc[date] = false;
        return acc;
      }, {} as Record<string, boolean>);
    }
    return {};
  }, [holidays]);

  const calendars = useMemo(() => {
    return [
      {
        id: "global",
        days: {
          weekdays: {
            0: !nonWorkingDays.includes(0), // Sunday
            1: true,
            2: true,
            3: true,
            4: true,
            5: !nonWorkingDays.includes(5),
            6: !nonWorkingDays.includes(6),
          },
          dates: holidaysDates,
        },
      },
    ];
  }, [nonWorkingDays, holidaysDates]);

  const ganttMarkers = useMemo(
    () => [
      {
        start_date: new Date(),
        css: "today",
        text: "Today",
        title: "Today",
      },
      ...additionalMarkers,
    ],
    []
  );

  if (module_key === "gantt_chart") {
    useGanttContextMenu();
  }

  return (
    <>
      <div className="relative gantt-chart-container !h-full overflow-hidden">
        {/* Gantt toolbar */}
        <div className="flex justify-between items-center">
          <GanttToolbar />

          {/* Gantt Action Dropdown */}
          <GanttDropdownAction />
        </div>

        <div
          id="gantt-fullscreen-wrapper"
          className="gantt-wrapper w-full"
          style={{ height: "540px", width: "100%" }}
        >
          <ReactGanttChart
            key="gantt_chart"
            ref={ganttRef}
            tasks={ganttTasks}
            links={ganttLinks}
            templates={templates}
            data={data}
            markers={ganttMarkers}
            config={config}
            calendars={calendars}
            plugins={plugins}
            onGanttReady={onGanttReady}
            onAfterTaskMove={onAfterTaskMove}
            onParse={onParse}
            onLinkDblClick={handleLinkDblClick}
            onExpand={() => fullscreenRef.current?.handleExpand()}
            onCollapse={() => fullscreenRef.current?.handleCollapse()}
            onCircularLinkError={onCircularLinkError}
            onTaskDblClick={onTaskDblClick}
            onTaskClick={onTaskClick}
            // @ts-ignore
            customLightbox={<CustomLightBox />}
            onBeforeTaskAdd={onBeforeTaskAdd}
            onBeforeTaskDelete={onBeforeTaskDelete}
            onAfterTaskDelete={onAfterTaskDelete}
            onAfterTaskAdd={onAfterTaskAdd}
            onAfterTaskUpdate={onAfterTaskUpdate}
            onAfterLinkAdd={updateSlack}
            onAfterLinkDelete={updateSlack}
          />

          {/* Full screen mode Wrapper */}
          <FullScreenGanttWrapper ref={fullscreenRef} />
        </div>
      </div>

      {/* Edit link Wrapper, opening on double click of any link */}
      <LinkEditModalWrapper ref={linkModalRef} />
    </>
  );
};

export default GanttChartView;
