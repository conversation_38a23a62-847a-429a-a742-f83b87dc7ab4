// Molecules
import { SelectField } from "~/shared/components/molecules/selectField";

import { useCallback, useEffect, useMemo, useState } from "react";
import { getTaxDetails } from "~/redux/action/getTaxDetailsAction";
import { sanitizeString } from "~/helpers/helper";
import { cleanTaxValue } from "~/modules/financials/pages/changeOrder/utils/helpers";
import { useTranslation } from "~/hook";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { apiRoutes } from "~/route-services/routes";
import { useAppDispatch, useAppSelector } from "~/redux/store";
import debounce from "lodash/debounce";

interface ISelectTaxRateProps extends Partial<ISelectFieldProps> {}

export default function SelectTaxRate({ ...props }: ISelectTaxRateProps) {
  const { _t } = useTranslation();
  const dispatch = useAppDispatch();
  const {
    taxDetailsList,
    taxDetailsLoading,
    totalTaxItem,
  }: ITaxDetailsInitialState = useAppSelector((state) => state.taxDetails);
  const [taxOption, setTaxOption] = useState<ITaxDetails[]>(taxDetailsList);
  const [searchValue, setSearchValue] = useState<string>("");

  useEffect(() => {
    if (taxDetailsList && taxDetailsList.length > 0) {
      setTaxOption(taxDetailsList);
    }
  }, [taxDetailsList]);

  const taxRateDetailList: ITaxDetailsList[] = useMemo(() => {
    const items =
      taxOption.length > 0
        ? taxOption?.map((item: ITaxDetails) => ({
            label: HTMLEntities.decode(
              sanitizeString(
                `${item.tax_name} (${cleanTaxValue(item.tax_rate)}%)`
              )
            ),
            value: item.tax_id.toString(),
            taxRate: item.tax_rate,
            ...item,
          }))
        : [];
    return items;
  }, [taxOption, taxDetailsList]);

  useEffect(() => {
    dispatch(
      getTaxDetails({
        status: 0,
        start: 0,
        limit: 100,
        selected_tax_id: props.value,
      })
    );
  }, []);

  const handleSearchCustomer = useCallback(
    debounce((searchCustomer: string) => {
      handleFetchData(searchCustomer, 0, true);
    }, 1000),
    []
  );

  const handleFetchData = async (search = "", start = 0, isSearch = false) => {
    setSearchValue(search);
    const response = (await webWorkerApi<IGetTaxDetailsRes>({
      url: apiRoutes.COMMON.get_tax_details,
      method: "post",
      data: {
        status: 0,
        start: start,
        limit: 100,
        search,
      },
    })) as IGetTaxDetailsRes;
    if (response.success) {
      if (search && isSearch) {
        setTaxOption(response.data.getTaxDetails);
      } else {
        if (start) {
          setTaxOption((prevTaxOption) => [
            ...prevTaxOption,
            ...response.data.getTaxDetails,
          ]);
        } else {
          setTaxOption(response.data.getTaxDetails);
        }
      }
    } else {
      setTaxOption((prevTaxOption) => [...prevTaxOption]);
    }
  };

  const onScroll = async (event: React.UIEvent<HTMLElement>) => {
    const target = event.target as HTMLElement;

    if (
      !taxDetailsLoading &&
      target.scrollTop + target.offsetHeight === target.scrollHeight &&
      totalTaxItem !== taxOption?.length?.toString()
    ) {
      handleFetchData(searchValue, taxOption?.length);
    }
  };

  // ✅ Core logic: After selecting searched item, reload full list
  const handleChange = (
    value: string | string[],
    option?: DefaultOptionType | BaseOptionType
  ) => {
    props?.onChange?.(value, option); // <-- Keep parent handler like handleTaxUpdate
    handleFetchData(); // <-- Reload full list after selection
  };

  return (
    <SelectField
      {...props}
      placeholder={_t(`${props.placeholder}`)}
      onPopupScroll={onScroll}
      labelPlacement={props.labelPlacement || "top"}
      fieldClassName="before:hidden"
      className="border-select-filed-option rounded"
      applyBorder={true}
      allowClear={true}
      options={taxRateDetailList}
      showSearch={true}
      loading={taxDetailsLoading}
      disabled={taxDetailsLoading || props.disabled}
      filterOption={false}
      onSearch={(value) => handleSearchCustomer(value)}
      onChange={handleChange}
    />
  );
}
