import { twMerge } from "tailwind-merge";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Typography } from "~/shared/components/atoms/typography";
import { Tooltip } from "~/shared/components/atoms/tooltip";

const FieldLabel = ({ children, ...props }: IFieldLabelProps) => {
  const { labelClass = "", required, informationProps, ...labelProps } = props;
  return (
    <label
      {...labelProps}
      className={`ant-input-label dark:text-white/90 ${labelClass}`}
    >
      {children}
      {required && (
        <Typography className="inline-block ml-0.5 leading-4 text-primary-900">
          *
        </Typography>
      )}
      {informationProps && Object.keys(informationProps)?.length > 0 && (
        <Tooltip
          title={informationProps?.message}
          rootClassName={informationProps?.rootClassName}
        >
          <FontAwesomeIcon
            className={twMerge(
              `w-3.5 h-3.5 ml-1 text-[#343a40e3] hover:text-primary-900 dark:text-white/90 ${informationProps?.className}`
            )}
            icon={informationProps?.icon ?? "fa-regular fa-circle-question"}
          />
        </Tooltip>
      )}
    </label>
  );
};

export default FieldLabel;
