import { memo, useEffect, useRef, useState } from "react";
import isEmpty from "lodash/isEmpty";
import delay from "lodash/delay";
// Atom
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Typography } from "~/shared/components/atoms/typography";
import { InputField } from "~/shared/components/molecules/inputField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { InfoTitle } from "~/shared/components/molecules/infoTitle";
import { AttachmentCard } from "~/shared/components/organisms/attachmentCard";
// Hook
import { useTranslation } from "~/hook";
// helper
import { sanitizeString } from "~/helpers/helper";

import { notesField, notesFieldStatus } from "~/utils/constasnts";
import { addUpCommonNote } from "~/redux/action/notesActions";
import {
  getStatusActionForField,
  getStatusForField,
} from "~/shared/utils/helper/common";

const NoteCard = ({
  projectid,
  isAddAttachAllow,
  isAttachReadOnly,
  files,
  noteData,
  isDeleting,
  onAddUpNote,
  isLoading = false,
  onAddAttachment,
  onDeleteNote,
  onFileUpdated,
  onDeleteFile,
  validationParams,
  isNotes,
  projectInputProps,
}: INoteCardProps) => {
  const { _t } = useTranslation();

  const decInpRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setInputValues(noteData);
  }, [noteData]);

  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(notesFieldStatus);
  const loadingStatusRef = useRef(notesFieldStatus);
  const [inputValues, setInputValues] = useState<INotesField>(notesField);

  const handleInpOnChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (name === "title" && value.length > 255) {
      notification.error({
        message: _t("Character Limit Exceeded"),
        description: _t("Title cannot exceed 255 characters."),
      });
      return;
    }
    setInputValues({ ...inputValues, [name]: value });
  };

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleUpdateField = async (data: INotesFields) => {
    const field = Object.keys(data)[0] as keyof INotesField;
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });
    const updateRes = (await addUpCommonNote({
      recordId: noteData?.record_id.toString(),
      moduleKey: noteData?.module_key,
      commonNoteId: noteData?.note_id.toString(),
      title: inputValues?.title?.trim() || "",
      description: inputValues?.description?.trim() || "",
    })) as ICommonNoteAddUpRes;
    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      updateRes.action = "update";
      onAddUpNote(updateRes);
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({ ...inputValues, [field]: noteData[field] });
      notification.error({
        description: updateRes?.message,
      });
    }

    // Remove success icon after 3 some second that's why use delay function.
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  return (
    <div className="common-card notes-card p-[15px] group/notes dark:!bg-dark-900">
      <div className="flex flex-col gap-1">
        <div className="flex items-center justify-between">
          <div className="w-[calc(100%-40px)]">
            <InputField
              className="!font-medium"
              placeholder={_t("Title")}
              labelPlacement="left"
              name="title"
              editInline={true}
              readOnly={isAttachReadOnly}
              readOnlyClassName={
                inputValues?.title?.trim() ? "!font-medium" : ""
              }
              maxLength={256}
              iconView={!isAttachReadOnly}
              value={HTMLEntities.decode(sanitizeString(inputValues?.title))}
              fixStatus={getStatusForField(loadingStatus, "title")}
              onChange={handleInpOnChange}
              onMouseEnter={() => {
                handleChangeFieldStatus({
                  field: "title",
                  status: "edit",
                  action: "ME",
                });
              }}
              onMouseLeaveDiv={() => {
                handleChangeFieldStatus({
                  field: "title",
                  status: "button",
                  action: "ML",
                });
              }}
              onFocus={() =>
                handleChangeFieldStatus({
                  field: "title",
                  status: "save",
                  action: "FOCUS",
                })
              }
              onBlur={(e) => {
                const value = e.target.value.trim();
                if (
                  value !== noteData?.title &&
                  (noteData?.title !== null || value)
                ) {
                  handleUpdateField({ title: value });
                } else {
                  handleChangeFieldStatus({
                    field: "title",
                    status: "button",
                    action: "BLUR",
                  });
                  setInputValues({ ...inputValues, title: noteData.title });
                }
              }}
            />
          </div>
          {!isAttachReadOnly && (
            <ButtonWithTooltip
              tooltipTitle={_t("Delete")}
              tooltipPlacement="top"
              icon="fa-regular fa-trash-can"
              disabled={isDeleting}
              onClick={onDeleteNote}
              iconClassName="group-hover/buttonHover:!text-[#FF0000] group-focus-within/buttonHover:!text-[#FF0000]"
              className="sm:opacity-0 group-hover/notes:opacity-100 focus-within:opacity-100 focus-within:!bg-[#FF00001a] hover:!bg-[#FF00001a]"
            />
          )}
        </div>
        <div className="w-full">
          <TextAreaField
            labelPlacement="left"
            editInline={true}
            placeholder={_t("Description")}
            name="description"
            value={HTMLEntities.decode(
              sanitizeString(inputValues?.description)
            )}
            readOnly={isAttachReadOnly}
            iconView={!isAttachReadOnly}
            ref={decInpRef}
            fixStatus={getStatusForField(loadingStatus, "description")}
            onChange={handleInpOnChange}
            onMouseEnter={() => {
              handleChangeFieldStatus({
                field: "description",
                status: "edit",
                action: "ME",
              });
            }}
            onMouseLeaveDiv={() => {
              handleChangeFieldStatus({
                field: "description",
                status: "button",
                action: "ML",
              });
            }}
            onFocus={() =>
              handleChangeFieldStatus({
                field: "description",
                status: "save",
                action: "FOCUS",
              })
            }
            onBlur={(e) => {
              const value = e.target.value.trim();
              if (
                value !== noteData?.description &&
                (noteData?.description !== null || value)
              ) {
                handleUpdateField({ description: value });
              } else {
                handleChangeFieldStatus({
                  field: "description",
                  status: "button",
                  action: "BLUR",
                });
                setInputValues({
                  ...inputValues,
                  description: noteData.description,
                });
              }
            }}
            onClickStsIcon={() => {
              if (getStatusForField(loadingStatus, "description") === "edit") {
                decInpRef.current?.focus();
              }
            }}
          />
        </div>
      </div>
      <div
        className={`flex items-start flex-wrap gap-3.5 my-1.5 ${
          isAttachReadOnly && files?.length === 0 ? "" : "min-h-[126px]"
        } pt-2`}
      >
        <AttachmentCard
          projectid={projectid}
          isAddAllow={isAddAttachAllow}
          isReadOnly={isAttachReadOnly}
          files={files}
          isLoading={isLoading}
          onAddAttachment={onAddAttachment}
          isShowDeleteMenu={true}
          editView={true}
          setUpdatedData={(data) => {
            onFileUpdated(
              !isEmpty(data)
                ? (data as Partial<IgetUpdatedFileRes>)
                : ({} as Partial<IgetUpdatedFileRes>)
            );
          }}
          onDeleteFile={onDeleteFile}
          validationParams={validationParams}
          isNotes={isNotes}
          projectInputProps={projectInputProps}
        />
      </div>
      <div className="flex md:flex-row flex-col md:items-center items-start gap-1 mt-2.5 pr-2.5 whitespace-nowrap overflow-x-auto">
        <div className="flex items-center gap-1 flex-wrap">
          <Typography className="text-gray-700 dark:text-white/90 flex items-center gap-1">
            <FontAwesomeIcon
              className="text-base w-3.5 h-3.5 text-primary-900 dark:text-white/80"
              icon="fa-regular fa-clock"
            />
            {_t("Posted")}:
          </Typography>
          <DateTimeCard
            format="datetime"
            date={noteData?.date_added}
            time={noteData?.time_added}
          />
        </div>
        <div className="flex items-center gap-1">
          <Typography className="text-gray-700 dark:text-white/90">
            {_t("by")}:
          </Typography>
          <InfoTitle
            icon="fa-regular fa-user"
            title={HTMLEntities.decode(sanitizeString(noteData?.added_by))}
            iconClassName="w-3 h-3"
          />
        </div>
      </div>
    </div>
  );
};

export default memo(NoteCard);
