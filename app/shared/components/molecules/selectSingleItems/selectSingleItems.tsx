import { useMemo } from "react";
// Hooks
import { useCurrencyFormatter } from "~/hook";
// Atoms
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import AnimateCheckmark from "~/shared/components/molecules/animateMark/AnimateCheckmark";

const SelectSingleItems = ({
  active,
  avatar,
  name,
  title,
  item,
  favoriteIcon = false,
  itemPriceUnit = false,
  onClick = () => {},
  onClickManageFav = () => {},
  isFavorite,
  isViewAvatar = true,
  subTitle = "",
}: ISelectSingleItemsProps<ObjType>) => {
  const { formatter } = useCurrencyFormatter();
  const unitCostPriceUnit = useMemo(() => {
    if (itemPriceUnit) {
      let unitCostPriceUnit = formatter(
        Number((Number(item?.unit_cost) / 100).toFixed(2))
      );
      if (item?.unit) {
        unitCostPriceUnit += `/${item?.unit}`;
      }
      return unitCostPriceUnit;
    }
    return "";
  }, [itemPriceUnit, item]);

  return (
    <div
      className={`relative first:before:opacity-0 before:absolute before:w-full before:h-px before:-top-[3px] before:bg-gray-200 dark:before:bg-white/10 project ${
        active ? "select-list-item" : ""
      }`}
    >
      <div
        className={`flex items-center justify-between hover:bg-gray-200/30 dark:hover:bg-[#2e3b47] ${
          favoriteIcon ? "pr-[15px]" : "pr-0"
        } ${active ? "bg-gray-200/30 dark:bg-[#2e3b47]" : ""}`}
      >
        <div
          className={`flex items-center justify-between py-[5px] text-primary-900 dark:text-white/90 cursor-pointer ${
            favoriteIcon
              ? "pl-[15px] pr-0 w-[calc(100%-20px)]"
              : "px-[15px] w-full"
          }`}
          onClick={onClick}
        >
          <div className="flex items-center justify-between gap-3 w-[calc(100%-26px)]">
            <div
              className={`flex items-center gap-3 ${
                itemPriceUnit
                  ? "w-[calc(100%-115px)]"
                  : subTitle
                  ? "w-[calc(100%-107px)]"
                  : "w-full"
              }`}
            >
              {isViewAvatar && (
                <AvatarProfile user={{ image: avatar, name: name }} />
              )}
              <div
                className="text-13 max-w-[calc(100%-50px)]"
                style={{ wordBreak: "break-word" }}
              >
                {name && name}
                <b>{title && `${!name ? "" : ","} ${title}`}</b>
              </div>
            </div>
            {subTitle && (
              <Typography className="text-13 text-primary-900">
                {subTitle || ""}
              </Typography>
            )}
            {itemPriceUnit && (
              <Typography className="text-primary-900 dark:text-white/90 font-light text-13">
                {unitCostPriceUnit}
              </Typography>
            )}
          </div>
          <div className="w-5 h-[18px]">
            <div
              className={`transition-all ease-in-out duration-100 overflow-hidden ${
                active ? "block" : "hidden"
              }`}
            >
              <AnimateCheckmark
                circleClass="hidden"
                strokeCircleWidth="8"
                strokePathWidth="8"
                color="#223558"
              />
            </div>
          </div>
        </div>
        <div className="flex cursor-pointer">
          {favoriteIcon && (
            <FontAwesomeIcon
              className={`w-4 h-4 text-deep-orange-500`}
              icon={
                isFavorite !== "0" ? "fa-solid fa-star" : "fa-regular fa-star"
              }
              onClick={() => onClickManageFav(item as ObjType)}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default SelectSingleItems;
