import {
  ActionCreatorWithPayload,
  // Dispatch,
  ThunkDispatch,
  UnknownAction,
} from "@reduxjs/toolkit";
import { Dispatch } from "react";
import { UseSelector } from "react-redux";

declare global {
  interface ISelectCustomerDrawerProps {
    projectId?: number;
    options?: CustomerEmailTab[];
    // isMail?: boolean;
    singleSelecte: boolean;
    closeDrawer?: () => void;
    emailApiCall?: (
      value: SendEmailDrawerFormDataWithApiDefault,
      closeSendMailSidebar: () => void,
      item: boolean
    ) => void;
    groupCheckBox?: boolean;
    isViewAttachment?: boolean;
    isViewSaveThisToFileCheckbox?: boolean;
    saveFileModuleName?: string;
    setCustomer?: (customerDetail: TselectedContactSendMail[]) => void;
    selectedCustomer?: TselectedContactSendMail[];
    openSelectCustomerSidebar?: boolean;
    showSaveBtn?: boolean;
    app_access?: boolean;
    canWrite?: boolean;
    additionalContactDetails?: number;
    activeTab?: string;
    hideAddBtnForFilter?: boolean;
    showCustomField?: boolean;
    is_all_tab?: boolean;
    modulesShouldNotHaveAddButton?: CustomerEmailTab[];
    buttontext?: string;
    isLoading?: boolean;
    loadingOnSave?: boolean;
  }

  interface SendEmailDrawerFormData {
    custom_subject: string;
    send_me_copy: boolean;
    emails: string[] | [];
    selected_user_arr?: Partial<TSendEmailCustomerSelectedData>[];
    custom_msg: string;
    method_to_send: "group" | "individual";
    send_custom_email: number;
    attached_email_files?: string[];
    save_file_to_folder?: string;
    "g-recaptcha-response"?: string | null;
  }

  interface SendEmailDrawerFormDataWithApiDefault
    extends SendEmailDrawerFormData,
      ApiDefaultParams {
    "g-recaptcha-response"?: string | null;
  }

  interface ISelectOptionsProps<TData> {
    addFunction?: React.MouseEventHandler;
    onClick?: (value: TData) => void;
    selectedOption: TData;
    themeSidebar?: boolean;
    optionList: CustomerSelectOption<TData>[];
    canWrite?: boolean;
    hideAddBtnForFilter?: boolean;
  }

  interface ICustomerSelectedProps {
    showSaveBtn?: boolean;
    loadMore: () => void;
    options?: CustomerEmailTab[];
    isInfiniteScrollLoading: boolean;
    hasMore: boolean;
    infiniteScrollHideLoadingComponent: boolean;
    selectedContact: TselectedContactSendMail[];
    receivedContact: TselectedContactSendMail[];
    dispatch: Dispatch<SendEmailAction>;
    activeField: CustomerEmailTab;
    customer: TInitialStateEmailCustomers["customer"];
    singleSelecte: boolean;
    sendEmail?: boolean;
    setSendEmail?: (value: boolean) => void;
    emailApiCall?: (
      value: SendEmailFormDataWithApiDefault,
      closeSendMailSidebar: () => void,
      item: boolean
    ) => void;
    groupCheckBox?: boolean;
    closeDrawer: () => void;
    isViewAttachment?: boolean;
    isViewSaveThisToFileCheckbox?: boolean;
    saveFileModuleName?: string;
    setCustomer?: (customerDetail: TselectedContactSendMail[]) => void;
    scrollRef?: React.RefObject<HTMLDivElement>;
    rightScrollRef?: React.RefObject<HTMLDivElement>;
    is_all_tab?: boolean;
    buttontext?: string;
    isLoading?: boolean;
    loadingOnSave?: boolean;
  }

  interface ISingleItemSendEmailProps<TData> {
    avatar?: string;
    name: string;
    title?: string;
    active: boolean;
    favoriteIcon?: boolean;
    itemPriceUnit?: boolean;
    onClick?: React.MouseEventHandler<HTMLDivElement>;
    isFavorite?: string;
    onClickManageFav?: (item: TData) => void;
    item?: { unit_cost: string; unit: string };
    isViewAvatar?: boolean;
    subName?: string;
    isCustomerRemovable?: boolean;
    iconPadding?: boolean;
  }

  interface ISendEmailSidebarSelectHeaderProps<TData> {
    sendEmail?: boolean;
    setSendEmail?: (value: boolean) => void;
    headerTitle: string;
    headerTitleIcon: IFontAwesomeIconProps["icon"];
  }

  interface ISendEmailSelectItemProps {
    name: string;
    avatar?: string;
    onClick?: () => void;
    subName?: string;
    isIcon?: boolean;
    isMail?: boolean;
    email?: string;
    externalEmail?: boolean;
    isCustomerRemovable?: boolean;
  }

  interface ISendEmailAccordionItemsListProps {
    children: string | JSX.Element | JSX.Element[];
    name: string;
    active: boolean;
    selectAll: (value: boolean) => void;
    checkBoxView: boolean;
  }

  interface IAddMultiselectDirectorySideProps {
    addMultiselectDirectoryOpen: boolean;
    setAddMultiselectDirectoryOpen: (value: boolean) => void;
    selectOption: CustomerSelectOption<CustomerEmailTab>;
    dispatch: Dispatch<SendEmailAction>;
    singleSelecte: boolean;
    setCustomer?: (customerDetail: TselectedContactSendMail[]) => void;
    closeDrawer: () => void;
  }

  interface IListOfSeletedCustomersDrawerProps {
    selectedContact: TselectedContactSendMail[];
    isViewSaveThisToFileCheckbox?: boolean;
    rightScrollRef?: React.RefObject<HTMLDivElement>;
    activeField: CustomerEmailTab;
    onClick: (
      employee:
        | CustomerEmail
        | DirectoryEmail
        | GroupDetailEmailEmployee
        | ISetEmail
    ) => void;
  }

  type ISelectCustomerAction =
    | { type: "SET_ACTIVE_FIELD"; payload: CustomerEmailTab }
    | {
        type: "SET_SELECTED_CONTACT";
        payload: TSetSelectedContactEmailActionPayload;
      }
    | { type: "SET_OPEN_SEND_EMAIL_SIDEBAR"; payload: boolean }
    | { type: "SET_INCREMENT_PAGE_NUMBER"; payload: CustomerEmailTab }
    | { type: "SET_SEARCH"; payload: ISetSearch }
    | { type: "SET_MAIN_SEARCH"; payload: ISetSearch }
    | {
        type: "GET_EMAIL_DETAILS_PENDING";
        payload: {
          tab: CustomerEmailTab;
          pageNumber?: number;
          formState?: IFormInitialState;
        };
      }
    | {
        type: "SET_ADD_DIRECOTRY_DATA";
        payload: {
          tab: CustomerEmailTab;
          response: Partial<IGetGlobalDirectoryApiResponseCustomer>;
          formState?: boolean | IFormInitialState;
          app_access?: boolean;
          pageNumber?: number;
        };
      }
    | {
        type: "GET_EMAIL_DETAILS_FULFILLED";
        payload: {
          tab: CustomerEmailTab;
          response: IGetGlobalDirectoryApiResponse;
          formState?: boolean | IFormInitialState;
          app_access?: boolean;
          pageNumber?: number;
          options?: CustomerEmailTab[];
        };
      }
    | {
        type: "CHANGE_FAV_ACTION_FULFILLED";
        payload: {
          response: IChangeFav;
          unique_id: string;
          is_favorite: number;
        };
      }
    | {
        type: "CHANGE_IS_FAVORITE_MAIN";
        payload: {
          tab: CustomerEmailTab;
          is_favorite: boolean;
          is_all_tab: boolean;
          callAgain?: number;
        };
      }
    | {
        type: "RESET_ALL_DATA";
        payload?: {};
      }
    | {
        type: "SET_RECEIVED_SELECTED_CONTATACT";
        payload: TselectedContactSendMail[];
      };
  interface IActiveFieldName {
    employee: string;
    my_crew: string;
    customer: string;
    contractor: string;
    vendor: string;
    misc_contact: string;
    lead: string;
    by_service: string;
    my_project: string;
    all: string;
  }
  interface ICustomerTypeName {
    [key: string]: string;
  }
}
