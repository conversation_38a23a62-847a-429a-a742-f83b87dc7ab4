import { replaceDOMParams, sanitizeString } from "~/helpers/helper";
import AccordionItemsList from "./AccordionItemsList";
import ItemsList from "./ItemsList";
import isEmpty from "lodash/isEmpty";
import SingleItem from "./SingleItem";
import { defaultConfig } from "~/data";
import { getUniqueIdDirData } from "../../sendEmailDrawer/utils";
import { Typography } from "~/shared/components/atoms/typography";
import NoDataFound from "~/components/common/no-data-found";

type Props = {
  customer: TInitialStateEmailCustomers["customer"];
  activeField: CustomerEmailTab;
  selectedContact: Partial<CustomerSelectedData>[];
  singleSelecte: boolean;
  dispatchSelectedContact: (data: TselectedContactSendMail) => void;
  dispatchChangeFavAction: (favData: CustomerEmail) => void;
  receivedContact: TselectedContactSendMail[];
  options?: CustomerEmailTab[];
};

const GetSelectedCom = ({
  customer,
  activeField,
  selectedContact,
  receivedContact = [],
  singleSelecte,
  dispatchSelectedContact,
  dispatchChangeFavAction,
  options,
}: Props) => {
  const renderItem = (list: CustomerEmail[]) => {
    const data = list.filter((item: CustomerEmail, key: number) => {
      return !receivedContact.some(
        (selectedItem: TselectedContactSendMail) =>
          getUniqueIdDirData(selectedItem) != getUniqueIdDirData(item)
      );
    });
    const filterData = list.filter((item: CustomerEmail, key: number) => {
      return !receivedContact.some(
        (selectedItem: TselectedContactSendMail) =>
          getUniqueIdDirData(selectedItem) == getUniqueIdDirData(item)
      );
    });

    const finalArr = singleSelecte
      ? JSON.stringify(data) === JSON.stringify(filterData)
        ? list
        : [...data, ...filterData]
      : list;
    return (
      <>
        {/* {list.length > 0 &&
          receivedContact.map((item: TselectedContactSendMail, key) => {
            if (item.type_key !== activeField) {
              return <></>;
            }

            if (
              customer[activeField].mainSearch &&
              !item.display_name?.includes(customer[activeField].mainSearch) &&
              !item.title?.includes(customer[activeField].mainSearch)
            ) {
              return <></>;
            }

            // if favorite in active field and selected contact is not fav then don't show
            // state_fav.customer[state_fav.activeField].isFavorite
            if (
              (activeField === "misc_contact" ||
                activeField === "contractor" ||
                activeField === "vendor" ||
                activeField === defaultConfig.lead_key) &&
              customer[activeField].isFavorite &&
              Number(item.is_favorite) === 0
            ) {
              return <></>;
            }

            return (
              <ItemsList
                avatar={undefined}
                key={key}
                name={replaceDOMParams(sanitizeString(item.display_name ?? ""))}
                favoriteIcon={
                  item.is_favorite !== null &&
                  (activeField === "misc_contact" ||
                    activeField === "contractor" ||
                    activeField === "vendor" ||
                    activeField === defaultConfig.lead_key)
                }
                isFavorite={Number(item?.is_favorite) == 1 ? "1" : "0"}
                active={selectedContact?.some(
                  (selectedItem: Partial<CustomerSelectedData>) =>
                    getUniqueIdDirData(selectedItem) ===
                    getUniqueIdDirData(item)
                )}
                onClick={() => {
                  dispatchSelectedContact(item);
                }}
                onClickManageFav={() => dispatchChangeFavAction(item)}
              />
            );
          })} */}

        {finalArr.map((item: CustomerEmail, key: number) => {
          item.contact_id =
            typeof item?.contact_id === "string"
              ? Number(item.contact_id)
              : item.contact_id;
          return activeField === "my_project" ? (
            <ItemsList
              avatar={!isEmpty(item?.image) ? item?.image : undefined}
              key={key}
              name={replaceDOMParams(sanitizeString(item.display_name ?? ""))}
              favoriteIcon={false}
              active={selectedContact?.some(
                (selectedItem: Partial<CustomerSelectedData>) =>
                  getUniqueIdDirData(selectedItem) === getUniqueIdDirData(item)
              )}
              onClick={() => {
                dispatchSelectedContact(item);
              }}
              subName={item?.type_name}
            />
          ) : (
            <ItemsList
              avatar={!isEmpty(item?.image) ? item?.image : undefined}
              key={key}
              name={item.display_name ?? ""}
              favoriteIcon={
                item?.contact_id == 0 &&
                (activeField === "misc_contact" ||
                  activeField === "contractor" ||
                  activeField === "vendor" ||
                  activeField === defaultConfig.lead_key)
              }
              iconPadding={
                item?.contact_id != 0 &&
                (activeField === "misc_contact" ||
                  activeField === "contractor" ||
                  activeField === "vendor" ||
                  activeField === defaultConfig.lead_key)
              }
              isFavorite={Number(item?.is_favorite) == 0 ? "0" : "1"}
              onClickManageFav={() => dispatchChangeFavAction(item)}
              active={selectedContact?.some(
                (selectedItem: Partial<CustomerSelectedData>) =>
                  getUniqueIdDirData(selectedItem) === getUniqueIdDirData(item)
              )}
              onClick={() => {
                // dispatch(setSelectedContact(item));
                dispatchSelectedContact(item);
              }}
              subName={item?.type_name}
              activeField={activeField}
            />
          );
        })}
      </>
    );
  };

  const renderGroup = (groups: GroupEmailDetail[]) => {
    return groups.map((group, key) => {
      typeof group?.contact_id === "string"
        ? Number(group.contact_id)
        : group.contact_id;
      const isActiveCheck =
        group.employees?.length !== 0 && selectedContact?.length !== 0
          ? group.employees?.every((data) =>
              selectedContact?.some(
                (item: Partial<CustomerSelectedData>) =>
                  getUniqueIdDirData(item) === getUniqueIdDirData(data)
              )
            )
          : false;
      let name = sanitizeString(group.group_name);
      if (!isEmpty(name)) {
        name = replaceDOMParams(name);
      }
      return (
        <AccordionItemsList
          key={key}
          name={name}
          checkBoxView={!singleSelecte}
          selectAll={(selectAll: boolean) => {
            if (group.employees?.length > 0) {
              if (selectAll) {
                group.employees.forEach((data) => {
                  if (
                    !selectedContact?.find(
                      (selectedItem: Partial<CustomerSelectedData>) =>
                        getUniqueIdDirData(selectedItem) ===
                        getUniqueIdDirData(data)
                    )
                  ) {
                    // dispatch(setSelectedContact(data));
                    dispatchSelectedContact(data);
                  }
                });
              } else {
                group.employees?.forEach((data) => {
                  if (
                    selectedContact?.find(
                      (selectedItem: Partial<CustomerSelectedData>) =>
                        getUniqueIdDirData(selectedItem) ===
                        getUniqueIdDirData(data)
                    )
                  ) {
                    // dispatch(setSelectedContact(data));
                    dispatchSelectedContact(data);
                  }
                });
              }
            }
          }}
          active={isActiveCheck}
        >
          {group.employees?.length > 0 ? (
            group.employees?.map((data, key) => {
              let name: string = data?.display_name ?? "";
              if (isEmpty(name)) {
                name = data?.unique_name;
              }
              if (!isEmpty(name)) {
                name = replaceDOMParams(name);
              }
              return (
                <SingleItem
                  avatar={!isEmpty(data?.image) ? data?.image : undefined}
                  name={sanitizeString(name)}
                  active={selectedContact?.some(
                    (item: Partial<CustomerSelectedData>) =>
                      item.user_id?.toString() === data.user_id?.toString()
                  )}
                  onClick={() => dispatchSelectedContact(data)}
                  key={key}
                  subName={data?.type_name}
                />
              );
            })
          ) : (
            <Typography className="text-center">No data</Typography>
          )}
        </AccordionItemsList>
      );
    });
  };

  const renderDirectories = (directories: DirectoryEmailServiceList[]) => {
    return directories.map((directory, key) => {
      const isActiveCheck =
        directory.directories?.length !== 0 && selectedContact?.length !== 0
          ? directory.directories?.every((data) =>
              selectedContact?.some(
                (item: Partial<CustomerSelectedData>) =>
                  getUniqueIdDirData(item) === getUniqueIdDirData(data)
              )
            )
          : false;
      let name = sanitizeString(directory.service_name);
      if (!isEmpty(name)) {
        name = replaceDOMParams(name);
      }
      return (
        <>
          {directory.directories?.length > 0 && (
            <AccordionItemsList
              key={key}
              name={name}
              checkBoxView={!singleSelecte}
              selectAll={(selectAll: boolean) => {
                if (directory.directories?.length > 0) {
                  if (selectAll) {
                    directory.directories?.forEach((data) => {
                      if (
                        !selectedContact?.find(
                          (selectedItem: Partial<CustomerSelectedData>) =>
                            getUniqueIdDirData(selectedItem) ===
                            getUniqueIdDirData(data)
                        )
                      ) {
                        dispatchSelectedContact(data);
                      }
                    });
                  } else {
                    directory.directories?.forEach((data) => {
                      if (
                        selectedContact?.find(
                          (selectedItem: Partial<CustomerSelectedData>) =>
                            getUniqueIdDirData(selectedItem) ===
                            getUniqueIdDirData(data)
                        )
                      ) {
                        dispatchSelectedContact(data);
                      }
                    });
                  }
                }
              }}
              active={isActiveCheck}
            >
              {directory.directories?.map((data, key) => {
                typeof data?.contact_id === "string"
                  ? Number(data.contact_id)
                  : data.contact_id;
                let name: string = data?.display_name ?? "";
                if (isEmpty(name)) {
                  name = data?.unique_name;
                }
                if (!isEmpty(name)) {
                  name = replaceDOMParams(name);
                }
                return (
                  <SingleItem
                    avatar={!isEmpty(data?.image) ? data?.image : undefined}
                    name={sanitizeString(name)}
                    active={selectedContact?.some(
                      (item: Partial<CustomerSelectedData>) =>
                        getUniqueIdDirData(item) === getUniqueIdDirData(data)
                    )}
                    onClick={() => dispatchSelectedContact(data)}
                    key={key}
                    subName={data?.type_name}
                  />
                );
              })}
            </AccordionItemsList>
          )}
        </>
      );
    });
  };

  if (activeField !== "my_crew" && activeField !== "by_service") {
    return renderItem(customer[activeField]?.list);
  } else if (activeField === "my_crew") {
    return renderGroup(customer.my_crew.list);
  } else if (activeField === "by_service") {
    return renderDirectories(customer.by_service.list);
  } else {
    return <></>;
  }
};

export default GetSelectedCom;
