import { library } from "@fortawesome/fontawesome-svg-core";
import { faAngleUp } from "@fortawesome/pro-regular-svg-icons/faAngleUp";
import { faAngleDown } from "@fortawesome/pro-regular-svg-icons/faAngleDown";
import { faUndo } from "@fortawesome/pro-regular-svg-icons/faUndo";
import { faRedo } from "@fortawesome/pro-regular-svg-icons/faRedo";
import { faCopy } from "@fortawesome/pro-regular-svg-icons/faCopy";
import { faCalendar } from "@fortawesome/pro-regular-svg-icons/faCalendar";

export const SchduleRegularIconAdd = () => {
  library.add([faAngleDown, faAngleUp, faUndo, faRedo, faCopy, faCalendar]);
};
