import { library } from "@fortawesome/fontawesome-svg-core";
import { faUserTie } from "@fortawesome/pro-solid-svg-icons/faUserTie";
import { faUser } from "@fortawesome/pro-solid-svg-icons/faUser";
import { faPhone } from "@fortawesome/pro-solid-svg-icons/faPhone";
import { faUserTieHair } from "@fortawesome/pro-solid-svg-icons/faUserTieHair";
import { faUserVneck } from "@fortawesome/pro-solid-svg-icons/faUserVneck";
import { faMemoCircleInfo } from "@fortawesome/pro-solid-svg-icons/faMemoCircleInfo";
import { faFileInvoice } from "@fortawesome/pro-solid-svg-icons/faFileInvoice";
import { faScrewdriverWrench } from "@fortawesome/pro-solid-svg-icons/faScrewdriverWrench";
import { faMemoCircleCheck } from "@fortawesome/pro-solid-svg-icons/faMemoCircleCheck";
import { faFileLines } from "@fortawesome/pro-solid-svg-icons/faFileLines";
import { faFileImage } from "@fortawesome/pro-solid-svg-icons/faFileImage";
import { faCartCircleXmark } from "@fortawesome/pro-solid-svg-icons/faCartCircleXmark";
import { faCalculator } from "@fortawesome/pro-solid-svg-icons/faCalculator";
import { faSackDollar } from "@fortawesome/pro-solid-svg-icons/faSackDollar";
import { faFileInvoiceDollar } from "@fortawesome/pro-solid-svg-icons/faFileInvoiceDollar";
import { faSquareDollar } from "@fortawesome/pro-solid-svg-icons/faSquareDollar";
import { faMoneyCheckDollarPen } from "@fortawesome/pro-solid-svg-icons/faMoneyCheckDollarPen";
import { faFileSignature } from "@fortawesome/pro-solid-svg-icons/faFileSignature";
import { faBagShopping } from "@fortawesome/pro-solid-svg-icons/faBagShopping";
import { faBadgeDollar } from "@fortawesome/pro-solid-svg-icons/faBadgeDollar";
import { faEnvelopeOpenDollar } from "@fortawesome/pro-solid-svg-icons/faEnvelopeOpenDollar";
import { faMoneyCheck } from "@fortawesome/pro-solid-svg-icons/faMoneyCheck";
import { faClipboardListCheck } from "@fortawesome/pro-solid-svg-icons/faClipboardListCheck";
import { faFileChartColumn } from "@fortawesome/pro-solid-svg-icons/faFileChartColumn";
import { faSquarePollVertical } from "@fortawesome/pro-solid-svg-icons/faSquarePollVertical";
import { faCalendarLines } from "@fortawesome/pro-solid-svg-icons/faCalendarLines";
import { faBoxCircleCheck } from "@fortawesome/pro-solid-svg-icons/faBoxCircleCheck";
import { faUserGroup } from "@fortawesome/pro-solid-svg-icons/faUserGroup";
import { faCalendarDays } from "@fortawesome/pro-solid-svg-icons/faCalendarDays";
import { faCalendar } from "@fortawesome/pro-solid-svg-icons/faCalendar";
import { faChartMixed } from "@fortawesome/pro-solid-svg-icons/faChartMixed";
import { faBoxDollar } from "@fortawesome/pro-solid-svg-icons/faBoxDollar";
import { faExclamation } from "@fortawesome/pro-solid-svg-icons/faExclamation";
import { faFolder } from "@fortawesome/pro-solid-svg-icons/faFolder";
import { faFolders } from "@fortawesome/pro-solid-svg-icons/faFolders";
import { faMagnifyingGlassMinus } from "@fortawesome/pro-solid-svg-icons/faMagnifyingGlassMinus";
import { faMagnifyingGlassPlus } from "@fortawesome/pro-solid-svg-icons/faMagnifyingGlassPlus";
import { faSquare } from "@fortawesome/pro-solid-svg-icons/faSquare";

export const ProjectDetailSolidIconAdd = () => {
  library.add([
    faUser,
    faUserTie,
    faPhone,
    faUserTieHair,
    faUserVneck,
    faMemoCircleInfo,
    faFileInvoice,
    faScrewdriverWrench,
    faMemoCircleCheck,
    faFileLines,
    faFileImage,
    faCartCircleXmark,
    faCalculator,
    faSackDollar,
    faFileInvoiceDollar,
    faSquareDollar,
    faMoneyCheckDollarPen,
    faFileSignature,
    faBagShopping,
    faBadgeDollar,
    faEnvelopeOpenDollar,
    faMoneyCheck,
    faClipboardListCheck,
    faFileChartColumn,
    faSquarePollVertical,
    faCalendarLines,
    faBoxCircleCheck,
    faUserGroup,
    faCalendarDays,
    faCalendar,
    faChartMixed,
    faBoxDollar,
    faExclamation,
    faFolder,
    faFolders,
    faMagnifyingGlassMinus,
    faMagnifyingGlassPlus,
    faSquare,
  ]);
};
