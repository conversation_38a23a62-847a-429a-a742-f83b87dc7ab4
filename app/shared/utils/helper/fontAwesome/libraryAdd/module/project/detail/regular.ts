import { library } from "@fortawesome/fontawesome-svg-core";
import { faNeuter } from "@fortawesome/pro-regular-svg-icons/faNeuter";
import { faLockKeyholeOpen } from "@fortawesome/pro-regular-svg-icons/faLockKeyholeOpen";
import { faListUl } from "@fortawesome/pro-regular-svg-icons/faListUl";
import { faUsd } from "@fortawesome/pro-regular-svg-icons/faUsd";
import { faSquareCheck } from "@fortawesome/pro-regular-svg-icons/faSquareCheck";
import { faCalendar } from "@fortawesome/pro-regular-svg-icons/faCalendar";
import { faHouse } from "@fortawesome/pro-regular-svg-icons/faHouse";
import { faCalendarLines } from "@fortawesome/pro-regular-svg-icons/faCalendarLines";
import { faFileImport } from "@fortawesome/pro-regular-svg-icons/faFileImport";
import { faFileInvoiceDollar } from "@fortawesome/pro-regular-svg-icons/faFileInvoiceDollar";
import { faRectangleList } from "@fortawesome/pro-regular-svg-icons/faRectangleList";
import { faCalendarLinesPen } from "@fortawesome/pro-regular-svg-icons/faCalendarLinesPen";
import { faNotesMedical } from "@fortawesome/pro-regular-svg-icons/faNotesMedical";
import { faClock } from "@fortawesome/pro-regular-svg-icons/faClock";
import { faFileImage } from "@fortawesome/pro-regular-svg-icons/faFileImage";
import { faPhone } from "@fortawesome/pro-regular-svg-icons/faPhone";
import { faSquarePollVertical } from "@fortawesome/pro-regular-svg-icons/faSquarePollVertical";
import { faMoneyCheck } from "@fortawesome/pro-regular-svg-icons/faMoneyCheck";
import { faUniversalAccess } from "@fortawesome/pro-regular-svg-icons/faUniversalAccess";
import { faFileChartColumn } from "@fortawesome/pro-regular-svg-icons/faFileChartColumn";
import { faPercent } from "@fortawesome/pro-regular-svg-icons/faPercent";
import { faDollarSign } from "@fortawesome/pro-regular-svg-icons/faDollarSign";
import { faCalendarDays } from "@fortawesome/pro-regular-svg-icons/faCalendarDays";
import { faCircleCheck } from "@fortawesome/pro-regular-svg-icons/faCircleCheck";
import { faMemoPad } from "@fortawesome/pro-regular-svg-icons/faMemoPad";
import { faListTree } from "@fortawesome/pro-regular-svg-icons/faListTree";
import { faFolder } from "@fortawesome/pro-regular-svg-icons/faFolder";
import { faList } from "@fortawesome/pro-regular-svg-icons/faList";
import { faTableCellsLarge } from "@fortawesome/pro-regular-svg-icons/faTableCellsLarge";
import { faArrowDown19 } from "@fortawesome/pro-regular-svg-icons/faArrowDown19";
import { faFileLines } from "@fortawesome/pro-regular-svg-icons/faFileLines";
import { faArrowDownToLine } from "@fortawesome/pro-regular-svg-icons/faArrowDownToLine";
import { faFileExport } from "@fortawesome/pro-regular-svg-icons/faFileExport";
import { faDownToBracket } from "@fortawesome/pro-regular-svg-icons/faDownToBracket";
import { faBoxesStacked } from "@fortawesome/pro-regular-svg-icons/faBoxesStacked";
import { faDownload } from "@fortawesome/pro-regular-svg-icons/faDownload";
import { faHandHoldingCircleDollar } from "@fortawesome/pro-regular-svg-icons/faHandHoldingCircleDollar";
import { faClipboardListCheck } from "@fortawesome/pro-regular-svg-icons/faClipboardListCheck";
import { faCartCircleCheck } from "@fortawesome/pro-regular-svg-icons/faCartCircleCheck";
import { faNotes } from "@fortawesome/pro-regular-svg-icons/faNotes";
import { faAlignLeft } from "@fortawesome/pro-regular-svg-icons/faAlignLeft";
import { faChartMixedUpCircleDollar } from "@fortawesome/pro-regular-svg-icons/faChartMixedUpCircleDollar";
import { faEquals } from "@fortawesome/pro-regular-svg-icons/faEquals";
import { faArrowUp } from "@fortawesome/pro-regular-svg-icons/faArrowUp";
import { faPencil } from "@fortawesome/pro-regular-svg-icons/faPencil";
import { faCircleExclamation } from "@fortawesome/pro-regular-svg-icons/faCircleExclamation";
import { faFilePen } from "@fortawesome/pro-regular-svg-icons/faFilePen";
import { faHospital } from "@fortawesome/pro-regular-svg-icons/faHospital";
import { faTriangleExclamation } from "@fortawesome/pro-regular-svg-icons/faTriangleExclamation";
import { faCalculator } from "@fortawesome/pro-regular-svg-icons/faCalculator";
import { faArrowDownArrowUp } from "@fortawesome/pro-regular-svg-icons/faArrowDownArrowUp";
import { faUserPlus } from "@fortawesome/pro-regular-svg-icons/faUserPlus";
import { faAngleRight } from "@fortawesome/pro-regular-svg-icons/faAngleRight";
import { faEdit } from "@fortawesome/pro-regular-svg-icons/faEdit";
import { faAngleUp } from "@fortawesome/pro-regular-svg-icons/faAngleUp";
import { faArrowDownAZ } from "@fortawesome/pro-regular-svg-icons/faArrowDownAZ";
import { faFiles } from "@fortawesome/pro-regular-svg-icons/faFiles";
import { faFolders } from "@fortawesome/pro-regular-svg-icons/faFolders";
import { faAngleDown } from "@fortawesome/pro-regular-svg-icons/faAngleDown";
import { faCompress } from "@fortawesome/pro-regular-svg-icons/faCompress";
import { faExpand } from "@fortawesome/pro-regular-svg-icons/faExpand";
import { faUndo } from "@fortawesome/pro-regular-svg-icons/faUndo";
import { faRedo } from "@fortawesome/pro-regular-svg-icons/faRedo";
import { faClipboardList } from "@fortawesome/pro-regular-svg-icons/faClipboardList";
import { faCopy } from "@fortawesome/pro-regular-svg-icons/faCopy";

export const ProjectDetailRegularIconAdd = () => {
  library.add([
    faNotes,
    faLockKeyholeOpen,
    faNeuter,
    faListUl,
    faUsd,
    faSquareCheck,
    faCalendar,
    faHouse,
    faCalendarLines,
    faFileImport,
    faFileInvoiceDollar,
    faRectangleList,
    faCalendarLinesPen,
    faNotesMedical,
    faClock,
    faFileImage,
    faPhone,
    faSquarePollVertical,
    faMoneyCheck,
    faUniversalAccess,
    faFileChartColumn,
    faPercent,
    faDollarSign,
    faCalendarDays,
    faCircleCheck,
    faMemoPad,
    faListTree,
    faFolder,
    faList,
    faTableCellsLarge,
    faArrowDown19,
    faFileLines,
    faArrowDownToLine,
    faFileExport,
    faDownToBracket,
    faBoxesStacked,
    faDownload,
    faHandHoldingCircleDollar,
    faClipboardListCheck,
    faCartCircleCheck,
    faAlignLeft,
    faChartMixedUpCircleDollar,
    faEquals,
    faPencil,
    faArrowUp,
    faCircleExclamation,
    faFilePen,
    faHospital,
    faTriangleExclamation,
    faCalculator,
    faArrowDownArrowUp,
    faUserPlus,
    faAngleRight,
    faEdit,
    faAngleUp,
    faArrowDownAZ,
    faFiles,
    faFolders,
    faAngleDown,
    faCompress,
    faExpand,
    faUndo,
    faRedo,
    faClipboardList,
    faCopy,
  ]);
};
