import { remixCompletePages } from "~/data/pages";
import { routes } from "~/route-services/routes";
import { UNDER_MAINTENANCE } from "~/shared/constants";

// Note : extension commented after getting updated extension list from Shamsabbas
// gif|jpe?g|png|txt|mp4|heic|pdf|xls|xlsx|xlsm|docx?|pptx?|zip|gz|rar|msg|webp|kmz|csv|ppt|dwg|avi|wmv|mkv|mov|webm|avchd|flv|mts|m2ts|mp3

export const VALID_EXTENSIONS = [
  "jpg",
  "jpeg",
  "png",
  "gif",
  "zip",
  "webp", // Image formats
  "pdf", // PDF
  "docx",
  "txt",
  "xls",
  "xlsx",
  "csv", // Spreadsheet formats
  "ppt",
  "pptx",
  "mp3",
  "avi",
  "mov",
  "wmv",
  "flv", // Video formats
  "mkv",
  "mp4",
  "webm",
  "dwg",
  // "bmp",
  // "tiff",
  // "svg",
  // "doc",
  // "odt",
  // "rtf",
  // "md", // Document formats
  // "ods",
  // "odp", // Presentation formats
  // "wav", remove based on sham's updated extension list
  // "aac",
  // "flac",
  // "ogg", // Audio formats
  // "m2ts", removing as it's not supported in PHP
  // "mts", removed as shams said
];

export const isValidURL = (url: string) => {
  try {
    new URL(url ?? "");
    return true;
  } catch (error) {
    return false;
  }
};

export const isValidFileURL = (url: string) => {
  try {
    const newUrl = new URL(url ?? "");
    const fileExtension = newUrl.pathname.split(".").pop();
    if (fileExtension) {
      return VALID_EXTENSIONS.includes(fileExtension.toLowerCase());
    }
  } catch (error) {}
  return false;
};

export const isURLUnderMaintenance = (url: string) =>
  UNDER_MAINTENANCE && url.includes("maintenance");

export const isURLModuleDeveloped = (url: string) => {
  if (url.includes("inprogress=1")) return true;
  try {
    if (new URL(url).pathname.startsWith(routes.ACCESS_DENIED.url)) return true;
  } catch (error) {
    // not need to add log
  }

  return remixCompletePages.some((remixCompletePage) =>
    url.includes(remixCompletePage.url)
  );
};

export const isValidMarkupExtensions = (file_ext: string) => {
  let fileExtension = file_ext.toLowerCase();
  if (VALID_EXTENSIONS.includes(fileExtension)) {
    if (["ppt", "pptx"].includes(fileExtension)) {
      return false;
    }
    return true;
  } else {
    return false;
  }
};

export const compareDateFormats = (
  date_added: string | undefined,
  format: string
): boolean | string => {
  // If date_added is undefined, return false or handle the error
  if (!date_added) {
    console.error("The provided date is undefined.");
    return false;
  }

  // Detect the separator from the format (could be '/', '.', or '-')
  const separator: string | null = format.includes("/")
    ? "/"
    : format.includes(".")
    ? "."
    : format.includes("-")
    ? "-"
    : null;

  // Check if separator is valid
  if (separator === null) {
    console.error("Unsupported or missing separator in date format.");
    return false;
  }

  // Split the format and the date_added using the detected separator
  let formatParts: string[] = format.split(separator);
  let dateParts: string[] = date_added.split(separator);

  // Ensure the parts have the same length
  if (formatParts.length !== dateParts.length) {
    console.error("Format and date parts count mismatch.");
    return false;
  }

  // Map the format components to the date components
  let formatMap: { [key: string]: string } = {};
  formatParts.forEach((part, index) => {
    formatMap[part] = dateParts[index];
  });

  // Validate based on format components
  let day = formatMap["DD"] || formatMap["dd"];
  let month = formatMap["MM"] || formatMap["mm"] || formatMap["MMM"];
  let year = formatMap["YYYY"] || formatMap["yyyy"];

  // Ensure the lengths match for a valid date (2 for day/month, 3 for short month name, 4 for year)
  const isDayValid = day && day.length === 2;
  const isMonthValid = month && (month.length === 2 || month.length === 3); // Allowing for month names (e.g., 'Aug')
  const isYearValid = year && year.length === 4;

  // Return true if the format and date match, otherwise false
  return isDayValid && isMonthValid && isYearValid;
};

const resolutionAry: IResolutionAry = {
  good: { height: 800, width: 600 },
  better: { height: 1024, width: 768 },
  best: { height: 1800, width: 1200 },
};
// Resize image function
export const ImageResizer = (file: File, resolution: keyof IResolutionAry) => {
  return new Promise<Blob>((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const img = new Image();
      img.src = e.target?.result as string;
      img.onload = () => {
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");
        const imgWidth = img.width;
        const imgHeight = img.height;
        const { width: resolutionWidth, height: resolutionHeight } =
          resolutionAry[resolution];
        const widthRatio = resolutionWidth / imgWidth;
        const heightRatio = resolutionHeight / imgHeight;

        let newWidth = imgWidth;
        let newHeight = imgHeight;

        if (widthRatio < 1 || heightRatio < 1) {
          if (widthRatio > heightRatio) {
            newWidth = imgWidth * heightRatio;
            newHeight = imgHeight * heightRatio;
          } else {
            newWidth = imgWidth * widthRatio;
            newHeight = imgHeight * widthRatio;
          }
        }
        canvas.width = newWidth;
        canvas.height = newHeight;

        if (!ctx) return;
        ctx.drawImage(img, 0, 0, newWidth, newHeight);

        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(blob);
            }
          },
          file.type,
          0.96 // Quality parameter for lossy formats
        );
      };

      img.onerror = () => {
        reject(new Error("Failed to load image."));
      };
    };

    reader.onerror = () => {
      reject(new Error("Failed to read file."));
    };

    reader.readAsDataURL(file);
  });
};
