import isEmpty from "lodash/isEmpty";
import { useEffect, useRef, useState, useMemo } from "react";
import { useTranslation } from "~/hook";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import { CFCheckBox } from "~/components/third-party/ant-design/cf-checkbox";
import { CFInput } from "~/components/third-party/ant-design/cf-input";
import { CFSelect } from "~/components/third-party/ant-design/cf-select";
import { CFTextarea } from "~/components/third-party/ant-design/cf-textarea";
import { CFTypography } from "~/components/third-party/ant-design/cf-typography";
import { getLumpSumTotal } from "~/zustand/pages/manage-bills/addBillDropDown/store";
import { Number, replaceDOMParams, sanitizeString } from "~/helpers/helper";
import { getGTypes } from "~/zustand/global-types/store";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { defaultConfig } from "~/data";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { getGSettings } from "~/zustand";
import { getBillsDetails } from "~/zustand/pages/manage-bills/store";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import {
  onKeyDownNumber,
  onKeyDownCurrency,
  getItemTypeIcon,
  filterOptionBySubstring,
} from "~/shared/utils/helper/common";
import { InputField } from "~/shared/components/molecules/inputField";
import { Typography } from "~/shared/components/atoms/typography";
import { SelectField } from "~/shared/components/molecules/selectField";
import { addUnit, getUnitList } from "~/redux/action/unitActions";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";

interface CostCodeData {
  code_id: string | number | undefined;
  original_code_id?: string;
  csi_code: string;
  parent_id?: string;
  csi_name?: string;
  parent_code_id?: string;
  cost_code_name: string;
  is_deleted?: string;
  is_managed_level?: string;
  has_no_child?: string;
  editForm?: boolean;
  archive?: boolean;
}

const ItemDetail = ({
  itemErrors,
  setItemErrors,
  formData,
  setFormData,
  isView,
  editForm,
  setFormUpdatedFields,
  setItemTypeCheckbox,
  itemTypeCheckBox,
}: ItemDetailFormProps) => {
  const { _t } = useTranslation();
  const gSettings: GSettings = getGSettings();
  const billDetailsSc: BillDetails = getBillsDetails();
  // const { currentCurrency } = useCurrencyFormatter();
  const { inputFormatter, unformatted, formatter } = useCurrencyFormatter();
  const costUnitRef = useRef<HTMLDivElement>(null);
  const [subjectValues, setSubjectValues] = useState<string | undefined>(
    formData?.subject
  );

  const [descriptionValues, setDescriptionValues] = useState<
    string | undefined
  >(formData?.description);
  const [notesValue, setNotesValue] = useState<string | undefined>(
    formData?.internal_notes
  );
  const [itemQty, setItemQty] = useState<number | string | undefined>(
    formData?.quantity
  );
  const [itemCheckBox, setItemCheckbox] = useState<boolean>(
    !!+(formData?.apply_global_tax || 0)
  );
  const [unit, setUnit] = useState<number | string | undefined>(formData?.unit);
  const [costCode, setCostCode] = useState<
    number | string | undefined | boolean
  >("");
  const [itemtype, setItemType] = useState<number | string | undefined>(
    formData?.item_type
  );
  const [itemTypeLabel, setItemTypeLabel] = useState("");

  const [showUnitInputs, setShowUnitInputs] = useState<boolean>(!isView);
  const unitCostContainerRef = useRef<HTMLDivElement>(null);
  const unitCostRef = useRef<InputRef>(null);
  const isTaxApplicable =
    gSettings?.is_non_united_state_qb_country ||
    gSettings?.quickbook_sync === "0";
  const costCodeData: CostCodeData[] = getLumpSumTotal();
  const itemType: GType[] = getGTypes();

  const { cost_code_id, cost_code_name, csi_code } = formData;

  if (
    cost_code_id &&
    cost_code_id.toString() !== "0" &&
    cost_code_name !== null
  ) {
    const costCodeExists = costCodeData.some(
      (item) => item.code_id?.toString() === cost_code_id?.toString()
    );

    if (!costCodeExists) {
      costCodeData.push({
        code_id: cost_code_id,
        cost_code_name: cost_code_name || "",
        csi_code: csi_code || "",
        archive: true,
      });
    }
  }

  const [newTypeName, setNewTypeName] = useState<string>("");
  const [unitData, setUnitData] = useState<IUnitData[]>([]);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);

  const getUnit = async () => {
    const unit: IUnitListResponse = await getUnitList();
    setUnitData(unit?.data?.units);
  };

  useEffect(() => {
    getUnit();
  }, []);
  const updateFormData = (
    key: string,
    value: string | number | boolean | undefined
  ) => {
    setFormData((prevFormData: Partial<BillItem>) => ({
      ...prevFormData,
      [key]: value,
    }));
    setFormUpdatedFields((prevFields: FormUpdatedFields) => ({
      ...prevFields,
      [key]: true,
    }));
  };
  const handleTypeOnchange = (
    selected: string,
    options: any // change this in future ( temporary resolve type issue )
  ) => {
    if (gSettings?.tax_all_items?.toString() === "1") {
      setItemCheckbox(true);
      updateFormData("apply_global_tax", true);
    }
    if (formData?.item_type === "0") {
      setItemTypeCheckbox(gSettings?.is_cidb_auto_save?.toString() === "1");
    }
    setItemType(selected);
    if (
      !Array.isArray(options) &&
      typeof options === "object" &&
      typeof options.name === "string"
    ) {
      setItemTypeLabel(options.name);
    }

    updateFormData("item_type", selected);
    setItemErrors((prev: ItemDetailsErrors) => {
      if (!isEmpty(selected)) {
        return { ...prev, itemType: "" };
      }
      return prev;
    });
  };
  const handleItemtext = (e: React.ChangeEvent<HTMLInputElement>) => {
    const trimValue =
      e?.target?.value.trim() === ""
        ? e?.target?.value.trim()
        : e?.target?.value;
    setSubjectValues(trimValue);
    updateFormData("subject", trimValue);
    setItemErrors((prev: ItemDetailsErrors) => {
      if (!isEmpty(trimValue)) {
        return { ...prev, itemItems: "" };
      }
      return prev;
    });
  };

  const handleQty = (e: React.ChangeEvent<HTMLInputElement>) => {
    const originalInput = e.target.value;

    if (originalInput.trim() === "-" || originalInput.trim() === "") {
      setItemQty(originalInput);
      setFormData((prevFormData: Partial<BillItem>) => ({
        ...prevFormData,
        quantity: 0,
      }));
      return;
    }

    const quantityValue = parseFloat(originalInput);
    if (isNaN(quantityValue)) {
      updateFormData("quantity", 0);
      setItemQty(0);
      setFormData((prevFormData: Partial<BillItem>) => ({
        ...prevFormData,
        quantity: 0,
        total: 0,
      }));

      return;
    }
    updateFormData("quantity", originalInput);
    setItemQty(quantityValue);
    const total = quantityValue * +(formData?.unit_cost ?? 0);
    setFormData((prevFormData: Partial<BillItem>) => ({
      ...prevFormData,
      quantity: originalInput,
      total: total,
    }));
  };

  const handleCost = (value: string) => {
    const originalInput = value;
    const cleanedInput = originalInput?.split(".")[0]?.replace("-", "");
    if (cleanedInput?.length > 10) {
      return;
    }
    const unitCostValue = parseFloat(cleanedInput);

    let updatedFormData: Partial<BillItem> = {};

    if (isNaN(unitCostValue)) {
      updatedFormData = {
        ...formData,
        unit_cost: "",
        total: 0,
      };
    } else {
      const total = Number(formData.quantity) * unitCostValue;
      updatedFormData = {
        ...formData,
        unit_cost: originalInput,
        total: total,
      };
    }
    setFormData(updatedFormData);
  };

  const handleUnit = (e: React.ChangeEvent<HTMLInputElement>) => {
    const originalInput = e?.target?.value;
    updateFormData("unit", originalInput);
    setFormData((prevFormData: Partial<BillItem>) => ({
      ...prevFormData,
      unit: originalInput,
    }));
  };

  const handleTaxCheckbox = (e: CheckboxChangeEvent) => {
    const value = e?.target?.checked;
    setItemCheckbox(value);
    updateFormData("apply_global_tax", value);
  };
  const handleItemTypeCheckbox = (e: CheckboxChangeEvent) => {
    const value = e?.target?.checked;
    setItemTypeCheckbox(value);
  };
  const handleDescription = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e?.target?.value;
    setDescriptionValues(value);
    updateFormData("description", value);
  };

  const handleCostCode = (
    selected: string,
    options: any // change this in future ( temporary resolve type issue )
  ) => {
    if (!Array.isArray(options)) {
      const selectedValue = options?.value;
      setCostCode(selectedValue);
      updateFormData("cost_code_id", selectedValue);
      updateFormData("cost_code_name", options.label.split("(")[0].trim());
      setItemErrors((prev: ItemDetailsErrors) => {
        if (!isEmpty(options.value)) {
          return { ...prev, itemCostCode: "" };
        }
        return prev;
      });
    }
  };

  const handleNotes = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e?.target?.value;
    setNotesValue(value);
    updateFormData("internal_notes", value);
  };

  const filteredItems = itemType?.filter(
    (item: Partial<GType>) => item?.type === "company_items"
  );
  const reorderedItems = (() => {
    if (!Array.isArray(filteredItems)) return [];
    const items = [...filteredItems];
    const index = items.findIndex((item) => item.type_id == "163");
    if (index > -1) {
      const [labourItem] = items.splice(index, 1); // remove item
      items.splice(1, 0, labourItem); // insert at 2nd place
    }
    return items;
  })();
  useEffect(() => {
    setSubjectValues(formData?.subject);
    setDescriptionValues(formData?.description);
    setNotesValue(formData?.internal_notes);
    // setUnitCost(formData?.unit_cost);
    setUnit(formData?.unit);
    setItemQty(formData?.quantity);
    setItemCheckbox(!!Number(formData?.apply_global_tax));
    setCostCode(formData?.cost_code_id?.toString());
    setItemType(formData.item_type);
  }, [formData]);

  useMemo(() => {
    if (
      editForm &&
      formData?.unit_cost !== "" &&
      !isEmpty(formData?.unit_cost) &&
      formData?.unit_cost !== undefined &&
      formData?.unit !== "" &&
      !isEmpty(formData?.unit) &&
      formData?.unit !== undefined &&
      !unitCostContainerRef.current?.contains(document.activeElement as Node)
    ) {
      setShowUnitInputs(false);
    } else {
      setShowUnitInputs(true);
    }
  }, [editForm, formData]);

  const handleFocusOut = (e: React.FocusEvent<HTMLInputElement>) => {
    if (!unitCostContainerRef.current?.contains(e.relatedTarget as Node)) {
      if (
        formData?.unit_cost &&
        !isEmpty(formData?.unit_cost) &&
        unit &&
        !isEmpty(unit)
      ) {
        setShowUnitInputs(false);
      }
    }
  };
  const handleParagraphClick = () => {
    if (!isView) {
      setShowUnitInputs(true);
      setTimeout(() => {
        unitCostRef?.current?.focus();
      }, 10);
    }
  };

  const itemTypeDisable = () => {
    if (formData?.reference_item_id || formData?.reference_module_id) {
      let itemType = true;
      if (formData?.reference_item_id?.toString() === "0") {
        itemType = false;
      }
      if (
        formData?.reference_module_id === defaultConfig.sub_contract_module_id
      ) {
        itemType = true;
      }

      return itemType;
    }
    return false;
  };

  const handleEnterKeyPress = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    event.preventDefault();
  };
  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
  };
  return (
    <>
      <div className="flex flex-col gap-5">
        <div className="w-full">
          {formData?.feature_type !== "credit_item" && (
            <CFSelect
              required
              name="type"
              label="Item Type"
              showSearch={false}
              value={
                !!itemtype && itemtype.toString() !== "0"
                  ? itemtype.toString()
                  : ""
              }
              options={[
                {
                  label: "Select Item Type",
                  value: "Select Item Type",
                  disabled: true,
                },
                ...(reorderedItems && reorderedItems?.length > 0
                  ? reorderedItems?.map(
                      (item: { name: string; type_id: string }) => ({
                        // label: item?.name,
                        label: (
                          <div className="flex items-center gap-1.5">
                            {item?.type_id ? (
                              <FontAwesomeIcon
                                icon={getItemTypeIcon({
                                  type: item.type_id.toString(),
                                })}
                              />
                            ) : (
                              ""
                            )}
                            {item?.name}
                          </div>
                        ),
                        value: item?.type_id,
                        ...item,
                      })
                    )
                  : []),
              ]}
              multiple={false}
              className="text-sm"
              onChange={handleTypeOnchange}
              errorMessage={itemErrors?.itemType}
              disabled={isView || itemTypeDisable()}
            />
          )}
        </div>
        <div className="w-full">
          <CFInput
            label="Item Name"
            type="text"
            size="middle"
            required
            name="subject"
            onChange={handleItemtext}
            value={replaceDOMParams(sanitizeString(subjectValues))}
            errorMessage={itemErrors?.itemItems}
            readOnly={isView}
            // will remove commne tonce tested in dev (NF)
            // disabled={
            //   Number(billDetailsSc?.due_balance ?? 0) <= 0 &&
            //   Number(billDetailsSc?.total ?? 0) > 0
            // }
          />
        </div>
        <div>
          <label className="ant-input-label dark:text-white/90">
            {_t("Pricing")}
          </label>
          <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] mt-2.5">
            <li>
              <ul className="w-full">
                <li className="flex justify-between items-center">
                  <CFTypography
                    title="small"
                    className="text-13 block text-primary-900 dark:text-white/90"
                  >
                    {_t("Qty")}
                  </CFTypography>
                  <div className="sm:w-40 w-28">
                    <InputNumberField
                      placeholder="Item Quantity"
                      rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input"
                      disabled={isView}
                      labelPlacement="left"
                      value={
                        itemQty && itemQty !== "0" && itemQty !== "0.00"
                          ? itemQty
                          : undefined
                      }
                      onChange={(inputValue) => {
                        const originalInput = inputValue;

                        if (originalInput === "-" || originalInput === "") {
                          setItemQty(originalInput);
                          setFormData((prevFormData: Partial<BillItem>) => ({
                            ...prevFormData,
                            quantity: 0,
                          }));
                          return;
                        }

                        const quantityValue = parseFloat(originalInput);
                        if (isNaN(quantityValue)) {
                          updateFormData("quantity", 0);
                          setItemQty(0);
                          setFormData((prevFormData: Partial<BillItem>) => ({
                            ...prevFormData,
                            quantity: 0,
                            total: 0,
                          }));

                          return;
                        }
                        updateFormData("quantity", originalInput);
                        setItemQty(quantityValue);
                        const total =
                          quantityValue * +(formData?.unit_cost ?? 0);
                        setFormData((prevFormData: Partial<BillItem>) => ({
                          ...prevFormData,
                          quantity: originalInput,
                          total: total,
                        }));
                      }}
                      onPaste={handlePaste}
                      parser={(value) => {
                        if (!value) return "";
                        const inputValue = unformatted(value.toString());
                        return inputValue;
                      }}
                      formatter={(value) => {
                        return inputFormatter(String(value)).value;
                      }}
                      onKeyDown={(event) =>
                        onKeyDownCurrency(event, {
                          integerDigits: 6,
                          decimalDigits: 2,
                          unformatted,
                          allowNegative: true,
                          decimalSeparator: inputFormatter().decimal_separator,
                        })
                      }
                    />
                  </div>
                </li>
              </ul>
              <div className="py-0.5 relative">
                <CFTypography
                  title="small"
                  className="w-[18px] h-[18px] flex items-center justify-center rounded-full !shadow-[0px_0px_10px] !shadow-black/10 !absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2"
                >
                  <FontAwesomeIcon
                    className="w-3 h-3 text-primary-900 dark:text-white"
                    icon="fa-regular fa-xmark"
                  />
                </CFTypography>
              </div>
              <ul className="w-full">
                <li className="flex justify-between items-center">
                  <Typography className="text-13 block text-primary-900 dark:text-white/90">
                    {_t("Unit Cost/Unit")}
                  </Typography>
                  <div className="sm:w-[260px] w-28 h-[22px]" ref={costUnitRef}>
                    <div
                      ref={unitCostContainerRef}
                      className="text-right !text-[#008000] leading-[22px] font-semibold text-sm"
                    >
                      {!isView && (
                        <>
                          {showUnitInputs ? (
                            <div className="flex gap-2">
                              <div className="w-[calc(100%-52px)]">
                                <InputNumberField
                                  name="unit_cost"
                                  id="unit_cost"
                                  rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input placeholder:text-13 !w-[112px]"
                                  placeholder={_t("Item Unit Cost")}
                                  autoFocus={Boolean(
                                    formData?.unit_cost &&
                                      !isEmpty(formData?.unit_cost) &&
                                      formData?.unit &&
                                      !isEmpty(formData?.unit)
                                  )}
                                  onPaste={handlePaste}
                                  disabled={
                                    isView ||
                                    (Number(
                                      formData.reference_primary_id ?? ""
                                    ) > 0 &&
                                      billDetailsSc?.sc_multi_bill?.toString() ===
                                        "1")
                                    // will remove commne tonce tested in dev (NF)
                                    //     ||
                                    // (Number(billDetailsSc?.due_balance ?? 0) <= 0 &&
                                    //   Number(billDetailsSc?.total ?? 0) > 0)
                                  }
                                  labelPlacement="left"
                                  value={
                                    Number(formData.unit_cost) !== 0
                                      ? formData.unit_cost
                                      : ""
                                  }
                                  onChange={(value: string) => {
                                    const cleanedValue = String(
                                      value ?? ""
                                    ).trim();
                                    if (cleanedValue === "") {
                                      handleCost("");
                                    } else {
                                      handleCost(cleanedValue);
                                    }
                                  }}
                                  formatter={(value, info) => {
                                    return inputFormatter(value?.toString())
                                      .value;
                                  }}
                                  parser={(value) => {
                                    const inputValue = value?.trim();
                                    if (!inputValue) return ""; // explicitly return empty string
                                    return unformatted(inputValue);
                                  }}
                                  onKeyDown={(event) =>
                                    onKeyDownCurrency(event, {
                                      integerDigits: 10,
                                      decimalDigits: 2,
                                      unformatted,
                                      allowNegative: false,
                                      decimalSeparator:
                                        inputFormatter().decimal_separator,
                                    })
                                  }
                                  onBlur={handleFocusOut}
                                />
                              </div>
                              <div className="w-[65px]">
                                {window.ENV.ENABLE_UNIT_DROPDOWN ? (
                                  <SelectField
                                    className="!p-0 text-success select-field-text-13 text-left select-unit-filed !h-[22px] !font-semibold placeholder:font-normal"
                                    placeholder="Unit"
                                    name="unit"
                                    disabled={isView}
                                    labelPlacement="left"
                                    maxLength={15}
                                    value={!!unit ? unit : null}
                                    iconView={true}
                                    popupClassName="!w-[260px]"
                                    showSearch
                                    options={
                                      unitData.map((type) => ({
                                        label: type.name.toString(),
                                        value: type.name.toString(),
                                      })) ?? []
                                    }
                                    allowClear
                                    filterOption={(input, option) =>
                                      filterOptionBySubstring(
                                        input,
                                        option?.label as string
                                      )
                                    }
                                    onChange={(value) => {
                                      updateFormData("unit", value);
                                      setFormData(
                                        (prevFormData: Partial<BillItem>) => ({
                                          ...prevFormData,
                                          unit: value,
                                        })
                                      );
                                    }}
                                    addItem={{
                                      text: "Add Unit: Type Unit & Press Enter",
                                      icon: "fa-regular fa-plus",
                                    }}
                                    onInputKeyDown={(e) => {
                                      if (e.key === "Enter") {
                                        const value =
                                          e?.currentTarget?.value?.trim();
                                        const newType =
                                          onEnterSelectSearchValue(
                                            e,
                                            unitData?.map((unit) => ({
                                              label: unit?.name,
                                              value: "",
                                            })) || []
                                          );
                                        if (newType) {
                                          setNewTypeName(newType);
                                        } else if (value) {
                                          notification.error({
                                            description:
                                              "Records already exist, no new records were added.",
                                          });
                                        }
                                      }
                                    }}
                                    onClear={() => {
                                      updateFormData("unit", "");
                                      setFormData(
                                        (prevFormData: Partial<BillItem>) => ({
                                          ...prevFormData,
                                          unit: "",
                                        })
                                      );
                                    }}
                                    onBlur={handleFocusOut}
                                    // errorMessage={errors.unit}
                                  />
                                ) : (
                                  <CFInput
                                    className={`!p-0 !pl-1.5 !text-13 text-sucess !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal placeholder:text-13 !text-right ${
                                      !showUnitInputs && "!hidden"
                                    }`}
                                    borderedApply={false}
                                    placeholder="Unit"
                                    onChange={handleUnit}
                                    onBlur={handleFocusOut}
                                    value={!!unit ? unit : ""}
                                    disabled={isView}
                                    maxLength={15}
                                    type="text"
                                    onPaste={handlePaste}
                                    // will remove commne tonce tested in dev (NF)
                                    // disabled={
                                    //   Number(billDetailsSc?.due_balance ?? 0) <= 0 &&
                                    //   Number(billDetailsSc?.total ?? 0) > 0
                                    // }
                                    onPressEnter={handleEnterKeyPress}
                                  />
                                )}
                              </div>
                            </div>
                          ) : (
                            <CFTypography
                              title="small"
                              className="text-[#008000] cursor-pointer text-13"
                              onClick={handleParagraphClick}
                            >
                              {
                                formatter(
                                  Number(formData?.unit_cost).toFixed(2)
                                ).value_with_symbol
                              }
                              /{unit}
                            </CFTypography>
                          )}
                        </>
                      )}

                      {isView ? (
                        !isEmpty(formData?.unit_cost) &&
                        formData?.unit_cost !== 0.0 &&
                        formData?.unit_cost !== "0.00" &&
                        !isEmpty(unit) &&
                        unit !== 0.0 ? (
                          <CFTypography
                            title="small"
                            className={`text-[#008000] font-medium text-13 ${
                              isView ? "cursor-no-drop" : ""
                            }`}
                          >
                            {`${formData?.unit_cost}/${unit}`}
                          </CFTypography>
                        ) : (
                          <div className="flex gap-2">
                            <div className="w-[calc(100%-52px)]">
                              <InputField
                                ref={unitCostRef}
                                rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input"
                                placeholder="Item Unit Cost"
                                labelPlacement="left"
                                maxLength={10}
                                value={
                                  Number(formData?.unit_cost) === 0
                                    ? ""
                                    : formData?.unit_cost
                                }
                                type="number"
                                disabled={true}
                                onChange={() => {}}
                              />
                            </div>
                            <div className="w-11">
                              <InputField
                                className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                placeholder={_t("Unit")}
                                maxLength={15}
                                name="unit"
                                id="unit"
                                disabled={true}
                                value={!!unit ? unit : ""}
                                type="text"
                                onChange={() => {}}
                              />
                            </div>
                          </div>
                        )
                      ) : (
                        <></>
                      )}
                    </div>
                  </div>
                </li>
              </ul>
              <ul className="w-full border-t border-dashed border-gray-300 pt-2 mt-2">
                <li className="flex items-center justify-between">
                  <div>
                    <CFTypography
                      title="small"
                      className="text-13 block text-primary-900 font-semibold"
                    >
                      {_t("Total Cost")}
                    </CFTypography>
                  </div>
                  <div className="flex justify-end items-center">
                    <CFTypography
                      title="small"
                      className="!text-red-600 font-semibold text-13"
                      disabled={true}
                    >
                      {/* disucss with harshil and update base on cu:- https://app.clickup.com/t/86cxpn4ay */}
                      {/* {Number(formData.reference_primary_id ?? "") > 0 &&
                      billDetailsSc?.sc_multi_bill?.toString() === "1"
                        ? " " +
                          formatter(Number(formData?.total).toFixed(2))
                            .value_with_symbol
                        : " " +
                          formatter(
                            Number(itemQty * +formData?.unit_cost).toFixed(2)
                          ).value_with_symbol} */}
                      {" " +
                        formatter(
                          Number(itemQty * +formData?.unit_cost).toFixed(2)
                        ).value_with_symbol}
                    </CFTypography>
                  </div>
                </li>
              </ul>
            </li>
          </ul>
        </div>
        <div className="w-full">
          <CFSelect
            required={
              gSettings?.quickbook_sync === "1" ||
              gSettings?.quickbook_desktop_sync === "1"
            }
            name="cost_code"
            label="Cost Code"
            options={
              costCodeData && costCodeData?.length > 0
                ? costCodeData?.map((item: CostCodeData) => ({
                    label:
                      `${item?.cost_code_name}` +
                      `${item?.csi_code ? ` (${item?.csi_code})` : ""}` +
                      `${item.archive ? " (Archived)" : ""}`,
                    value: item?.code_id?.toString(),
                  }))
                : []
            }
            className="text-sm"
            onChange={handleCostCode}
            value={!!costCode && costCode !== "0" ? costCode : ""}
            errorMessage={itemErrors?.itemCostCode}
            // will remove commne tonce tested in dev (NF)
            disabled={
              isView
              // ||
              // (Number(billDetailsSc?.due_balance ?? 0) <= 0 &&
              //   Number(billDetailsSc?.total ?? 0) > 0)
            }
            allowClear={gSettings?.quickbook_sync === "0"}
          />
        </div>
        <div className="w-full">
          <CFTextarea
            label="Description"
            name="notes"
            labelPlacement="top"
            placeholder="Notes added here are transferred to other records (such as an Estimate or PO) and can be optionally made visible to the recipient when submitted."
            labelClass="dark:text-white/90"
            onChange={handleDescription}
            value={descriptionValues}
            readOnly={isView}
            // will remove commne tonce tested in dev (NF)
            // disabled={
            //   Number(billDetailsSc?.due_balance ?? 0) <= 0 &&
            //   Number(billDetailsSc?.total ?? 0) > 0
            // }
          />
        </div>
        <div className="w-full">
          <CFTextarea
            label="Internal Note"
            name="notes"
            placeholder="Notes added here are transferred to other records (such as an Estimate or PO) and are not visible to the recipient."
            labelClass="dark:text-white/90"
            onChange={handleNotes}
            value={notesValue}
            readOnly={isView}
            // will remove commne tonce tested in dev (NF)
            // disabled={
            //   Number(billDetailsSc?.due_balance ?? 0) <= 0 &&
            //   Number(billDetailsSc?.total ?? 0) > 0
            // }
          />
        </div>
        {isTaxApplicable ? (
          <div className="w-full">
            <CFCheckBox
              className="gap-1.5 text-primary-900"
              label="Collect Tax on this Item?"
              checked={itemCheckBox}
              onChange={handleTaxCheckbox}
              // will remove commne tonce tested in dev (NF)
              disabled={
                isView
                // ||
                // (Number(billDetailsSc?.due_balance ?? 0) <= 0 &&
                //   Number(billDetailsSc?.total ?? 0) > 0)
              }
            />
          </div>
        ) : (
          ""
        )}
        {!editForm && !isView && itemTypeLabel && (
          <div className="w-full">
            <CFCheckBox
              className="gap-1.5 text-primary-900"
              label={`Save this item into my ${itemTypeLabel} Items list?`}
              checked={itemTypeCheckBox}
              onChange={handleItemTypeCheckbox}
              disabled={
                isView
                // will remove commne tonce tested in dev (NF)
                // ||
                // (Number(billDetailsSc?.due_balance ?? 0) <= 0 &&
                //   Number(billDetailsSc?.total ?? 0) > 0)
              }
            />
          </div>
        )}
      </div>

      {newTypeName.trim() && (
        <ConfirmModal
          isOpen={Boolean(newTypeName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newTypeName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => setNewTypeName("")}
          onAccept={async () => {
            if (!isAddingCustomData && newTypeName) {
              setIsAddingCustomData(true);
              const response: IUnitAddResponse = await addUnit({
                name: newTypeName,
              });
              if (response.success && response.data) {
                setUnitData([{ name: newTypeName }, ...unitData]);
                updateFormData("unit", newTypeName);
                setFormData((prevFormData: Partial<BillItem>) => ({
                  ...prevFormData,
                  unit: newTypeName,
                }));
                setNewTypeName("");
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => setNewTypeName("")}
        />
      )}
    </>
  );
};

export default ItemDetail;
