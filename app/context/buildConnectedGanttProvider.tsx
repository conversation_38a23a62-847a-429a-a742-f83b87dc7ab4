import { ReactNode, useCallback } from "react";
import { GanttContextProvider } from "./GanttContext";
import { AppDispatch } from "~/modules/projectManagement/pages/project/redux/store";

export function buildConnectedGanttProvider<
  State extends IGanttScheduleInitialState,
  Dispatch extends AppDispatch
>(selectorHook: () => State, dispatchHook: () => Dispatch) {
  return function ConnectedGanttContextProvider({
    children,
  }: {
    children: ReactNode;
  }) {
    const selector = useCallback(() => selectorHook(), []);
    const dispatch = dispatchHook();

    return (
      <GanttContextProvider selector={selector} dispatch={dispatch}>
        {children}
      </GanttContextProvider>
    );
  };
}
