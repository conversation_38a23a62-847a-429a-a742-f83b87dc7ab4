import { createContext, ReactNode, useCallback, useMemo, useRef } from "react";
import { AppDispatch } from "~/modules/projectManagement/pages/project/redux/store";


type GanttContextType = {
  ganttRef: React.MutableRefObject<TReactGanttRefTypes | null>;
  getGanttInstance: () => TGanttInstance;
  selector: () => IGanttScheduleInitialState;
  dispatch: AppDispatch;
};

export const GanttContext = createContext<GanttContextType | null>(null);

type GanttContextProviderProps = {
  selector: () => IGanttScheduleInitialState;
  dispatch: AppDispatch;
  children: ReactNode;
};

export const GanttContextProvider: React.FC<GanttContextProviderProps> = ({
  selector,
  dispatch,
  children,
}) => {
  const ganttRef = useRef<TReactGanttRefTypes>(null);

  const getGanttInstance = useCallback(
    () => ganttRef.current?.instance,
    [ganttRef]
  );

  const value = useMemo(
    () => ({ ganttRef, getGanttInstance, selector, dispatch }),
    [ganttRef, getGanttInstance, selector, dispatch]
  );

  return (
    <GanttContext.Provider value={value}>{children}</GanttContext.Provider>
  );
};
