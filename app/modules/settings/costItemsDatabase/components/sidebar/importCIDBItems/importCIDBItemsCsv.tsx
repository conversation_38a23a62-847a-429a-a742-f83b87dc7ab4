import { useEffect, useState } from "react";

// Atoms
import { Button } from "~/shared/components/atoms/button";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { TypographyLink } from "~/shared/components/atoms/typographyLink";

// Molecules
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { DrawerVericalOptions } from "~/shared/components/molecules/drawerVericalOptions";

// Lodash

import isEmpty from "lodash/isEmpty";

// React
import { useTranslation } from "~/hook";
import { CSV_HEADER_OPTIONS, SELECT_OPTIONS } from "../../../utils/constants";
import ImportTab from "./tab/ImportTab";
import PreviousImportsTab from "./tab/PreviousImportsTab";
import ExportTab from "./tab/ExportTab";

const ImportCIDBItemsCsv = ({
  importCIDBItemsCsv,
  setImportCIDBItemsCsv,
  options,
  module,
  refreshAgGrid,
}: any) => {
  const { _t } = useTranslation();
  const [sideMenuOpen, setSideMenuOpen] = useState<boolean>(false);
  const [anyChangesApplied, setAnyChangesApplied] = useState<boolean>(false);
  let selectedComponent;

  switch (importCIDBItemsCsv) {
    case "import":
      selectedComponent = (
        <ImportTab
          setAnyChangesApplied={setAnyChangesApplied}
          setImportCIDBItemsCsv={setImportCIDBItemsCsv}
          refreshAgGrid={refreshAgGrid}
        />
      );
      break;
    case "previous_imports":
      selectedComponent = (
        <PreviousImportsTab
          module={module}
          setAnyChangesApplied={setAnyChangesApplied}
        />
      );
      break;
    case "export":
      selectedComponent = <ExportTab />;
      break;
    default:
      selectedComponent = <></>;
  }

  useEffect(() => {
    if (!importCIDBItemsCsv) {
      setAnyChangesApplied(false);
    }
  }, [importCIDBItemsCsv]);

  return (
    <Drawer
      open={!isEmpty(importCIDBItemsCsv)}
      rootClassName="drawer-open"
      width={1600}
      classNames={{
        header: "!hidden",
        body: "!p-0 !overflow-hidden",
      }}
    >
      <div className="sidebar-body">
        <div className="flex">
          <DrawerVericalOptions<TItemsScheduleValues | "">
            sideMenuOpen={sideMenuOpen}
            setSideMenuOpen={setSideMenuOpen}
            defaultOptions={SELECT_OPTIONS}
            options={options}
            selectedOption={importCIDBItemsCsv}
            onClick={(value: TItemsScheduleValues | "") => {
              setImportCIDBItemsCsv(value);
            }}
            buttonClassName="whitespace-normal"
            className="!h-[calc(100dvh-110px)]"
            SelecteMenuBottom={
              <div
                className={`fixed md:bg-gray-200/50 bg-gray-200 bottom-0 ${
                  sideMenuOpen ? "w-0 md:flex hidden" : "w-[240px] pb-5 p-2.5"
                }`}
              >
                <TypographyLink
                  href="https://vimeo.com/347382090"
                  target="_blank"
                  className="!text-13 group/helpyou"
                >
                  <div className="bg-primary-8 rounded-lg py-4 text-center flex flex-col items-center gap-2.5">
                    <FontAwesomeIcon
                      className="w-5 h-5 text-primary-600 group-hover/helpyou:!text-[#ff5400] transition-all duration-300 ease-in-out"
                      icon="fa-solid fa-video"
                    />
                    <Typography className="text-13 font-medium text-primary-900 group-hover/helpyou:!text-[#ff5400] transition-all duration-300 ease-in-out">
                      {_t("How can we help you today?")}
                    </Typography>
                  </div>
                </TypographyLink>
              </div>
            }
          />

          <div className="flex w-full max-w-[1360px] flex-[1_0_0%] overflow-hidden flex-col">
            <div className="md:px-4 pl-10 pr-5 py-2.5 border-b border-gray-300 dark:border-white/10 flex items-center relative justify-start">
              <Button
                className="md:!hidden flex !w-6 h-6 !absolute left-2.5"
                type="text"
                onClick={() => setSideMenuOpen((prev) => !prev)}
                icon={
                  <FontAwesomeIcon
                    className="text-base w-[18px] h-[18px] text-primary-gray-80 dark:text-white/90"
                    icon="fa-regular fa-bars"
                  />
                }
              />
              <div className="flex justify-between w-full">
                <div className="flex items-center  text-primary-900 dark:text-white/90">
                  <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-gray-200/50 dark:bg-dark-500">
                    <FontAwesomeIcon
                      className="w-4 h-4 !text-primary-900 dark:!text-white/90"
                      // icon="fa-regular fa-file-arrow-up"
                      icon={
                        importCIDBItemsCsv === "import"
                          ? "fa-regular fa-file-arrow-down"
                          : importCIDBItemsCsv === "previous_imports"
                          ? "fa-regular fa-eye"
                          : importCIDBItemsCsv === "export"
                          ? "fa-regular fa-file-arrow-up"
                          : ""
                      }
                    />
                  </div>
                  <Header
                    level={5}
                    className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
                  >
                    {importCIDBItemsCsv === "import"
                      ? _t("Import Items")
                      : importCIDBItemsCsv === "previous_imports"
                      ? _t("Previous Imports")
                      : importCIDBItemsCsv === "export"
                      ? _t("Export Items")
                      : ""}
                  </Header>
                </div>
                <CloseButton
                  onClick={() => {
                    setImportCIDBItemsCsv("");
                    if (anyChangesApplied) {
                      refreshAgGrid();
                      setAnyChangesApplied(false);
                    }
                  }}
                />
              </div>
            </div>
            <div className="py-4">{selectedComponent}</div>
          </div>
        </div>
      </div>
    </Drawer>
  );
};
export default ImportCIDBItemsCsv;

export const findBestMatchingOption = (label: string) => {
  const sanitizedLabel = label.trim().toLowerCase().replace(/\s+/g, "");

  const matchingOption = CSV_HEADER_OPTIONS.find(
    (option) =>
      option.label.trim().toLowerCase().replace(/\s+/g, "") === sanitizedLabel
  );

  return matchingOption ? matchingOption.value : "";
};
