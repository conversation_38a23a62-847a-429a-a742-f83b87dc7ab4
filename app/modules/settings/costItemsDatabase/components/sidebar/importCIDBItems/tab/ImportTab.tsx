// Atoms
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { SelectField } from "~/shared/components/molecules/selectField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
// Other
import { useEffect, useMemo, useRef, useState } from "react";
// Lodash
import isEmpty from "lodash/isEmpty";

// React
import { useDropzone } from "react-dropzone";
import { ColDef } from "ag-grid-community";
import { useTranslation } from "~/hook";
import Papa from "papaparse";
import {
  getWebWorkerApiParams,
  webWorker<PERSON><PERSON>,
} from "~/shared/services/apiService/web-worker";
import { Number } from "~/helpers/helper";
import { cidbItemRoutes } from "~/route-services/cidb-item.routes";
import {
  CSV_HEADER_OPTIONS,
  itemTypes,
  MLESOITEMS_SELECTOPTION_TAB_KEYS,
} from "../../../../utils/constants";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { IHeaderParams } from "ag-grid-community";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "antd";
import { ICellRendererParams } from "ag-grid-community";

const ImportTab = ({
  setAnyChangesApplied,
  setImportCIDBItemsCsv,
  refreshAgGrid,
}: {
  setAnyChangesApplied: React.Dispatch<React.SetStateAction<boolean>>;
  setImportCIDBItemsCsv: any;
  refreshAgGrid: any;
}) => {
  const { _t } = useTranslation();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFileData, setSelectedFileData] = useState<File>();

  const [rowData, setRowData] = useState<ImportPreViewRowData[]>([]);
  const [columnDefs, setColumnDefs] = useState<ColDef[]>([]);
  const [isButtonDisabled, setButtonDisabled] = useState<boolean>(false);
  const [columnMapping, setColumnMapping] = useState<Record<string, string>>(
    {}
  );
  const [updateExisting, setUpdateExisting] = useState<boolean>(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);

  const [selectedItemType, setSelectedItemType] = useState<
    (typeof itemTypes)[0]["value"]
  >(MLESOITEMS_SELECTOPTION_TAB_KEYS.material);

  const { getGlobalModuleByKey, checkGlobalModulePermission } =
    useGlobalModule();

  const module = useMemo(() => {
    const key = (() => {
      switch (selectedItemType) {
        case MLESOITEMS_SELECTOPTION_TAB_KEYS.material:
          return CFConfig.material_module;
        case MLESOITEMS_SELECTOPTION_TAB_KEYS.labor:
          return CFConfig.labour_module;
        case MLESOITEMS_SELECTOPTION_TAB_KEYS.equipment:
          return CFConfig.equipment_module;
        case MLESOITEMS_SELECTOPTION_TAB_KEYS.sub_contractor:
          return CFConfig.sub_contractor_module;
        default:
          return CFConfig.otherItems_module;
      }
    })();
    return getGlobalModuleByKey(key);
  }, [selectedItemType]);

  const resetStates = () => {
    if (fileInputRef?.current) {
      fileInputRef.current.value = ""; // Clear the file input
    }
    setSelectedFileData(undefined);
    setRowData([]);
    setColumnDefs([]);
    setButtonDisabled(false);
    setUpdateExisting(false);
    setSelectedItemType(MLESOITEMS_SELECTOPTION_TAB_KEYS.material);
  };
  useEffect(() => {
    if (selectedFileData) {
      try {
        Papa.parse<ImportPreViewRowData>(selectedFileData, {
          header: true,
          skipEmptyLines: true,
          complete: (result) => {
            if (result.data?.length < 1) {
              notification.error({
                description: `Selected CSV file is Empty!`,
              });

              return false;
            }
            const allValuesEmpty = Object.values(result?.data[0]).every(
              (value) => isEmpty(value || "")
            );

            if (allValuesEmpty) {
              notification.error({
                description: `Selected CSV file is Empty!`,
              });

              return false;
            }
            setRowData(result.data);
            setColumnDefs(
              (result.meta.fields || [])
                .map((field) => {
                  const matchingOption = findBestMatchingOption(field);
                  if (matchingOption) {
                    setColumnMapping((prev) => ({
                      ...prev,
                      [field]: matchingOption,
                    }));
                  }
                  return {
                    headerValueGetter: "colDef.headerName",
                    headerComponent: (props: IHeaderParams) => {
                      const dropdownOptions =
                        selectedItemType === "all"
                          ? CSV_HEADER_OPTIONS
                          : CSV_HEADER_OPTIONS.filter(
                              (option) => option.label !== "Item Type"
                            );
                      const matchingOption = findBestMatchingOption(
                        props.displayName
                      );
                      return (
                        <SelectField
                          editInline={true}
                          iconView={true}
                          labelPlacement="left"
                          value={matchingOption || ""}
                          options={dropdownOptions}
                          onChange={(value, option) => {
                            if (!Array.isArray(option)) {
                              const valueString =
                                typeof value === "string" ? value : "";
                              setColumnMapping((prev) => ({
                                ...prev,
                                [props.column.getColId()]: valueString,
                              }));

                              const newColumnDefs = props.columnApi
                                .getColumnState()
                                .map((column) => {
                                  let newColumn =
                                    props.api.getColumnDef(column.colId) || {};
                                  if (
                                    column.colId === props.column.getColId() &&
                                    Object.keys(newColumn).length
                                  ) {
                                    return {
                                      ...newColumn,
                                      headerName: option?.label || "",
                                    };
                                  }
                                  return newColumn;
                                });
                              props.api.setColumnDefs(newColumnDefs);
                            }
                          }}
                        />
                      );
                    },
                    field: field,
                    headerName: field,
                    cellRenderer: (params: ICellRendererParams) => (
                      <Tooltip title={params.value} placement="topLeft">
                        <div className="truncate overflow-hidden whitespace-nowrap max-w-[200px]">
                          {params.value}
                        </div>
                      </Tooltip>
                    ),
                  } as ColDef;
                })
                .filter((colRef) => colRef?.field !== "CIDB ID#")
            );
          },
        });
      } catch (err) {
        notification.error({
          description: `Error parsing CSV file`,
        });
      }
    }
  }, [selectedFileData]);

  // Use the dropzone
  const { getRootProps, getInputProps } = useDropzone({
    onDrop: (acceptedFiles, rejectedFiles) => {
      // Check if any files were rejected
      if (rejectedFiles.length > 0) {
        notification.error({
          description: "Please upload a valid CSV file",
        });
        return; // Prevent further processing
      }

      // Proceed with accepted files
      const file = acceptedFiles?.[0];
      if (file) {
        setRowData([]);
        setColumnDefs([]);
        setButtonDisabled(false);
        setSelectedFileData(file);
      }
    },
    accept: {
      "text/csv": [".csv"], // MIME type for CSV files
    },
    disabled: !selectedItemType || selectedItemType === "Select Item Type",
  });

  const options = itemTypes.filter((option) =>
    "key" in option
      ? option.key
        ? checkGlobalModulePermission(Number(option.key)) === "full_access"
        : false
      : true
  );

  useEffect(() => {
    const moduleTab = [
      MLESOITEMS_SELECTOPTION_TAB_KEYS.material,
      MLESOITEMS_SELECTOPTION_TAB_KEYS.equipment,
      MLESOITEMS_SELECTOPTION_TAB_KEYS.labor,
      MLESOITEMS_SELECTOPTION_TAB_KEYS.other,
      MLESOITEMS_SELECTOPTION_TAB_KEYS.sub_contractor,
    ].find((key) => window.location.pathname.endsWith(`/${key}`));

    const selectedOption = options.find((option) => option.value === moduleTab);

    setSelectedItemType(selectedOption?.value || options[0]?.value);
  }, [options[0]]);

  return (
    <>
      <div className="modal-body h-[calc(100dvh-132px)] px-4">
        <div className="grid gap-3.5">
          <div className="w-full bg-[#F8F8F8] rounded-lg">
            <div className="w-full flex bg-white border border-[#E5E7EB] rounded-lg">
              <div className="bg-[#EBF1F9] flex flex-col gap-2.5 w-full max-w-[220px] rounded-s-lg py-[15px] px-5">
                <FieldLabel>{_t("Item Type")}</FieldLabel>
                <SelectField
                  popupClassName="!min-w-[150px] !w-[178px]"
                  className="bg-white disabled:opacity-50 header-dropdown-select pl-3 !h-7 min-w-[90px] rounded"
                  fieldClassName="before:hidden"
                  placement="bottomRight"
                  value={selectedItemType}
                  placeholder={_t("Select")}
                  labelPlacement="left"
                  options={options}
                  onChange={(value) => {
                    if (typeof value === "string") {
                      setSelectedItemType(value);
                    }
                  }}
                />
              </div>
              <div
                className="new-file-upload-Dragger m-3.5 w-full"
                {...getRootProps()}
              >
                <input ref={fileInputRef} {...getInputProps()} />
                <div
                  className={`flex md:flex-row flex-col md:gap-1.5 gap-1 items-center justify-center p-4 border-2 border-dashed border-[#d9d9d9] rounded-lg hover:border-primary-900 ${
                    selectedItemType === "Select Item Type"
                      ? "cursor-not-allowed"
                      : "cursor-pointer"
                  }`}
                >
                  <img
                    src="https://cdn.contractorforeman.net/assets/images/upload-file.svg"
                    alt="Upload Image"
                    className="w-[35px]"
                  />
                  <Typography className="sm:text-sm text-xs pl-2.5 text-primary-900 text-center font-semibold">
                    {_t("Click here or Drop files here to Upload")}
                    {_t(" OR ")}
                    <Typography className="font-bold">
                      {_t("Browse Files")}
                    </Typography>{" "}
                    {_t("On Your Computer")}
                  </Typography>
                </div>
              </div>
            </div>
            <Typography className="text-13 text-deep-orange-500 block py-1 px-2">
              {_t(
                "Make 100% sure that all of your data has the correct column names. Do not click Import until you have reviewed your data. If you need help with this step, send a <NAME_EMAIL>."
              )}
            </Typography>
          </div>

          <Header level={5} className="!m-0 !text-primary-900">
            UPLOADED CSV REVIEW
          </Header>

          <div
            className={`p-2 common-card ${
              rowData.length <= 0
                ? "max-h-[calc(100dvh-371px)] overflow-y-auto"
                : ""
            }`}
          >
            <div className="ag-theme-alpine">
              {useMemo(
                () => (
                  <StaticTable
                    className="static-table thead-select-field upload-csv-table-drawer"
                    columnDefs={[
                      {
                        headerName: "CIDB ID#",
                        field: "CIDB ID#",
                      },
                      ...columnDefs,
                    ]}
                    rowData={rowData}
                    noRowsOverlayComponent={() => (
                      <NoRecords
                        image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                      />
                    )}
                  />
                ),
                [rowData, columnDefs]
              )}
            </div>
          </div>
          <div className="md:flex hidden">
            {/* <CustomCheckBox
              className="gap-1.5"
              name="update_existing_items"
              checked={Boolean(updateExisting)}
              disabled={isButtonDisabled}
              onChange={(e) => {
                setUpdateExisting(e.target.checked);
              }}
            > */}
            <Typography className="text-13 !text-deep-orange-500">
              {_t(
                "Update Rules: Do not remove the CIDB ID# column. Items with a matching ID# will be updated (even if the Item name is different).  Items without an ID# but have a matching name and Unit value will be updated."
              )}
            </Typography>
            {/* </CustomCheckBox> */}
          </div>
        </div>
      </div>
      <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
        <PrimaryButton
          className="min-w-20 w-auto"
          onClick={() => setIsConfirmDialogOpen(true)}
          disabled={isButtonDisabled || !selectedFileData}
          isLoading={isButtonDisabled}
          buttonText={_t("Import Items")}
        />
      </div>
      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-triangle-exclamation"
          modaltitle={_t("Warning")}
          description={_t(
            "WARNING: The data shown in the preview will be uploaded as-is once you click Yes. If help is needed, please click No and contact Support for assistance."
          )}
          onCloseModal={() => setIsConfirmDialogOpen(false)}
          onAccept={() => {
            (async function () {
              setIsConfirmDialogOpen(false);
              setButtonDisabled(true);

              const payloadData = rowData?.map((row) => {
                const newRow: Record<string, string> = {};
                Object.keys(row).forEach((key) => {
                  if (columnMapping?.[key]) {
                    if (
                      ["name", "unit", "cost_code", "sku"].includes(
                        columnMapping[key]
                      )
                    ) {
                      newRow[columnMapping[key]] = HTMLEntities.encode(
                        row[key]
                      );
                    } else {
                      newRow[columnMapping[key]] = row[key];
                    }
                  }
                });
                return newRow;
              });
              try {
                const data = await getWebWorkerApiParams({
                  otherParams: {
                    cost_items: payloadData,
                    module_id: module?.module_id,
                    // selectedItemType === "all" ? 73 : module?.module_id,
                    modify_existing_items: Number(updateExisting),
                  },
                });
                const response = (await webWorkerApi({
                  url: cidbItemRoutes.import_items_json,
                  method: "POST",
                  data,
                })) as IApiCallResponse;
                if (response.success) {
                  notification.success({
                    message: _t("Success"),
                    description: _t("Items imported successfully!"),
                  });
                  resetStates();
                  setAnyChangesApplied(true);
                  setImportCIDBItemsCsv("");
                  refreshAgGrid();
                } else {
                  notification.error({
                    description: response.message,
                  });
                }
              } catch (error) {
                notification.error({
                  description: (error as Error).message,
                });
              }
              setButtonDisabled(false);
            })();
          }}
          onDecline={() => setIsConfirmDialogOpen(false)}
        />
      )}
    </>
  );
};

export default ImportTab;

export const findBestMatchingOption = (label: string) => {
  const sanitizedLabel = label.trim().toLowerCase().replace(/\s+/g, "");

  const matchingOption = CSV_HEADER_OPTIONS.find(
    (option) =>
      option.label.trim().toLowerCase().replace(/\s+/g, "") === sanitizedLabel
  );

  return matchingOption ? matchingOption.value : "";
};
