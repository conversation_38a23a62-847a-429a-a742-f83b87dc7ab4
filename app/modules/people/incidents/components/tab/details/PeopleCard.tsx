import { useEffect, useMemo, useRef, useState } from "react";
// Molecules
import { InlineField } from "~/shared/components/molecules/inlineField";
import { InputField } from "~/shared/components/molecules/inputField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { TimePickerField } from "~/shared/components/molecules/timePickerField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { AvatarIconPopover } from "~/shared/components/organisms/avatarIconPopover";
// Hook
import { useTranslation } from "~/hook";
import { getStatusForField } from "~/shared/utils/helper/common";
import {
  backendDateFormat,
  backendTimeFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import { getGConfig, getGSettings } from "~/zustand";
import dayjs from "dayjs";
import { sendNotification } from "../../../redux/action/sendNotification";
import { useParams } from "@remix-run/react";
import { useAppINSelector } from "../../../redux/store";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
import { getDirectaryKeyById } from "~/components/sidebars/multi-select/customer/zustand/action";
import { sanitizeString } from "~/helpers/helper";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

const PeopleCard = ({
  inputValues,
  setInputValues,
  handleUpdateField,
  loadingStatus,
  handleChangeFieldStatus,
  setIsOpenEmployee,
  employee,
  peopleInvolved,
  witness,
  peopleNotified,
  setIsOpenPeopleInvolved,
  setIsOpenWitness,
  setIsOpenPeopleNotified,
  setSelectedPeopleNotifiedUserId,
  setIsOpenPeopleNotifiedContactDetails,
  isReadOnly = false,
}: IIncidentPeopleCardProps) => {
  const { _t } = useTranslation();
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const closeConfirmationModal = () => {
    setIsConfirmDialogOpen(false);
  };
  const { date_format }: GSettings = getGSettings();
  const { module_access }: GConfig = getGConfig();
  const { incidentDetail } = useAppINSelector((state) => state.incidentDetail);
  const [isTimerDropdown, setTimerDropdown] = useState<boolean>(false);
  const [isDateTimeVisible, setDateTimeVisible] = useState<boolean>(false);

  const [peopleInvolvedNames, setPeopleInvolvedNames] = useState<string>("");
  const [peopleNotifiedNames, setPeopleNotifiedNames] = useState<string>("");
  const [witnessNames, setWitnessNames] = useState<string>("");
  const dtDivRef = useRef<HTMLLIElement>(null);
  const dateTimeSelectRef = useRef<HTMLLIElement>(null);
  const gConfig: GConfig = getGConfig();
  const dueDateTimeFormat = useMemo(() => {
    return `${date_format} hh:mm A`;
  }, [date_format]);

  const handleChangeDate = (dateString: string, options: string) => {
    const formatDate = (date: string, type: string) => {
      if (type === "notify_date") return backendDateFormat(date, date_format);
      if (type === "notify_time") return backendTimeFormat(date);
      return date;
    };

    const formattedDate = dateString ? formatDate(dateString, options) : null;

    const updatedValues = { ...inputValues, [options]: dateString || null };
    setInputValues(updatedValues);

    handleUpdateField({ [options]: formattedDate }, dateString);
  };

  const handleClearDatesAndSendFullPayload = () => {
    const payload = {
      notify_date: null,
      notify_time: null,
    };
    setInputValues((prev) => ({
      ...prev,
      notify_date: null,
      notify_time: null,
    }));
    handleUpdateField(payload, "");
  };

  useEffect(() => {
    if (peopleInvolved?.length) {
      setPeopleInvolvedNames(
        peopleInvolved.length <= 1
          ? peopleInvolved.map((item) => item?.display_name).join(", ")
          : `${peopleInvolved.length} Selected`
      );
    } else {
      setPeopleInvolvedNames("");
    }
    if (peopleNotified?.length) {
      setPeopleNotifiedNames(
        peopleNotified.length <= 2
          ? peopleNotified.map((item) => item?.display_name).join(", ")
          : `${peopleNotified.length} Selected`
      );
    } else {
      setPeopleNotifiedNames("");
    }
    if (witness?.length) {
      setWitnessNames(
        witness.length <= 2
          ? witness.map((item) => item?.display_name).join(", ")
          : `${witness.length} Selected`
      );
    } else {
      setWitnessNames("");
    }
  }, [peopleInvolved, peopleNotified, witness]);

  const params = useParams<{ id: string }>();
  const [sendNotificationLoading, setSendNotificationLoading] =
    useState<boolean>(false);

  const handleSendNotification = async () => {
    setSendNotificationLoading(true);
    if (params && params.id) {
      const res = await sendNotification({ incident_id: params.id });
    }
    setSendNotificationLoading(false);
    closeConfirmationModal();
  };
  const handleNameOutsideClick = (event: MouseEvent) => {
    const clickedElement = event.target as HTMLElement;
    const targetElement = document.querySelector(".ant-picker-dropdown");

    if (targetElement && targetElement.contains(clickedElement)) {
      return;
    }

    if (
      dtDivRef.current &&
      dtDivRef.current.contains(clickedElement) &&
      clickedElement.tagName.toLowerCase() === "svg" &&
      !isTimerDropdown
    ) {
      setDateTimeVisible(false);
      return;
    }

    if (dtDivRef.current && !dtDivRef.current.contains(event.target as Node)) {
      setDateTimeVisible(false);
    }
  };
  useEffect(() => {
    document.addEventListener("mousedown", handleNameOutsideClick);
    return () => {
      document.removeEventListener("mousedown", handleNameOutsideClick);
    };
  }, []);
  return (
    <>
      <CrudCommonCard
        headerTitle={_t("People")}
        iconProps={{
          icon: "fa-solid fa-user-group",
          containerClassName:
            "bg-[linear-gradient(180deg,#ADD1001a_0%,#7B920A1a_100%)]",
          id: "in_people_icon",
          colors: ["#ADD100", "#7B920A"],
        }}
        children={
          <div className="pt-2">
            <ul className="w-full flex flex-col gap-1 mt-[3px]">
              {inputValues.txn_type === "incident" && (
                <li className="overflow-hidden">
                  <ButtonField
                    label={_t("People Involved")}
                    placeholder="Select/View People Involved"
                    labelPlacement="left"
                    readOnly={module_access === "read_only"}
                    editInline={true}
                    iconView={true}
                    avatarProps={
                      peopleInvolved?.length == 1
                        ? {
                            user: {
                              name: HTMLEntities.decode(
                                sanitizeString(peopleInvolved[0]?.display_name)
                              ),
                              image: peopleInvolved[0]?.image || "",
                            },
                          }
                        : undefined
                    }
                    rightIcon={
                      peopleInvolved.length ? (
                        <div className="flex items-center gap-1">
                          {peopleInvolved.length &&
                            peopleInvolved.length === 1 && (
                              <>
                                <ContactDetailsButton
                                  onClick={(event) => {
                                    event.stopPropagation();
                                    setSelectedPeopleNotifiedUserId(
                                      peopleInvolved[0].user_id
                                    );
                                    setIsOpenPeopleNotifiedContactDetails(true);
                                  }}
                                />
                                <DirectoryFieldRedirectionIcon
                                  className="!w-5 !h-5"
                                  directoryId={
                                    peopleInvolved[0]?.user_id?.toString() || ""
                                  }
                                  directoryTypeKey={getDirectaryKeyById(
                                    peopleInvolved[0]?.type_name?.toLowerCase() ===
                                      "employee"
                                      ? 2
                                      : peopleInvolved[0]?.type_name?.toLowerCase() ===
                                        "contractor"
                                      ? 4
                                      : peopleInvolved[0]?.type_name,
                                    gConfig
                                  )}
                                />
                              </>
                            )}
                          {peopleInvolved?.length > 1 && (
                            <AvatarIconPopover
                              placement="bottom"
                              redirectionIcon={true}
                              assignedTo={peopleInvolved as IAssignedToUsers[]}
                              setSelectedUserId={(data) => {
                                setSelectedPeopleNotifiedUserId(data.id);
                              }}
                              setIsOpenContactDetails={
                                setIsOpenPeopleNotifiedContactDetails
                              }
                            />
                          )}
                        </div>
                      ) : (
                        <></>
                      )
                    }
                    name="emp_involved"
                    onClick={() => {
                      setIsOpenPeopleInvolved(true);
                    }}
                    value={
                      peopleInvolved?.length > 1
                        ? peopleInvolvedNames
                        : peopleInvolved?.length === 1
                        ? HTMLEntities.decode(
                            sanitizeString(peopleInvolved[0]?.display_name)
                          )
                        : ""
                    }
                    statusProps={
                      peopleInvolvedNames
                        ? {
                            status: getStatusForField(
                              loadingStatus,
                              "emp_involved"
                            ),
                          }
                        : { status: getStatusForField(loadingStatus, " ") }
                    }
                    disabled={
                      isReadOnly ||
                      getStatusForField(loadingStatus, "emp_involved") ===
                        "loading"
                    }
                  />
                </li>
              )}
              <li className="overflow-hidden">
                <ButtonField
                  label={witness.length <= 1 ? _t("Witness") : _t("Witnesses")}
                  placeholder="Select/View Witness"
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={module_access === "read_only"}
                  name="emp_witness"
                  onClick={() => {
                    setIsOpenWitness(true);
                  }}
                  avatarProps={
                    witness?.length == 1
                      ? {
                          user: {
                            name: HTMLEntities.decode(
                              sanitizeString(witness[0]?.display_name)
                            ),
                            image: witness[0]?.image || "",
                          },
                        }
                      : undefined
                  }
                  rightIcon={
                    witness.length ? (
                      <div className="flex items-center gap-1">
                        {witness.length && witness.length === 1 && (
                          <>
                            <ContactDetailsButton
                              onClick={(event) => {
                                event.stopPropagation();
                                setSelectedPeopleNotifiedUserId(
                                  witness[0].user_id
                                );
                                setIsOpenPeopleNotifiedContactDetails(true);
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              className="!w-5 !h-5"
                              directoryId={
                                witness[0]?.user_id?.toString() || ""
                              }
                              directoryTypeKey={getDirectaryKeyById(
                                witness[0]?.type_name?.toLowerCase() ===
                                  "employee"
                                  ? 2
                                  : witness[0]?.type_name?.toLowerCase() ===
                                    "contractor"
                                  ? 4
                                  : witness[0]?.type_name,
                                gConfig
                              )}
                            />
                          </>
                        )}

                        {witness?.length > 1 && (
                          <AvatarIconPopover
                            placement="bottom"
                            redirectionIcon={true}
                            assignedTo={witness as IAssignedToUsers[]}
                            setSelectedUserId={(data) => {
                              setSelectedPeopleNotifiedUserId(data.id);
                            }}
                            setIsOpenContactDetails={
                              setIsOpenPeopleNotifiedContactDetails
                            }
                          />
                        )}
                      </div>
                    ) : (
                      <></>
                    )
                  }
                  value={
                    witness?.length > 1
                      ? witnessNames
                      : witness?.length === 1
                      ? HTMLEntities.decode(
                          sanitizeString(witness[0]?.display_name)
                        )
                      : ""
                  }
                  statusProps={
                    witnessNames
                      ? {
                          status: getStatusForField(
                            loadingStatus,
                            "emp_witness"
                          ),
                        }
                      : { status: getStatusForField(loadingStatus, " ") }
                  }
                  disabled={
                    isReadOnly ||
                    getStatusForField(loadingStatus, "emp_witness") ===
                      "loading"
                  }
                />
              </li>
              <li className="overflow-hidden">
                <ButtonField
                  label={_t("People Notified ")}
                  placeholder="Select/View People Notified"
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  name="emp_notified"
                  readOnly={module_access === "read_only"}
                  onClick={() => {
                    setIsOpenPeopleNotified(true);
                  }}
                  value={
                    peopleNotified?.length > 1
                      ? peopleNotifiedNames
                      : peopleNotified?.length === 1
                      ? HTMLEntities.decode(
                          sanitizeString(peopleNotified[0]?.display_name)
                        )
                      : ""
                  }
                  statusProps={
                    peopleNotifiedNames
                      ? {
                          status: getStatusForField(
                            loadingStatus,
                            "emp_notified"
                          ),
                        }
                      : { status: getStatusForField(loadingStatus, " ") }
                  }
                  disabled={
                    isReadOnly ||
                    getStatusForField(loadingStatus, "emp_notified") ===
                      "loading"
                  }
                  avatarProps={
                    peopleNotified?.length == 1
                      ? {
                          user: {
                            name: HTMLEntities.decode(
                              sanitizeString(peopleNotified[0]?.display_name)
                            ),
                            image: peopleNotified[0]?.image || "",
                          },
                        }
                      : undefined
                  }
                  rightIcon={
                    peopleNotified.length ? (
                      <div className="flex items-center gap-1">
                        {peopleNotified.length &&
                          peopleNotified.length === 1 && (
                            <>
                              <ContactDetailsButton
                                onClick={(event) => {
                                  event.stopPropagation();
                                  setSelectedPeopleNotifiedUserId(
                                    peopleNotified[0].user_id
                                  );
                                  setIsOpenPeopleNotifiedContactDetails(true);
                                }}
                              />
                              <DirectoryFieldRedirectionIcon
                                className="!w-5 !h-5"
                                directoryId={
                                  peopleNotified[0]?.user_id?.toString() || ""
                                }
                                directoryTypeKey={getDirectaryKeyById(
                                  peopleNotified[0]?.type_name?.toLowerCase() ===
                                    "employee"
                                    ? 2
                                    : peopleNotified[0]?.type_name?.toLowerCase() ===
                                      "contractor"
                                    ? 4
                                    : peopleNotified[0]?.type_name,
                                  gConfig
                                )}
                              />
                            </>
                          )}
                        {peopleNotified?.length > 1 && (
                          <AvatarIconPopover
                            placement="bottom"
                            redirectionIcon={true}
                            assignedTo={peopleNotified as IAssignedToUsers[]}
                            setSelectedUserId={(data) => {
                              setSelectedPeopleNotifiedUserId(data.id);
                            }}
                            setIsOpenContactDetails={
                              setIsOpenPeopleNotifiedContactDetails
                            }
                          />
                        )}
                        {peopleNotifiedNames &&
                          peopleNotifiedNames.length &&
                          module_access !== "read_only" && (
                            <ButtonWithTooltip
                              tooltipTitle={_t("Send Notification")}
                              tooltipPlacement="top"
                              icon="fa-regular fa-paper-plane"
                              iconClassName="w-3.5 h-3.5"
                              onClick={() => setIsConfirmDialogOpen(true)}
                            />
                          )}
                      </div>
                    ) : (
                      <></>
                    )
                  }
                />
              </li>
              <li
                className={`overflow-hidden flex ${
                  !isDateTimeVisible ? "hidden" : ""
                }`}
                ref={dtDivRef}
              >
                <InlineField
                  label={_t("Notified Date")}
                  labelPlacement="left"
                  field={
                    <div className="grid sm:grid-cols-2 gap-1.5 items-center w-full">
                      <DatePickerField
                        label=""
                        labelPlacement="left"
                        placeholder={_t("Select Date")}
                        required={false}
                        editInline={true}
                        iconView={true}
                        inputReadOnly={true}
                        readOnly={module_access === "read_only"}
                        allowClear={true}
                        value={
                          inputValues.notify_date
                            ? dayjs(inputValues?.notify_date, date_format)
                            : undefined
                        }
                        format={date_format}
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "notify_date"
                        )}
                        onChange={(_, dateString) => {
                          if (
                            inputValues?.notify_date !== dateString &&
                            !!dateString
                          ) {
                            handleChangeDate(
                              dateString as string,
                              "notify_date"
                            );
                          } else if (!dateString) {
                            handleChangeDate("", "notify_date");
                          }
                        }}
                        disabledDate={(currentDate) =>
                          currentDate &&
                          currentDate <
                            dayjs(inputValues.incident_date, date_format)
                        }
                      />
                      <TimePickerField
                        label=""
                        id="timePickerField"
                        name="notify_time"
                        placeholder={_t("Select Time")}
                        labelPlacement="left"
                        editInline={true}
                        iconView={true}
                        allowClear={true}
                        inputReadOnly={true}
                        readOnly={module_access === "read_only"}
                        showNow={true}
                        format="hh:mm A"
                        use12Hours
                        value={
                          inputValues?.notify_time?.trim()
                            ? dayjs(inputValues.notify_time.trim(), "hh:mm A")
                            : null
                        }
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "notify_time"
                        )}
                        onOk={() => {
                          setTimerDropdown(false);
                          setDateTimeVisible(false);
                        }}
                        onFocus={() => {
                          setTimerDropdown(true);
                        }}
                        onChange={(_, val) => {
                          setTimerDropdown(false);
                          if (inputValues?.notify_time !== val) {
                            handleChangeDate(val as string, "notify_time");
                          }
                        }}
                        disabledTime={(current) => {
                          const incidentDate = dayjs(
                            inputValues?.incident_date,
                            date_format
                          );
                          const incidentTime = dayjs(
                            inputValues?.incident_time,
                            "hh:mm A"
                          );

                          const notifiedDate = dayjs(
                            inputValues?.notify_date,
                            date_format
                          );

                          // If the incident date and the notified date are the same
                          if (incidentDate.isSame(notifiedDate, "day")) {
                            return {
                              disabledHours: () => {
                                return Array.from(
                                  { length: 24 },
                                  (_, i) => i
                                ).filter((hour) => hour < incidentTime.hour());
                              },
                              disabledMinutes: () => {
                                if (current.hour() === incidentTime.hour()) {
                                  return Array.from(
                                    { length: 60 },
                                    (_, i) => i
                                  ).filter(
                                    (minute) => minute < incidentTime.minute()
                                  );
                                }
                                return [];
                              },
                              disabledSeconds: () => {
                                if (
                                  current.hour() === incidentTime.hour() &&
                                  current.minute() === incidentTime.minute()
                                ) {
                                  return Array.from(
                                    { length: 60 },
                                    (_, i) => i
                                  ).filter(
                                    (second) => second < incidentTime.second()
                                  );
                                }
                                return [];
                              },
                            };
                          } else {
                            // If the input date is not today, allow all times
                            return {};
                          }
                        }}
                      />
                    </div>
                  }
                />
              </li>
              <li
                className={`overflow-hidden ${
                  isDateTimeVisible ? "hidden" : ""
                }`}
                ref={dateTimeSelectRef}
              >
                <InlineField
                  label={_t("Notified Date")}
                  labelPlacement="left"
                  field={
                    <div className="relative w-full group/edit">
                      <InputField
                        labelPlacement="left"
                        placeholder={_t("Select Date/Time")}
                        required={false}
                        editInline={true}
                        iconView={true}
                        readOnly={module_access === "read_only"}
                        readOnlyClassName="!h-[34px]"
                        value={
                          !!inputValues?.notify_date &&
                          !!inputValues?.notify_time
                            ? dayjs(
                                `${inputValues.notify_date || ""} ${
                                  inputValues.notify_time || ""
                                }`,
                                `${date_format} hh:mm A`
                              ).format(dueDateTimeFormat)
                            : !!inputValues?.notify_date
                            ? inputValues.notify_date
                            : !!inputValues?.notify_time
                            ? dayjs(inputValues.notify_time, "hh:mm A").format(
                                "hh:mm A"
                              )
                            : ""
                        }
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "notify_date_time"
                        )}
                        onChange={() => {}}
                        onFocus={() => {
                          setDateTimeVisible(true);
                        }}
                        onMouseLeaveDiv={() => {
                          handleChangeFieldStatus({
                            field: "notify_date_time",
                            status: "button",
                            action: "ML",
                          });
                        }}
                      />
                      {(!!inputValues.notify_date ||
                        !!inputValues.notify_time) &&
                      !isReadOnly &&
                      !["loading", "success", "error"].includes(
                        getStatusForField(loadingStatus, "notify_date_time")
                      ) ? (
                        <div className="absolute z-10 top-1/2 -translate-y-1/2 right-2.5 opacity-0 group-hover/edit:opacity-100">
                          <FontAwesomeIcon
                            icon="fa-solid fa-circle-xmark"
                            className="text-[#00000040] hover:text-[#00000073] cursor-pointer w-3 h-3"
                            onClick={() => {
                              handleClearDatesAndSendFullPayload();
                            }}
                          />
                        </div>
                      ) : null}
                    </div>
                  }
                />
              </li>
            </ul>
          </div>
        }
      />
      <ConfirmModal
        isOpen={isConfirmDialogOpen}
        modalIcon="fa-regular fa-paper-plane"
        size="510px"
        modaltitle={_t("Send Notification")}
        description={_t(
          `Do you want to send a notification to the assigned contact? Notifications will be sent based on the preferences within the user's account.`
        )}
        onCloseModal={closeConfirmationModal}
        onAccept={handleSendNotification}
        onDecline={closeConfirmationModal}
      />
    </>
  );
};

export default PeopleCard;
