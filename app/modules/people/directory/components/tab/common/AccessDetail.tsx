// Atoms
import { <PERSON>lt<PERSON> } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { ColorPicker } from "~/shared/components/atoms/colorPicker";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { RadioGroupList } from "~/shared/components/molecules/radioGroupList";
import { SelectField } from "~/shared/components/molecules/selectField";
// Organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";
// Antd
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import type { CheckboxProps, RadioChangeEvent } from "antd";
import type { ColorPickerProps, GetProp } from "antd";
// hook
import { useTranslation } from "~/hook";
// react
import { useEffect, useMemo, useRef, useState } from "react";
// lodash
import delay from "lodash/delay";
// redux
import { updateDirDetailApi } from "../../../redux/action/dirDetailsAction";
import { useAppDispatch, useAppSelector } from "../../../redux/store";
import { updateDirAccess } from "../../../redux/slices/dirDetailsSlice";
// TODO replace this with redux.
import { getGConfig, useGModules } from "~/zustand";
// constant
import {
  dirTypeKeyByKey,
  dirTypeKeys,
  fieldStatus,
  VIEWABLE_PROJECT_OPTIONS,
} from "../../../utils/constasnts";
import {
  filterOptionBySubstring,
  getStatusForField,
  getStatusActionForField,
} from "~/shared/utils/helper/common";
// Helper
import { replaceDOMParams, sanitizeString } from "~/helpers/helper";
import { getCostCode } from "~/redux/action/getCostCodeAction";
import { getGlobalUser } from "~/zustand/global/user/slice";

type Color = GetProp<ColorPickerProps, "value">;
const AccessDetail = (costCodeReqBody: IGetCostCodeRequestBody) => {
  const { _t } = useTranslation();

  const gConfig: GConfig = getGConfig();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { user_id = 0 } = user || {};
  const { checkModuleAccessByKey } = useGModules();
  const dispatch = useAppDispatch();
  const loadingStatusRef = useRef(fieldStatus);
  const [selectProject, setSelectProject] = useState<boolean>(false);
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);
  const [enableAssociatedProject, setEnableAssociatedProject] =
    useState<boolean>(false);
  const [redirectToTimecard, setRedirectToTimecard] = useState<boolean>(false);
  const { accessDetail, details }: IDirInitialState = useAppSelector(
    (state) => state.dirDetails
  );
  const [projectSaved, setProjectSaved] = useState<boolean>(false);
  const [dirColorVal, setDirColorVal] = useState<Color>("#000000");

  const employeeTaskColor = useMemo<string>(
    () =>
      typeof dirColorVal === "string"
        ? dirColorVal
        : typeof dirColorVal === "object" && "toHexString" in dirColorVal
        ? dirColorVal.toHexString()
        : "#000000",
    [dirColorVal]
  );

  const [isFidLoading, setIsFidLoading] = useState<
    Partial<IEmpFooterFldLoading>
  >({
    show_in_crew_schedule: false,
    dir_color: false,
  });

  useEffect(() => {
    dispatch(getCostCode(costCodeReqBody));
  }, []);

  const [selectedProject, setSelectedProject] = useState<IProject[]>([]);

  const initAccessDetails: IAccessDetails = {
    enable_associated_projects: false,
    redirect_to_timecard: false,
    cost_code_id: "",
    is_viewable_projects: 0,
    array_assigned_projects: [],
    assigned_projects: "",
    assigned_project_names: "",
    show_in_crew_schedule: 0,
    dir_color: "",
    cost_code_csi_code: "",
    cost_code_name: "",
  };

  const [inputValues, setInputValues] =
    useState<IAccessDetails>(initAccessDetails);

  const initialValues: IAccessDetails = useMemo(() => {
    setSelectedProject(accessDetail.array_assigned_projects);
    setDirColorVal(accessDetail?.dir_color || "#000000");
    return {
      redirect_to_timecard: accessDetail.redirect_to_timecard,
      cost_code_id: accessDetail.cost_code_id,
      cost_code_csi_code: accessDetail.cost_code_csi_code,
      cost_code_name: accessDetail.cost_code_name,
      is_viewable_projects: accessDetail.is_viewable_projects,
      array_assigned_projects: accessDetail.array_assigned_projects,
      assigned_projects: accessDetail.assigned_projects,
      assigned_project_names: replaceDOMParams(
        sanitizeString(accessDetail.assigned_project_names)
      ),
      enable_associated_projects: accessDetail.enable_associated_projects,
      show_in_crew_schedule: accessDetail.show_in_crew_schedule,
      dir_color: accessDetail.dir_color,
    };
  }, [accessDetail]);

  useEffect(() => {
    setInputValues(initialValues);
  }, [initialValues]);

  const handleChange: CheckboxProps["onChange"] = async (e) => {
    setEnableAssociatedProject(true);
    handleUpdateField({ enable_associated_projects: e.target.checked });
  };

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleUpdateField = async (data: IDirDetailFieldsBoolean) => {
    const field = Object.keys(data)[0] as keyof IDirAccessDetail;
    setIsFidLoading((prev) => ({
      ...prev,
      [field]: true,
    }));
    if (field === "assigned_projects") {
      const assignProject = selectedProject?.map((data) => data.id).join(",");
      data = {
        assigned_projects: assignProject,
        is_viewable_projects: !!assignProject ? 1 : 0,
      };
    }
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });
    const values = Object.values(data)[0];
    const updateRes = (await updateDirDetailApi({
      directory_id: details?.user_id || "",
      type: details?.type || "",
      module_id: gConfig?.module_id,
      ...data,
    })) as IDirDetailsUpdateApiRes;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      if (
        [
          "enable_associated_projects",
          "redirect_to_timecard",
          "show_in_crew_schedule",
          "dir_color",
          "cost_code_id",
        ].includes(field)
      ) {
        dispatch(
          updateDirAccess({
            ...initialValues,
            [field]: values,
          })
        );
      }
      // remove by shail and nishant (09-08-2024)
      // Saloni naik suggest uncommit code (12-08-2024)
      else if (field === "is_viewable_projects") {
        dispatch(
          updateDirAccess({
            ...initialValues,
            [field]: values,
            array_assigned_projects: [],
            assigned_projects: "",
            assigned_project_names: "",
            enable_associated_projects: false,
          })
        );
      } else if (field === "assigned_projects") {
        const assignProject = selectedProject
          ?.map((project) => project.id)
          .join(", ");
        const assignProjectName = selectedProject
          ?.map((project) => project.project_name)
          .join(", ");
        const arrayAssignProject = selectedProject?.map((data) => ({
          id: Number(data?.id),
          project_name: data?.project_name,
        }));
        dispatch(
          updateDirAccess({
            ...initialValues,
            array_assigned_projects: arrayAssignProject,
            assigned_projects: assignProject,
            assigned_project_names: assignProjectName,
            is_viewable_projects: arrayAssignProject.length ? 1 : 0,
          })
        );
        setSelectedProject(arrayAssignProject);
      }
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      notification.error({
        description: updateRes?.message || "Something went wrong!",
      });
      if (field !== "assigned_projects") {
        setInputValues({
          ...inputValues,
          [field]: inputValues[field],
        });
      }
    }
    setEnableAssociatedProject(false);
    setRedirectToTimecard(false);
    setIsFidLoading((prev) => ({
      ...prev,
      [field]: false,
    }));
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  const onSaveProjectDrawer = () => {
    const arrayAssignProject = selectedProject?.map((data) => ({
      id: Number(data?.id),
      project_name: data?.project_name,
    }));
    handleUpdateField({ assigned_projects: arrayAssignProject });
  };

  useEffect(() => {
    if (projectSaved) {
      onSaveProjectDrawer();
      setProjectSaved(false);
    }
  }, [projectSaved]);
  const isReadOnly = useMemo(
    () =>
      checkModuleAccessByKey(dirTypeKeys[details?.type_key || ""]) ===
      "read_only",
    [details?.type_key]
  );

  const { codeCostData }: IGetCostCodeList = useAppSelector(
    (state) => state.costCode
  );

  const codeCostList: ISelectList[] = useMemo(() => {
    const costCodeList = codeCostData?.map((item: ICostCode) => ({
      label: `${HTMLEntities.decode(sanitizeString(item.cost_code_name))} ${
        !!item?.csi_code ? "(" + item?.csi_code + ")" : ""
      }`,
      value: item.code_id.toString(),
    }));
    if (!!accessDetail.cost_code_id) {
      const is_cost_code_avail_in_list = codeCostData.some(
        (c) => c.code_id?.toString() === accessDetail.cost_code_id?.toString()
      );
      if (!is_cost_code_avail_in_list) {
        const codeName = HTMLEntities.decode(
          sanitizeString(accessDetail.cost_code_name ?? "")
        );
        const csiCode = HTMLEntities.decode(
          sanitizeString(accessDetail.cost_code_csi_code ?? "")
        );

        const label = _t(
          `${codeName}${
            accessDetail.cost_code_csi_code ? ` (${csiCode})` : ""
          } (Archived)`
        );

        const newCostCodeToAdd = {
          label: label,
          value: accessDetail.cost_code_id?.toString(),
        };
        costCodeList.unshift(newCostCodeToAdd);
      }
    }
    return costCodeList;
  }, [codeCostData, accessDetail?.cost_code_id]);

  const handleTimeCardChange: CheckboxProps["onChange"] = async (e) => {
    setRedirectToTimecard(true);
    handleUpdateField({ redirect_to_timecard: e.target.checked });
  };

  const handleChangeCostCode = ({ value, name }: ISingleSelectOption) => {
    const newValue = typeof value === "object" ? value[0] : value;
    setInputValues({
      ...inputValues,
      [name]: newValue,
    });
    handleUpdateField({
      [name]: newValue,
    });
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Access Details")}
        iconProps={{
          icon: "fa-solid fa-unlock-keyhole",
          containerClassName:
            "bg-[linear-gradient(180deg,#63759A1a_0%,#63759A1a_100%)]",
          id: "access_detail_icon",
          colors: ["#63759A", "#63759A "],
        }}
        children={
          <div className="pt-2">
            <div className="flex lg:flex-row flex-col lg:gap-4 gap-2 mt-[3px] items-start">
              <ul className="w-full flex flex-col gap-1">
                <li>
                  <SelectField
                    label={_t("Default Cost Code")}
                    placeholder={_t("Select Cost Code")}
                    labelPlacement="left"
                    editInline={true}
                    iconView={true}
                    options={codeCostList}
                    className="select-cursor-no-drop"
                    value={codeCostList.find(
                      (item) =>
                        item?.value?.toString() ===
                        inputValues?.cost_code_id?.toString()
                    )}
                    onChange={(value: string | string[]) => {
                      handleChangeCostCode({
                        value,
                        name: "cost_code_id",
                      });
                    }}
                    allowClear
                    showSearch={true}
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                    fixStatus={getStatusForField(loadingStatus, "cost_code_id")}
                    disabled={
                      getStatusForField(loadingStatus, "cost_code_id") ===
                      "loading"
                    }
                    // Dhrvit and Shamsabbas Gheewala add for own user (23-08-2024)
                    readOnly={
                      isReadOnly ||
                      user_id.toString() === details?.user_id?.toString()
                    }
                    onClear={() => {
                      handleChangeCostCode({
                        value: "",
                        name: "cost_code_id",
                      });
                    }}
                  />
                </li>
                <li>
                  <InlineField
                    label={_t("Time Card")}
                    labelPlacement="left"
                    field={
                      <div className="flex items-center px-1.5">
                        <CustomCheckBox
                          className="gap-1.5"
                          name="redirect_to_timecard"
                          checked={inputValues?.redirect_to_timecard}
                          onChange={handleTimeCardChange}
                          disabled={
                            isReadOnly ? isReadOnly : redirectToTimecard
                          }
                          loadingProps={{
                            isLoading: redirectToTimecard,
                            className: "bg-[#ffffff]",
                          }}
                        >
                          {_t("Take User Directly to Timecard?")}
                        </CustomCheckBox>
                        <Tooltip
                          title={_t(
                            "If checked, the user will be taken directly to the Time Card section once they open the app.  This is recommended if your employee is primarily only using the Time Card feature."
                          )}
                          placement="top"
                        >
                          <FontAwesomeIcon
                            className="h-[15px] w-[15px] text-primary-900/50 hover:text-primary-900 cursor-pointer"
                            icon="fa-solid fa-circle-info"
                          />
                        </Tooltip>
                      </div>
                    }
                  />
                </li>
                <li>
                  <InlineField
                    label={_t("Viewable Projects")}
                    labelPlacement="left"
                    field={
                      <div className="w-full">
                        <RadioGroupList
                          view="row"
                          options={VIEWABLE_PROJECT_OPTIONS}
                          value={inputValues?.is_viewable_projects}
                          disabled={isReadOnly}
                          fixStatus={getStatusForField(
                            loadingStatus,
                            "is_viewable_projects"
                          )}
                          onChange={(e: RadioChangeEvent) => {
                            if (!e.target.value) {
                              handleUpdateField({
                                is_viewable_projects: e.target.value,
                              });
                            } else {
                              setSelectProject(true);
                            }
                          }}
                        />
                      </div>
                    }
                  />
                </li>
                {(Boolean(inputValues?.is_viewable_projects) ||
                  selectProject) && (
                  <>
                    <li className="overflow-hidden">
                      <ButtonField
                        label={_t("Assigned Projects")}
                        labelPlacement="left"
                        placeholder={_t("Assigned Projects")}
                        placeholderClassName="w-full"
                        editInline={true}
                        iconView={true}
                        onClick={() => setSelectProject(true)}
                        value={
                          accessDetail.array_assigned_projects.length > 2
                            ? `${accessDetail.array_assigned_projects.length} Selected`
                            : initialValues?.assigned_project_names
                        }
                        statusProps={{
                          status: getStatusForField(
                            loadingStatus,
                            "assigned_projects"
                          ),
                        }}
                        disabled={
                          isReadOnly ||
                          user_id.toString() === details?.user_id?.toString()
                        }
                        readOnly={
                          isReadOnly ||
                          user_id.toString() === details?.user_id?.toString()
                        }
                      />
                    </li>
                  </>
                )}
                {/**Add by Dhrvit Vaghasiya (20-08-2024) Nishant, Mitul and me **/}
                {Boolean(inputValues?.is_viewable_projects) &&
                  accessDetail.array_assigned_projects.length > 0 && (
                    <li>
                      <InlineField
                        label={"+ " + _t("Associated Projects")}
                        labelPlacement="left"
                        field={
                          <div className="flex items-center px-1.5">
                            <CustomCheckBox
                              className="gap-1.5"
                              name="enable_associated_projects"
                              checked={initialValues.enable_associated_projects}
                              onChange={handleChange}
                              disabled={
                                isReadOnly
                                  ? isReadOnly
                                  : enableAssociatedProject
                              }
                              loadingProps={{
                                isLoading: enableAssociatedProject,
                                className: "bg-[#ffffff]",
                              }}
                            />
                            <Tooltip
                              title={_t(
                                "When this box is checked, the user will only be able to see Assigned Projects as well as projects where they are listed within Project > Contacts."
                              )}
                              placement="top"
                            >
                              <FontAwesomeIcon
                                className="h-[15px] w-[15px] text-primary-900/50 hover:text-primary-900 cursor-pointer"
                                icon="fa-solid fa-circle-info"
                              />
                            </Tooltip>
                          </div>
                        }
                      />
                    </li>
                  )}
                <li>
                  <Typography className="text-xs text-[#D2322D] font-normal">
                    {_t(
                      "When you assign a user to a project, they ONLY see data for that project. If you or an employee needs to see ALL data, do not select any projects because this will prevent you from seeing newly created records."
                    )}
                  </Typography>
                </li>
                {details.type_key === dirTypeKeyByKey.employee && (
                  <>
                    <li className="h-px relative before:absolute before:left-0 before:bottom-0 before:w-full before:h-px before:bg-[linear-gradient(90deg,#3f4c653d_24%,#3f4c6500_100%)]"></li>
                    <li className="xl:col-span-2 flex sm:flex-row flex-col sm:items-center items-start gap-5 mt-2">
                      <CustomCheckBox
                        className="gap-1.5 !font-medium"
                        name="show_in_crew_schedule"
                        checked={
                          accessDetail?.show_in_crew_schedule == 1
                            ? true
                            : false
                        }
                        disabled={
                          isFidLoading.show_in_crew_schedule || isReadOnly
                        }
                        loadingProps={{
                          isLoading: isFidLoading.show_in_crew_schedule,
                          className: "bg-[#ffffff]",
                        }}
                        onChange={(e: CheckboxChangeEvent) => {
                          handleUpdateField({
                            show_in_crew_schedule: e.target?.checked
                              ? "1"
                              : "0",
                          });
                        }}
                      >
                        {_t("Show in Crew Schedule")}
                      </CustomCheckBox>
                      <div className="relative flex items-center gap-2 sm:before:absolute sm:before:w-0.5 sm:before:h-11 sm:before:-left-3 sm:before:top-1/2 sm:before:-translate-y-1/2 sm:before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)]">
                        <FieldLabel>{_t("Employee Tasks Color")}</FieldLabel>
                        <ColorPicker
                          size="small"
                          value={
                            !!dirColorVal
                              ? dirColorVal
                              : !!initialValues?.dir_color
                              ? initialValues?.dir_color
                              : "#000000"
                          }
                          disabled={isReadOnly}
                          disabledAlpha={true}
                          onChange={setDirColorVal}
                          onOpenChange={(isOpen) => {
                            if (
                              !isOpen &&
                              employeeTaskColor !== accessDetail?.dir_color
                            ) {
                              handleUpdateField({
                                dir_color: employeeTaskColor,
                              });
                            }
                          }}
                        />
                      </div>
                    </li>
                  </>
                )}
              </ul>
            </div>
          </div>
        }
      />

      {selectProject && (
        <SelectProject
          isSingleSelect={false}
          open={selectProject && !isReadOnly}
          setOpen={setSelectProject}
          selectedProjects={selectedProject}
          onProjectSelected={(data) => {
            setSelectedProject(data);
            setProjectSaved(true);
          }}
          isRequired={false}
        />
      )}
    </>
  );
};

export default AccessDetail;
