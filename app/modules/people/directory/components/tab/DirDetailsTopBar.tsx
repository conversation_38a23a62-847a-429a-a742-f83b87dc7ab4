import { useEffect, useMemo, useRef, useState } from "react";
import { useNavigate, useParams } from "@remix-run/react";
import isEmpty from "lodash/isEmpty";
// Atoms
import {
  SkeletonAvatar,
  SkeletonInput,
} from "~/shared/components/atoms/skeleton";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { InputField } from "~/shared/components/molecules/inputField";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import { IconButton } from "~/shared/components/molecules/iconButton";
import { ShareInternalLinkModal } from "~/shared/components/molecules/shareInternalLinkModal";
// Hooks
import { useCurrencyFormatter, useIframe, useTranslation } from "~/hook";
import { addCustomData } from "~/redux/action/customDataAction";
import { addCustomStatusDataAct } from "~/redux/slices/customStatusListDataSlice";
import CustomContextMenu from "~/shared/components/molecules/CustomContextMenu";
import { faArrowUpRightFromSquare } from "@fortawesome/pro-regular-svg-icons";

import {
  actionLeadMenuOptList,
  actionMenuOptList,
  addItemObject,
  dirTypeIds,
  dirTypeKeyByKey,
  dirTypeKeys,
  fieldStatus,
  leadOpportunityVideo,
} from "~/modules/people/directory/utils/constasnts";
import {
  useAppDispatch,
  useAppSelector,
} from "~/modules/people/directory/redux/store";
import {
  updateDirAddtDetail,
  updateDirDetail,
} from "~/modules/people/directory/redux/slices/dirDetailsSlice";
import {
  allowAccessApi,
  updateDirDetailApi,
} from "~/modules/people/directory/redux/action/dirDetailsAction";
import {
  archiveDirectoryApi,
  convertLeadToCustApi,
  convertVndToContrApi,
  deleteDirectoryApi,
  generateEstFromLeadApi,
} from "~/modules/people/directory/redux/action/driDashAction";

// TODO replace this with redux.
import { getGConfig, getGModuleByKey, getGUser, useGModules } from "~/zustand";

// lodash
import delay from "lodash/delay";
// antd
import type { CheckboxProps, InputRef } from "antd";
import {
  getStatusForField,
  getStatusActionForField,
  removeFirstSlash,
  validateSpecialAlphabeticInput,
  filterOptionBySubstring,
  validateSpecialAlphaNumericInput,
} from "~/shared/utils/helper/common";
import { routes } from "~/route-services/routes";
import DirSendEmail from "../DirSendEmail";
import { setCommonSidebarCollapse } from "~/zustand"; // In future this code move in redux, developer change this code
import { getUserAccessAndRolesField } from "../../utils/accessFields";
import { defaultConfig } from "~/data";
import {
  escapeHtmlEntities,
  fnum,
  formatAmount,
  replaceDOMParams,
  sanitizeString,
} from "~/helpers/helper";
import { customDataTypesByKey } from "~/utils/constasnts";
import { LoginDetails } from "./sidebar";
import { parseParamsFromURL } from "~/components/page/$url/helper";
import { getRoles } from "~/redux/action/getRolesAction";
import { sendMessageKeys } from "~/components/page/$url/data";
import { SelectField } from "~/shared/components/molecules/selectField";
import { setDataChanged } from "../../redux/slices/leadDashSlice";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";

const DirDetailsTopBar = ({
  selectedDirTab,
  isLoading,
  onReloadDirDetails,
  sidebarCollapse,
  moduleName,
}: IDirDetailsTopBarProps) => {
  const gConfig: GConfig = getGConfig();
  const { getGlobalModuleByKey } = useGlobalModule();
  const dirModule: GModule | undefined = getGModuleByKey(
    defaultConfig.directory_module
  );
  const { module_name }: GConfig = getGConfig();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { allow_delete_module_items = "0" } = user || {};
  const { tab }: RouteParams = useParams();
  const { _t } = useTranslation();
  const estimateModule: GModule | undefined = getGModuleByKey(
    defaultConfig.estimate_module
  );
  const leadModuleName: IModule | undefined = getGlobalModuleByKey(
    CFConfig.leads_module || "Leads"
  );
  const oppModuleName: IModule | undefined = getGlobalModuleByKey(
    CFConfig.opportunity_module || "Opportunities"
  );
  const navigate = useNavigate();
  const { checkModuleAccessByKey } = useGModules();
  const { formatter, currentCurrency } = useCurrencyFormatter();
  const [loginDetailsOpen, setLoginDetailsOpen] = useState<boolean>(false);

  const dispatch = useAppDispatch();
  const { details, loginDetail, isDetailLoading }: IDirInitialState =
    useAppSelector((state) => state.dirDetails);
  // after update in BE
  const { customStatusList }: ICustomStatusListInitialState = useAppSelector(
    (state) => state.customStatusListData
  );

  const { parentPostMessage } = useIframe();
  const flNameDivRef = useRef<HTMLDivElement>(null);
  const nameInputRef = useRef<InputRef>(null);
  const firstNameInputRef = useRef<InputRef>(null);
  const [inputValues, setInputValues] = useState<Partial<IDirDetails>>(details);
  const [selectedData, setSelectedData] = useState<Partial<IDirDetails>>({});
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [appAccess, setAppAccess] = useState<boolean>(false);
  const [delArchConfirmOpen, setDelArchConfirmOpen] = useState<string>("");
  const [isShareOpen, setIsShareOpen] = useState<boolean>(false);
  const [isConverting, setIsConverting] = useState<boolean>(false);
  const [convertConfirmOpen, setConvertConfirmOpen] = useState<string>("");
  const [isLoadingCrtEst, setIsLoadingCrtEst] = useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [shareLink, setShareLink] = useState<string>("");
  const loadingStatusRef = useRef(fieldStatus);
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);
  const [isFLNameVisible, setIsFLNameVisible] = useState<boolean>(false);
  const [customDataAdd, setCustomDataAdd] = useState<ICommonCustomDataFrm>({});
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [checkStatusLoading, setCheckStatusLoading] = useState<boolean>(true);
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    visible: boolean;
    data?: string;
  }>({ x: 0, y: 0, visible: false, data: undefined });
  const {
    first_name,
    last_name,
    type_key,
    type_name,
    app_access,
    login_info,
    status_name,
    company_name,
    stage,
  } = details;

  const isReadOnly = useMemo(
    () =>
      checkModuleAccessByKey(dirTypeKeys[details?.type_key || ""] || "") ===
      "read_only",
    [details?.type_key]
  );
  const hasEstimateAccessShowMessages = useMemo(() => {
    const mAccess = checkModuleAccessByKey(defaultConfig.estimate_module);
    return mAccess === "no_access" || mAccess === "read_only";
  }, []);
  const contractorAccessShowMessages = checkModuleAccessByKey(
    defaultConfig.contractor_module
  );
  const hasCustomerAccessTo =
    contractorAccessShowMessages !== "no_access" &&
    contractorAccessShowMessages !== "read_only";
  const initialValues: Partial<IDirDetails> = useMemo(
    () => ({
      first_name: first_name,
      last_name: last_name,
      type_key: type_key,
      type_name: type_name,
      app_access: app_access,
      login_info: login_info,
      company_name: company_name,
      stage: stage,
    }),
    [details]
  );

  const subContractAndLeadDropStatus = useMemo(() => {
    const accumulatedData = customStatusList
      ?.filter((data) => data.module_id == dirModule?.module_id)
      ?.map((item) => {
        return {
          label: HTMLEntities.decode(sanitizeString(item?.name)),
          value: item?.item_id?.toString(),
          key: item?.key?.toString(),
          sort_order: item?.sort_order,
          status_color: item?.status_color,
        };
      });

    const seenIds = new Set<string>();
    let newDataList = (accumulatedData ?? [])
      .sort((a, b) => {
        const orderA = a.sort_order === null ? Infinity : Number(a.sort_order);
        const orderB = b.sort_order === null ? Infinity : Number(b.sort_order);
        return orderA - orderB;
      })
      .filter((item) => {
        if (seenIds.has(item.value)) {
          return false; // Skip duplicates
        }
        seenIds.add(item.value);
        return true; // Include unique items
      }) as {
      label: string;
      value: string;
      key: string;
      sort_order: string;
      status_color: string;
    }[];

    if (details?.stage && details?.stage_is_deleted == 1) {
      newDataList = [
        ...newDataList,
        {
          label: `${details?.stage_name} (Archived)`,
          value: details.stage,
          key: details.stage,
          status_color: details?.stage_default_color,
        },
      ] as {
        label: string;
        value: string;
        key: string;
        sort_order: string;
        status_color: string;
      }[];
    }

    return { statusList: newDataList };
  }, [
    customStatusList,
    inputValues?.stage,
    gConfig?.module_id,
    details?.stage,
  ]);

  useEffect(() => {
    if (checkStatusLoading) {
      setInputValues(initialValues);
    }
  }, [initialValues, checkStatusLoading]);

  useEffect(() => {
    if (
      loadingStatus.length > 0 &&
      loadingStatus.some((item) => item.status === "loading")
    ) {
      setCheckStatusLoading(false);
    } else {
      setCheckStatusLoading(true);
    }
  }, [loadingStatus]);

  const dirDetailsOptions = useMemo(() => {
    const isArchived = status_name === "Archived";
    const isDeleteDisabled = allow_delete_module_items === "0";

    const contractorAccess = checkModuleAccessByKey(
      defaultConfig.contractor_module
    );
    const vendorAccess = checkModuleAccessByKey(defaultConfig.vendor_module);
    const estimateAccess = checkModuleAccessByKey(estimateModule?.module_key);
    const customerAccess = checkModuleAccessByKey(
      defaultConfig.customer_module
    );

    const hasEstimateAccess =
      estimateAccess !== "no_access" && estimateAccess !== "read_only";
    const hasContractorAccess =
      contractorAccess !== "no_access" && contractorAccess !== "read_only";
    const hasVendorAccess =
      vendorAccess !== "no_access" && vendorAccess !== "read_only";
    const hasCustomerAccess =
      customerAccess !== "no_access" && customerAccess !== "read_only";

    // Step 1: Filter based on archived/active
    let filteredOptions = actionMenuOptList.filter((item) =>
      isArchived ? item.key !== "archive" : item.key !== "active"
    );

    // Step 2: Apply `delete` disable logic
    const updatedOptions = filteredOptions.map((option) => ({
      ...option,
      ...(option.key === "delete" && { disabled: isDeleteDisabled }),
    }));

    // Step 3: Handle Contractor/Vendor convert options
    if (
      [dirTypeKeyByKey.contractor, dirTypeKeyByKey.vendor].includes(
        type_key || ""
      )
    ) {
      const isVendor = type_key === dirTypeKeyByKey.vendor;

      const convertOption = {
        label: isVendor ? "Convert to Contractor" : "Convert to Vendor",
        icon: isVendor
          ? "fa-regular fa-user-helmet-safety"
          : "fa-regular fa-person-digging",
        key: isVendor ? "convert_to_contractor" : "convert_to_vendor",
      };

      let resultOptions = [convertOption, ...updatedOptions];

      // Filter out based on permissions
      if (!hasContractorAccess) {
        resultOptions = resultOptions.filter(
          (item) => item.key !== "convert_to_contractor"
        );
      }
      if (!hasVendorAccess) {
        resultOptions = resultOptions.filter(
          (item) => item.key !== "convert_to_vendor"
        );
      }

      return resultOptions;
    }

    // Step 4: Handle Customer create_estimate option
    if (type_key === dirTypeKeyByKey.customer && hasEstimateAccess) {
      return [
        {
          label: `Create an ${estimateModule?.module_name || _t("Estimate")}`,
          icon: "fa-regular fa-file-user",
          key: "create_estimate",
        },
        ...updatedOptions,
      ];
    }

    // Step 5: Handle Lead options
    if (type_key === dirTypeKeyByKey.lead) {
      let leadOptions = actionLeadMenuOptList
        .filter((item) =>
          isArchived ? item.key !== "archive" : item.key !== "active"
        )
        .map((option) => ({
          ...option,
          ...(option.key === "create_estimate" && {
            label: `${_t("Create an")} ${
              estimateModule?.module_name || _t("Estimate")
            }`,
          }),
          ...(option.key === "lead_opportunity" && {
            label: `${leadModuleName?.plural_name} vs ${oppModuleName?.plural_name}`,
          }),
          ...(option.key === "delete" && { disabled: isDeleteDisabled }),
        }));

      if (!hasEstimateAccess) {
        leadOptions = leadOptions.filter(
          (item) => item.key !== "create_estimate"
        );
      }
      if (!hasCustomerAccess) {
        leadOptions = leadOptions.filter(
          (item) => item.key !== "convert_to_customer"
        );
      }

      return leadOptions;
    }

    // Step 6: Default return
    return updatedOptions;
  }, [
    estimateModule?.module_name,
    estimateModule?.module_key,
    type_key,
    status_name,
    allow_delete_module_items,
  ]);

  const handleInpOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setInputValues({ ...inputValues, [name]: value });
    dispatch(setDataChanged());
  };

  useEffect(() => {
    if (
      type_key === dirTypeKeyByKey.employee ||
      type_key === dirTypeKeyByKey.contractor
    ) {
      dispatch(getRoles({ isAccessModulesRequired: true }));
    }
  }, [type_key]);

  const handleAllowAccess = async (app_access: boolean) => {
    const allowAccessRes = (await allowAccessApi({
      directory_id: details?.user_id || "",
      module_id: gConfig.module_id,
      revise_app_access: 1,
      app_access,
      ...(app_access ? { cell: details.cell } : {}),
      ...(app_access ? { email: details.email } : {}),
    })) as IDirAllowAccessApiRes;
    if (allowAccessRes.success) {
      handleChangeFieldStatus({
        field: "app_access",
        status: "success",
        action: "API",
      });
      dispatch(updateDirDetail({ app_access: app_access ? 1 : 0 }));
      setInputValues({ ...inputValues, app_access: app_access ? 1 : 0 });
      setAppAccess(false);
    } else {
      handleChangeFieldStatus({
        field: "app_access",
        status: "error",
        action: "API",
      });
      dispatch(updateDirDetail({ app_access: details["app_access"] }));
      setInputValues({ ...inputValues, app_access: details["app_access"] });
      notification.error({
        description: allowAccessRes?.message,
      });
      setAppAccess(false);
    }
  };

  const onChange: CheckboxProps["onChange"] = (e) => {
    if (e.target.checked && (!loginDetail?.username || !loginDetail?.role_id)) {
      setLoginDetailsOpen(true);
    }
    if (e.target.checked) {
      if (!loginDetail?.username || !loginDetail?.role_id) {
        setInputValues({
          ...inputValues,
          app_access: e.target.checked ? 1 : 0,
        });
        dispatch(updateDirDetail({ app_access: e.target.checked ? 1 : 0 }));
      } else {
        setAppAccess(true);
        handleAllowAccess(e.target.checked);
      }
    } else {
      setAppAccess(true);
      handleAllowAccess(e.target.checked);
    }
  };

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleUpdateField = async (
    data: IDirDetailFieldsBoolean,
    isGo: boolean = false
  ) => {
    const field = Object.keys(data)[0] as keyof IDirDetails;

    if (
      isGo &&
      field === "last_name" &&
      data.last_name === details?.last_name &&
      inputValues.first_name === details?.first_name
    ) {
      handleChangeFieldStatus({
        field: "last_name",
        status: "button",
        action: "BLUR",
      });
      return false;
    }

    if (
      isFLNameVisible &&
      !isGo &&
      (field === "first_name" || field === "last_name")
    ) {
      return false;
    }

    let isError = false;
    let errorMsg = "";
    if (
      field === "first_name" &&
      data.first_name === "" &&
      type_key === dirTypeKeyByKey.employee
    ) {
      errorMsg = "First name and last name are both required";
      isError = true;
    }
    if (
      field === "last_name" &&
      data.last_name === "" &&
      type_key === dirTypeKeyByKey.employee
    ) {
      errorMsg = "First name and last name are both required";
      isError = true;
    }

    if (isError) {
      notification.error({
        description: errorMsg,
      });
      handleChangeFieldStatus({
        field: "emp_name",
        status: "error",
        action: "API",
      });
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({
        ...inputValues,
        ...{ first_name: details.first_name, last_name: details.last_name },
      });

      delay(() => {
        const fieldAction = getStatusActionForField(
          loadingStatusRef.current,
          field
        );
        if (field === "first_name" || field === "last_name") {
          handleChangeFieldStatus({
            field: "emp_name",
            status: "FOCUS" === fieldAction ? "save" : "button",
            action: fieldAction || "API",
          });
        }
        handleChangeFieldStatus({
          field: field,
          status: "FOCUS" === fieldAction ? "save" : "button",
          action: fieldAction || "API",
        });
      }, 3000);
      return false;
    }
    if (field === "first_name" || field === "last_name") {
      data = {
        first_name: data.first_name,
        last_name: inputValues.last_name || "",
      };
    }

    if (field === "last_name") {
      data = {
        first_name: inputValues.first_name || "",
        last_name: data.last_name,
      };
    }

    if (field === "first_name" || field === "last_name") {
      handleChangeFieldStatus({
        field: "emp_name",
        status: "loading",
        action: "API",
      });
    }
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });
    let newData = { ...data };
    if (field === "company_name") {
      newData.company_name = escapeHtmlEntities(
        (data?.company_name as string) || ""
      );
    }
    const updateRes = (await updateDirDetailApi({
      directory_id: details?.user_id || "",
      type: details?.type || "",
      module_id: dirModule?.module_id || 0,
      ...newData,
    })) as IDirDetailsUpdateApiRes;

    if (updateRes?.success) {
      if (field === "first_name" || field === "last_name") {
        handleChangeFieldStatus({
          field: "emp_name",
          status: "success",
          action: "API",
        });
      }
      if (
        field === "first_name" ||
        field === "last_name" ||
        field === "company_name"
      ) {
        dispatch(
          updateDirDetail({
            module_display_name: updateRes?.data?.module_display_name,
          })
        );
      }

      if (field === "company_name") {
        dispatch(
          updateDirAddtDetail({
            billed_to_display_name: updateRes?.data?.billed_to_display_name,
          })
        );
      }

      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });

      dispatch(updateDirDetail(data));
    } else {
      if (field === "first_name" || field === "last_name") {
        handleChangeFieldStatus({
          field: "emp_name",
          status: "error",
          action: "API",
        });
      }
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({ ...inputValues, [field]: details[field] });
      notification.error({
        description: updateRes?.message,
      });
    }
    setAppAccess(false);
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      if (field === "first_name" || field === "last_name") {
        handleChangeFieldStatus({
          field: "emp_name",
          status: "FOCUS" === fieldAction ? "save" : "button",
          action: fieldAction || "API",
        });
      }
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  // Actions
  const handleConvertDir = async () => {
    const type = convertConfirmOpen;
    if (!isConverting && selectedData?.user_id) {
      setIsConverting(true);
      let convertRes = { success: false, message: "Something went wrong" };
      if (type === "convert_to_customer") {
        convertRes = (await convertLeadToCustApi({
          paramsData: {
            directoryId: selectedData.user_id?.toString() || "",
          },
        })) as IConvertVndToContrRes;
      } else {
        let formDataObj = { sourceType: 0, targetType: 0 };
        if (type === "convert_to_vendor") {
          formDataObj = {
            sourceType: dirTypeIds.contractors,
            targetType: dirTypeIds.vendors,
          };
        } else {
          formDataObj = {
            sourceType: dirTypeIds.vendors,
            targetType: dirTypeIds.contractors,
          };
        }
        convertRes = (await convertVndToContrApi({
          formData: formDataObj,
          paramsData: {
            directoryId: selectedData.user_id?.toString() || "",
          },
        })) as IConvertVndToContrRes;
      }
      if (convertRes?.success) {
        onReloadDirDetails();
        onCloseConvertModal();
        navigate(
          `${routes.MANAGE_DIRECTORY.url}/${selectedData.user_id?.toString()}`
        );
      } else {
        notification.error({
          description: convertRes?.message,
        });
      }
      setIsConverting(false);
    }
  };

  const onCloseConvertModal = () => {
    setConvertConfirmOpen("");
    setSelectedData({});
  };

  const handleDirDelArch = async ({
    isAssocDel = false,
  }: IHandleDirDelArch) => {
    if (!isDeleting && selectedData?.user_id) {
      setIsDeleting(true);
      let deleteRes = { success: false, message: "Something went wrong" };
      if (delArchConfirmOpen === "archive") {
        deleteRes = (await archiveDirectoryApi({
          formData: {
            moduleKey: selectedData?.type_key
              ? dirTypeKeys[selectedData.type_key]
              : "",
            status: 1,
            moduleId: dirModule?.module_id || 0,
          },
          paramsData: {
            directoryId: selectedData.user_id?.toString() || "",
          },
        })) as IDeleteDirectoryRes;
      } else {
        deleteRes = (await deleteDirectoryApi({
          formData: {
            remove_associated_data: isAssocDel ? 1 : 0,
            module_key: selectedData?.type_key
              ? dirTypeKeys[selectedData.type_key]
              : "",
          },
          paramsData: {
            directoryId: selectedData.user_id?.toString() || "",
          },
        })) as IDeleteDirectoryRes;
      }

      if (deleteRes?.success) {
        if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
          parentPostMessage(sendMessageKeys?.modal_change, {
            open: false,
          });
        } else {
          navigate(
            moduleName === dirTypeKeyByKey.lead
              ? `${routes.MANAGE_LEADS.url}`
              : `${routes.MANAGE_DIRECTORY.url}`
          );
        }
      } else {
        notification.error({
          description: deleteRes?.message,
        });
      }
      setSelectedData({});
      setDelArchConfirmOpen("");
      setIsDeleting(false);
    }
  };

  const handleDirActive = async () => {
    if (!isDeleting && selectedData?.user_id) {
      setIsDeleting(true);
      const deleteRes = (await archiveDirectoryApi({
        formData: {
          moduleKey: selectedData?.type_key
            ? dirTypeKeys[selectedData.type_key]
            : "",
          status: 0,
          moduleId: dirModule?.module_id || 0,
        },
        paramsData: {
          directoryId: selectedData.user_id?.toString() || "",
        },
      })) as IDeleteDirectoryRes;

      if (deleteRes?.success) {
        if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
          parentPostMessage(sendMessageKeys?.modal_change, {
            open: false,
          });
        } else {
          navigate(
            moduleName === dirTypeKeyByKey.lead
              ? `${routes.MANAGE_LEADS.url}`
              : `${routes.MANAGE_DIRECTORY.url}`
          );
        }
      } else {
        notification.error({
          description: deleteRes?.message,
        });
      }
      setIsDeleting(false);
    }
  };

  const onCloseDelModal = () => {
    setSelectedData({});
    setDelArchConfirmOpen("");
  };

  const handleCreateEstimate = async (id: string, newTab = false) => {
    let errorMessage = "";
    if (type_key === dirTypeKeyByKey?.lead && !details?.opportunity_name) {
      errorMessage = "Project Name is required to create an estimate.";
    }
    if (errorMessage) {
      notification.error({
        description: _t(errorMessage),
      });
      return;
    }

    if (id) {
      if (window) {
        const currentUrl = window.location.href;
        const isIframeCall = currentUrl.includes("iframecall=1");
        const url = `${routes.MANAGE_ESTIMATE.url}?action=new&${type_key}_id=${id}`;
        isIframeCall || newTab ? window.open(url, "_blank") : navigate(url);
      } else {
        notification.error({
          description: "Something went wrong!",
        });
      }
    }

    // CU task: https://app.clickup.com/t/86cyt4e19
    // if (!isLoadingCrtEst && id) {
    //   setIsLoadingCrtEst(true);
    //   const createEstRes = (await generateEstFromLeadApi({
    //     paramsData: {
    //       directoryId: id,
    //     },
    //   })) as IGenerateEstFromLeadRes;
    //   if (createEstRes?.success) {
    //     if (window) {
    //       const currentUrl = window.location.href;
    //       const isIframeCall = currentUrl.includes("iframecall=1");
    //       const url = `${routes.MANAGE_ESTIMATE.url}?action=new&${type_key}_id=${id}`;
    //       isIframeCall ? window.open(url, "_blank") : navigate(url);
    //     } else {
    //       notification.error({
    //         description: "Something went wrong!",
    //       });
    //     }
    //   } else {
    //     notification.error({
    //       description: createEstRes?.message,
    //     });
    //   }
    //   setSelectedData({});
    //   setIsLoadingCrtEst(false);
    // }
  };

  // ? Show / Hide First Name Last Name filed
  const handleNameOutsideClick = (event: MouseEvent) => {
    const clickedElement = event.target as HTMLElement;

    if (
      flNameDivRef.current &&
      flNameDivRef.current.contains(clickedElement) &&
      clickedElement.tagName.toLowerCase() === "svg"
    ) {
      setIsFLNameVisible(false);
      return;
    }

    if (
      flNameDivRef.current &&
      !flNameDivRef.current.contains(event.target as Node)
    ) {
      setIsFLNameVisible(false);
    }
  };
  useEffect(() => {
    document.addEventListener("mousedown", handleNameOutsideClick);
    return () => {
      document.removeEventListener("mousedown", handleNameOutsideClick);
    };
  }, []);

  const { isNoAccessUserAccessAndRoleField } = getUserAccessAndRolesField();

  const handlekeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>,
    itemType: string
  ) => {
    if (event.key === "Enter") {
      const value = event?.currentTarget?.value?.trim();
      const newType = onEnterSelectSearchValue(
        event,
        customStatusList
          ?.filter((data) => data.module_id == dirModule?.module_id)
          ?.map((item) => {
            return {
              label: HTMLEntities.decode(sanitizeString(item?.name)),
              value: item?.item_id?.toString(),
            };
          }) || []
      );
      if (newType) {
        setCustomDataAdd({
          itemType,
          name: escapeHtmlEntities(newType),
        });
        setIsConfirmDialogOpen(true);
      } else if (value) {
        notification.error({
          description: "Records already exist, no new records were added.",
        });
      }
    }
  };

  const handleAddCustomData = async () => {
    if (!isAddingCustomData && customDataAdd?.name) {
      setIsAddingCustomData(true);

      const cDataRes = (await addCustomData({
        itemType: customDataAdd?.itemType,
        name: customDataAdd?.name,
      })) as ICustomDataAddUpRes;

      if (cDataRes?.success) {
        dispatch(
          addCustomStatusDataAct({
            ...cDataRes?.data,
            module_id: dirModule?.module_id || 0,
          })
        );

        let newData = {};
        if (
          customDataAdd?.itemType?.toString() ===
          customDataTypesByKey.leadStageKeyID.toString()
        ) {
          newData = { stage: cDataRes?.data?.item_id };
        }

        setInputValues({
          ...inputValues,
          ...newData,
        });
        handleUpdateField(newData);
        setIsConfirmDialogOpen(false);
      } else {
        notification.error({ description: cDataRes.message });
      }
      setIsAddingCustomData(false);
    }
  };

  const closeConfirmationModal = () => {
    setIsConfirmDialogOpen(false);
    setInputValues(initialValues);
  };

  const contextMenuItems = [
    {
      label: "Open in new tab",
      icon: faArrowUpRightFromSquare,
      onClick: async () => {
        if (contextMenu.data) {
          await handleCreateEstimate(contextMenu.data, true);
        }
      },
    },
  ];

  return (
    <>
      <div className="sticky top-0 z-[99] bg-[#F8F8F9] p-[15px] pb-0 mb-[15px]">
        <div className="flex items-center bg-white dark:bg-dark-800 py-[5px] px-3.5 shadow-[0_4px_24px_0] shadow-[#22292f1a] rounded-md">
          <div className="w-full flex flex-col-reverse xl:flex-row items-center justify-between gap-1">
            <div
              className={`flex justify-between items-center md:mt-0 mt-2.5 xl:w-fit w-full `}
            >
              <div className="flex items-center w-full">
                {isLoading ? (
                  <SkeletonAvatar className="!w-11 !h-11" />
                ) : (
                  <div
                    className="w-11 h-11 flex items-center justify-center rounded-full relative before:absolute before:w-[39px] before:h-[39px] before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full before:border-2 before:border-white"
                    style={{
                      background: `linear-gradient(${selectedDirTab?.dirTypeColors?.join(
                        ","
                      )})`,
                    }}
                  >
                    <FontAwesomeIcon
                      className="w-[18px] h-[18px] text-white"
                      icon={
                        selectedDirTab?.dirTypeIcon as IFontAwesomeIconProps["icon"]
                      }
                    />
                  </div>
                )}
                <div className="pl-1 w-[calc(100%-44px)]">
                  {isLoading ? (
                    <SkeletonInput className="!w-[200px] !h-[20px] pl-1.5" />
                  ) : (
                    <>
                      {type_key === dirTypeKeyByKey.lead ? (
                        <>
                          <div className="mb-0.5 flex gap-px pl-1.5 items-center">
                            <FieldLabel labelClass="!w-fit !text-base !font-medium min-w-fit !text-primary-900">
                              {_t("Primary Contact")}:
                            </FieldLabel>

                            <div
                              className={`flex gap-2 ${
                                !isFLNameVisible ? "hidden" : ""
                              }`}
                              ref={flNameDivRef}
                            >
                              <InputField
                                ref={firstNameInputRef}
                                value={inputValues?.first_name}
                                name="first_name"
                                labelPlacement="left"
                                placeholder={_t("First Name")}
                                editInline={true}
                                iconView={!isReadOnly}
                                readOnly={isReadOnly}
                                inputStatusClassName="!w-3.5 !h-3.5"
                                className="h-6 py-0 sm:max-w-[175px] max-w-full text-base font-medium"
                                readOnlyClassName="text-base h-6 font-medium whitespace-nowrap truncate sm:block flex"
                                formInputClassName="ellipsis-input-field"
                                labelClass="hidden"
                                fixStatus={getStatusForField(
                                  loadingStatus,
                                  "first_name"
                                )}
                                onChange={(e) => {
                                  const lastKey =
                                    e.currentTarget.dataset.lastKey;

                                  if (
                                    lastKey == "Backspace" ||
                                    lastKey == "Delete" ||
                                    validateSpecialAlphabeticInput(
                                      e?.target?.value,
                                      20
                                    )
                                  ) {
                                    handleInpOnChange(e);
                                  }
                                }}
                                onKeyDown={(e) => {
                                  e.currentTarget.dataset.lastKey = e.key; // Store key in dataset
                                }}
                                onMouseEnter={() => {
                                  handleChangeFieldStatus({
                                    field: "first_name",
                                    status: "edit",
                                    action: "ME",
                                  });
                                }}
                                onMouseLeaveDiv={() => {
                                  handleChangeFieldStatus({
                                    field: "first_name",
                                    status: "button",
                                    action: "ML",
                                  });
                                }}
                                onFocus={() => {
                                  handleChangeFieldStatus({
                                    field: "first_name",
                                    status: "save",
                                    action: "FOCUS",
                                  });
                                }}
                                onBlur={(e) => {
                                  const value = e?.target?.value.trim();
                                  if (value !== details?.first_name) {
                                    handleUpdateField({
                                      first_name: value.trim(),
                                    });
                                  } else {
                                    handleChangeFieldStatus({
                                      field: "first_name",
                                      status: "button",
                                      action: "BLUR",
                                    });
                                  }
                                }}
                              />
                              <InputField
                                value={inputValues?.last_name}
                                name="last_name"
                                labelPlacement="left"
                                placeholder={_t("Last Name")}
                                editInline={true}
                                iconView={!isReadOnly}
                                readOnly={isReadOnly}
                                inputStatusClassName="!w-3.5 !h-3.5"
                                className="h-6 py-0 sm:max-w-[175px] text-base font-medium max-w-full"
                                readOnlyClassName="text-base h-6 font-medium whitespace-nowrap truncate sm:block flex"
                                formInputClassName="ellipsis-input-field"
                                labelClass="hidden"
                                fixStatus={getStatusForField(
                                  loadingStatus,
                                  "last_name"
                                )}
                                onChange={(e) => {
                                  const lastKey =
                                    e.currentTarget.dataset.lastKey;
                                  if (
                                    lastKey == "Backspace" ||
                                    lastKey == "Delete" ||
                                    validateSpecialAlphabeticInput(
                                      e?.target?.value,
                                      20
                                    )
                                  ) {
                                    handleInpOnChange(e);
                                  }
                                }}
                                onKeyDown={(e) => {
                                  e.currentTarget.dataset.lastKey = e.key; // Store key in dataset
                                }}
                                onMouseEnter={() => {
                                  handleChangeFieldStatus({
                                    field: "last_name",
                                    status: "edit",
                                    action: "ME",
                                  });
                                }}
                                onMouseLeaveDiv={() => {
                                  handleChangeFieldStatus({
                                    field: "last_name",
                                    status: "button",
                                    action: "ML",
                                  });
                                }}
                                onFocus={() => {
                                  handleChangeFieldStatus({
                                    field: "last_name",
                                    status: "save",
                                    action: "FOCUS",
                                  });
                                }}
                                onBlur={(e) => {
                                  const value = e?.target?.value.trim();
                                  handleUpdateField(
                                    { last_name: value.trim() },
                                    true
                                  );
                                  setIsFLNameVisible(false);
                                }}
                              />
                            </div>

                            <div
                              className={`flex gap-2 ${
                                isReadOnly
                                  ? "w-fit"
                                  : "max-w-[calc(100%-105px)]"
                              } ${isFLNameVisible ? "hidden" : ""}`}
                            >
                              <Tooltip
                                title={
                                  inputValues?.last_name
                                    ? `${inputValues?.first_name} ${inputValues?.last_name}`
                                    : inputValues?.first_name
                                }
                                placement="topLeft"
                              >
                                <div>
                                  <InputField
                                    ref={nameInputRef}
                                    labelPlacement="left"
                                    placeholder={_t("Name")}
                                    labelClass="hidden"
                                    editInline={true}
                                    iconView={!isReadOnly}
                                    readOnly={isReadOnly}
                                    className="max-w-[350px] !text-base font-medium h-6 py-0"
                                    readOnlyClassName="text-base h-6 font-medium whitespace-nowrap truncate sm:block flex"
                                    inputStatusClassName="!w-3.5 !h-3.5"
                                    formInputClassName={`items-start ellipsis-input-field ${
                                      isReadOnly
                                        ? "w-fit"
                                        : "sm:w-[350px] w-auto"
                                    }`}
                                    onChange={() => {}}
                                    onFocus={() => {
                                      if (isReadOnly) {
                                        return;
                                      }
                                      setIsFLNameVisible(true);
                                      delay(() => {
                                        firstNameInputRef.current?.focus();
                                      }, 50);
                                    }}
                                    value={
                                      inputValues?.last_name
                                        ? `${inputValues?.first_name} ${inputValues?.last_name}`
                                        : inputValues?.first_name
                                    }
                                    fixStatus={getStatusForField(
                                      loadingStatus,
                                      "emp_name"
                                    )}
                                    disabled={
                                      getStatusForField(
                                        loadingStatus,
                                        "emp_name"
                                      ) === "loading"
                                    }
                                    onMouseEnter={() => {
                                      handleChangeFieldStatus({
                                        field: "emp_name",
                                        status: "edit",
                                        action: "ME",
                                      });
                                    }}
                                    onMouseLeaveDiv={() => {
                                      handleChangeFieldStatus({
                                        field: "emp_name",
                                        status: "button",
                                        action: "ML",
                                      });
                                    }}
                                  />
                                </div>
                              </Tooltip>
                            </div>
                          </div>
                          <div className="flex gap-px pl-1.5 items-center">
                            <FieldLabel labelClass="!w-fit !font-normal min-w-fit !text-primary-900 pb-px">
                              {`${type_name || ""}:`}
                            </FieldLabel>
                            <Tooltip
                              title={inputValues?.company_name?.trim()}
                              placement="topLeft"
                            >
                              <InputField
                                placeholder={_t("Company")}
                                labelPlacement="left"
                                className="h-5 py-0.5"
                                formInputClassName="ellipsis-input-field"
                                inputStatusClassName="!w-3.5 !h-3.5"
                                fieldClassName="2xl:w-[430px] xl:w-[380px] w-full"
                                readOnlyClassName="text-base h-5 whitespace-nowrap truncate sm:block flex"
                                editInline={true}
                                iconView={true}
                                readOnly={isReadOnly}
                                name="company_name"
                                maxLength={150}
                                value={inputValues?.company_name}
                                onChange={(e) => {
                                  const lastKey =
                                    e.currentTarget.dataset.lastKey;
                                  if (
                                    lastKey == "Backspace" ||
                                    lastKey == "Delete" ||
                                    validateSpecialAlphaNumericInput(
                                      e?.target?.value,
                                      150
                                    )
                                  ) {
                                    handleInpOnChange(e);
                                  }
                                }}
                                onKeyDown={(e) => {
                                  e.currentTarget.dataset.lastKey = e.key; // Store key in dataset
                                }}
                                fixStatus={getStatusForField(
                                  loadingStatus,
                                  "company_name"
                                )}
                                statusIconClassName="flex"
                                onMouseEnter={() => {
                                  handleChangeFieldStatus({
                                    field: "company_name",
                                    status: "edit",
                                    action: "ME",
                                  });
                                }}
                                onMouseLeaveDiv={() => {
                                  handleChangeFieldStatus({
                                    field: "company_name",
                                    status: "button",
                                    action: "ML",
                                  });
                                }}
                                onFocus={() =>
                                  handleChangeFieldStatus({
                                    field: "company_name",
                                    status: "save",
                                    action: "FOCUS",
                                  })
                                }
                                onBlur={(e) => {
                                  const value = e?.target?.value
                                    ?.replace(/\s+/g, " ")
                                    ?.trim();
                                  if (value !== details?.company_name) {
                                    handleUpdateField({
                                      company_name: value,
                                    });
                                  } else {
                                    handleChangeFieldStatus({
                                      field: "company_name",
                                      status: "button",
                                      action: "BLUR",
                                    });
                                    setInputValues({
                                      ...inputValues,
                                      company_name: details.company_name
                                        ?.replace(/\s+/g, " ")
                                        ?.trim(),
                                    });
                                  }
                                }}
                              />
                            </Tooltip>
                          </div>
                        </>
                      ) : (
                        <>
                          {type_key !== dirTypeKeyByKey.employee && (
                            <div className="mb-0.5 flex gap-px pl-1.5 items-center">
                              <FieldLabel labelClass="!w-fit !text-base !font-normal min-w-fit !text-primary-900 pb-px">
                                {`${type_name || ""}:`}
                              </FieldLabel>
                              <Tooltip
                                title={inputValues?.company_name?.trim()}
                                placement="topLeft"
                              >
                                <InputField
                                  placeholder={_t("Company")}
                                  labelPlacement="left"
                                  className="h-6 py-0.5 !text-base font-medium"
                                  formInputClassName="ellipsis-input-field"
                                  inputStatusClassName="!w-3.5 !h-3.5"
                                  fieldClassName="2xl:w-[430px] xl:w-[380px] w-full"
                                  readOnlyClassName="text-base h-6 font-medium whitespace-nowrap truncate sm:block flex"
                                  editInline={true}
                                  iconView={true}
                                  readOnly={isReadOnly}
                                  name="company_name"
                                  maxLength={150}
                                  value={inputValues?.company_name}
                                  onChange={(e) => {
                                    const lastKey =
                                      e.currentTarget.dataset.lastKey;
                                    if (
                                      lastKey == "Backspace" ||
                                      lastKey == "Delete" ||
                                      validateSpecialAlphaNumericInput(
                                        e?.target?.value,
                                        150
                                      )
                                    ) {
                                      handleInpOnChange(e);
                                    }
                                  }}
                                  onKeyDown={(e) => {
                                    e.currentTarget.dataset.lastKey = e.key; // Store key in dataset
                                  }}
                                  fixStatus={getStatusForField(
                                    loadingStatus,
                                    "company_name"
                                  )}
                                  statusIconClassName="flex"
                                  onMouseEnter={() => {
                                    handleChangeFieldStatus({
                                      field: "company_name",
                                      status: "edit",
                                      action: "ME",
                                    });
                                  }}
                                  onMouseLeaveDiv={() => {
                                    handleChangeFieldStatus({
                                      field: "company_name",
                                      status: "button",
                                      action: "ML",
                                    });
                                  }}
                                  onFocus={() =>
                                    handleChangeFieldStatus({
                                      field: "company_name",
                                      status: "save",
                                      action: "FOCUS",
                                    })
                                  }
                                  onBlur={(e) => {
                                    const value = e?.target?.value.trim();
                                    if (value !== details?.company_name) {
                                      handleUpdateField({
                                        company_name: value,
                                      });
                                    } else {
                                      handleChangeFieldStatus({
                                        field: "company_name",
                                        status: "button",
                                        action: "BLUR",
                                      });
                                      setInputValues({
                                        ...inputValues,
                                        company_name: details.company_name,
                                      });
                                    }
                                  }}
                                />
                              </Tooltip>
                            </div>
                          )}

                          <div className="flex gap-px pl-1.5 items-center">
                            <FieldLabel labelClass="!w-fit !font-normal min-w-fit !text-primary-900">
                              {_t("Primary Contact")}:
                            </FieldLabel>

                            <div
                              className={`flex gap-2 ${
                                !isFLNameVisible ? "hidden" : ""
                              }`}
                              ref={flNameDivRef}
                            >
                              <InputField
                                ref={firstNameInputRef}
                                value={inputValues?.first_name}
                                name="first_name"
                                labelPlacement="left"
                                placeholder={_t("First Name")}
                                editInline={true}
                                iconView={!isReadOnly}
                                readOnly={isReadOnly}
                                inputStatusClassName="!w-3.5 !h-3.5"
                                className="h-5 py-0 sm:max-w-[175px] max-w-full"
                                readOnlyClassName="text-base h-5 font-medium whitespace-nowrap truncate sm:block flex"
                                formInputClassName="ellipsis-input-field"
                                labelClass="hidden"
                                fixStatus={getStatusForField(
                                  loadingStatus,
                                  "first_name"
                                )}
                                onChange={(e) => {
                                  const lastKey =
                                    e.currentTarget.dataset.lastKey;

                                  if (
                                    lastKey == "Backspace" ||
                                    lastKey == "Delete" ||
                                    validateSpecialAlphabeticInput(
                                      e?.target?.value,
                                      20
                                    )
                                  ) {
                                    handleInpOnChange(e);
                                  }
                                }}
                                onKeyDown={(e) => {
                                  e.currentTarget.dataset.lastKey = e.key; // Store key in dataset
                                }}
                                onMouseEnter={() => {
                                  handleChangeFieldStatus({
                                    field: "first_name",
                                    status: "edit",
                                    action: "ME",
                                  });
                                }}
                                onMouseLeaveDiv={() => {
                                  handleChangeFieldStatus({
                                    field: "first_name",
                                    status: "button",
                                    action: "ML",
                                  });
                                }}
                                onFocus={() => {
                                  handleChangeFieldStatus({
                                    field: "first_name",
                                    status: "save",
                                    action: "FOCUS",
                                  });
                                }}
                                onBlur={(e) => {
                                  const value = e?.target?.value.trim();
                                  if (value !== details?.first_name) {
                                    handleUpdateField({
                                      first_name: value.trim(),
                                    });
                                  } else {
                                    handleChangeFieldStatus({
                                      field: "first_name",
                                      status: "button",
                                      action: "BLUR",
                                    });
                                  }
                                }}
                              />
                              <InputField
                                value={inputValues?.last_name}
                                name="last_name"
                                labelPlacement="left"
                                placeholder={_t("Last Name")}
                                editInline={true}
                                iconView={!isReadOnly}
                                readOnly={isReadOnly}
                                inputStatusClassName="!w-3.5 !h-3.5"
                                className="h-5 py-0 sm:max-w-[175px] max-w-full"
                                readOnlyClassName="text-base h-5 font-medium whitespace-nowrap truncate sm:block flex"
                                formInputClassName="ellipsis-input-field"
                                labelClass="hidden"
                                fixStatus={getStatusForField(
                                  loadingStatus,
                                  "last_name"
                                )}
                                onChange={(e) => {
                                  const lastKey =
                                    e.currentTarget.dataset.lastKey;
                                  if (
                                    lastKey == "Backspace" ||
                                    lastKey == "Delete" ||
                                    validateSpecialAlphabeticInput(
                                      e?.target?.value,
                                      20
                                    )
                                  ) {
                                    handleInpOnChange(e);
                                  }
                                }}
                                onKeyDown={(e) => {
                                  e.currentTarget.dataset.lastKey = e.key; // Store key in dataset
                                }}
                                onMouseEnter={() => {
                                  handleChangeFieldStatus({
                                    field: "last_name",
                                    status: "edit",
                                    action: "ME",
                                  });
                                }}
                                onMouseLeaveDiv={() => {
                                  handleChangeFieldStatus({
                                    field: "last_name",
                                    status: "button",
                                    action: "ML",
                                  });
                                }}
                                onFocus={() => {
                                  handleChangeFieldStatus({
                                    field: "last_name",
                                    status: "save",
                                    action: "FOCUS",
                                  });
                                }}
                                onBlur={(e) => {
                                  const value = e?.target?.value.trim();
                                  handleUpdateField(
                                    { last_name: value.trim() },
                                    true
                                  );
                                  setIsFLNameVisible(false);
                                }}
                              />
                            </div>

                            <div
                              className={`flex gap-2 ${
                                isReadOnly
                                  ? "w-fit"
                                  : "max-w-[calc(100%-105px)]"
                              } ${isFLNameVisible ? "hidden" : ""}`}
                            >
                              <Tooltip
                                title={
                                  inputValues?.last_name?.trim()
                                    ? `${inputValues?.first_name} ${inputValues?.last_name}`
                                    : inputValues?.first_name?.trim()
                                }
                                placement="topLeft"
                              >
                                <div>
                                  <InputField
                                    ref={nameInputRef}
                                    labelPlacement="left"
                                    placeholder={_t("Name")}
                                    labelClass="hidden"
                                    editInline={true}
                                    iconView={!isReadOnly}
                                    readOnly={isReadOnly}
                                    className="max-w-[350px] h-5 py-0"
                                    readOnlyClassName="text-base h-5 font-medium whitespace-nowrap truncate sm:block flex"
                                    inputStatusClassName="!w-3.5 !h-3.5"
                                    formInputClassName={`items-start ellipsis-input-field ${
                                      isReadOnly
                                        ? "w-fit"
                                        : "sm:w-[350px] w-auto"
                                    }`}
                                    onChange={() => {}}
                                    onFocus={() => {
                                      if (isReadOnly) {
                                        return;
                                      }
                                      setIsFLNameVisible(true);
                                      delay(() => {
                                        firstNameInputRef.current?.focus();
                                      }, 50);
                                    }}
                                    value={
                                      inputValues?.last_name
                                        ? `${inputValues?.first_name} ${inputValues?.last_name}`
                                        : inputValues?.first_name
                                    }
                                    fixStatus={getStatusForField(
                                      loadingStatus,
                                      "emp_name"
                                    )}
                                    disabled={
                                      getStatusForField(
                                        loadingStatus,
                                        "emp_name"
                                      ) === "loading"
                                    }
                                    onMouseEnter={() => {
                                      handleChangeFieldStatus({
                                        field: "emp_name",
                                        status: "edit",
                                        action: "ME",
                                      });
                                    }}
                                    onMouseLeaveDiv={() => {
                                      handleChangeFieldStatus({
                                        field: "emp_name",
                                        status: "button",
                                        action: "ML",
                                      });
                                    }}
                                  />
                                </div>
                              </Tooltip>
                            </div>
                          </div>
                        </>
                      )}

                      {(type_key === dirTypeKeyByKey.customer ||
                        type_key === dirTypeKeyByKey.lead) && (
                        <div className="pl-1.5 flex gap-1 mt-1">
                          <Tooltip
                            title={
                              subContractAndLeadDropStatus?.statusList?.find(
                                (stageItem) =>
                                  stageItem.key == inputValues.stage
                              )?.label
                            }
                          >
                            <SelectField
                              labelPlacement="left"
                              placeholder={_t("Select Stage")}
                              formInputClassName="w-fit overflow-visible"
                              containerClassName="overflow-visible"
                              fieldClassName="before:hidden w-fit"
                              className="h-[21px] directory-stage header-select-status-dropdown !rounded"
                              popupClassName="min-w-[260px]"
                              readOnly={isReadOnly}
                              showSearch
                              title=""
                              options={subContractAndLeadDropStatus?.statusList}
                              optionRender={(oriOption) => {
                                const option =
                                  subContractAndLeadDropStatus?.statusList?.find(
                                    (item) => item.value === oriOption.value
                                  );
                                return (
                                  <div className="flex items-center gap-2">
                                    <FontAwesomeIcon
                                      icon="fa-solid fa-square"
                                      className="h-3.5 w-3.5"
                                      style={{
                                        color: option?.status_color,
                                      }}
                                    />
                                    <span>{option?.label}</span>
                                  </div>
                                );
                              }}
                              addItem={addItemObject}
                              allowClear={type_key === dirTypeKeyByKey.customer}
                              onClear={() => {
                                handleChangeFieldStatus({
                                  field: "stage",
                                  status: "loading",
                                  action: "API",
                                });
                                handleUpdateField({
                                  stage: "",
                                });
                              }}
                              value={
                                !!inputValues?.stage
                                  ? subContractAndLeadDropStatus?.statusList?.find(
                                      (stageItem) =>
                                        stageItem.key == inputValues.stage
                                    )
                                  : null
                              }
                              filterOption={(input, option) =>
                                filterOptionBySubstring(
                                  input,
                                  option?.label
                                    ?.toString()
                                    ?.toLowerCase() as string
                                )
                              }
                              onChange={(value: string | string[], event) => {
                                const newValue =
                                  typeof value === "string" ? value : value[0];
                                setInputValues({
                                  ...inputValues,
                                  ...{ stage: newValue },
                                });
                                handleChangeFieldStatus({
                                  field: "stage",
                                  status: "loading",
                                  action: "API",
                                });
                                const reqStage = customStatusList?.find(
                                  (data) => data.item_id.toString() == newValue
                                )?.key;
                                if (!!reqStage) {
                                  handleUpdateField({
                                    stage: reqStage,
                                  });
                                }
                                const activeElement =
                                  document.activeElement as HTMLElement;
                                if (activeElement) {
                                  activeElement.blur();
                                }
                                dispatch(setDataChanged());
                              }}
                              onInputKeyDown={(e) => {
                                handlekeyDown(
                                  e,
                                  customDataTypesByKey.leadStageKeyID.toString()
                                );
                              }}
                              style={{
                                backgroundColor:
                                  !!subContractAndLeadDropStatus?.statusList?.find(
                                    (item) => item.key == inputValues?.stage
                                  )?.status_color
                                    ? `${
                                        subContractAndLeadDropStatus?.statusList?.find(
                                          (item) =>
                                            item.key == inputValues?.stage
                                        )?.status_color
                                      }1d`
                                    : "#2235581d",
                                color:
                                  !!subContractAndLeadDropStatus?.statusList?.find(
                                    (item) => item.key == inputValues?.stage
                                  )?.status_color
                                    ? `${
                                        subContractAndLeadDropStatus?.statusList?.find(
                                          (item) =>
                                            item.key == inputValues?.stage
                                        )?.status_color
                                      }`
                                    : "#223558",
                              }}
                            />
                          </Tooltip>

                          <FieldStatus
                            className="flex items-center"
                            iconProps={{
                              className: "!w-[15px] !h-[15px]",
                            }}
                            status={getStatusForField(loadingStatus, "stage")}
                          />
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
            <div className="md:justify-end justify-between xl:w-fit w-full flex flex-col xl:flex-row">
              <div
                className={`flex items-end md:gap-0 sm:gap-2.5 gap-2 ${
                  (type_key === dirTypeKeyByKey.contractor ||
                    type_key === dirTypeKeyByKey.customer ||
                    type_key === dirTypeKeyByKey.vendor) &&
                  tab === "documents" &&
                  !isDetailLoading
                    ? "justify-between"
                    : "md:justify-end justify-between"
                }`}
              >
                {!window.ENV.PAGE_IS_IFRAME && (
                  <div className="flex gap-2.5">
                    <div
                      className="flex items-center cursor-pointer md:!hidden"
                      onClick={() => {
                        const params: Partial<IframeRouteParams> =
                          parseParamsFromURL(window?.location?.pathname);
                        if (params?.page && params?.id) {
                          navigate("/" + params?.page);
                        }
                      }}
                    >
                      <IconButton
                        htmlType="button"
                        variant="default"
                        className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                        iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                        icon="fa-regular fa-chevron-left"
                      />
                    </div>
                    <div>
                      <IconButton
                        htmlType="button"
                        variant="default"
                        className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                        iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                        icon="fa-regular fa-bars"
                        onClick={() =>
                          setCommonSidebarCollapse(!sidebarCollapse)
                        }
                      />
                    </div>
                  </div>
                )}
                <div className="flex">
                  {(type_key === dirTypeKeyByKey.contractor ||
                    type_key === dirTypeKeyByKey.customer ||
                    type_key === dirTypeKeyByKey.vendor) &&
                    tab === "documents" &&
                    !isDetailLoading && (
                      <div className="sm:flex hidden items-end justify-between ">
                        <div className="relative first:!pl-0 2xl:px-3 py-1.5 sm:px-2 flex gap-1 first:before:hidden sm:before:absolute sm:before:w-0.5 sm:before:h-full sm:before:left-0 sm:before:top-0 sm:before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)]">
                          <Typography className="text-sm text-primary-900  dark:text-white/90 font-semibold">
                            {_t("Total")}:
                          </Typography>
                          <Tooltip
                            title={
                              type_key === dirTypeKeyByKey.customer
                                ? formatter(
                                    Number(
                                      formatAmount(
                                        details?.cust_invoice_owed ?? 0
                                      )
                                    )
                                  )
                                : formatter(
                                    Number(
                                      formatAmount(
                                        details?.vendor_bill_owed ?? 0
                                      )
                                    )
                                  )
                            }
                          >
                            <Typography className="text-sm text-primary-900  dark:text-white/90 font-normal">
                              {type_key === dirTypeKeyByKey.customer
                                ? `${currentCurrency()}${
                                    Number(details?.cust_invoice_owed ?? 0) ===
                                    0
                                      ? "0.00"
                                      : fnum(Number(details?.cust_invoice_owed))
                                  }`
                                : `${currentCurrency()}${
                                    Number(details?.vendor_bill_owed ?? 0) === 0
                                      ? "0.00"
                                      : fnum(Number(details?.vendor_bill_owed))
                                  }`}
                            </Typography>
                          </Tooltip>
                        </div>
                        <div className="relative first:!pl-0 2xl:px-3 py-1.5 sm:px-2 flex gap-1 first:before:hidden sm:before:absolute sm:before:w-0.5 sm:before:h-full sm:before:left-0 sm:before:top-0 sm:before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)]">
                          <Typography className="text-sm text-primary-900  dark:text-white/90 font-semibold">
                            {_t("Paid")}:
                          </Typography>
                          <Tooltip
                            title={
                              type_key === dirTypeKeyByKey.customer
                                ? formatter(
                                    Number(
                                      formatAmount(
                                        details?.cust_invoice_payment_paid ?? 0
                                      )
                                    )
                                  )
                                : formatter(
                                    Number(
                                      formatAmount(
                                        details?.vendor_bill_payment ?? 0
                                      )
                                    )
                                  )
                            }
                          >
                            <Typography className="text-sm text-primary-900  dark:text-white/90 font-normal">
                              {type_key === dirTypeKeyByKey.customer
                                ? `${currentCurrency()}${
                                    Number(
                                      details?.cust_invoice_payment_paid ?? 0
                                    ) === 0
                                      ? "0.00"
                                      : fnum(
                                          Number(
                                            details?.cust_invoice_payment_paid ??
                                              0
                                          )
                                        )
                                  }`
                                : `${currentCurrency()}${
                                    Number(
                                      details?.vendor_bill_payment ?? 0
                                    ) === 0
                                      ? "0.00"
                                      : fnum(
                                          Number(
                                            details?.vendor_bill_payment ?? 0
                                          )
                                        )
                                  }`}
                            </Typography>
                          </Tooltip>
                        </div>
                        <div className="relative first:!pl-0 2xl:px-3 py-1.5 sm:px-2 flex gap-1 after:absolute xl:after:w-0.5 xl:after:h-full xl:after:right-0 xl:after:top-0 xl:after:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)] first:before:hidden sm:before:absolute sm:before:w-0.5 sm:before:h-full sm:before:left-0 sm:before:top-0 sm:before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)]">
                          <Typography className="text-sm text-primary-900  dark:text-white/90 font-semibold">
                            {_t("Balance")}:
                          </Typography>
                          <Tooltip
                            title={
                              type_key === dirTypeKeyByKey.customer
                                ? formatter(
                                    Number(
                                      formatAmount(
                                        details?.cust_invoice_payment_balance ??
                                          0
                                      )
                                    )
                                  )
                                : formatter(
                                    Number(
                                      formatAmount(
                                        details?.vendor_bill_owed ?? 0
                                      )
                                    ) -
                                      Number(
                                        formatAmount(
                                          details?.vendor_bill_payment ?? 0
                                        )
                                      )
                                  )
                            }
                          >
                            <Typography className="text-sm text-primary-900  dark:text-white/90 font-normal">
                              {type_key === dirTypeKeyByKey.customer
                                ? `${currentCurrency()}${
                                    Number(
                                      details?.cust_invoice_payment_balance ?? 0
                                    ) === 0
                                      ? "0.00"
                                      : fnum(
                                          Number(
                                            details?.cust_invoice_payment_balance ??
                                              0
                                          )
                                        )
                                  }`
                                : `${currentCurrency()}${
                                    Number(details?.vendor_bill_owed ?? 0) -
                                      Number(
                                        details?.vendor_bill_payment ?? 0
                                      ) ===
                                    0
                                      ? "0.00"
                                      : fnum(
                                          Number(
                                            details?.vendor_bill_owed ?? 0
                                          ) -
                                            Number(
                                              details?.vendor_bill_payment ?? 0
                                            )
                                        )
                                  }`}
                            </Typography>
                          </Tooltip>
                        </div>
                      </div>
                    )}
                  {(type_key === dirTypeKeyByKey.employee ||
                    type_key === dirTypeKeyByKey.contractor) &&
                    (!tab || tab === "details") && (
                      <ul className="flex md:flex-row flex-col sm:gap-2.5 items-start pl-2">
                        {!isEmpty(login_info) && (
                          <li className="md:flex gap-1 sm:rounded sm:!shadow-[0px_1px_3px] sm:!shadow-primary-200 sm:px-3 sm:py-[7px] hidden">
                            <Typography className="text-sm font-semibold">
                              {_t("App Version")}:
                            </Typography>
                            <Typography className="text-sm font-normal text-primary-900">
                              {!!login_info
                                ? `${JSON.parse(login_info).version} ${
                                    JSON.parse(login_info).from == "ios"
                                      ? " (iOS)"
                                      : ""
                                  }${
                                    JSON.parse(login_info).from == "android"
                                      ? " (Android)"
                                      : ""
                                  }`
                                : " - "}
                            </Typography>
                          </li>
                        )}
                        {!isNoAccessUserAccessAndRoleField && (
                          <li>
                            <div className="group/module-menu sm:px-3 py-2 h-[34px] rounded sm:!shadow-[0px_1px_3px] sm:!shadow-primary-200 whitespace-nowrap">
                              <CustomCheckBox
                                name="app_access"
                                className="gap-1.5 !text-deep-orange-500 orange-checkBox checkBox-Font600 checkbox-border sm:text-sm text-xs"
                                children={_t("Allow Login Access")}
                                checked={inputValues?.app_access == 1}
                                onChange={onChange}
                                disabled={appAccess || isReadOnly}
                                loadingProps={{
                                  isLoading: appAccess,
                                  className: "bg-[#ffffff]",
                                }}
                              />
                            </div>
                          </li>
                        )}
                      </ul>
                    )}
                  <ul className="flex items-center pl-2 justify-end gap-2.5">
                    <li>
                      <ButtonWithTooltip
                        tooltipTitle={_t("Refresh")}
                        tooltipPlacement="top"
                        icon="fa-regular fa-arrow-rotate-right"
                        iconClassName={`!text-primary-900 ${
                          isLoading
                            ? "group-hover/buttonHover:!text-primary-900 fa-spin"
                            : "group-hover/buttonHover:!text-deep-orange-500"
                        }`}
                        className={`!w-[34px] !h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 ${
                          isLoading
                            ? "hover:bg-transparent"
                            : "hover:!bg-deep-orange-500/5"
                        }`}
                        disabled={isLoading}
                        onClick={() => {
                          onReloadDirDetails();
                        }}
                      />
                    </li>
                    <li>
                      {!isReadOnly && !isLoading && (
                        <DropdownMenu
                          options={
                            dirDetailsOptions?.length > 0
                              ? dirDetailsOptions.map((option) => ({
                                  ...option,
                                  onClick: (e: { domEvent: any }) => {
                                    setSelectedData(details);
                                    if (option.key === "delete") {
                                      if (app_access == 1) {
                                        notification.error({
                                          description: `${type_name} can not be deleted while they are assigned as a User. Instead, remove App Access and then try again.`,
                                        });
                                        setSelectedData({});
                                        return;
                                      }
                                      setDelArchConfirmOpen("delete");
                                    } else if (option.key === "archive") {
                                      setDelArchConfirmOpen("archive");
                                    } else if (option.key === "active") {
                                      setDelArchConfirmOpen("active");
                                    } else if (option.key === "share") {
                                      setIsShareOpen(true);
                                    } else if (
                                      option.key === "convert_to_vendor" ||
                                      option.key === "convert_to_contractor" ||
                                      option.key === "convert_to_customer"
                                    ) {
                                      setConvertConfirmOpen(option.key);
                                    } else if (
                                      option.key === "create_estimate"
                                    ) {
                                      const domEvent = e.domEvent;
                                      const isNewTab =
                                        Number(window.ENV.ENABLE_ALL_CLICK) &&
                                        (domEvent?.ctrlKey ||
                                          domEvent?.metaKey);
                                      // if (isNewTab) {
                                      //   const url = `${routes.MANAGE_ESTIMATE.url}?action=new&${type_key}_id=${details?.user_id}`;
                                      //   window.open(url, "_blank");
                                      // } else {
                                      handleCreateEstimate(
                                        details?.user_id?.toString() || "",
                                        isNewTab
                                      );
                                      // }
                                    } else {
                                      window.open(
                                        `${leadOpportunityVideo}`,
                                        "_blank"
                                      );
                                    }
                                  },
                                  ...(Number(window.ENV.ENABLE_ALL_CLICK) && {
                                    onAuxClick: (e: React.MouseEvent) => {
                                      if (e.button === 1) {
                                        // Middle mouse button
                                        handleCreateEstimate(
                                          details?.user_id?.toString() || "",
                                          true
                                        );
                                      }
                                    },
                                    onContextMenu: (e: React.MouseEvent) => {
                                      e.preventDefault();
                                      setContextMenu({
                                        x: e.clientX,
                                        y: e.clientY,
                                        visible: true,
                                        data: details?.user_id?.toString(),
                                      });
                                    },
                                  }),
                                }))
                              : []
                          }
                          iconClassName={`text-primary-900 group-hover/buttonHover:text-deep-orange-500 ${
                            isLoadingCrtEst && "fa-spin"
                          }`}
                          buttonClass="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !rounded !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          icon={
                            isLoadingCrtEst
                              ? "fa-regular fa-spinner-third"
                              : "fa-regular fa-ellipsis-vertical"
                          }
                          disabledIcon={isLoadingCrtEst}
                          tooltipcontent={_t("More")}
                          {...((hasEstimateAccessShowMessages ||
                            !hasCustomerAccessTo ||
                            user?.allow_delete_module_items === "0") && {
                            footerText: _t(
                              "Some actions might be unavailable depending on your privilege."
                            ),
                          })}
                          disabled={isLoading}
                        />
                      )}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* // Need to responsive screen */}

      {(type_key === dirTypeKeyByKey.employee ||
        type_key === dirTypeKeyByKey.contractor) &&
        (!tab || tab === "details") && (
          <div className="block top-0 bg-[#f8f8f8] p-[15px] pt-0 pb-0 mb-2.5 md:hidden ">
            <div className="flex items-center bg-white dark:bg-dark-800 py-[5px] shadow-[0_4px_24px_0] shadow-[#22292f1a] rounded-md">
              <ul className="flex md:flex-row flex-col sm:gap-2.5 items-start">
                {!isEmpty(login_info) && (
                  <li className="flex gap-1 sm:rounded px-3 py-[7px]">
                    <Typography className="text-sm font-semibold">
                      {_t("App Version")}:
                    </Typography>
                    <Typography className="text-sm font-normal text-primary-900">
                      {!!login_info
                        ? `${JSON.parse(login_info).version} ${
                            JSON.parse(login_info).from == "ios" ? " (iOS)" : ""
                          }${
                            JSON.parse(login_info).from == "android"
                              ? " (Android)"
                              : ""
                          }`
                        : " - "}
                    </Typography>
                  </li>
                )}
              </ul>
            </div>
          </div>
        )}

      {delArchConfirmOpen !== "" && (
        <ConfirmModal
          isOpen={delArchConfirmOpen !== ""}
          modaltitle={_t(
            delArchConfirmOpen === "delete"
              ? "Delete"
              : delArchConfirmOpen === "archive"
              ? "Archive"
              : "Active"
          )}
          description={_t(
            delArchConfirmOpen === "delete"
              ? "Are you sure you want to delete this Item?"
              : delArchConfirmOpen === "archive"
              ? "Are you sure you want to Archive this item? To view it or Activate it later, set the filter to show Archived items."
              : "Are you sure you want to Activate this data?"
          )}
          withConfirmText={delArchConfirmOpen === "delete"}
          modalIcon={
            delArchConfirmOpen === "delete"
              ? "fa-regular fa-trash-can"
              : delArchConfirmOpen === "archive"
              ? "fa-regular fa-box-archive"
              : "fa-regular fa-regular-active"
          }
          isLoading={isDeleting}
          assocDelCheckBoxProps={{
            view:
              type_key === dirTypeKeyByKey.employee || details.app_access == 1
                ? true
                : false,
          }}
          onAccept={(data) => {
            if (delArchConfirmOpen === "active") {
              handleDirActive();
            } else {
              handleDirDelArch({ isAssocDel: data });
            }
          }}
          onDecline={onCloseDelModal}
          onCloseModal={onCloseDelModal}
        />
      )}

      {isShareOpen && (
        <ShareInternalLinkModal
          isOpen={isShareOpen}
          shareLinkParams={{
            record_id: Number(selectedData?.user_id),
            module_key: gConfig?.module_key,
            module_page: removeFirstSlash(routes.MANAGE_DIRECTORY.url || ""),
          }}
          onEmailLinkClick={(data) => {
            setIsSendEmailSidebarOpen(true);
            setShareLink(data);
            setIsShareOpen(false);
          }}
          onCloseModal={() => {
            setSelectedData({});
            setIsShareOpen(false);
            setShareLink("");
          }}
        />
      )}

      {convertConfirmOpen !== "" && (
        <ConfirmModal
          isOpen={convertConfirmOpen !== ""}
          modaltitle={_t(
            convertConfirmOpen === "convert_to_vendor"
              ? "Are you sure you want to convert this Contractor to Vendor?"
              : convertConfirmOpen === "convert_to_contractor"
              ? "Are you sure you want to convert this Vendor to Contractor?"
              : "Are you sure you want to convert this Lead to Customer?"
          )}
          description={_t(
            convertConfirmOpen === "convert_to_vendor"
              ? "If you click 'Yes', this record will no longer be in the Contractor section as it will be moved to the Vendor section of the Directory."
              : convertConfirmOpen === "convert_to_contractor"
              ? "If you click 'Yes', this record will no longer be in the Vendor section as it will be moved to the Contractor section of the Directory."
              : "If you click 'Yes', this record will no longer be in the Lead section as it will be moved to the Customer section of the Directory."
          )}
          modalIcon={
            convertConfirmOpen === "convert_to_vendor"
              ? "fa-regular fa-person-digging"
              : convertConfirmOpen === "convert_to_contractor"
              ? "fa-regular fa-user-helmet-safety"
              : "fa-regular fa-user"
          }
          isLoading={isConverting}
          onAccept={handleConvertDir}
          onDecline={onCloseConvertModal}
          onCloseModal={onCloseConvertModal}
        />
      )}

      <DirSendEmail
        isOpen={isSendEmailSidebarOpen}
        canWrite={false}
        contactId={0}
        isViewAttachment={false}
        emailData={{
          subject: "Shared Link",
          body: `A link to a record within Contractor Foreman has been shared with you. <a href="${shareLink}">View Details.</a>`,
        }}
        appUsers={true}
        onSendResponse={() => {}}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setShareLink("");
        }}
        app_access={false}
      />

      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${replaceDOMParams(
              sanitizeString(customDataAdd?.name || "")
            )}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAddCustomData();
          }}
          onDecline={closeConfirmationModal}
        />
      )}
      <CustomContextMenu
        position={{ x: contextMenu.x, y: contextMenu.y }}
        visible={contextMenu.visible}
        items={contextMenuItems}
        onClose={() => setContextMenu((c) => ({ ...c, visible: false }))}
      />
      {loginDetailsOpen && (
        <LoginDetails
          isOpen={loginDetailsOpen}
          onClose={() => {
            setLoginDetailsOpen(false);
          }}
        />
      )}
    </>
  );
};

export default DirDetailsTopBar;
