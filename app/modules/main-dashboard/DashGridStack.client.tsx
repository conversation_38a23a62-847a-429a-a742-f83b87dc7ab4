import {
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useRef,
} from "react";
import { GridStack } from "gridstack";
// Redux + hook
import { useAppDashDispatch, useAppDashSelector } from "./redux/store";
import { addUpdateWidgetAct, changeWidgetAct } from "./redux/slices";

// const DashGridStack = ({ autoArrange, isGridEditable, children }: any) => {
const DashGridStack = forwardRef<
  IDashGridStackHandle,
  {
    isGridEditable: boolean;
    children: React.ReactNode;
  }
>(({ isGridEditable, children }, ref) => {
  const gridRef = useRef<HTMLDivElement>(null);
  const gridInstance = useRef<any>(null);
  const dispatch = useAppDashDispatch();
  const { dashWidgetGridStack }: IDashIntlState = useAppDashSelector(
    (state) => state.dashboard
  );

  useEffect(() => {
    if (!gridRef.current) return;

    // Initialize grid once
    gridInstance.current = GridStack.init(
      {
        staticGrid: !isGridEditable,
        float: true,
        column: 12,
        minRow: 1,
        cellHeight: 85,
        disableDrag: false,
        disableResize: false,
        // acceptWidgets: ".grid-stack-item",
      },
      gridRef.current
    );

    return () => {
      gridInstance.current?.destroy(false);
    };
  }, []);

  useEffect(() => {
    if (gridInstance.current) {
      gridInstance.current.setStatic(!isGridEditable);
    }
  }, [isGridEditable]);

  // Expose methods to parent
  useImperativeHandle(ref, () => ({
    autoArrange: () => {
      gridInstance.current?.compact();
    },
    saveLayout: () => {
      const layout = gridInstance.current?.save();
      const cleanedLayout = layout.map(({ ...item }: IDashWidgetGridStack) => ({
        ...item,
        content: "",
      }));
      if (cleanedLayout) {
        console.log("Saved:", cleanedLayout);
      }
    },
    addUpdateWidget: (data, type) => {
      if (!gridInstance.current || !gridRef.current) return;
      let selWid = dashWidgetGridStack.find((i) => i.id == data);
      if (type == "remove") {
        if (!selWid?.id) return;
        const el = document.querySelector(
          `.grid-stack-item[gs-id="${data}"]`
        ) as HTMLElement;
        if (el && gridInstance.current) {
          gridInstance.current.removeWidget(el, false);
          el.style.display = "none";
        }
      } else {
        if (!selWid?.id) {
          selWid = {
            id: data,
            x: undefined,
            y: undefined,
            w: "3",
            h: "4",
            content: "",
          };
          gridInstance.current?.compact();
          dispatch(addUpdateWidgetAct(selWid));
          return false;
        }

        const el = document.querySelector(
          `.grid-stack-item[gs-id="${data}"]`
        ) as HTMLElement;

        if (el && gridInstance.current) {
          if (selWid?.x != undefined) {
            el.setAttribute("gs-x", String(selWid?.x ?? 0));
          }
          if (selWid?.y != undefined) {
            el.setAttribute("gs-y", String(selWid?.y ?? 0));
          }
          el.setAttribute("gs-w", String(selWid?.w ?? 1));
          el.setAttribute("gs-h", String(selWid?.h ?? 1));
          el.style.display = "";
          gridInstance.current.makeWidget(el);
        }
      }
    },
  }));

  useEffect(() => {
    if (!gridInstance.current || !gridRef.current) return;
    const items = gridRef.current.querySelectorAll(".grid-stack-item");
    items.forEach((item) => {
      if (!item.classList.contains("gs-added")) {
        gridInstance.current.makeWidget(item as HTMLElement);
        item.classList.add("gs-added");
      }
    });
  }, [dashWidgetGridStack]);

  useEffect(() => {
    const grid = gridInstance.current;
    if (!grid) return;

    const onChange = (_: any, items: IDashWidgetGridStack[]) => {
      const coordinates = items.map(({ x, y, h, w, id }) => ({
        x: String(x),
        y: String(y),
        h: String(h),
        w: String(w),
        id,
      }));
      const coordMap = new Map(coordinates.map((i) => [i.id, i]));
      // Step 2: Merge updates efficiently
      const updatedStack = dashWidgetGridStack.map((item) => {
        const existing = coordMap.get(item.id);
        return existing ? { ...item, ...existing } : item;
      });
      dispatch(changeWidgetAct(updatedStack));
    };
    grid.on("change", onChange);

    return () => {
      grid.off("change", onChange);
    };
  }, [dashWidgetGridStack]);

  return (
    <div className="grid-stack" ref={gridRef}>
      {children}
    </div>
  );
});

export default memo(DashGridStack);
