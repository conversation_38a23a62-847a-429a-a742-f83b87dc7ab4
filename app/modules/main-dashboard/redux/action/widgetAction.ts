import { createAsyncThunk } from "@reduxjs/toolkit";
import { handleApiCatchError } from "~/helpers/helper";
import { dashboardRoutes } from "~/route-services";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";

// Get Dashboard Data
export const fetchWidgetDashData = createAsyncThunk(
  "widgetDash/fetchData",
  async (params?: IDashWidgetApiParamsReq) => {
    try {
      const data = await getWebWorkerApiParams({
        otherParams: { ...params },
      });
      const response = (await webWorkerApi({
        url: dashboardRoutes.list,
        method: "post",
        data: data,
      })) as IWidgetDashApiRes;

      // if (response && params?.refresh_type) {
      //   response.refresh_type = params.refresh_type;
      // }

      if (response && params?.widget) {
        response.widget = params.widget;
      }
      return response;
    } catch (error) {
      return handleApiCatchError(error as Error);
    }
  }
);
