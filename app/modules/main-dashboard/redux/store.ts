import { useDispatch, useSelector } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import dashboardSlice from "./slices/dashboardSlice";
import dashCommonDataSlice from "./slices/commonDashSlice";

import sendEmailSlice from "~/redux/slices/sendEmailSlice";
import widgetSlice from "./slices/widgetSlice";

export const store = configureStore({
  reducer: {
    dashboard: dashboardSlice,
    dashCommonData: dashCommonDataSlice,
    sendEmailData: sendEmailSlice,
    widgetData: widgetSlice,
  },
});
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export const useAppDashDispatch = useDispatch.withTypes<AppDispatch>();
export const useAppDashSelector = useSelector.withTypes<RootState>();
