import { createSlice } from "@reduxjs/toolkit";
import { fetchDashData } from "~/modules/main-dashboard/redux/action";

const initialState: IDashIntlState = {
  dashWidgetGridStack: [],
  isDashLoading: true,
  isDataFetched: false,
};

export const dashboardSlice = createSlice({
  name: "dashboard",
  initialState,
  reducers: {
    addUpdateWidgetAct: (state, { payload }) => {
      state.dashWidgetGridStack.push(payload);
    },
    changeWidgetAct: (state, { payload }) => {
      state.dashWidgetGridStack = payload;
    },
    resetDash: (state) => {
      state.isDataFetched = false;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchDashData.pending, (state, { meta }) => {
      state.isDashLoading = true;
    });
    builder.addCase(fetchDashData.fulfilled, (state, { payload }) => {
      const newPayload = payload as IDashApiRes;
      if (newPayload && newPayload?.success) {
        const parsedData = JSON.parse(
          newPayload?.data?.dashboard_widget_order || ""
        );
        state.dashWidgetGridStack = parsedData;
      }
      state.isDataFetched = true;
      state.isDashLoading = false;
    });
    builder.addCase(fetchDashData.rejected, (state) => {
      state.isDashLoading = false;
      state.isDataFetched = false;
    });
  },
});

export const { addUpdateWidgetAct, changeWidgetAct, resetDash } =
  dashboardSlice.actions;
export default dashboardSlice.reducer;
