import { createSlice } from "@reduxjs/toolkit";
import { fetchWidgetDashData } from "~/modules/main-dashboard/redux/action";

const initialState: IWidgetDashIntlState = {
  dailyLogWidgetData: {
    item: [],
    dlFields: {},
    isDLWidDashLoading: true,
    isDLWidDataFetched: false,
  },
  equipmentLogWidgetData: {
    equipmentLogItem: [],
    equipmentLogFields: {},
    isELWidDashLoading: true,
    isELWidDataFetched: false,
  },
  inspectionWidgetData: {
    inspectionItem: [],
    inspectionFields: {},
    isInsWidDashLoading: true,
    isInsWidDataFetched: false,
  },
  recentNotesWidgetData: {
    recentNotesItem: [],
    recentNotesFields: {},
    isRecNoteWidDashLoading: true,
    isRecNoteWidDataFetched: false,
  },
  punchlistWidgetData: {
    punchlistItem: [],
    punchlistFields: {},
    isPunchWidDashLoading: true,
    isPunchWidDataFetched: false,
  },
};

export const widgetSlice = createSlice({
  name: "widgetData",
  initialState,
  reducers: {
    resetDash: (state) => {
      // state.dailyLogWidgetData.isDLWidDataFetched = false;
      // state.equipmentLogWidgetData.isELWidDataFetched = false;
      // state.inspectionWidgetData.isInsWidDataFetched = false;
      // state.recentNotesWidgetData.isRecNoteWidDataFetched = false;
      // state.punchlistWidgetData.isPunchWidDataFetched = false;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchWidgetDashData.pending, (state, { meta }) => {
      if (meta?.arg?.widget === "daily_logs") {
        state.dailyLogWidgetData.isDLWidDashLoading = true;
      }
      if (meta?.arg?.widget === "equipment_logs") {
        state.equipmentLogWidgetData.isELWidDashLoading = true;
      }
      if (meta?.arg?.widget === "inspections") {
        state.inspectionWidgetData.isInsWidDashLoading = true;
      }
      if (meta?.arg?.widget === "notes") {
        state.inspectionWidgetData.isInsWidDashLoading = true;
      }
      if (meta?.arg?.widget === "punchlists") {
        state.punchlistWidgetData.isPunchWidDashLoading = true;
      }
    });
    builder.addCase(fetchWidgetDashData.fulfilled, (state, { payload }) => {
      const newPayload = payload as IWidgetDashApiRes;

      if (newPayload && newPayload?.success) {
        if (newPayload.data.modules[0].module_key == "daily_logs") {
          state.dailyLogWidgetData.item =
            newPayload?.data?.modules[0].data || [];
          state.dailyLogWidgetData.dlFields =
            newPayload?.data?.modules[0]?.fields || {};
          state.dailyLogWidgetData.isDLWidDataFetched = false;
          state.dailyLogWidgetData.isDLWidDashLoading = false;
        }
        if (newPayload.data.modules[0].module_key == "equipment_logs") {
          state.equipmentLogWidgetData.equipmentLogItem =
            newPayload?.data?.modules[0].data || [];
          state.equipmentLogWidgetData.equipmentLogFields =
            newPayload?.data?.modules[0]?.fields || {};
          state.equipmentLogWidgetData.isELWidDataFetched = false;
          state.equipmentLogWidgetData.isELWidDashLoading = false;
        }
        if (newPayload.data.modules[0].module_key == "inspections") {
          state.inspectionWidgetData.inspectionItem =
            newPayload?.data?.modules[0].data || [];
          state.inspectionWidgetData.inspectionFields =
            newPayload?.data?.modules[0]?.fields || {};
          state.inspectionWidgetData.isInsWidDataFetched = false;
          state.inspectionWidgetData.isInsWidDashLoading = false;
        }
        if (newPayload.data.modules[0].module_key == "notes") {
          state.recentNotesWidgetData.recentNotesItem =
            newPayload?.data?.modules[0].data || [];
          state.recentNotesWidgetData.recentNotesFields =
            newPayload?.data?.modules[0]?.fields || {};
          state.recentNotesWidgetData.isRecNoteWidDataFetched = false;
          state.recentNotesWidgetData.isRecNoteWidDashLoading = false;
        }
        if (newPayload.data.modules[0].module_key == "punchlists") {
          state.punchlistWidgetData.punchlistItem =
            newPayload?.data?.modules[0].data || [];
          state.punchlistWidgetData.punchlistFields =
            newPayload?.data?.modules[0]?.fields || {};
          state.punchlistWidgetData.isPunchWidDashLoading = false;
          state.punchlistWidgetData.isPunchWidDataFetched = false;
        }

        // const parsedData = newPayload?.data?.modules[0].data || [];
        // state.widgetDetails = parsedData;
      }
      //   state.isDataFetched = true;
      //   state.isDashLoading = false;
    });
    builder.addCase(fetchWidgetDashData.rejected, (state) => {
      state.dailyLogWidgetData.isDLWidDataFetched = false;
      state.dailyLogWidgetData.isDLWidDashLoading = false;

      state.equipmentLogWidgetData.isELWidDataFetched = false;
      state.equipmentLogWidgetData.isELWidDashLoading = false;

      state.inspectionWidgetData.isInsWidDataFetched = false;
      state.inspectionWidgetData.isInsWidDashLoading = false;

      state.recentNotesWidgetData.isRecNoteWidDataFetched = false;
      state.recentNotesWidgetData.isRecNoteWidDashLoading = false;

      state.punchlistWidgetData.isPunchWidDashLoading = false;
      state.punchlistWidgetData.isPunchWidDataFetched = false;
    });
  },
});

export const { resetDash } = widgetSlice.actions;
export default widgetSlice.reducer;
