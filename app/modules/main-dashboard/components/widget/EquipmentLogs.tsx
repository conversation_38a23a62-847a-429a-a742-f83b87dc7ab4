import { useEffect, useState } from "react";
import { useTranslation } from "~/hook";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
// Other
import { getDefaultStatuscolor, sanitizeString } from "~/helpers/helper";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import {
  useAppDashDispatch,
  useAppDashSelector,
} from "~/modules/main-dashboard/redux/store";
import { fetchWidgetDashData } from "~/modules/main-dashboard/redux/action";

const EquipmentLogs = () => {
  const { _t } = useTranslation();
  const { getGlobalModuleByKey } = useGlobalModule();
  const dispatch = useAppDashDispatch();
  const EquipmentLogsName = getGlobalModuleByKey(CFConfig.equipment_log_module);

  const [isCashLoading, setIsCashLoading] = useState<boolean>(false);
  const [rowData, setRowData] = useState<any[]>([]);

  const {
    isELWidDashLoading,
    equipmentLogItem,
    equipmentLogFields,
  }: IELWidgetDashIntlState = useAppDashSelector(
    (state) => state.widgetData.equipmentLogWidgetData
  );

  useEffect(() => {
    handleClickRefresh(0);
  }, []);

  useEffect(() => {
    if (!isCashLoading && equipmentLogItem) {
      setRowData(equipmentLogItem);
    }
  }, [equipmentLogItem, isCashLoading]);

  const handleClickRefresh = async (isRef = 0) => {
    setIsCashLoading(true);
    setRowData([]);
    await dispatch(
      fetchWidgetDashData({
        is_refresh: isRef,
        iframe_call: 0,
        global_project: 0,
        status: 1,
        from: "panel",
        widget: "equipment_logs",
        widget_list: [
          {
            module_id: "13",
            module_key: "equipment_logs",
            module_name: "Equipment Logs",
            widget_key: "equipment_logs",
            key_name: "equipment_logs",
            web_page: "manage_equipment_logs.php",
            app_request: "equipment-logs",
            header: "Equipment Logs",
            name: "Equipment Logs",
            tooltip: "",
            access_to_all_data: "1",
          },
        ],
      })
    );
    setIsCashLoading(false);
  };

  const columnDefs = [
    {
      headerName: equipmentLogFields?.log_date || _t("Date"),
      field: "date",
      minWidth: 135,
      maxWidth: 135,
      suppressMenu: true,
      cellRenderer: ({ data }: any) => {
        return <DateTimeCard format="date" date={data.log_date} />;
      },
    },
    {
      headerName: equipmentLogFields?.equipment_name || _t("Equipment Name"),
      field: "equipment_name",
      minWidth: 120,
      flex: 2,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      suppressMenu: true,
      cellRenderer: ({ data }: any) => {
        const equipmentName = HTMLEntities.decode(
          sanitizeString(data?.equipment_name)
        );
        if (!equipmentName) return <>-</>;
        return (
          <Tooltip title={_t(equipmentName)}>
            <Typography className="table-tooltip-text !w-fit">
              {_t(equipmentName)}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: equipmentLogFields?.hours_used || _t("Hours"),
      field: "hours",
      minWidth: 90,
      maxWidth: 90,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      suppressMenu: true,
      cellRenderer: ({ data }: any) => {
        if (!data.hours_used) return <>-</>;
        return (
          <Tooltip title={_t(data.hours_used)}>
            <Typography className="table-tooltip-text !w-fit">
              {_t(data.hours_used)}
            </Typography>
          </Tooltip>
        );
      },
    },
  ];

  const noRowsOverlay = () => <StaticTableRowLoading columnDefs={columnDefs} />;

  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-recent-sub-contract.svg`}
    />
  );

  return (
    // <CFGridStackItem gs-w={4} gs-min-w={3} gs-max-w={4} gs-h={4} gs-min-h={4} gs-max-h={5} gs-x={4} gs-y={21}></CFGridStackItem>
    <div className="common-card">
      <DashboardCardHeader
        title={_t(EquipmentLogsName?.plural_name ?? "Equipment Logs")}
        showRefreshIcon={true}
        isRefreshing={isCashLoading}
        onClickRefresh={() => handleClickRefresh(1)}
      />
      <div className="py-2 px-2.5 ">
        <div className="ag-theme-alpine h-[209px]">
          <StaticTable
            className="static-table"
            columnDefs={columnDefs}
            rowData={rowData}
            key={isELWidDashLoading ? "loading" : "loaded"}
            noRowsOverlayComponent={
              isCashLoading || isELWidDashLoading ? noRowsOverlay : noData
            }
          />
        </div>
      </div>
    </div>
  );
};

export default EquipmentLogs;
