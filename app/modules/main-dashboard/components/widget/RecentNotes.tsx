import { useEffect, useState } from "react";
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
// Other
import { getDefaultStatuscolor, sanitizeString } from "~/helpers/helper";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import {
  useAppDashDispatch,
  useAppDashSelector,
} from "~/modules/main-dashboard/redux/store";
import { fetchWidgetDashData } from "~/modules/main-dashboard/redux/action";

const RecentNotes = () => {
  const { _t } = useTranslation();
  const { getGlobalModuleByKey } = useGlobalModule();
  const dispatch = useAppDashDispatch();

  const NotesName = getGlobalModuleByKey(CFConfig.notes_module);
  const [isCashLoading, setIsCashLoading] = useState<boolean>(false);
  const [rowData, setRowData] = useState<any[]>([]);

  const {
    isRecNoteWidDashLoading,
    recentNotesItem,
    recentNotesFields,
  }: IRecNoteWidgetDashIntlState = useAppDashSelector(
    (state) => state.widgetData.recentNotesWidgetData
  );

  useEffect(() => {
    handleClickRefresh(0);
  }, []);

  useEffect(() => {
    if (!isCashLoading && recentNotesItem) {
      setRowData(recentNotesItem);
    }
  }, [recentNotesItem, isCashLoading]);

  const handleClickRefresh = async (isRef = 0) => {
    setIsCashLoading(true);
    setRowData([]);
    await dispatch(
      fetchWidgetDashData({
        is_refresh: isRef,
        iframe_call: 0,
        global_project: 0,
        status: 1,
        from: "panel",
        widget: "notes",
        widget_list: [
          {
            module_id: "21",
            module_key: "notes",
            module_name: "Notes",
            widget_key: "notes",
            key_name: "notes",
            web_page: "manage_notes.php",
            app_request: "notes",
            header: "Recent Notes",
            name: "Recent Notes",
            tooltip: "",
            access_to_all_data: "1",
          },
        ],
      })
    );
    setIsCashLoading(false);
  };

  const columnDefs = [
    {
      headerName: recentNotesFields?.note_date || _t("Date"),
      field: "date",
      minWidth: 135,
      maxWidth: 135,
      suppressMenu: true,
      cellRenderer: ({ data }: any) => {
        return data.note_date ? (
          <DateTimeCard format="date" date={data.note_date} />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: recentNotesFields?.subject || _t("Title"),
      field: "title_name",
      minWidth: 120,
      flex: 2,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      suppressMenu: true,
      cellRenderer: ({ data }: any) => {
        const title = HTMLEntities.decode(sanitizeString(data?.subject));
        if (!title) return <>-</>;
        return (
          <Tooltip title={_t(title)}>
            <Typography className="table-tooltip-text !w-fit">
              {_t(title)}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: recentNotesFields?.priority || _t("Priority"),
      field: "priority_name",
      maxWidth: 80,
      minWidth: 80,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellStyle: { textAlign: "center" },
      suppressMenu: true,
      cellRenderer: ({ data }: any) => {
        const priority_name = data.display_name;
        const default_priority_color = data.status_color || "";
        const { color, textColor } = getDefaultStatuscolor(
          default_priority_color || ""
        );
        return priority_name ? (
          <Tooltip title={priority_name}>
            {priority_name && (
              <FontAwesomeIcon
                icon="fa-solid fa-flag"
                className="h-3.5 w-3.5 mx-auto"
                style={{
                  color: default_priority_color,
                }}
              />
            )}
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
  ];

  const noRowsOverlay = () => <StaticTableRowLoading columnDefs={columnDefs} />;

  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-recent-sub-contract.svg`}
    />
  );

  return (
    // <CFGridStackItem gs-w={3} gs-min-w={3} gs-max-w={4} gs-h={4} gs-min-h={4} gs-max-h={5} gs-x={9} gs-y={5}></CFGridStackItem>
    <div className="common-card">
      <DashboardCardHeader
        title={_t(`Recent ${NotesName?.plural_name ?? "Notes"}`)}
        showRefreshIcon={true}
        isRefreshing={isCashLoading}
        onClickRefresh={() => handleClickRefresh(1)}
      />
      <div className="py-2 px-2.5 ">
        <div className="ag-theme-alpine h-[209px]">
          <StaticTable
            className="static-table"
            columnDefs={columnDefs}
            rowData={rowData}
            key={isRecNoteWidDashLoading ? "loading" : "loaded"}
            noRowsOverlayComponent={
              isCashLoading || isRecNoteWidDashLoading ? noRowsOverlay : noData
            }
          />
        </div>
      </div>
    </div>
  );
};

export default RecentNotes;
