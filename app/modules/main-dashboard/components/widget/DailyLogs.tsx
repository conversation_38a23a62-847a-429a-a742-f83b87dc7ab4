import { useEffect, useState } from "react";
import { useTranslation } from "~/hook";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
// Other
import { getDefaultStatuscolor, sanitizeString } from "~/helpers/helper";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import {
  useAppDashDispatch,
  useAppDashSelector,
} from "~/modules/main-dashboard/redux/store";
import { fetchWidgetDashData } from "~/modules/main-dashboard/redux/action";

const DailyLogs = () => {
  const { _t } = useTranslation();
  const { getGlobalModuleByKey } = useGlobalModule();
  const dispatch = useAppDashDispatch();

  const DailyLogsName = getGlobalModuleByKey(CFConfig.daily_log_module);

  const [isCashLoading, setIsCashLoading] = useState<boolean>(false);
  const [rowData, setRowData] = useState<any[]>([]);

  const { isDLWidDashLoading, item, dlFields }: IDLWidgetDashIntlState =
    useAppDashSelector((state) => state.widgetData.dailyLogWidgetData);

  useEffect(() => {
    handleClickRefresh(0);
  }, []);

  useEffect(() => {
    if (!isCashLoading && item) {
      setRowData(item);
    }
  }, [item, isCashLoading]);

  const handleClickRefresh = async (isRef = 0) => {
    setIsCashLoading(true);
    setRowData([]);
    await dispatch(
      fetchWidgetDashData({
        is_refresh: isRef,
        iframe_call: 0,
        global_project: 0,
        status: 1,
        from: "panel",
        widget: "daily_logs",
        widget_list: [
          {
            module_id: "4",
            module_key: "daily_logs",
            module_name: "Daily+Logs",
            widget_key: "daily_logs",
            key_name: "daily_logs",
            web_page: "manage_daily_logs.php",
            app_request: "daily-logs",
            header: "Daily+Logs",
            name: "Daily+Logs",
            tooltip: "",
            access_to_all_data: "1",
          },
        ],
      })
    );
    setIsCashLoading(false);
  };

  const columnDefs = [
    {
      headerName: dlFields?.log_date || _t("Date"),
      field: "date",
      minWidth: 135,
      maxWidth: 135,
      suppressMenu: true,
      cellRenderer: ({ data }: any) => {
        return data.log_date ? (
          <DateTimeCard format="date" date={data.log_date} />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: dlFields?.project_name || _t("Project"),
      field: "project_name",
      minWidth: 120,
      flex: 2,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      suppressMenu: true,
      cellRenderer: ({ data }: any) => {
        const projectName = HTMLEntities.decode(
          sanitizeString(data?.project_name)
        );
        if (!projectName) return <>-</>;
        return (
          <Tooltip title={_t(projectName)}>
            <Typography className="table-tooltip-text !w-fit">
              {_t(projectName)}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: dlFields?.job_status_name || _t("Status"),
      field: "status",
      maxWidth: 100,
      minWidth: 100,
      headerClass: "ag-header-center",
      suppressMenu: true,
      cellRenderer: ({ data }: any) => {
        const inspection_status_name = "Pending";
        const default_color = "";
        const { color, textColor } = getDefaultStatuscolor(default_color || "");
        return data.job_status_name ? (
          <Tooltip title={data.job_status_name}>
            <div className="text-center overflow-hidden">
              {data.job_status_name && (
                <Tag
                  color={color}
                  style={{
                    color: `${textColor || ""}`,
                  }}
                  className={`${
                    textColor === "" && "!text-primary-900"
                  } mx-auto text-13 type-badge common-tag`}
                >
                  {data.job_status_name}
                </Tag>
              )}
            </div>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
  ];

  const noRowsOverlay = () => <StaticTableRowLoading columnDefs={columnDefs} />;

  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-recent-sub-contract.svg`}
    />
  );

  return (
    // <CFGridStackItem gs-w={3} gs-min-w={3} gs-max-w={4} gs-h={4} gs-min-h={4} gs-max-h={5} gs-x={9} gs-y={5}></CFGridStackItem>
    <div className="common-card">
      <DashboardCardHeader
        title={_t(DailyLogsName?.plural_name ?? "Daily Logs")}
        showRefreshIcon={true}
        isRefreshing={isCashLoading}
        onClickRefresh={() => handleClickRefresh(1)}
      />
      <div className="py-2 px-2.5 ">
        <div className="ag-theme-alpine h-[209px]">
          <StaticTable
            className="static-table"
            columnDefs={columnDefs}
            rowData={rowData}
            key={isDLWidDashLoading ? "loading" : "loaded"}
            noRowsOverlayComponent={
              isCashLoading || isDLWidDashLoading ? noRowsOverlay : noData
            }
          />
        </div>
      </div>
    </div>
  );
};

export default DailyLogs;
