// Dashboard
// interface ISafetyMeetingsIntlState extends ISafetyMeetingsState {
interface IDashWidgetGridStack {
  x?: string;
  y?: string;
  w: string;
  h: string;
  id: string;
  widget_height?: number;
  content: string;
  hide?: boolean;
}
interface IDashIntlState {
  dashWidgetGridStack: IDashWidgetGridStack[];
  isDashLoading: boolean;
  isDataFetched: boolean;
}

interface IDLWidgetFields {
  log_date?: string;
  project_name?: string;
  job_status_name?: string;
  [key: string]: string | undefined;
}
interface IELWidgetFields {
  log_date?: string;
  equipment_name?: string;
  hours_used?: string;
  [key: string]: string | undefined;
}
interface IInsWidgetFields {
  inspection_date?: string;
  project_name?: string;
  inspection_status_name?: string;
  [key: string]: string | undefined;
}
interface IRecNoteWidgetFields {
  note_date?: string;
  priority?: string;
  subject?: string;
  [key: string]: string | undefined;
}
interface IPunchWidgetFields {
  date_added?: string;
  project_name?: string;
  punchlist_name?: string;
  [key: string]: string | undefined;
}

interface IDLWidgetItem {
  project_name: string;
  primary_id: number;
  log_date: string;
  job_status_name: string;
}

interface IDLWidgetDashIntlState {
  item: IDLWidgetItem[];
  dlFields?: IDLWidgetFields;
  isDLWidDashLoading: boolean;
  isDLWidDataFetched: boolean;
}
interface IELWidgetItem {
  e_hours_used: string;
  primary_id: number;
  equipment_name: string;
  log_date: string;
  hours_used: string;
}
interface IELWidgetDashIntlState {
  equipmentLogItem: IELWidgetItem[];
  equipmentLogFields?: IELWidgetFields;
  isELWidDashLoading: boolean;
  isELWidDataFetched: boolean;
}

interface IInsWidgetItem {
  primary_id: number;
  project_name: string;
  inspection_status_name: string;
  inspection_date: string;
  status_color: string;
}

interface IInspectionWidgetDashIntlState {
  inspectionItem: IInsWidgetItem[];
  inspectionFields?: IInsWidgetFields;
  isInsWidDashLoading: boolean;
  isInsWidDataFetched: boolean;
}
interface IRecNoteWidgetItem {
  primary_id: string;
  subject: string;
  note_date: string;
  priority: string;
  display_name: string;
  status_color: string;
}
interface IRecNoteWidgetDashIntlState {
  recentNotesItem: IRecNoteWidgetItem[];
  recentNotesFields?: IRecNoteWidgetFields;
  isRecNoteWidDashLoading: boolean;
  isRecNoteWidDataFetched: boolean;
}
interface IPunchWidgetItem {
  punchlist_id: string;
  primary_id: string;
  punchlist_name: string;
  project_name: string;
  date_added: string;
}
interface IPunchWidgetDashIntlState {
  punchlistItem: IPunchWidgetItem[];
  punchlistFields?: IPunchWidgetFields;
  isPunchWidDashLoading: boolean;
  isPunchWidDataFetched: boolean;
}
interface IWidgetDashIntlState {
  dailyLogWidgetData: IDLWidgetDashIntlState;
  equipmentLogWidgetData: IELWidgetDashIntlState;
  inspectionWidgetData: IInspectionWidgetDashIntlState;
  recentNotesWidgetData: IRecNoteWidgetDashIntlState;
  punchlistWidgetData: IPunchWidgetDashIntlState;
}

interface dashWidgetItem {
  module_id: string;
  module_key: string;
  module_name: string;
  widget_key: string;
  key_name: string;
  web_page: string;
  app_request: string;
  header: string;
  name: string;
  tooltip: string;
  access_to_all_data: string;
}

interface IDashApiParamsReq {}

interface IDashWidgetApiParamsReq {
  is_refresh: number;
  iframe_call: number;
  global_project: number;
  status: number;
  from: string;
  widget: string;
  widget_list: WidgetItem[];
  widget?: string;
}

interface IDashApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    dashboard_widget_order: string;
  }; // TODO Need to change this type
  refresh_type?: string;
}
interface IWidgetDashApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    modules: any[];
  }; // TODO Need to change this type
  refresh_type?: string;
  widget?: string;
}
