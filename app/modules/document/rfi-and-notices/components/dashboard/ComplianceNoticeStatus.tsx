import { useTranslation } from "~/hook";
// Atoms
import { ApexChart } from "~/shared/components/atoms/chart";
// Molecules
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import PieChartSkeleton from "~/shared/components/molecules/charts/skeleton/PieChart.skeleton";
// Other
import { useMemo, useState } from "react";
import { useApexCharts } from "~/shared/hooks/useApexCharts";
import { fetchDashData } from "~/modules/document/rfi-and-notices/redux/action/dashboardAction";
import {
  useAppRFIDispatch,
  useAppRFISelector,
} from "~/modules/document/rfi-and-notices/redux/store";

const ComplianceNoticeStatus = () => {
  const { _t } = useTranslation();
  const dispatch = useAppRFIDispatch();

  const {
    isDashLoading,
    complianceNoticeStatusData,
    complianceNoticeStatusDataLastRefreshTime,
  } = useAppRFISelector((state) => state.dashboard);

  const [isRefreshLoading, setIsRefreshLoading] = useState<boolean>(false);

  const SeriesDatabar = useMemo(() => {
    return complianceNoticeStatusData?.map((item) => Number(item.count));
  }, [complianceNoticeStatusData]);

  const optionsLine = useApexCharts({ type: "donut" });
  const chartOptions = useMemo(() => {
    return {
      ...optionsLine,
      chart: {
        offsetY: 15,
      },
      labels: complianceNoticeStatusData?.map((item) => item.name),
      dataLabels: {
        enabled: false,
      },
      legend: {
        show: true,
        position: "right",
        horizontalAlign: "center",
        offsetX: 30,
        offsetY: 25,
        onItemClick: { toggleDataSeries: false },
        onItemHover: { highlightDataSeries: true },
      },
      colors: complianceNoticeStatusData?.map((item) => item.default_color),
      plotOptions: {
        pie: {
          expandOnClick: false,
          donut: {
            labels: {
              show: true,
              name: {
                show: true,
                fontSize: "12px",
                color: "#000",
                offsetY: -7,
              },
              value: {
                show: true,
                fontSize: "12px",
                color: "#000",
                opacity: 1,
                offsetY: -2,
                // formatter: function (val: any) {
                //   return val;
                // },
              },
              total: {
                show: true,
                color: "#000",
                fontSize: "12px",
                fontWeight: "600",
                showAlways: true,
                formatter: () =>
                  `${SeriesDatabar?.reduce(
                    (total, current) => total + current,
                    0
                  )}`,
              },
            },
            size: "60%",
          },
        },
      },
      states: {
        hover: {
          filter: {
            type: "none",
          },
        },
        active: {
          allowMultipleDataPointsSelection: false,
          filter: {
            type: "none",
          },
        },
      },
      responsive: [
        {
          breakpoint: 500, // Adjust for tablets or small screens
          options: {
            legend: {
              offsetX: -20,
            },
          },
        },
      ],
    };
  }, [complianceNoticeStatusData, SeriesDatabar]);

  const handleRefreshWidget = async () => {
    setIsRefreshLoading(true);
    await dispatch(fetchDashData({ refresh_type: "compliance_notice_status" }));
    setIsRefreshLoading(false);
  };

  return (
    <>
      <DashboardCardHeader
        title={_t("Compliance Notices by Status")}
        showRefreshIcon={true}
        refreshIconTooltip={complianceNoticeStatusDataLastRefreshTime}
        onClickRefresh={handleRefreshWidget}
        isRefreshing={isRefreshLoading}
      />
      {isDashLoading || isRefreshLoading ? (
        <div className="py-2 px-2.5 h-[190px] flex justify-center items-center">
          <PieChartSkeleton />
        </div>
      ) : (
        <ApexChart
          className="donut-chart legend-small-gap"
          series={SeriesDatabar}
          options={chartOptions}
          type={"donut"}
          height={162}
        />
      )}
    </>
  );
};

export default ComplianceNoticeStatus;
