// React + ag-grid
import { useEffect, useMemo, useRef, useState } from "react";
import { useLocation, useParams } from "@remix-run/react";
import dayjs from "dayjs";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
// Hooks and Redux
import { getCommonSidebarCollapse, getGSettings } from "~/zustand";
// Molecules
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
// Organisms
import { TimeLineFooter } from "~/shared/components/organisms/timeLine/timeLineFooter";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import DetailsTopBar from "./DetailsTopBar";
import { statusColorMap, statusIconMap } from "../../utils/constasnts";
import { ProgressBarHeader } from "~/shared/components/molecules/ProgressBarHeader";
import { useAppRFIDispatch, useAppRFISelector } from "../../redux/store";
import { sanitizeString } from "~/helpers/helper";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Typography } from "~/shared/components/atoms/typography";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { MenuProps } from "antd";
import { RFIFieldStatus } from "~/constants/rfi-and-notices";
import {
  getRfiDetail,
  updateRfiDetailsApi,
} from "../../redux/action/rfiDetailAction";
import {
  setRFILoading,
  updateRFIDetails,
} from "../../redux/slices/rfiDetailSlice";
import { delay } from "lodash";
import { getStatusActionForField } from "~/shared/utils/helper/common";
import { getStatusList } from "~/redux/action/getStatusListAction";
import { fetchRfiFilePhoto } from "../../redux/action/rfiAndNoticesFilePhotoAction";
import { fetchRFINotes } from "../../redux/action/rfiNotes";
import { backendDateFormat } from "~/shared/utils/helper/defaultDateFormat";

// Other

const ManageRfiNoticeTab = ({ children }: { children: React.ReactNode }) => {
  const { pathname } = useLocation();
  const { tab, id: RFI_Id }: RouteParams = useParams();
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const {
    module_key = "",
    module_id = 0,
    module_access = "no-access",
  } = currentModule || {};
  const detailsRef = useRef<HTMLDivElement>(null);
  const sidebarCollapse: boolean | undefined = getCommonSidebarCollapse();
  const [activeStep, setActiveStep] = useState<string>("");
  const { isRFIDetailLoading, rfiDetail }: IRFIInitialState = useAppRFISelector(
    (state) => state.rfiDetail
  );

  const isReadOnly = useMemo(
    () => module_access === "read_only",
    [module_access]
  );
  const [loadingDataStatus, setLoadingDataStatus] =
    useState<Array<IFieldStatus>>(RFIFieldStatus);
  const loadingStatusRef = useRef(RFIFieldStatus);
  const [inputValues, setInputValues] = useState<Partial<IRFIDetails>>({});
  const [checkStatusLoading, setCheckStatusLoading] = useState<boolean>(true);
  const {
    correspondence_schedule_status,
    correspondence_compliance_status,
    correspondence_rfi,
  }: IStatusListInitialState = useAppRFISelector(
    (state) => state.getStatusList
  );
  const dispatch = useAppRFIDispatch();
  const { project_id, project_name, rfi_title, custom_correspondence_id } =
    rfiDetail;
  const initialValues: Partial<IRFIDetails> = useMemo(
    () => ({
      project_id: project_id,
      project_name: project_name,
      rfi_title: rfi_title,
      custom_correspondence_id: custom_correspondence_id,
    }),
    [rfiDetail]
  );
  useEffect(() => {
    if (checkStatusLoading) {
      setInputValues(initialValues);
    }
  }, [initialValues, checkStatusLoading]);

  useEffect(() => {
    if (detailsRef.current) {
      detailsRef.current.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }
  }, [tab, detailsRef.current]);

  useEffect(() => {
    if (
      loadingDataStatus.length > 0 &&
      loadingDataStatus.some((item) => item.status === "loading")
    ) {
      setCheckStatusLoading(false);
    } else {
      setCheckStatusLoading(true);
    }
  }, [loadingDataStatus]);

  useEffect(() => {
    const fetchStatusList = async () => {
      if (rfiDetail?.correspondence_id) {
        setInputValues(rfiDetail);

        if (
          rfiDetail?.correspondence_key === "correspondence_schedule_notice"
        ) {
          dispatch(
            getStatusList({
              types: ["correspondence_schedule_status"],
            })
          );
        } else if (
          rfiDetail?.correspondence_key === "correspondence_complience_notice"
        ) {
          dispatch(
            getStatusList({
              types: ["correspondence_compliance_status"],
            })
          );
        } else {
          dispatch(
            getStatusList({
              types: ["correspondence_rfi"],
            })
          );
        }

        await new Promise((resolve) => setTimeout(resolve, 1500));
      }
    };

    fetchStatusList();
  }, [rfiDetail?.correspondence_id]);

  const rfiStatusList: IStatusList[] = useMemo(() => {
    const sourceList =
      rfiDetail?.correspondence_key === "correspondence_schedule_notice"
        ? correspondence_schedule_status
        : rfiDetail?.correspondence_key === "correspondence_complience_notice"
        ? correspondence_compliance_status
        : rfiDetail?.correspondence_key === "correspondence_rfi"
        ? correspondence_rfi
        : [];

    return (sourceList ?? []).map((item: any) => ({
      label: HTMLEntities.decode(sanitizeString(item.display_name)),
      value: item.type_id.toString(),
      default_color: statusColorMap[item.name],
      icon: statusIconMap[item.name],
    })) as IStatusList[];
  }, [
    rfiDetail?.correspondence_key,
    correspondence_schedule_status,
    correspondence_compliance_status,
    correspondence_rfi,
  ]);

  const selectedStatusInd = useMemo(() => {
    return rfiStatusList
      ?.filter((item) => item.show_in_progress_bar != 0)
      ?.findIndex((item) => item.value == rfiDetail.rfi_status);
  }, [JSON.stringify(rfiStatusList), rfiDetail.rfi_status]);

  const status = useMemo(() => {
    const statusList = rfiStatusList?.map((item) => ({
      label: HTMLEntities.decode(sanitizeString(item?.label)),
      key: item?.value?.toString() ?? "",
      icon: (
        <FontAwesomeIcon
          icon="fa-solid fa-square"
          className="h-3.5 w-3.5"
          style={{
            color: item?.default_color,
          }}
        />
      ),
    }));
    const getSelectStatus = rfiStatusList?.find(
      (item) => item.value?.toString() === rfiDetail.rfi_status?.toString()
    );

    const selectStatus = (
      <Tooltip title={getSelectStatus?.label}>
        <div
          className={`py-0.5 rounded flex items-center justify-center w-full status-dropdown-block group/status-dropdown px-2.5 ${
            isReadOnly ? "" : "hover:px-1 cursor-pointer"
          }`}
          style={{
            backgroundColor: getSelectStatus?.default_color + "1d",
          }}
        >
          <Typography
            style={{
              color: getSelectStatus?.default_color,
            }}
            className="text-xs whitespace-nowrap truncate"
          >
            {getSelectStatus?.label}
          </Typography>
          {!isReadOnly && (
            <FontAwesomeIcon
              className="w-2.5 h-2.5 pl-0.5 group-hover/status-dropdown:flex hidden ease-in-out duration-300"
              style={{
                color: getSelectStatus?.default_color,
              }}
              icon="fa-regular fa-chevron-down"
            />
          )}
        </div>
      </Tooltip>
    );
    return { statusList, selectStatus, getSelectStatus };
  }, [rfiStatusList, rfiDetail.rfi_status]);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingDataStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingDataStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };
  const handleUpdateField = async (data: IRfiDetailFieldsBoolean) => {
    const field = Object.keys(data)[0] as keyof IRFIDetails;
    let isError = false;
    let errorMsg = "";
    if (isError) {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({ ...inputValues, [field]: rfiDetail[field] });
      return false;
    }

    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });
    const updateRes = (await updateRfiDetailsApi({
      correspondence_id: RFI_Id,
      ...data,
    })) as ApiCallResponse;
    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      if (data?.response_received) {
        let formattedDate = "";

        if (
          typeof data?.response_received === "string" ||
          typeof data?.response_received === "number" ||
          data?.response_received instanceof Date
        ) {
          formattedDate = dayjs(data?.response_received, "YYYY-MM-DD").format(
            CFConfig.day_js_date_format
          );
        }

        dispatch(
          updateRFIDetails({
            ...data,
            response_received: formattedDate,
          })
        );
      } else {
        dispatch(updateRFIDetails(data));
      }
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      notification.error({
        description: updateRes.message,
      });
      setInputValues({ ...inputValues, [field]: rfiDetail[field] });
    }
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };
  function handleStatus(key: string) {
    if (key?.toString() !== rfiDetail?.rfi_status?.toString()) {
      const selectedOption = status?.statusList?.find(
        (option) => option.key === key?.toString()
      );

      const newStatusKey = key?.toString();
      const selectedDate = dayjs(
        rfiDetail?.response_received,
        CFConfig.day_js_date_format
      );
      const currentDate = dayjs().format(CFConfig.day_js_date_format);
      const currentDateapi = dayjs().format("YYYY-MM-DD");
      const formattedDate = rfiDetail?.response_received
        ? backendDateFormat(
            rfiDetail?.response_received,
            CFConfig.day_js_date_format
          )
        : null;

      const today = dayjs().startOf("day");

      if (selectedOption) {
        if (
          key !== "326" &&
          key !== "323" &&
          rfiDetail?.response_received &&
          rfiDetail?.correspondence_key === "correspondence_rfi"
        ) {
          notification.info({
            description:
              "Status set to Received as there is a response received date is set.",
          });
          handleUpdateField({
            rfi_status: 326,
            response_received: formattedDate,
          });
          setInputValues((prev) => ({
            ...prev,
            response_received: selectedDate?.format(
              CFConfig.day_js_date_format
            ),
          }));
        } else if (newStatusKey === "326" && selectedDate?.isBefore(today)) {
          handleUpdateField({
            rfi_status: 326,
            response_received: currentDateapi,
          });
          setInputValues((prev) => ({
            ...prev,
            response_received: currentDate,
          }));
        } else {
          const responseReceivedExists =
            rfiDetail?.response_received &&
            rfiDetail?.response_received.trim() !== "";
          const newResponseReceived =
            key === "326" && !responseReceivedExists
              ? currentDate
              : selectedDate?.format(CFConfig.day_js_date_format);
          const newResponseReceivedapi =
            key === "326" && !responseReceivedExists
              ? currentDateapi
              : formattedDate ?? null;
          handleUpdateField({
            rfi_status: key,
            response_received: newResponseReceivedapi,
          });
          setInputValues((prev) => ({
            ...prev,
            response_received: newResponseReceived,
          }));
        }
      } else {
        const description = "Selected option not found in statusList";
        notification.error({
          description,
        });
      }
    }
  }

  const getStatusBar = (responsive = false) => {
    return (
      <ul
        className={
          "items-center justify-center w-[calc(100%-0px)] hidden " +
          (responsive
            ? "xl:w-[calc(100%-30px)] xl:hidden sm:flex mb-[15px]"
            : "2xl:-ml-3.5 xl:flex")
        }
      >
        {rfiStatusList?.map((item: IStatusList, index: number) => {
          const isActive =
            selectedStatusInd != undefined && selectedStatusInd >= index;
          return (
            <li
              key={index}
              className={`relative 2xl:min-w-[125px] xl:min-w-24 lg:w-24 w-20 grid justify-end first:before:hidden before:absolute before:h-[2px] before:!w-[calc(100%-54px)]  ${
                isActive ? "before:bg-primary-900" : "before:bg-[#ACAEAF]"
              } before:top-[30%] lg:before:left-[-22px] before:left-[-13px]`}
            >
              <ProgressBarHeader
                option={item as unknown as IProgressBarHeaderPropOption}
                isActive={isActive}
                onClick={(data) => {
                  if (!isReadOnly) {
                    if (data?.value) {
                      return handleStatus(data?.value);
                    }
                  } else {
                    return;
                  }
                }}
              />
            </li>
          );
        })}
      </ul>
    );
  };
  const handleReloadDetails = (id: string) => {
    fetchData(true);
  };
  useEffect(() => {
    fetchData();
  }, [pathname, RFI_Id]);

  const fetchData = (isReload: boolean = false) => {
    const tab = pathname.split(RFI_Id + "/")?.[1] || "details";
    if (tab === "details" || tab === undefined) {
      if (isReload) {
        dispatch(setRFILoading(true));
        dispatch(
          getRfiDetail({
            correspondence_id: RFI_Id || "",
            add_event: true,
            custom_correspondence_id: "",
          })
        );
      }
    }
    if (tab != "details" && isReload) {
      dispatch(setRFILoading(true));
      dispatch(
        getRfiDetail({
          correspondence_id: RFI_Id || "",
          add_event: true,
          custom_correspondence_id: "",
        })
      );
    }
    if (tab === "notes") {
      if (isReload) {
        dispatch(setRFILoading(true));
        dispatch(
          fetchRFINotes({
            record_id: Number(RFI_Id || ""),
            module_key: module_key,
          })
        );
      }
    }
    if (tab === "files") {
      if (isReload) {
        dispatch(setRFILoading(true));
        dispatch(
          fetchRfiFilePhoto({
            record_id: parseInt(RFI_Id || ""),
            module_key: module_key,
          })
        );
      }
    }
  };

  return (
    <>
      <div
        className={`ease-in-out duration-300 w-full overflow-y-auto ${
          sidebarCollapse ? "lg:w-[calc(100%-75px)]" : "xl:w-[calc(100%-225px)]"
        }`}
        ref={detailsRef}
      >
        <DetailsTopBar
          sidebarCollapse={sidebarCollapse}
          activeStep={activeStep}
          stepStatusLoaing="success"
          onStepClick={() => {}}
          onReloadDetails={() => handleReloadDetails(RFI_Id?.toString() || "")}
        />
        {!isRFIDetailLoading && (
          <ReadOnlyPermissionMsg className="p-4 pt-0" view={isReadOnly} />
        )}
        <div className={window.ENV.PAGE_IS_IFRAME ? "" : "sm:pb-0 pb-10"}>
          <div
            className={`px-[15px] pb-[15px] ${
              module_access === "read_only"
                ? window.ENV.PAGE_IS_IFRAME
                  ? "md:min-h-[calc(100dvh-196px)] min-h-[calc(100dvh-262px)]"
                  : "md:min-h-[calc(100dvh-343px)] min-h-[calc(100dvh-414px)]"
                : window.ENV.PAGE_IS_IFRAME
                ? "md:min-h-[calc(100dvh-164px)] min-h-[calc(100dvh-210px)]"
                : "md:min-h-[calc(100dvh-308px)] min-h-[calc(100dvh-363px)]"
            }`}
          >
            {isRFIDetailLoading ? (
              <Spin
                className={`flex items-center justify-center ${
                  window.ENV.PAGE_IS_IFRAME
                    ? "md:h-[calc(100vh-161px)] h-[calc(100vh-171px)]"
                    : "md:h-[calc(100vh-304px)] h-[calc(100vh-332px)]"
                }`}
              />
            ) : (
              getStatusBar(true)
            )}
            {children}
          </div>

          <TimeLineFooter
            data={{
              addedDate: rfiDetail?.date_added || "",
              addedTime: rfiDetail?.time_added || "",
              addedBy: rfiDetail?.employee || "",
              moduleId: module_id,
              recordId: Number(RFI_Id),
            }}
            sidebarCollapse={sidebarCollapse}
            isLoading={isRFIDetailLoading}
          />
        </div>
      </div>
    </>
  );
};

export default ManageRfiNoticeTab;

export { ErrorBoundary };
