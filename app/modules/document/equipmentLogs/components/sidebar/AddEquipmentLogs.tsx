// Fontawesome
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Hook
import { useDateFormatter, useTranslation } from "~/hook";
// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
// molecules
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
// organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";
import OnlyRequiredCustomFields from "~/shared/components/organisms/OnlyRequiredCustomFields/OnlyRequiredCustomFields";
// Form
import { Form, useNavigate, useSearchParams } from "@remix-run/react";
import { getGConfig, getGProject, getGSettings } from "~/zustand";
import useSideBarCustomField from "~/shared/hooks/useCustomField/useSidebarCustomField";
import useDirectoryKeyValue from "~/shared/hooks/useCustomField/useDirectoryKeyValue";
import * as Yup from "yup";
import getCustomFieldAccess from "~/shared/utils/helper/getCustomFieldAccess";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useFormik } from "formik";
import { formatCustomFieldForRequest } from "~/shared/utils/helper/customFieldSidebarFormat";
import {
  getApiDefaultParams,
  getFormat,
  getValuableObj,
  Number,
  sanitizeString,
} from "~/helpers/helper";
import {
  EVENT_LOGGER_ACTION,
  EVENT_LOGGER_NAME,
} from "~/shared/constants/event-logger";
import { apiRoutes, routes } from "~/route-services/routes";
import {
  getDirectaryIdByKey,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import { defaultConfig } from "~/data";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
import { useELAppSelector } from "../../redux/store";
import { addEquipmentLog } from "../../redux/action/equipLogDashAction";
import dayjs, { Dayjs } from "dayjs";
import { getProjectDetails } from "~/redux/action/getProjectDetailsAction";
import isEmpty from "lodash/isEmpty";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { displayDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { CidbItemDrawer } from "~/shared/components/molecules/cidbItemDrawer";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";

const AddEquipmentLogs = ({
  drowerOpen,
  setDrowerOpen,
}: IAddEquipmentLogsProps) => {
  const { _t } = useTranslation();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const {
    timezone_utc_tz_id = "",
    user_id = 0,
    display_name = "",
    company_display_name = "",
    type = 0,
    image = "",
  } = user || {};
  const gConfig: GConfig = getGConfig();
  const { date_format }: GSettings = getGSettings();
  const navigate = useNavigate();
  const dateFormat = useDateFormatter();
  const { project_id, project_name }: GProject = getGProject();
  const { module_singular_name, module_id, module_key }: GConfig = getGConfig();
  const { isNoAccessCustomField }: ICustomFieldAccess = getCustomFieldAccess();
  const [selectedProject, setSelectedProject] = useState<IProject[]>([]);
  const [searchParams, setSearchParams] = useSearchParams();
  const [isSubmit, setIsSubmit] = useState<boolean>(false);
  const [isSelectProjectOpen, setIsSelectProjectOpen] =
    useState<boolean>(false);
  const { directoryKeyValue, directory }: IDirectoryFormCustomField =
    useDirectoryKeyValue();
  const [isOpenSelectOperator, setIsOpenSelectOperator] =
    useState<boolean>(false);
  const [additionalContact, setAdditionContact] = useState<number>(0);
  const [isContactDetails, setIsContactDetails] = useState<boolean>(false);
  const [contactId, setcontactId] = useState<number>();
  const isCalledProApiRef = useRef<string | null>(null);
  const [iIsSelectEquipOpen, setIsSelectEquipOpen] = useState<boolean>(false);
  const { itemTypes } = useELAppSelector((state) => state.itemTypes);
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const userAgent = navigator.userAgent || "";
  const isAndroid = userAgent.toLowerCase().includes("android");
  const isiOS = /iPhone|iPad|iPod/i.test(userAgent);
  const currentDateTime = useMemo(() => {
    try {
      const testDate = dayjs(new Date()).tz(timezone_utc_tz_id);
      return testDate.format("z") !== "Invalid Date"
        ? dayjs().tz(timezone_utc_tz_id)
        : dayjs();
    } catch (e) {
      return dayjs();
    }
  }, [timezone_utc_tz_id]);

  const { componentList, loadingCustomField, setLoadingCustomFields } =
    useSideBarCustomField(
      { directory, directoryKeyValue } as IDirectoryFormCustomField,
      {
        moduleId: module_id,
      } as IRequestCustomFieldForSidebar
    );

  const initValues: IaddEquipmentLogData = {
    equipment_id: null,
    operator_id: null,
    operator_type: null,
    operator_name: "",
    operator_image: "",
    project_id: "",
    operator_company_name: "",
    used_date: currentDateTime.format(date_format),
    equipment_name: "",
  };

  const dynamicValidationSchema = componentList.reduce((acc, fieldName) => {
    if (fieldName.multiple || fieldName.type === "checkbox-group") {
      acc[fieldName.name] = Yup.array()
        .of(Yup.string().required("This field is required."))
        .min(1, "This field is required.")
        .required("This field is required.");
    } else {
      acc[fieldName.name] = Yup.string().required("This field is required.");
    }
    return acc;
  }, {} as Record<string, Yup.StringSchema | Yup.AnySchema>);

  const baseValidationSchema = {
    project_id: Yup.string().required("This field is required."),
    equipment_id: Yup.number().required("This field is required."),
  };

  const validationSchema =
    componentList.length && !isNoAccessCustomField
      ? Yup.object().shape({
          ...baseValidationSchema,
          custom_fields: Yup.object().shape(dynamicValidationSchema),
        })
      : Yup.object().shape({
          ...baseValidationSchema,
        });

  const initialFormValues =
    componentList.length && !isNoAccessCustomField
      ? {
          ...initValues,
          custom_fields: componentList.reduce((acc, item) => {
            acc[item.name] = item?.value ?? "";
            return acc;
          }, {} as ICustomFieldInitValue),
        }
      : initValues;

  const formik = useFormik({
    initialValues: initialFormValues,
    validationSchema: validationSchema,
    validateOnChange: false,
    enableReinitialize: true,
    onSubmit: async (values, { setSubmitting }) => {
      if (hasSubmitted) return;
      setHasSubmitted(true);
      setIsSubmit(true);

      try {
        const formData = {
          ...values,
          custom_fields:
            values.custom_fields && !isNoAccessCustomField
              ? formatCustomFieldForRequest(
                  values.custom_fields,
                  componentList,
                  date_format
                ).custom_fields
              : undefined,
          access_to_custom_fields:
            componentList.length && !isNoAccessCustomField ? 1 : 0,
        };

        const addEquipmentLogData: Partial<IAddEquipmentLog> = {
          equipment_id: formData.equipment_id ?? 0,
          operator_id: formData.operator_id ?? 0,
          project_id: formData.project_id,
          access_to_custom_fields: formData.access_to_custom_fields,
          custom_fields: formData.custom_fields,
          used_date: formData.used_date
            ? dateFormat({
                date: formData.used_date,
                dateFormat: getFormat(date_format),
                format: "yyyy-MM-dd",
              })
            : "",
        };

        const resData = (await addEquipmentLog(
          getValuableObj(addEquipmentLogData)
        )) as Partial<IResponse<IAddEquipmentApiRes>>;

        if (resData.success && resData.data?.inserted_id) {
          EventLogger.log(
            EVENT_LOGGER_NAME.equipment_logs + EVENT_LOGGER_ACTION.added,
            1
          );
          navigate(
            `${routes.MANAGE_EQUIPMENT_LOGS.url}/${resData.data.inserted_id}${
              isAndroid ? "?from=android" : isiOS ? "?from=ios" : ""
            }`
          );
        } else {
          notification.error({
            description: resData?.message || "Something went wrong!",
          });
          setHasSubmitted(false);
        }
      } catch (err) {
        notification.error({ description: "Something went wrong!" });
        setHasSubmitted(false);
      } finally {
        setSubmitting(false);
        setIsSubmit(false);
      }
    },
  });

  const { handleSubmit, setFieldValue, values } = formik;

  useEffect(() => {
    if (drowerOpen) {
      setLoadingCustomFields(true);
      setTimeout(() => setLoadingCustomFields(false), 3000);
    }
  }, [drowerOpen]);

  const onChangeProject = (projects: IProject[]) => {
    setSelectedProject(projects);
    if (projects.length) {
      setFieldValue("project_id", projects[0].id);
    } else {
      setFieldValue("project_id", null);
    }
  };

  useEffect(() => {
    if (
      searchParams.get("action")?.trim() === "new" &&
      searchParams.get("project")
    ) {
      if (isCalledProApiRef.current !== searchParams.get("project")) {
        isCalledProApiRef.current = searchParams.get("project");

        (async () => {
          try {
            const proResApi = (await getProjectDetails({
              start: 0,
              limit: 1,
              projects: searchParams.get("project") || "",
              need_all_projects: 0,
              global_call: true,
              is_completed: true,
              filter: { status: "0" },
            })) as IProjectDetailsRes;

            const queryPro = proResApi?.data?.projects[0];
            setFieldValue("project_id", queryPro?.id);
            setSelectedProject([
              {
                id: Number(queryPro?.id),
                project_name: queryPro?.project_name,
              },
            ]);
          } catch (e) {}
        })();
      }
    } else {
      if (
        project_id &&
        project_id !== "0" &&
        isEmpty(searchParams.get("project"))
      ) {
        setFieldValue("project_id", Number(project_id));
        setSelectedProject([
          {
            id: Number(project_id),
            project_name: project_name,
          },
        ]);
      } else {
        setFieldValue("project_id", null);
        setSelectedProject([]);
      }
    }
  }, [
    drowerOpen,
    project_id,
    project_name,
    isCalledProApiRef,
    searchParams.get("action"),
    searchParams.get("project"),
    componentList,
  ]);

  useEffect(() => {
    if (!loadingCustomField) {
      setFieldValue("operator_name", display_name);
      setFieldValue("operator_id", user_id);
      setFieldValue("operator_type", type);
      setFieldValue("operator_company_name", company_display_name);
      setFieldValue("operator_image", image);
    }
  }, [
    display_name,
    type,
    company_display_name,
    drowerOpen,
    loadingCustomField,
    isCalledProApiRef,
    componentList,
  ]);

  const handleOperator = (data: Partial<TselectedContactSendMail>) => {
    if (data?.user_id !== undefined) {
      if (data?.user_id != values.operator_id) {
        setFieldValue("operator_id", data?.user_id);
        setFieldValue("operator_name", data?.display_name);
        setFieldValue(
          "operator_type",
          data?.orig_type?.toString() ||
            getDirectaryIdByKey(data.type_key as CustomerTabs, gConfig)
        );
        setFieldValue("operator_image", data?.image);
      }
    } else {
      setFieldValue("operator_id", null);
      setFieldValue("operator_name", "");
      setFieldValue("operator_type", null);
    }
  };

  const onOrderDateChange = (date: Dayjs | DateValue | null) => {
    if (!Array.isArray(date)) {
      const orderDate = !!date ? date?.format(date_format) : "";
      setFieldValue("used_date", orderDate);
    } else {
      setFieldValue("used_date", "");
    }
  };

  const handleCloseDrawer = () => {
    setDrowerOpen(false);
    setSelectedProject([]);
    formik.resetForm();
    formik.setErrors({});
    setIsSubmit(false); // Reset other states
    setSearchParams({}, { replace: true });

    let desiredFrom = null;
    if (isAndroid) {
      desiredFrom = "android";
    } else if (isiOS) {
      desiredFrom = "ios";
    }

    if (desiredFrom) {
      const newParams = new URLSearchParams(searchParams);
      newParams.set("from", desiredFrom);
      setSearchParams(newParams);
    } else if (!isAndroid && !isiOS) {
      const newParams = new URLSearchParams(searchParams);
      newParams.delete("from");
      setSearchParams(newParams);
    }
  };
  const getCustomer = async (usersIds: string, contactIDs?: string) => {
    let customer: Partial<CustomerSelectedData> = {};
    const data = await getApiDefaultParams({
      otherParams: {
        directory: [2, 3, 4, 22, 23, 204],
        directories: usersIds,
        start: 0,
        sales_address_details: 1,
        limit: 10,
        app_users: false,
        service_details: undefined,
        contacts_on_demand: 1,
      },
    });

    const response = (await webWorkerApi<IgetTagsRes>({
      url: apiRoutes.COMMON.get_global_directory,
      method: "post",
      data: data,
    })) as IGetGlobalDirectoryRes;
    if (response?.success) {
      customer = response?.data[0] || {};
    }
    return customer;
  };
  const handleSelectEquipment = useCallback(
    async (selectedItem: TExntendedEquipType[]) => {
      if (selectedItem.length) {
        const item = selectedItem[0];

        if (
          item.equipment_id &&
          item.equipment_id?.toString() === values.equipment_id?.toString()
        ) {
          setFieldValue("equipment_id", "");
          setFieldValue("equipment_name", "");
          setFieldValue("operator_id", user_id);
          setFieldValue("operator_name", display_name);
        } else {
          setFieldValue("equipment_id", item.equipment_id || item.id);
          setFieldValue("equipment_name", item.name);
          if (item.operator_id) {
            setFieldValue("operator_id", item.operator_id);
            setFieldValue("operator_type", item.operator_type);
            const newCustomer = await getCustomer(item.operator_id?.toString());
            setFieldValue(
              "operator_image",
              newCustomer?.image ? newCustomer?.image : null
            );
          } else {
            setFieldValue("operator_id", user_id);
            setFieldValue("operator_type", type);
            setFieldValue("operator_image", image ? image : null);
          }
          if (item.operator_name) {
            setFieldValue("operator_name", item.operator_name);
          } else {
            setFieldValue("operator_name", display_name);
          }
        }
      }
    },
    [values.equipment_id, values.operator_id, user_id, display_name]
  );

  const selectedEquipment = useMemo(() => {
    const items = [
      {
        name: values.equipment_name || "",
        // reference_item_id: details?.reference_item_id,
        // item_id: details?.item_id as string,
        equipment_id: values?.equipment_id || 0,
        item_type: defaultConfig.equipment_teb_id?.toString(),
        type_name: "Equipment",
      },
    ];

    return items;
  }, [values]);

  return (
    <>
      <Drawer
        open={drowerOpen}
        rootClassName="drawer-open"
        width={718}
        maskClosable={false}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-forklift"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t("Add " + module_singular_name)}
            </Header>
          </div>
        }
        closeIcon={<CloseButton onClick={() => handleCloseDrawer()} />}
      >
        <Form method="post" onSubmit={handleSubmit} noValidate className="py-4">
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="flex flex-col gap-5">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <ButtonField
                    label={_t("Equipment")}
                    name="equipment_id"
                    id="equipment_id"
                    key="equipment_id"
                    onClick={() => setIsSelectEquipOpen(true)}
                    required={true}
                    labelPlacement="top"
                    value={HTMLEntities.decode(
                      sanitizeString(values.equipment_name || "")
                    )}
                    errorMessage={
                      formik.touched?.equipment_id &&
                      !formik.values.equipment_id
                        ? formik.errors.equipment_id
                        : ""
                    }
                  />
                </div>
                <div className="w-full">
                  <ButtonField
                    label={_t("Location/Project")}
                    name="location_project"
                    id="project_id"
                    key="project_id"
                    labelPlacement="top"
                    onClick={() => setIsSelectProjectOpen(true)}
                    required={true}
                    value={
                      selectedProject.length
                        ? HTMLEntities.decode(
                            sanitizeString(selectedProject[0]?.project_name)
                          )
                        : ""
                    }
                    errorMessage={
                      formik.touched?.project_id && !formik.values.project_id
                        ? formik.errors.project_id
                        : ""
                    }
                    addonBefore={
                      selectedProject.length &&
                      !isNaN(Number(selectedProject[0]?.id)) ? (
                        <ProjectFieldRedirectionIcon
                          projectId={`${selectedProject[0]?.id}`}
                        />
                      ) : null
                    }
                  />
                </div>
                <div className="w-full">
                  <ButtonField
                    label={_t("Operator")}
                    name="operator"
                    labelPlacement="top"
                    value={HTMLEntities.decode(
                      sanitizeString(values.operator_name || "")
                    )}
                    onClick={() => {
                      setIsOpenSelectOperator(true);
                    }}
                    avatarProps={{
                      user: {
                        name: HTMLEntities.decode(
                          sanitizeString(values?.operator_name || "")
                        ),
                        image: values?.operator_image,
                      },
                    }}
                    addonBefore={
                      <>
                        {values.operator_id &&
                        Number(values.operator_id) !== 0 ? (
                          <div className="flex items-center gap-1">
                            <ContactDetailsButton
                              onClick={async (e) => {
                                e.stopPropagation();
                                await setAdditionContact(Number(0));
                                setIsContactDetails(true);
                                setcontactId(values.operator_id as number);
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              directoryId={values.operator_id?.toString()}
                              directoryTypeKey={
                                values.operator_type
                                  ? getDirectaryKeyById(
                                      values.operator_type === 1
                                        ? 2
                                        : Number(values.operator_type),
                                      gConfig
                                    )
                                  : ""
                              }
                            />
                          </div>
                        ) : (
                          <></>
                        )}
                      </>
                    }
                  />
                </div>
                <div className="w-full">
                  <DatePickerField
                    label={_t("Log Date")}
                    labelPlacement="top"
                    placeholder=""
                    name="used_date"
                    id="used_date"
                    allowClear={true}
                    value={displayDateFormat(
                      values.used_date?.toString().trim(),
                      date_format
                    )}
                    onChange={(date) => {
                      onOrderDateChange(date);
                    }}
                    format={date_format}
                  />
                </div>
              </SidebarCardBorder>
              <OnlyRequiredCustomFields
                componentList={componentList}
                formik={formik}
                isSubmit={isSubmit}
                loadingCustomField={loadingCustomField}
              />
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              onClick={() => {
                setIsSubmit(true);
              }}
              buttonText={_t(`Create ${module_singular_name}`)}
              isLoading={formik.isSubmitting}
              disabled={formik.isSubmitting || loadingCustomField}
            />
          </div>
        </Form>
      </Drawer>

      {isOpenSelectOperator && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectOperator}
          closeDrawer={() => {
            setIsOpenSelectOperator(false);
          }}
          singleSelecte={true}
          setCustomer={(data) => {
            handleOperator(
              data.length ? (data[0] as Partial<TselectedContactSendMail>) : {}
            );
          }}
          options={[defaultConfig.employee_key, "my_crew", "my_project"]}
          selectedCustomer={
            values.operator_id
              ? ([
                  {
                    display_name: values?.operator_name,
                    user_id: values?.operator_id,
                    type: values.operator_type,
                    type_key: getDirectaryKeyById(
                      values.operator_type === 1
                        ? 2
                        : Number(values.operator_type || 2),
                      gConfig
                    ),
                    image: values.operator_image,
                  },
                ] as TselectedContactSendMail[])
              : []
          }
          groupCheckBox={true}
          projectId={Number(values?.project_id) as number}
          additionalContactDetails={0}
        />
      )}

      {isContactDetails && (
        <ContactDetailsModal
          isOpenContact={isContactDetails}
          onCloseModal={() => {
            setcontactId(0);
            setIsContactDetails(false);
          }}
          contactId={contactId}
          additional_contact_id={additionalContact}
        />
      )}

      {isSelectProjectOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProjectOpen}
          setOpen={setIsSelectProjectOpen}
          genericProjects="project"
          category="project_location"
          selectedProjects={selectedProject}
          onProjectSelected={(data) => {
            onChangeProject(data);
          }}
          isRequired={false}
          module_key={module_key}
        />
      )}

      {iIsSelectEquipOpen && (
        <CidbItemDrawer
          closeDrawer={() => {
            setIsSelectEquipOpen(false);
          }}
          options={["equipment"]}
          singleSelecte={true}
          addItem={(data) => {
            handleSelectEquipment(data as TExntendedEquipType[]);
          }}
          itemTypes={itemTypes.map((item) => {
            return {
              ...item,
              default_color: item.default_color?.toString(),
            };
          })}
          openSendEmailSidebar={iIsSelectEquipOpen}
          data={
            values.equipment_id
              ? (selectedEquipment as Partial<CIDBItemSideData>[])
              : []
          }
          isViewSaveAndAddAnother={false}
          cidbModuleVIseIdAndValue={{
            [defaultConfig.equipment_key]: {
              id: 162,
              value: defaultConfig.equipment_key,
            },
          }}
        />
      )}
    </>
  );
};

export default AddEquipmentLogs;
