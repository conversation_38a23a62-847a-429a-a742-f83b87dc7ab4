import { Draft } from "@reduxjs/toolkit";

declare global {
  interface IFilePhotoLeftInitialState {
    folderList: ISelectedFolder[];
    folderListForMoveCopyDrawer: ISelectedFolder[];
    isFolderLoading: boolean;
    selectedFolder: ISelectedFolder;
    subFolderList: ISelectedFolderChildren[];
    subFolderListForDrawer: ISelectedFolderChildren[];
    isSubFolderLoading: boolean;
    isSubFolderLoadingInDrawer: boolean;
    activeFolders: IActiveFolder[];
    removedFromActiveFolders: IActiveFolder[];
    activeFoldersInDrawer: IActiveFolder[];
    removedFromActiveFoldersInDrawer: IActiveFolder[];
    selectedFolderForMoveOrCopy: ISelectedFolder;
    isMoveCopyLoading: boolean;
    callAgainFolderList: number;
    selectedFolderInDrawer?: ISelectedFolder;
    parentFolderName: string;
  }

  interface ActiveFolderItemType {
    [key: string]: {
      module_name: string;
      module_id: string;
      module_key: string;
      folder_name: string;
      folder_id: string;
      next_request_type: string;
      file_count: string;
      project_name: string;
      total_file_count: string;
      total_subfolder_count: string;
      display_name: string;
      project_id: number;
      isCollapse?: boolean | undefined;
    };
  }

  // Array type containing objects of the defined type
  type HeaderPathItem = {
    [key: string]: ActiveFolderItemType;
  };

  type activeFolderItem = {
    [key: string]: ItemType;
  };

  interface IFolderUpdateApiRes extends Omit<IDefaultAPIRes, "data"> {
    data: {
      date_modified: string;
      folderId: number;
      folder_name: string;
      project_id: number;
      parentFolderId?: string | number;
    };
    message: string;
    statusCode: number;
    success: boolean;
  }

  interface IFolderDeleteApiRes extends Omit<IDefaultAPIRes, "data"> {
    data: ObjType;
    message: string;
    statusCode: number;
    success: boolean;
  }

  interface IFilePhotoDeleteApiRes extends Omit<IDefaultAPIRes, "data"> {
    data: ObjType;
    message: string;
    statusCode: number;
    success: boolean;
  }

  interface IFilePhotoUpdateApiRes extends Omit<IDefaultAPIRes, "data"> {
    data: IFilePhotoUpdateApiResData;
    message: string;
    statusCode: number;
    success: boolean;
  }
  interface IFilePhotoUpdateApiResData {
    files: FilePhotoRightDetail[];
  }
  interface IDeleteApiParam {
    folderId?: number | string;
  }

  interface IUpdateApiParam {
    folderId?: number | string;
    folderName?: string;
    projectId?: number | string;
  }

  interface ICopyOrMoveApiParam {
    sourceFolderId?: number;
    targetModuleId: number;
    targetFolderId: number;
    targetProjectId: number;
    isStaticFolder: boolean;
    sourceFileId?: number;
  }

  interface IGetFolderListParam {
    projectId?: string | number;
    search?: string;
    page?: number;
    length?: number;
    type?: string | number;
    globalProject: string;
    allProjectCheck: number;
    version: string;
    from: string;
    moduleId?: string | null | number;
    folderId?: string | null | number;
    filter?: {
      tags?: number[] | string;
      customer?: number | string;
      extension?: string;
      project?: number | string;
      tagsText?: string;
    };
  }

  interface IGetFolderRes extends Omit<IDefaultAPIRes, "data"> {
    data: ISelectedFolder;
    assigned_projects: string;
    clickedFolder: ISelectedFolder;
    subFolderList: [];
  }
  interface ICreateProjectParam {
    projectIds?: number[];
    parentProjectId?: string | number;
    folderName?: string;
    parentFolderId?: string | number;
    parentModuleId?: string | number;
  }

  interface IProjectsListData {
    handleSearch?: () => void;
    zipFile?: [];
    istagLoading?: boolean;
    projectsList: {
      data: IProject[];
      infiniteScrollhasMore: boolean;
      error: boolean;
    };
    filePhotosProjectsList: {
      data: IProjectList[];
      infiniteScrollhasMore: boolean;
      error: boolean;
    };
    isfilePhotosProjectsLoading: boolean;
    selectedProject?: null | IProject | undefined;
  }
  interface IFilePhotoHeaderInitialState {
    filePhotosProjectsList: IProjectList[];
    isfilePhotosProjectsLoading: boolean;
  }

  interface IGetProjectListRes extends Omit<IDefaultAPIRes, "data"> {
    data: IProjectList[];
  }
  interface IFileResponse {
    message: string;
    success: boolean;
  }

  //update project interface
  interface IUpdateFilesRequestBody {
    fileUrl?: string[] | undefined;
    from?: string;
    fileIds?: number[] | undefined;
    projectId?: number | string;
    file_tags?: string[];
    fileName?: string;
    notes?: string;
    isFileShared?: number;
    selectedProject?: string;
    calledFromOutside?: number;
    fileTags?: string[] | undefined;
  }

  interface IDeleteFilesRequestBody {
    fileUrl?: string[];
  }

  interface IFileDetailParam {
    fileId: number;
  }
  interface IUpdateFilesData {
    dataCount: number;
    files: File[];
  }
  interface IUpdateFile {
    image_id: number;
    project_id: number;
    type: string;
    type_id: string | number;
    file_ext: string;
    file_path: string;
    title: string;
    notes: string;
    is_deleted: number;
    date_added: string;
    date_modified: string;
    module_id: number;
    primary_id: number;
    file_tags: string;
    child_item_id: number;
    user_id: number;
    company_id: number;
    file_type: string;
    is_image: number;
    parent_image_id: string | number;
    demo_data: number;
    height: number;
    width: number;
    size: string | number;
    mode: string;
    child_item_backup: string;
    child_item_backup12: string;
    upload_from: string;
    image_res: string;
    camera_res: string;
    file_name: string;
    thumb_flag: number;
    large_flag: number;
    static_folder: number;
    folder_id: number;
    refrence_img_id: number;
    quickbook_attachable_id: string;
    is_common_notes: number;
    is_bidding_file: number;
    child_item_name: string;
    file_save_when_send_email: number;
    is_file_shared: number;
    annotation_data: string;
    original_file_path: string;
    is_google_drive_file: number;
    companycam_photo_id: number;
    companycam_creator_name: string;
    original_image_id: number;
    is_project_template: number;
    project_template_id: string | number;
    project_name: string;
    module_name: string;
    module_key: string;
    time_added: string;
  }

  interface IUpdateFilesRes extends Omit<IDefaultAPIRes, "data"> {
    data: IUpdateFilesData[];
  }

  interface ISelectedProject {
    selectedProject: string;
  }

  interface IFilePhotoViewSlice {
    currentView: "folder" | "timeline";
    folderView: {
      viewAllFiles: boolean;
      fileStructure: "thumbnail" | "detail";
    };
    timelineView: {
      fileStructure: "thumbnail" | "detail";
    };
  }

  interface DataPayload {
    sortOrder: string;
    type: "folder" | "timeline";
    files?: FilePhotoRightDetail[];
    filesTimeline?: Record<string, FilesTimelineDateId>;
    filesCount: string;
  }

  interface IFilePhotoPayload {
    message: string;
    statusCode: number;
    success: boolean;
    data: DataPayload;
  }

  interface IFilePhotoRight {
    primary_id?: number;
    project_id?: number;
    sortOrder?: string;
    image_id?: number | string;
    original_image_id?: number | string;
    selectedFiles: FilePhotoRightDetail[];
    selectedAllFiles: boolean;
    isfileLoading: boolean;
    uploadFileList: IUploadFileList[];
    infiniteScrollHasMore?: boolean;
    singleFile?: {};
    headerDebounceSearch: string;
    search: string;
    fileList: {
      infiniteScrollhasMore: boolean;
      error: boolean;
      data: FilePhotoRightDetail[];
      sortOrder: string;
      page: number;
      sort: "by_date" | "by_name" | "";
      changeSort: number;
      filesCount?: string | number;
    };
    fileTimelineList: {
      infiniteFlieTimelineScrollhasMore: boolean;
      error: boolean;
      data: FilesTimeline;
      page: number;
    };
    singleFileDetails: ISingleFileDetail | {};
    imageData: FilePhotoRightDetail;
    addOrEditFileOpen: boolean;
    isMarkupModalOpen: boolean;
    isConfirmModalOpen: boolean;
    editView: boolean;
    sendEmailDrawerOpen: boolean;
    data?: FilePhotoRightTimeline | {};
    page?: number;
    filter: IFiledFilePhotoFilter;
    callAgainFileList: number;
    fileListCount: number;
    onSuccessForDetailView: boolean;
    detailViewLength: number;
    isPunchListPdfViewOpen: boolean;
    selectedFileTitle: string;
    selectedFileUrl: string;
    isCommonModalOpen: boolean;
    selectedFilePath: string;
  }

  interface IUploadFileList {
    loading: boolean;
    file_path: string | undefined;
    error?: boolean;
  }

  interface IFileUploadListURL {
    file_url: string;
  }

  interface IaddUploadFileList {
    files: IselectedFilesUpload[];
  }

  interface ItoggleLoadingErrorUploadFileList extends IaddUploadFileList {
    loading: boolean;
    error?: boolean;
  }

  interface FilePhotoRightTimeline {
    assigned_projects: string;
    fileTimelineList: FilesTimeline;
  }

  interface FilesTimeline {
    [key: string]: FilesTimelineDateId;
  }

  interface FilesTimelineDateId {
    [key: string]: IFilesTimelineFileList;
  }

  interface IFilesTimelineFileList {
    files: FilePhotoRightDetail[];
    userInfo: {
      name: string;
      user_icon: string;
    };
  }

  interface IFileListProps {
    infiniteScrollhasMore: boolean;
    error: boolean;
    data: FilePhotoRightDetail[];
  }

  interface FilesDefaultViewProps {
    title?: string;
    loadMore?: () => void;
    hasMore?: boolean;
    isfileLoading?: boolean;
    mobileMenu?: IFilePhotoLeftSideProps["mobileMenu"];
    infiniteScrollHideLoadingComponent?: boolean;
    isInfiniteScrollLoading?: boolean;
    fileList?: IFileListProps;
    changeAddOrEditFileOpen?: (
      { addOrEditFileOpen, editView }: IActionPayloadSetAddOrEditFileOpenRedux,
      imageData: FilePhotoRightDetail
    ) => void;
    changeMarkupModelOpen?: (
      value: boolean | "previewUrl",
      imageData: FilePhotoRightDetail
    ) => void;
    changeConfirmModelOpen?: (
      value: boolean,
      imageData: FilePhotoRightDetail
    ) => void;
    changeSendEmailDrawerOpen?: {
      (value: boolean, imageData: FilePhotoRightDetail): void;
    };
    handleClose?: IFilePhotoLeftSideProps["handleClose"];
    markupedFileData?: Partial<IFile>;
  }
  interface IFilePhotoRightFolder {
    assigned_projects: string;
    length: number;
    error: boolean;
    infiniteScrollhasMore: boolean;
    data: FilePhotoRightDetail[];
    selectedFiles: FilePhotoRightDetail[];
    selectedAllFiles: boolean;
    fileListCount: number;
    isfileLoading: boolean;
    uploadFileList: IUploadFileList[];
    infiniteScrollHasMore: boolean;
    singleFile: {};
    fileList: {
      infiniteScrollhasMore: boolean;
      error: boolean;
      data: IFilePhotoRightFolder[];
    };
    singleFileDetails?: ISingleFileDetail | {};
  }
  interface FilesTimelineProps {
    loadMore: () => void;
    hasMore: boolean;
    infiniteScrollHideLoadingComponent: boolean;
    isInfiniteScrollLoading: boolean;
    changeAddOrEditFileOpen?: (
      { addOrEditFileOpen, editView }: IActionPayloadSetAddOrEditFileOpenRedux,
      imageData: FilePhotoRightDetail
    ) => void;
    changeMarkupModelOpen?: (
      value: boolean | "previewUrl",
      imageData: FilePhotoRightDetail
    ) => void;
    changeConfirmModelOpen?: (
      value: boolean,
      imageData: FilePhotoRightDetail
    ) => void;
    changeSendEmailDrawerOpen?: {
      (value: boolean, imageData: FilePhotoRightDetail): void;
    };
  }

  interface IActionPayloadSetAddOrEditFileOpenRedux {
    addOrEditFileOpen?: boolean;
    editView?: boolean;
  }
  interface FilePhotoRightDetail {
    file_url?: string;
    camera_res?: string;
    attach_item_id?: number;
    annotation_data?: string;
    original_pdf_url?: string;
    projectId?: number;
    mode?: string;
    image?: string;
    name?: string;
    fileName?: string;
    file_ext: string;
    file_name: string;
    file_path: string;
    file_save_when_send_email?: number;
    file_tags?: string;
    file_type?: number;
    folder_id: number | string;
    image_id?: number | string;
    image_res?: string;
    is_bidding_file?: number;
    is_common_notes?: number;
    is_file_shared?: number;
    is_google_drive_file?: number;
    is_image?: number;
    module_id?: number;
    notes?: string;
    original_file_path?: string;
    original_image_id?: number | string;
    primary_id?: number;
    project_id?: number;
    quickbook_attachable_id?: number;
    static_folder?: number;
    user_id?: number;
    size?: string;
    project_name?: string;
    module_name?: string;
    module_key?: string;
    date_added?: string;
    time_added?: string;
    tag_names?: string;
    employee_name?: string;
    user_image?: string;
    record_type?: string;
    original_url?: string;
    is_new_mk_file?: boolean;
    signedUrl?: string;
    image_res: string;
    show_client_access?: number;
    isheic?: number | boolean;
    thumb_file_path?: string;
  }

  interface IGetFileListParam {
    start?: number;
    is_completed?: boolean;
    projects?: string;
    page: number;
    search?: string;
    limit: number;
    type?: number | string;
    allProjectCheck?: number;
    from?: string;
    module_id?: string | null | number;
    isTimelineView?: number;
    clientOnlyFiles?: boolean;
    onlyImage?: number;
    staticFolder?: number;
    projectId?: number | string;
    folderId?: string | null | number;
    sortOrder?: string;
    filter?: {
      tags?: number[] | string;
      customer?: number | string;
      extension?: string;
      project?: number | string;
      tagsText?: string;
    };
  }
  interface IFolderMoveCopyApiRes extends Omit<IDefaultAPIRes, "data"> {
    data: object;
    message: string;
    statusCode: number;
    success: boolean;
  }
  interface IUpdateDeleteListApiRes extends Omit<IDefaultAPIRes, "data"> {
    data: ObjType;
  }

  interface IToggleSelectedFiles {
    selectedFile: FilePhotoRightDetail;
    is_file_checked: boolean;
    currentView: "folder" | "timeline";
  }

  interface ISelectedAllFiles {
    checked?: boolean;
    currentView?: "folder" | "timeline";
    fileStructure?: "thumbnail" | "detail";
  }

  interface IFileUploadApiResponseData {
    statusCode: number;
    message: string;
    success: string;
    data: {
      signedUrl: string;
      cdnUrl: string;
      fileName: string;
      fileUrl: string;
      thumbCdnUrl?: string;
    };
  }

  interface FileUrlInAws {
    file_path?: string;
    file_url?: string;
    is_image?: boolean | undefined | number;
    file_name?: string;
    file_ext?: string;
    thumb_file_path?: string;
    moduleName?: string;
    signedUrl?: string;
    isheic?: number;
  }

  interface IAddFileDataToServer {
    files: FileUrlInAws[];
    module_id: number | string | undefined;
    module_key: "projects";
    is_google_drive_file?: number;
    project_id: number | string | undefined;
    folder_id?: number | string | undefined;
    file_tags?: string | undefined;
  }
  interface IFileAddFilesResponseData {
    statusCode: number;
    message: string;
    success: boolean;
    data: {
      add_to_sub_folder: number;
      zapier_resp: {
        company_id: number;
        user_id: number;
        file_id: number;
        req_type: string;
      };
      aws_files: FilePhotoRightDetail[];
    };
  }

  interface IActiveType {
    project_id: number | string;
    project_name: string;
    next_request_type: string;
    module_id?: number | string;
    folder_id?: number | string;
    folder_name?: string;
    parent_folder_id1?: number;
    parent_folder_mame?: string;
    parent_id_module?: number;
    parent_id_folder?: number | string;
    total_file_count?: string;
    total_subfolder_count?: string;
    display_name?: string;
    module_name?: string;
    module_key?: string;
    file_count?: string;
    parent_folder_id?: string;
    parent_folder_name?: string;
    isCollapse?: boolean;
  }

  interface IFolder {
    next_request_type: string;
    project_id: number;
    module_id?: number;
    folder_id?: number;
  }

  interface IActiveFolder {
    [key: string]: IActiveType;
  }

  interface ISelectedFolderValue {
    [key: string]: any;
  }
}
