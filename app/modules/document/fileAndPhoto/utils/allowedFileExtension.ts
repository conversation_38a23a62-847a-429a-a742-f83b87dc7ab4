// Note : extension commented after getting updated extension list from <PERSON><PERSON><PERSON><PERSON>s
// gif|jpe?g|png|txt|mp4|heic|pdf|xls|xlsx|xlsm|docx?|pptx?|zip|gz|rar|msg|webp|kmz|csv|ppt|dwg|avi|wmv|mkv|mov|webm|avchd|flv|mts|m2ts|mp3

export const acceptedFileTypes = {
  "image/gif": [".gif"],
  "image/jpg": [".jpg"],
  "image/jpeg": [".jpeg"],
  "image/png": [".png"],
  "text/plain": [".txt"],
  "video/mp4": [".mp4"],
  "image/heic": [".heic"],
  "application/pdf": [".pdf"],
  "application/vnd.ms-excel": [".xls"],
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
    ".xlsx",
  ],
  "application/vnd.ms-excel.sheet.macroEnabled.12": [".xlsm"],
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [
    ".docx",
  ],
  "application/zip": [".zip"],
  "application/x-gzip": ["gz"],
  "application/vnd.rar": [".rar"],
  "application/vnd.ms-outlook": [".msg"],
  "image/webp": [".webp"],
  "application/vnd.google-earth.kmz": [".kmz"],
  "text/csv": [".csv"],
  "application/vnd.ms-powerpoint": ["ppt"],
  "application/vnd.openxmlformats-officedocument.presentationml.presentation": [
    ".pptx",
  ],
  "application/acad": [".dwg"], // Add support for DWG files
  "application/x-acad": [".dwg"],
  "video/x-msvideo": [".avi"], // AVI
  "video/x-ms-wmv": [".wmv"],
  "video/wmv": [".wmv"],
  "video/x-matroska": [".mkv"], // MKV
  "video/quicktime": [".mov"],
  "video/webm": [".webm"], // WEBM
  "video/x-flv": [".flv"],
  // "video/avchd-stream": [".m2ts"], // AVCHD removing as it's not supported in PHP
  // "video/avchd-stream": [".mts"], // AVCHD
  "audio/mp3": [".mp3"],
  "audio/mpeg": [".mp3"], // Add this line for MP3 support
  // "image/tiff": [".tiff"], removed as it's not in PHP
  // "image/svg+xml": [".svg"],
  // "application/rtf": [".rtf"],
  // "application/vnd.oasis.opendocument.spreadsheet": [".ods"],
  // "application/vnd.sun.xml.calc": [".sxc"],
  // "text/tab-separated-values": [".tsv"],
  // "text/html": [".html", ".htm"],
  // "application/msword": [".doc"],
  // "text/plain": [".txt", ".dif"],
  // "image/heif": [".heif"],
  // "image/heic-sequence": [".heics"],
  // "application/vnd.oasis.opendocument.text": [".odt"],
  // "application/x-vnd.oasis.opendocument.text": [".odt"],
};

export const acceptedFileTypesInNoAccessRole = {
  "image/heic": [".heic"],
  "image/jpg": [".jpg"],
  "image/jpeg": [".jpeg"],
  "image/png": [".png"],
  "image/gif": [".gif"],
  "image/webp": [".webp"],
};

export const imageExtensions = [
  // "heif",
  "jpg",
  "jpeg",
  "png",
  "gif",
  "webp",
  "heic",
  // "heic-sequence",
  // "bmp",
  // "jfif",
  // "tiff",
  // "svg",
];

export const allowUploadExtensions = [
  // Image
  ...imageExtensions,
  // old
  // Document
  // "pdf",
  // "doc",
  // "docx",
  // "xls",
  // "xlsx",
  // "ppt",
  // "pptx",
  // "txt",
  // "csv",
  // "rtf",
  // "ods",
  // "tsv",
  // "sxc",
  // "html",
  // "zip",
  // "rar",
  // "dwg",
  // "odt",
  // "msg",
  // "kmz",
  // "dwg",
  // // video and audio
  // "mp4",
  // "mp3",
  // "wav",
  // "avi",
  // "mov",
  // "flv",
  // "wmv",
  // "3gp",
  // "mkv",
  // "webm",
  // "mkv",
  // "m2ts",
  // "mts",

  //updated
  "txt",
  "mp4",
  "pdf",
  "xls",
  "xlsx",
  "xlsm",
  "doc",
  "docx",
  "zip",
  "gz",
  "rar",
  "msg",
  "kmz",
  "csv",
  "ppt", // likely meant to be "ppt" or "pptx"
  "pptx",
  "dwg",
  "avi",
  "wmv",
  "mkv",
  "mov",
  "webm",
  // "m2ts",
  // "mts", removed as shams said
  "flv",
  "mp3",
];
