import { CheckboxChangeEvent } from "antd/es/checkbox";
// Hook & Redux
import { useTranslation } from "~/hook";
import { changeViewAllFiles } from "~/modules/document/fileAndPhoto/redux/slices/filePhotoViewSlice";
import {
  useAppDispatch,
  useAppSelector,
} from "~/modules/document/fileAndPhoto/redux/store";
import { updateFileCount } from "~/modules/document/fileAndPhoto/redux/slices/filePhotoLeftSlice";

// Atoms
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// Molecules
import { AddButton } from "~/shared/components/molecules/addButton";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { FilePhotoActionHeader } from "~/shared/components/molecules/filePhotoActionHeader";

// Organisms
import { FileSelect } from "~/shared/components/organisms/fileSelect";

// Other
import { getGConfig, useGModules, getGProject, getGSettings } from "~/zustand";
import { defaultConfig } from "~/data";
import {
  callAgainFileList,
  changeHeaderDebounceSearch,
  changeSearch,
  changeSort,
  setSelectedAllFiles,
} from "../../redux/slices/filePhotoRightSlice";
import { ChangeEvent, useCallback, useEffect, useState } from "react";
import {
  createProjectFolders,
  getFileProjectsList,
} from "../../redux/action/fileAndPhotoHeaderAction";
import { removeProjectList } from "../../redux/slices/fileAndPhotoHeaderSlice";
import AddOrEditFile from "../../sidebar/AddOrEditFile";
import debounce from "lodash/debounce";
import isEmpty from "lodash/isEmpty";
import { getTagCategories } from "~/redux/action/tagCategoriesAction";
import CreateAFolder from "./CreateAFolder";
import PageDashboardHeader from "~/components/page/common/page-dashboard-header";
import { fileExtensionList } from "~/redux/action/fileExtensionAction";
import FilePhotoFilter from "./FilePhotoFilter";
import {
  clearAllSelectedFolders,
  setCallAgainFolderList,
} from "../../redux/slices/filePhotoLeftSlice";
import { changeFilter } from "../../redux/slices/filePhotoRightSlice";
import { addFile } from "~/redux/action/fileAttachmentAction";
import { removeDuplicatesFile } from "~/shared/utils/helper/removeDuplicatesFile";
import { getFolderListAfterFileAdd } from "../../utils/getFolderListAfterFileAdd";
import { fileAddOptions } from "../../constant";

// Default state
const defaultValue = {
  customer: "",
  extension: "",
  tagsText: "",
  tags: "",
  showAllFiles: "0",
  project: "",
  search: "",
  project_names: "",
  customer_names: "",
};

interface FilePhotoHeaderProps {
  handleSearch: (event: React.ChangeEvent<HTMLInputElement>) => void;
}
interface ISelectedProjects {
  id: number;
  project_name: string;
  project_id: string;
  project_status_key: string;
  create_file_folder: string;
  total_files_attached: string;
}
//its will update in future

export const FilePhotoHeader: React.FC<FilePhotoHeaderProps> = ({
  handleSearch,
}) => {
  //its will update in future
  const [createFolderOpen, setCreateFolderOpen] = useState<boolean>(false);
  const [selectedProjects, setSelectedProjects] = useState<ISelectedProjects[]>(
    []
  );
  const [projectIds, setProjectIds] = useState<number[]>([]);
  const [search, setSearch] = useState<string>("");

  const { filePhotosProjectsList, isfilePhotosProjectsLoading }: IProjectsData =
    useAppSelector((state) => state.fileAndPhotoHeader);
  const dispatch = useAppDispatch();
  const [addOrEditFileOpen, setAddOrEditFileOpen] = useState<boolean>(false);
  const [saveButtonLoading, setSaveButtonLoading] = useState<boolean>(false);
  const [viewSearch, setViewSearch] = useState<boolean>(false);
  const [projectSearchDebounce, setProjectSearchDebounce] =
    useState<IProjectSearchDebounceProps>({
      searchProjectDebounce: "",
      projectPage: 0,
    });
  const selectedProjectDropbox: ISelectedFolder = useAppSelector(
    (state) => state.filePhotoFolderList.selectedFolder
  );
  const { checkModuleAccessByKey } = useGModules();
  const [selectedFile, setSelectedFile] = useState<IFile[]>([]);
  const [selectedTab, setSelectedTab] = useState<
    TFileAttachmentTabsValues | ""
  >("");
  const [load, isLoad] = useState(false);
  const selectView: IFilePhotoViewSlice = useAppSelector(
    (state) => state.filePhotoView
  );
  const selectedFiles: FilePhotoRightDetail[] = useAppSelector(
    (state) => state.filePhotoRightList.selectedFiles
  );
  const hearderSearch: string = useAppSelector(
    (state) => state.filePhotoRightList.search
  );
  const { fileList, detailViewLength }: IFilePhotoRight = useAppSelector(
    (state) => state.filePhotoRightList
  );
  const selectedAllFiles: boolean = useAppSelector(
    (state) => state.filePhotoRightList.selectedAllFiles
  );
  const {
    selectedFolder,
    folderList,
    parentFolderName,
  }: IFilePhotoLeftInitialState = useAppSelector(
    (state) => state.filePhotoFolderList
  );

  const isCurrentViewFolder = selectView.currentView === "folder";
  const isCurrentViewTimeline = selectView.currentView === "timeline";
  const isFolderDetailView = selectView.folderView.fileStructure === "detail";
  const isCurrentView = selectView.folderView.fileStructure;

  const {
    module_can_read,
    module_can_write,
    module_id,
    module_access,
    module_key,
  }: GConfig = getGConfig();
  const { date_format, image_resolution }: GSettings = getGSettings();
  const modulePermission = module_can_read === 1 && module_can_write !== 0;
  const fileSupportAccess = checkModuleAccessByKey(
    defaultConfig.file_support_key
  );
  const [filePhotoFilter, setFilePhotoFilter] =
    useState<IFiledFilePhotoFilter>(defaultValue);
  const { _t } = useTranslation();
  const { project_id }: GProject = getGProject();

  useEffect(() => {
    if (project_id && project_id !== "0") {
      setFilePhotoFilter({
        ...filePhotoFilter,
        project: project_id ?? "",
      });
      dispatch(clearAllSelectedFolders());
      dispatch(changeFilter({ project: project_id }));
    } else {
      if (filePhotoFilter?.project && filePhotoFilter?.project !== "0") {
        setFilePhotoFilter({});
        dispatch(clearAllSelectedFolders());
        dispatch(changeFilter({}));
      }
    }
  }, [project_id]);

  const handleSort = () => {
    dispatch(changeSort());
  };

  const handleViewAllFiles = () => {
    dispatch(changeViewAllFiles());
  };

  const hanldeCreateNewFolder = () => {
    setSearch("");
    setCreateFolderOpen(true);
  };

  const loadMore = () => {
    setProjectSearchDebounce((prev) => ({
      ...prev,
      projectPage: prev.projectPage + 1,
    }));
  };

  const handleCheckedProjects = (item: IProjectList) => {
    // Create a copy of the array to avoid mutating state directly
    setSelectedProjects((prevSelectedProjects) => {
      const prev = [...prevSelectedProjects];
      const itemIndex = prev.findIndex(
        (selectedItem) => selectedItem.id === item.id
      );

      if (itemIndex !== -1) {
        // If the item is already selected, remove it from the array
        prev.splice(itemIndex, 1);
      } else {
        // If the item is not selected, add it to the array
        prev.push(item);
      }

      return prev;
    });

    setProjectIds((prevProjectIds) => {
      const prevId = [...prevProjectIds];
      const itemIndex = prevId.indexOf(item.id);

      if (itemIndex !== -1) {
        // If the item ID is already in the array, remove it from the array
        prevId.splice(itemIndex, 1);
      } else {
        // If the item ID is not in the array, add it to the array
        prevId.push(item.id);
      }
      return prevId;
    });
  };

  const handleCreateFile = async (data: IAddFileRequestBodyChild[]) => {
    const selectedFileData = removeDuplicatesFile(data);
    const newFilesArray = selectedFileData?.map((file) => ({
      file_url: file.signedUrl,
      is_image: file.is_image,
    }));

    const addFileRes = (await addFile({
      files: newFilesArray,
      module_id:
        !isEmpty(selectedFolder) &&
        selectedFolder?.project_id === defaultConfig.company_files_project_id
          ? defaultConfig.company_files_folder_id
          : selectedFolder?.project_id ===
            defaultConfig.unassigned_files_project_id
          ? defaultConfig.unassigned_files_folder_id
          : selectedFolder.module_id !== null
          ? selectedFolder.module_id ?? defaultConfig.file_photo_module_id
          : module_id ?? defaultConfig.file_photo_module_id,
      folder_id: isEmpty(selectedFolder)
        ? 0
        : selectedFolder?.project_id ===
            defaultConfig.company_files_project_id ||
          selectedFolder?.project_id ===
            defaultConfig.unassigned_files_project_id
        ? 0
        : selectedFolder.folder_id,
      project_id: isEmpty(selectedFolder)
        ? 0
        : selectedFolder?.project_id ===
            defaultConfig.company_files_project_id ||
          selectedFolder?.project_id ===
            defaultConfig.unassigned_files_project_id
        ? 0
        : selectedFolder?.project_id,
      ...(!isEmpty(parentFolderName) ? { file_tags: parentFolderName } : {}),
      ...((!isEmpty(selectedFolder) &&
        selectedFolder?.project_id ===
          defaultConfig.company_files_project_id) ||
      selectedFolder?.project_id === defaultConfig.unassigned_files_project_id
        ? {
            static_folder: 1,
          }
        : {}),
      ...((!isEmpty(selectedFolder) &&
        selectedFolder?.project_id ===
          defaultConfig.company_files_project_id) ||
      selectedFolder?.project_id === defaultConfig.unassigned_files_project_id
        ? {
            module_name: selectedFolder?.project_id,
          }
        : {}),
    })) as IAddFileApiRes;

    if (addFileRes?.success) {
      const new_files = addFileRes?.data?.aws_files;
      const fileLength =
        isCurrentView === "detail"
          ? detailViewLength ?? 0
          : Number(selectedFolder?.total_file_count) ?? 0;
      dispatch(callAgainFileList());
      if (selectedFolder?.next_request_type === "2") {
        getFolderListAfterFileAdd(dispatch, selectedFolder);
      } else {
        if (fileSupportAccess === "no_access") {
          const onlyImageData =
            new_files && new_files.filter((item) => item.is_image === 1);
          dispatch(callAgainFileList());
          dispatch(
            updateFileCount({
              selectedFolder: selectedFolder,
              selectedProject: selectedFolder?.project_id,
              fileLength: fileLength
                ? fileLength + (onlyImageData?.length ?? 0)
                : 0,
              action: "addOrDeleteOrUpdate",
            })
          );
        } else {
          dispatch(
            updateFileCount({
              selectedFolder: selectedFolder,
              selectedProject: selectedFolder?.project_id,
              fileLength: fileLength
                ? fileLength + (new_files?.length ?? 0)
                : 0,
              action: "addOrDeleteOrUpdate",
            })
          );
        }
      }
    }
  };
  const handleCreateFolder = async () => {
    setSaveButtonLoading(true);
    const createFolderRes = (await createProjectFolders({
      projectIds: projectIds,
    })) as ICreateFolderApiRes;
    if (
      createFolderRes &&
      "success" in createFolderRes &&
      createFolderRes.success
    ) {
      setCreateFolderOpen(!createFolderOpen);
      setSelectedProjects([]);
      setSearch("");
      setProjectIds([]);
      dispatch(removeProjectList({ projectIds: projectIds }));
      dispatch(setCallAgainFolderList());
    } else {
      notification.error({
        description: createFolderRes
          ? createFolderRes?.message
          : "Something went wrong!",
      });
    }
    setSaveButtonLoading(false);
  };
  const handleSelectImage = (imageData: IFile) => {
    setSelectedFile((prevSelectedFiles) => {
      if (
        prevSelectedFiles.some((file) => file.image_id === imageData.image_id)
      ) {
        return prevSelectedFiles.filter(
          (file) => file.image_id !== imageData.image_id
        );
      } else {
        return [...prevSelectedFiles, imageData];
      }
    });
  };
  const handleSearchProject = useCallback(
    debounce((searchProject: string) => {
      setProjectSearchDebounce((prev) => ({
        ...prev,
        projectPage: 0,
        searchProjectDebounce: searchProject,
      }));
    }, 1000),
    []
  );

  useEffect(() => {
    if (!createFolderOpen) {
      return;
    }
    dispatch(
      getFileProjectsList({
        search:
          HTMLEntities.encode(projectSearchDebounce.searchProjectDebounce) ||
          undefined,
        page: projectSearchDebounce.projectPage,
        limit: 20,
      })
    );
  }, [
    projectSearchDebounce.projectPage,
    projectSearchDebounce.searchProjectDebounce,
    createFolderOpen,
  ]);

  useEffect(() => {
    if (createFolderOpen) {
      setSearch(""); // Reset the search state when opening the sidebar
    }
  }, [createFolderOpen]);

  const handleAddNewFile = () => {
    setAddOrEditFileOpen(true);
  };

  const handleOptionClick = (option: IFileAddOptions) => {
    if (option.key === "create_a_folder") {
      hanldeCreateNewFolder();
    } else if (option.key === "add_new_files") {
      handleAddNewFile();
    } else if (option.key === "create_new_file") {
      setSelectedTab("new");
    }
  };

  const handleCheckAll = (e: CheckboxChangeEvent | boolean) => {
    dispatch(
      setSelectedAllFiles({
        checked: typeof e !== "boolean" ? e.target.checked : e,
        currentView: selectView.currentView,
        fileStructure: selectView.folderView.fileStructure,
      })
    );
  };
  const handleClearSearch = () => {
    dispatch(changeSearch(""));
    dispatch(clearAllSelectedFolders());
  };
  const handleSearchHeaderDebounce = useCallback(
    debounce((event: ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value;
      dispatch(clearAllSelectedFolders());
      dispatch(changeHeaderDebounceSearch(value));
    }, 1000),
    []
  );

  const handleSearchHeader = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    if (!value?.trim() && value?.startsWith(" ")) return;
    dispatch(changeSearch(event.target.value));
    handleSearchHeaderDebounce(event);
  };

  useEffect(() => {
    dispatch(
      getTagCategories({
        is_deleted: 0,
        module_id: null,
      })
    );
    dispatch(fileExtensionList());
  }, []);

  const leftComponent = modulePermission
    ? isCurrentViewFolder && (
        <li className="w-10 text-center">
          <ButtonWithTooltip
            tooltipTitle={_t(
              selectView.folderView.viewAllFiles
                ? _t("View files by project folder")
                : _t("View all files at once")
            )}
            tooltipPlacement="top"
            icon={
              selectView.folderView.viewAllFiles
                ? "fa-regular fa-folder"
                : "fa-regular fa-file-lines"
            }
            onClick={handleViewAllFiles}
            iconClassName="h-5 w-5 text-primary-gray-80"
            className="w-7 max-w-[28px] max-h-[28px] m-auto border-0 shadow-none rounded focus-visible:!outline-0 hover:!bg-[#f0f0f0]"
          />
        </li>
      )
    : null;

  const rightComponent = modulePermission ? (
    <>
      {fileList.sort &&
      isCurrentViewFolder &&
      ((!isFolderDetailView && fileList.data.length) ||
        (isFolderDetailView && detailViewLength !== 0)) ? (
        <li>
          <ButtonWithTooltip
            tooltipTitle={_t(
              fileList.sort === "by_date"
                ? _t("Sort files by filename")
                : _t("Sort files by date uploaded")
            )}
            tooltipPlacement="top"
            icon={
              fileList.sort === "by_date"
                ? "fa-regular fa-arrow-down-a-z"
                : "fa-regular fa-arrow-down-1-9"
            }
            onClick={handleSort}
            iconClassName="h-5 w-5 text-primary-gray-80"
            className="w-7 max-w-[28px] max-h-[28px] m-auto border-0 shadow-none rounded focus-visible:!outline-0 hover:!bg-[#f0f0f0]"
          />
        </li>
      ) : (
        ""
      )}

      {(selectView.folderView.viewAllFiles ||
        isCurrentViewTimeline ||
        isFolderDetailView ||
        (!isEmpty(selectedFolder) &&
          selectedFolder?.next_request_type !== "2")) && (
        <li className="md:block hidden">
          <AddButton onClick={handleAddNewFile}>
            {_t("Add New Files")}
          </AddButton>
        </li>
      )}

      {/* Create New File option will temporary hide based on this cu link:- https://app.clickup.com/t/86cw25nqt  */}

      {/* {!selectView.folderView.viewAllFiles &&
      folderList.length &&
      isCurrentViewFolder &&
      selectView.folderView.fileStructure === "thumbnail" ? (
        <li className="md:block hidden">
          <AddButton onClick={() => setSelectedTab("new")}>
            {_t("Create New File")}
          </AddButton>
        </li>
      ) : null} */}
      <li className="md:hidden block">
        <DropdownMenu
          options={fileAddOptions
            .filter((option: IFileAddOptions) => {
              const { key } = option;
              const { viewAllFiles, fileStructure } = selectView.folderView;

              if (key === "create_a_folder") {
                return (
                  !viewAllFiles &&
                  isCurrentViewFolder &&
                  isEmpty(selectedFolder)
                );
              }

              if (key === "add_new_files") {
                return (
                  viewAllFiles ||
                  isCurrentViewTimeline ||
                  isFolderDetailView ||
                  (!isEmpty(selectedFolder) &&
                    selectedFolder?.next_request_type !== "2")
                );
              }

              if (key === "create_new_file") {
                return (
                  !viewAllFiles &&
                  folderList.length &&
                  isCurrentViewFolder &&
                  fileStructure === "thumbnail"
                );
              }

              // Keep all other options visible
              return true;
            })
            .map((option) => ({
              ...option,
              label: `+ ${option.label}`,
              onClick: () => handleOptionClick(option),
            }))}
          contentClassName=""
          buttonClass="w-fit h-[26px] !bg-blue-100 hover:!bg-blue-100 p-0 add-select-dropdown rounded-r-sm"
          children={
            <div className="flex items-center gap-2">
              <div className="h-[26px] w-6 flex items-center justify-center text-white bg-primary-900 rounded-l dark:!bg-dark-950">
                <FontAwesomeIcon
                  icon="fa-regular fa-plus"
                  className="w-[13px] h-[13px] m-auto !text-white dark:!text-white"
                />
              </div>
              <Typography className="text-13 text-primary-900 font-semibold">
                {_t("Contact")}
              </Typography>
              <FontAwesomeIcon
                className="pr-2 w-3 h-3"
                icon="fa-regular fa-chevron-down"
              />
            </div>
          }
        />
      </li>
      {!selectView.folderView.viewAllFiles &&
        isCurrentViewFolder &&
        isEmpty(selectedFolder) && (
          <li className="md:block hidden">
            <AddButton onClick={hanldeCreateNewFolder}>
              {_t("Create a Folder")}
            </AddButton>
          </li>
        )}
    </>
  ) : null;

  const isActionHeader: boolean = selectedFiles.length ? true : false;
  const isCheckAll: boolean = selectedAllFiles;
  return (
    <>
      {/* {isActionHeader ? ( will improve code letter
        <FilePhotoActionHeader
          selectedFiles={selectedFiles}
          handleCheckAll={handleCheckAll}
          checkAll={isCheckAll}
          check
        />
      ) : (
        <DashboardHeader
          searchPlaceHolder="Keywords, Tags, Notes, etc."
          leftComponent={leftComponent}
          rightComponent={rightComponent}
          // onSearchChange={handleSearch}
          // handleFilter={handleFilter}
        />
      )} */}
      {isActionHeader ? (
        <FilePhotoActionHeader
          selectedFiles={selectedFiles}
          handleCheckAll={handleCheckAll}
          checkAll={isCheckAll}
          check
          isReadOnly={module_access === "read_only"}
        />
      ) : (
        <PageDashboardHeader
          filterComponent={
            <FilePhotoFilter
              filePhotoFilter={filePhotoFilter}
              setFilePhotoFilter={setFilePhotoFilter}
              defaultValue={defaultValue}
            />
          }
          viewSearch={viewSearch}
          setViewSearch={setViewSearch}
          isFilter={false}
          leftComponent={leftComponent}
          rightComponent={rightComponent}
          searchProps={{
            onChange: (e: ChangeEvent<HTMLInputElement>) => {
              handleSearchHeader(e);
            },
            value: hearderSearch,
            placeholder: _t("Keywords, Tags, Notes, etc."),
            onClear: () => handleClearSearch(),
          }}
        />
      )}
      <CreateAFolder
        isSingleSelect={false}
        open={createFolderOpen}
        setOpen={setCreateFolderOpen}
        filteredItems={filePhotosProjectsList.data}
        projectAdd={handleCheckedProjects}
        selectedProjects={selectedProjects}
        setSelectedProjects={setSelectedProjects}
        setSelectedProject={() => {
          setSelectedProjects([]);
        }}
        saveButtonLoading={saveButtonLoading}
        handleCreateFolder={handleCreateFolder}
        handleSearchProject={handleSearchProject}
        search={search}
        isfilePhotosProjectsLoading={isfilePhotosProjectsLoading}
        setSearch={setSearch}
        loadMore={loadMore}
        hasMore={filePhotosProjectsList.infiniteScrollhasMore}
        infiniteScrollHideLoadingComponent={filePhotosProjectsList.error}
        isInfiniteScrollLoading={isfilePhotosProjectsLoading}
        setProjectIds={setProjectIds}
      />
      {addOrEditFileOpen && (
        <AddOrEditFile
          addOrEditFileOpen={addOrEditFileOpen}
          setAddOrEditFileOpen={setAddOrEditFileOpen}
          moduleId={module_id}
        />
      )}
      {selectedTab !== "" && (
        <FileSelect
          options={["new", "google", "dropbox", "url"]}
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
          setSelectedFileData={(data) => {
            handleCreateFile(data as IFile[]);
          }}
          handleSelectImage={handleSelectImage}
          selectedFiles={selectedFile}
          selectedProjectDropbox={selectedProjectDropbox}
          useAppSelector={useAppSelector}
          dispatch={dispatch}
          validationParams={{
            date_format,
            file_support_module_access: checkModuleAccessByKey(
              defaultConfig.file_support_key
            ),
            image_resolution,
            module_key,
            module_id,
            module_access,
          }}
          addFilesRes={{}}
          load={load}
          isLoad={isLoad}
        />
      )}
    </>
  );
};
