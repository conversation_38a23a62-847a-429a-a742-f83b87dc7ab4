// Hook
import { useTranslation } from "~/hook";
// atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Head<PERSON> } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// Molecules
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { InputField } from "~/shared/components/molecules/inputField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { RadioGroupList } from "~/shared/components/molecules/radioGroupList";
import { ErrorMessage } from "~/shared/components/molecules/errorMessage";
import { CheckboxGroupList } from "~/shared/components/molecules/checkboxGroupList";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { CustomFieldSkeleton } from "~/shared/components/molecules/customFieldSkeleton";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";

// Organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";

// Other
import { sanitizeString } from "~/helpers/helper";
import {
  getGConfig,
  getGProject,
  getGSettings,
  useExistingProjects,
} from "~/zustand";
import { useNavigate, useSearchParams } from "@remix-run/react";
import { useEffect, useMemo, useState } from "react";
import { defaultConfig } from "~/data";
import { formAddVehicleLogSchema } from "./utils";
import { useFormik } from "formik";
import { getCustomFieldAccess } from "~/shared/utils/helper/getCustomFieldAccess";
import {
  filterOptionBySubstring,
  onKeyDownNumber,
} from "~/shared/utils/helper/common";
import {
  displayDateFormat,
  backendDateFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { useDirectoryKeyValue } from "~/shared/hooks/useCustomField/useDirectoryKeyValue";
import { useSideBarCustomField } from "~/shared/hooks/useCustomField/useSidebarCustomField";
import { getDirectaryKeyById } from "~/components/sidebars/multi-select/customer/zustand/action";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
import { formatCustomFieldForRequest } from "~/shared/utils/helper/customFieldSidebarFormat";
import { addVehicleAPI } from "../../redux/action/vehicleDashAction";
import isEmpty from "lodash/isEmpty";
import { useExistingCustomers } from "~/components/sidebars/multi-select/customer/zustand";
import { getVehiclesData } from "../../redux/action/vehicleDetailAction";
import { useAppVLDispatch, useAppVLSelector } from "../../redux/store";
import {
  EVENT_LOGGER_ACTION,
  EVENT_LOGGER_NAME,
} from "~/shared/constants/event-logger";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { getGlobalUser } from "~/zustand/global/user/slice";
import OnlyRequiredCustomFields from "~/shared/components/organisms/OnlyRequiredCustomFields/OnlyRequiredCustomFields";

const AddVehicleLog = ({
  addVehicleLogSidebarOpen,
  setAddVehicleLogSidebarOpen,
  moduleId,
  action,
  moduleSingularName,
  defaultProjectId,
}: IAddVehicleLogProps) => {
  const { _t } = useTranslation();
  const { initValues, validateSchema } = formAddVehicleLogSchema();
  const gSettings: GSettings = getGSettings();
  const gConfig: GConfig = getGConfig();
  const navigate = useNavigate();
  const { module_key }: GConfig = getGConfig();
  const { directoryKeyValue, directory }: IDirectoryFormCustomField =
    useDirectoryKeyValue();
  const { isNoAccessCustomField }: ICustomFieldAccess = getCustomFieldAccess();
  const { date_format } = gSettings;
  const dispatch = useAppVLDispatch();
  const { vehiclesData }: vehicleDetailsInitialState = useAppVLSelector(
    (state) => state.vehicalLogDetail
  );
  const { componentList, loadingCustomField } = useSideBarCustomField(
    { directory, directoryKeyValue } as IDirectoryFormCustomField,
    {
      moduleId: moduleId,
      userType: "2",
    } as IRequestCustomFieldForSidebar
  );
  const user: IInitialGlobalData["user"] = getGlobalUser();
  let { getExistingUsersWithApi } = useExistingCustomers();

  const [isOpenSelectEmployee, setIsOpenSelectEmployee] =
    useState<boolean>(false);
  const [contactDetailDialogOpen, setContactDetailDialogOpen] =
    useState<boolean>(false);
  const [isSubmit, setIsSubmit] = useState<boolean>(false);
  const [employeeData, setEmployeeData] = useState<TselectedContactSendMail[]>(
    []
  );
  const [searchParams, setSearchParams] = useSearchParams();
  const [clearVehical, setClearVehical] = useState<boolean>(false);
  const { project_id }: GProject = getGProject();
  const [isSelectProjectOpen, setIsSelectProjectOpen] =
    useState<boolean>(false);
  const [selectedProject, setSelectedProject] = useState<IProject[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  // this is will NF once testing done it will be merge on dev
  // const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
  //   useState<boolean>(false);
  const [selectedContactId, setSelectedContactId] = useState<
    number | string | undefined
  >("");
  let { getExistingProjectsWithApi }: UseExistingProjectsResponse =
    useExistingProjects("project");

  useEffect(() => {
    dispatch(getVehiclesData({ status: 0 }));
  }, []);

  const initialFormValues = componentList.length
    ? {
        custom_fields: componentList.reduce((acc, item) => {
          acc[item.name] = item?.value ?? "";
          return acc;
        }, {} as ICustomFieldInitValue),
      }
    : {};

  const handleSubmitForm = async () => {
    setIsSubmit(true);
    setIsLoading(true);
    let isCustomFieldValid = true;
    if (componentList.length && !isNoAccessCustomField) {
      for (let index = 0; index < componentList.length; index++) {
        const value =
          formik?.values?.custom_fields?.[componentList[index].name];
        const multiple = componentList[index].multiple;
        const typeComponent = componentList[index].type;
        if (multiple || typeComponent === "checkbox-group") {
          if (
            (Array.isArray(value) &&
              value?.some((item) => item.trim() === "")) ||
            !value?.length
          ) {
            isCustomFieldValid = false;
            break;
          }
        } else if (!value) {
          isCustomFieldValid = false;
          break;
        }
      }
    }

    if (!formik.isValid || !isCustomFieldValid) {
      setIsLoading(false);
      return;
    }

    const formData = {
      ...formik.values,
      log_date: backendDateFormat(
        formik.values.log_date as string,
        date_format
      ),
      directory_id: Number(formik.values.employeeId),
      ...(!!formik.values.directory_contact_id
        ? { directory_contact_id: Number(formik.values.directory_contact_id) }
        : {}),
      vehicle_id: Number(formik.values.vehicle),
      custom_fields:
        formik.values.custom_fields && !isNoAccessCustomField
          ? formatCustomFieldForRequest(
              formik.values.custom_fields,
              componentList,
              date_format
            ).custom_fields
          : undefined,
      access_to_custom_fields:
        formik.values.custom_fields && !isNoAccessCustomField ? 1 : 0,
    };

    delete formData.employeeId;
    delete formData.employeeName;
    delete formData.vehicle;
    try {
      const response = await addVehicleAPI(getValuableObj(formData));
      if (response?.success) {
        EventLogger.log(
          EVENT_LOGGER_NAME.vehicle_logs + EVENT_LOGGER_ACTION.added,
          1
        );
        setIsSubmit(false);
        setIsLoading(false);
        setAddVehicleLogSidebarOpen(!addVehicleLogSidebarOpen);
        navigate(`${response?.data?.vehicle_log_id?.toString()}`);
      } else {
        notification.error({
          description: response?.message,
        });
      }
    } catch (error) {
      setIsSubmit(false);
      setIsLoading(false);
    } finally {
      setIsSubmit(false);
      setIsLoading(false);
    }
  };

  const formik = useFormik({
    initialValues: {
      ...initValues,
      ...initialFormValues,
      custom_fields: initialFormValues.custom_fields
        ? initialFormValues.custom_fields
        : {},
    } as Partial<IVehicleLogAdd>,
    validationSchema: validateSchema,
    enableReinitialize: true,
    onSubmit: () => {
      handleSubmitForm();
    },
  });
  const [initialValuesState, setInitialValuesState] = useState<
    Partial<IVehicleLogAdd>
  >(formik.values);

  const { values, setFieldValue, errors, setValues, handleSubmit } = formik;
  const vehicleList = vehiclesData
    .map((item: IVehicledata) => ({
      ...item,
      label: HTMLEntities.decode(sanitizeString(`${item.name}`)),
      value: item?.vehicle_id?.toString() ?? "",
    }))
    .filter((item) => !item.is_deleted);
  const selectedArchiveVehicle: IVehicledata =
    (vehiclesData.find(
      ({ vehicle_id, is_deleted, name }) =>
        vehicle_id?.toString() === values.vehicle && is_deleted
    ) as IVehicledata) || null;

  if (selectedArchiveVehicle && selectedArchiveVehicle.vehicle_id) {
    const { vehicle_id, name } = selectedArchiveVehicle;
    vehicleList.unshift({
      ...selectedArchiveVehicle,
      label: HTMLEntities.decode(sanitizeString(`${name} (Archived)`)),
      value: vehicle_id.toString(),
    });
  }
  const handleEmployee = (data: ISTCustomerDetails) => {
    const { user_id, display_name, orig_type, type_name, contact_id, image } =
      data;
    const dirType = type_name === "Employee" ? 2 : 204;
    const selectedCustomer = {
      employeeId: user_id?.toString() || "",
      employeeName: display_name?.toString() || "",
      directory_contact_id: contact_id?.toString() || "",
      dir_type: orig_type?.toString() || dirType?.toString() || "",
    };

    const customerInfo = [
      {
        display_name,
        user_id: user_id,
        type: orig_type || dirType,
        type_name,
        contact_id,
        image,
      },
    ];

    setEmployeeData(
      !isEmpty(data) ? (customerInfo as TselectedContactSendMail[]) : []
    );

    setValues((prevValues) => ({
      ...prevValues,
      ...selectedCustomer,
    }));

    setIsOpenSelectEmployee(false);
  };

  const fetchDefaultProject = async (id: string) => {
    const projects = await getExistingProjectsWithApi(id);
    const projectName = projects && projects[0] && projects[0].project_name;
    const projectId = Number(id) || projects[0]?.key;

    if (projectId && projectName) {
      setSelectedProject([
        {
          id: projectId,
          project_name: projectName,
        },
      ]);
      setInitialValuesState((prevState: any) => ({
        ...prevState,
        project_id: projectId,
      }));
    } else {
      setSelectedProject([]);
    }
  };

  useEffect(() => {
    if (user?.user_id && !loadingCustomField) {
      getExistingUsersWithApi({
        usersIds: user?.user_id?.toString(),
        contactIDs: "0",
        apiDataReturn: (customers: Partial<CustomerSelectedData>[]) => {
          if (customers.length > 0) {
            setEmployeeData(customers);
            if (!formik.values.employeeName) {
              formik.setFieldValue(
                "employeeName",
                `${customers[0].display_name}`
              );
              formik.setFieldValue("employeeId", customers[0]?.user_id);
              formik.setFieldValue("dir_type", customers[0]?.type);
              setInitialValuesState((prevState: any) => ({
                ...prevState,
                employeeName: customers[0].display_name,
                employeeId: customers[0].user_id,
                dir_type: customers[0].type,
              }));
            }
          } else {
            formik.setFieldValue("employeeName", `${user?.unique_name}`);
            formik.setFieldValue("employeeId", user?.user_id);
            formik.setFieldValue("dir_type", user?.type === 1 ? 2 : user?.type);
          }
        },
      });
    }
  }, [user?.user_id, loadingCustomField, action]);
  useEffect(() => {
    if (selectedProject && selectedProject.length) {
      formik.setFieldValue(
        "project_id",
        selectedProject[0].id || selectedProject[0]?.key
      );
    } else {
      formik.setFieldValue("project_id", "");
      setInitialValuesState((prevState: any) => ({
        ...prevState,
        project_id: "",
      }));
    }
  }, [selectedProject]);

  useEffect(() => {
    if (defaultProjectId) {
      fetchDefaultProject(defaultProjectId.toString());
    } else if (!selectedProject.length && project_id) {
      fetchDefaultProject(project_id.toString());
    }
  }, [defaultProjectId, project_id, action]);

  useEffect(() => {
    if (!clearVehical) {
      if (user?.default_vehicle && !values.vehicle) {
        formik.setFieldValue("vehicle", String(user?.default_vehicle));
      }
    }
  }, [user?.default_vehicle, values.vehicle, formik]);

  useEffect(() => {
    if (!clearVehical) {
      if (user?.default_vehicle && !values.vehicle) {
        setInitialValuesState((prevState: any) => ({
          ...prevState,
          vehicle: String(user?.default_vehicle),
        }));
      }
    }
  }, [user?.default_vehicle]);

  // this is will NF once testing done it will be merge on dev
  // const isFormModified = useMemo(() => {
  //   return JSON.stringify(formik.values) !== JSON.stringify(initialValuesState);
  // }, [formik.values, initialValuesState]);

  // const closeConfirmationModal = () => {
  //   addVehicleLogSidebarOpen;
  //   setIsConfirmDialogOpen(false);
  // };

  // const handleAlertBox = async () => {
  //   setIsConfirmDialogOpen(false);
  //   setAddVehicleLogSidebarOpen(false);
  //   setSearchParams({});
  // };

  // const handleCloseDrawer = () => {
  //   if (!isFormModified) {
  //     setAddVehicleLogSidebarOpen(false);
  //     setSearchParams({});
  //     setIsSubmit(false);
  //   } else {
  //     setIsConfirmDialogOpen(true);
  //   }
  // };

  return (
    <>
      <Drawer
        open={addVehicleLogSidebarOpen}
        rootClassName="drawer-open"
        width={718}
        push={false}
        classNames={{
          body: "!p-0 !overflow-hidden ",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon className="w-4 h-4" icon="fa-regular fa-car" />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(
                `Add ${
                  HTMLEntities.decode(sanitizeString(moduleSingularName)) ??
                  "Vehicle Log"
                }`
              )}
            </Header>
          </div>
        }
        // this is will NF once testing done it will be merge on dev
        // closeIcon={<CloseButton onClick={() => handleCloseDrawer()} />}
        closeIcon={
          <CloseButton
            onClick={() => {
              setAddVehicleLogSidebarOpen(false);
              setSearchParams({});
            }}
          />
        }
      >
        <form className="py-4" onSubmit={handleSubmit} noValidate>
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <ButtonField
                    label={_t("Project")}
                    name="project_id"
                    labelPlacement="top"
                    onClick={() => setIsSelectProjectOpen(true)}
                    value={
                      selectedProject.length &&
                      HTMLEntities.decode(
                        sanitizeString(selectedProject[0].project_name)
                      )
                    }
                    addonBefore={
                      selectedProject.length &&
                      selectedProject[0]?.id &&
                      !isNaN(Number(selectedProject[0]?.id)) ? (
                        <ProjectFieldRedirectionIcon
                          projectId={`${
                            selectedProject[0].id || selectedProject[0]?.key
                          }`}
                        />
                      ) : null
                    }
                  />
                </div>
                <div className="w-full">
                  <DatePickerField
                    label={_t("Log Date")}
                    labelPlacement="top"
                    name="log_date"
                    isRequired={true}
                    placeholder=""
                    value={displayDateFormat(
                      values.log_date?.toString().trim(),
                      date_format
                    )}
                    onChange={(value) => {
                      if (value) {
                        setFieldValue("log_date", value.format(date_format));
                      } else {
                        setFieldValue("log_date", undefined);
                      }
                    }}
                    format={date_format}
                    errorMessage={
                      isSubmit && errors.log_date ? errors.log_date : ""
                    }
                  />
                </div>
                <div className="w-full">
                  <ButtonField
                    label={_t("Employee")}
                    name="employee"
                    required={true}
                    labelPlacement="top"
                    value={values?.employeeName}
                    onClick={() => {
                      setIsOpenSelectEmployee(true);
                    }}
                    avatarProps={{
                      user: {
                        name: HTMLEntities.decode(
                          sanitizeString(employeeData[0]?.display_name || "")
                        ),
                        image: employeeData[0]?.image,
                      },
                    }}
                    addonBefore={
                      <>
                        {values?.employeeName && (
                          <div className="flex items-center gap-1">
                            <ContactDetailsButton
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedContactId(values.employeeId);
                                setContactDetailDialogOpen(true);
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              directoryId={values.employeeId || ""}
                              directoryTypeKey={
                                values?.dir_type
                                  ? getDirectaryKeyById(
                                      Number(values?.dir_type),
                                      gConfig
                                    )
                                  : ""
                              }
                            />
                          </div>
                        )}
                      </>
                    }
                    errorMessage={
                      isSubmit && errors.employeeId ? errors.employeeId : ""
                    }
                  />
                </div>
                <div className="w-full">
                  <SelectField
                    label={_t("Vehicle")}
                    labelPlacement="top"
                    options={vehicleList}
                    isRequired={true}
                    showSearch
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                    allowClear={values.vehicle ? true : false}
                    value={values.vehicle ?? ""}
                    onChange={(value) => {
                      formik.setFieldValue("vehicle", value);
                    }}
                    onClear={() => {
                      setClearVehical(true);
                      formik.setFieldValue("vehicle", "");
                    }}
                    errorMessage={
                      isSubmit && errors.vehicle ? errors.vehicle : ""
                    }
                  />
                </div>
              </SidebarCardBorder>

              {!isNoAccessCustomField && loadingCustomField && (
                <CustomFieldSkeleton />
              )}
              <OnlyRequiredCustomFields
                componentList={componentList}
                formik={formik}
                isSubmit={isSubmit}
                loadingCustomField={loadingCustomField}
              />
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              onClick={() => setIsSubmit(true)}
              disabled={isLoading || loadingCustomField}
              isLoading={isLoading}
              buttonText={_t(
                `Create ${
                  HTMLEntities.decode(sanitizeString(moduleSingularName)) ??
                  "Vehicle Log"
                }`
              )}
            />
          </div>
        </form>
      </Drawer>
      {isOpenSelectEmployee && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectEmployee}
          closeDrawer={() => {
            setIsOpenSelectEmployee(false);
          }}
          singleSelecte={true}
          options={[
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.misc_contact_key,
            defaultConfig.contractor_key,
            "by_service",
            "my_project",
          ]}
          projectId={
            selectedProject
              ? Number(selectedProject[0]?.id) ||
                Number(selectedProject[0]?.key)
              : 0
          }
          setCustomer={(data) => {
            handleEmployee(data.length ? (data[0] as ISTCustomerDetails) : {});
          }}
          selectedCustomer={employeeData ?? []}
          groupCheckBox={true}
        />
      )}
      {contactDetailDialogOpen && selectedContactId && (
        <ContactDetailsModal
          isOpenContact={contactDetailDialogOpen}
          onCloseModal={() => setContactDetailDialogOpen(false)}
          contactId={Number(selectedContactId)}
          additional_contact_id={employeeData[0]?.contact_id?.toString()}
        />
      )}
      {isSelectProjectOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProjectOpen}
          setOpen={setIsSelectProjectOpen}
          selectedProjects={selectedProject}
          onProjectSelected={(data) => {
            setSelectedProject(data);
          }}
          isRequired={false}
          genericProjects="project"
          category="project_location"
          module_key={module_key}
        />
      )}
      {/* this is will NF once testing done it will be merge on dev */}
      {/* {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-file-check"
          modaltitle={_t("Confirmation")}
          description={_t(
            `Do you really want to leave this page and lose your unsaved changes?`
          )}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAlertBox();
          }}
          onDecline={closeConfirmationModal}
        />
      )} */}
    </>
  );
};

export default AddVehicleLog;
