import {
  Form,
  NavigateFunction,
  useNavigate,
  useSearchParams,
} from "@remix-run/react";
import { useEffect, useMemo, useState } from "react";

// Shared
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { InputField } from "~/shared/components/molecules/inputField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import {
  filterOptionBySubstring,
  onKeyDownCurrency,
} from "~/shared/utils/helper/common";
import { SelectProject } from "~/shared/components/organisms/selectProject";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { getDirectaryKeyById } from "~/components/sidebars/multi-select/customer/zustand/action";

// Formik
import { useFormik } from "formik";
import * as Yup from "yup";

// Zustand
import { getGlobalProject } from "~/zustand/global/config/slice";
import { addNewPermitType } from "../../zustand/types/actions";
import { getPermitTypes } from "../../zustand/types/slice";

// Other
import { useIframe, useTranslation } from "~/hook";
import { Number, sanitizeString } from "~/helpers/helper";
import { permitRoutes } from "~/route-services/permit.routes";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
import { sendMessageKeys } from "~/components/page/$url/data";
import { addItemObject } from "~/modules/people/directory/utils/constasnts";
import { getGConfig } from "~/zustand";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import {
  EVENT_LOGGER_ACTION,
  EVENT_LOGGER_NAME,
} from "~/shared/constants/event-logger";

const AddPermitSide = ({ open, setOpen, module, project }: IAddPermitSide) => {
  const navigate: NavigateFunction = useNavigate();

  const { inputFormatter, unformatted } = useCurrencyFormatter();

  const { singular_name = "Permit", module_key, icon } = module || {};

  const globalProject: IInitialGlobalData["config"]["global_project"] =
    getGlobalProject();
  const gConfig: GConfig = getGConfig();
  const defaultProject: IProject = useMemo(() => {
    if (globalProject) {
      const { project_id, project_name } = globalProject || {};
      const id = Number(project_id);
      if (id) {
        return { id, project_name };
      }
    }
    return {} as IProject;
  }, [JSON.stringify(globalProject || {})]);

  const { _t } = useTranslation();
  const { parentPostMessage } = useIframe();

  const [searchParams, setSearchParams] = useSearchParams();

  const handleClosePermit = () => {
    if (window.ENV.PAGE_IS_IFRAME) {
      parentPostMessage(sendMessageKeys?.modal_change, {
        open: false,
      });
      return;
    }
    searchParams.delete("project");
    searchParams.delete("action");
    setSearchParams(searchParams);
    setOpen(false);
  };

  const formik = useFormik({
    initialValues: {
      project: defaultProject,
      permission: "",
      permit_type: "",
      permit_fees: "",
      agency: {} as TselectedContactSendMail,
    },
    validationSchema: Yup.object({
      permission: Yup.string().required("This field is required."),
      permit_type: Yup.string().required("This field is required."),
      project: Yup.object().shape({
        id: Yup.number()
          .required("This field is required.")
          .notOneOf([0], "Select any project."),
      }),
      agency: Yup.object().shape({
        user_id: Yup.number().notOneOf([0], "Select any agency."),
      }),
    }),
    onSubmit: async (values) => {
      if (!values.permission.trim()) {
        formik.setFieldError("permission", "This field is required.");
        return;
      }
      setIsLoading(true);
      try {
        const response = (await webWorkerApi<AddPermitData>({
          url: permitRoutes.add,
          method: "post",
          data: {
            project_id: values.project.id,
            project_name: values.project.project_name,
            agency: values.agency.user_id || "",
            agency_name: values.agency.display_name || "",
            permission: HTMLEntities.encode(values.permission).trim(),
            permit_type: values.permit_type,
            permit_fees: (Number(values?.permit_fees) * 100).toString(),
            agency_contact_id: values.agency.contact_id || 0,
          },
        })) as AddPermitData;
        if (response) {
          if (response.success) {
            if (response.data) {
              EventLogger.log(
                EVENT_LOGGER_NAME.permits + EVENT_LOGGER_ACTION.added,
                1
              );
              const permitId = response.data.permit_id?.toString();
              if (permitId && permitId.trim()) {
                if (window.ENV.PAGE_IS_IFRAME) {
                  handleClosePermit();
                } else {
                  navigate(module?.web_page + `/${permitId}`);
                }
              }
            }
          } else {
            setIsLoading(false);
            notification.error({
              description: response.message,
            });
          }
        }
      } catch (error: unknown) {
        setIsLoading(false);
        notification.error({
          description: (error as Error)?.message,
        });
      }
    },
    enableReinitialize: true,
  });

  const [isSubmit, setIsSubmit] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSelectProOpen, setIsSelectProOpen] = useState<boolean>(false);
  const [newTypeName, setNewTypeName] = useState<string>("");
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [directoryOpen, setDirectoryOpen] = useState<boolean>(false);
  const [contactOpen, setContactOpen] = useState<boolean>(false);
  const [isFocusPermitFees, setIsFocusPermitFees] = useState(false);

  const termTypes: IInitialPermitData["types"] = getPermitTypes();

  const newProject = project || defaultProject;

  useEffect(() => {
    formik.setFieldValue("project", newProject);
  }, [JSON.stringify(newProject || {})]);
  const handleEnterKeyPress = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    event.preventDefault();
  };
  return (
    <>
      <Drawer
        open={open}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon={`fa-regular ${icon}`}
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(`Add ${singular_name}`)}
            </Header>
          </div>
        }
        closeIcon={
          window.ENV.PAGE_IS_IFRAME ? null : (
            <CloseButton onClick={handleClosePermit} />
          )
        }
      >
        <Form
          method="post"
          noValidate
          className="py-4"
          onSubmit={formik.handleSubmit}
        >
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="flex flex-col gap-5">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <ButtonField
                    label={_t("Project")}
                    spanWidthClass="!w-[calc(100%-15px)]"
                    name="project_id"
                    labelPlacement="top"
                    required={true}
                    addonBefore={
                      formik.values.project.id &&
                      formik.values.project.id.toString() !== "0" && (
                        <ProjectFieldRedirectionIcon
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                          projectId={formik.values.project.id.toString()}
                        />
                      )
                    }
                    value={HTMLEntities.decode(
                      sanitizeString(formik.values.project.project_name)
                    )}
                    onClick={() => {
                      setIsSelectProOpen(true);
                    }}
                    errorMessage={isSubmit ? formik.errors.project?.id : ""}
                  />
                </div>
                <div className="grid md:grid-cols-2 gap-5">
                  <div className="w-full">
                    <InputField
                      label={_t("Permit") + " #"}
                      labelPlacement="top"
                      inputType="text"
                      name="permission"
                      size="middle"
                      value={HTMLEntities.decode(
                        sanitizeString(formik.values.permission)
                      )}
                      labelClass="dark:text-white/90"
                      onChange={(e) => {
                        formik.setFieldValue("permission", e.target.value);
                      }}
                      maxLength={25}
                      isRequired={true}
                      errorMessage={isSubmit ? formik.errors.permission : ""}
                      onPressEnter={handleEnterKeyPress}
                    />
                  </div>
                  <div className="w-full">
                    <SelectField
                      label={_t("Type")}
                      placeholder=""
                      name="permit_type"
                      value={newTypeName || formik.values.permit_type}
                      labelPlacement="top"
                      iconView={true}
                      isRequired={true}
                      showSearch
                      options={
                        termTypes.map((type) => ({
                          label: HTMLEntities.decode(sanitizeString(type.name)),
                          value: type.item_id.toString(),
                        })) ?? []
                      }
                      allowClear
                      filterOption={(input, option) =>
                        filterOptionBySubstring(input, option?.label as string)
                      }
                      onChange={(value) => {
                        formik.setFieldValue("permit_type", value);
                      }}
                      addItem={addItemObject}
                      onInputKeyDown={(e) => {
                        if (e.key === "Enter") {
                          const value = e?.currentTarget?.value?.trim();
                          const newType = onEnterSelectSearchValue(
                            e,
                            termTypes.map((type) => ({
                              label: HTMLEntities.decode(
                                sanitizeString(type.name)
                              ),
                              value: type.item_id.toString(),
                            })) || []
                          );
                          if (newType) {
                            setNewTypeName(newType);
                          } else if (value) {
                            notification.error({
                              description:
                                "Records already exist, no new records were added.",
                            });
                          }
                        }
                      }}
                      onClear={() => {
                        formik.setFieldValue("permit_type", "");
                      }}
                      errorMessage={isSubmit ? formik.errors.permit_type : ""}
                    />
                  </div>
                </div>
                <div className="grid md:grid-cols-2 gap-5">
                  <div className="w-full">
                    <InputNumberField
                      label={_t("Fee")}
                      name="permit_fees"
                      labelClass="dark:text-white/90"
                      placeholder={inputFormatter("0.00").value}
                      labelPlacement="top"
                      value={formik.values.permit_fees}
                      formatter={(value, info) => {
                        const inputValue = info.input.trim();
                        const cleanValue = unformatted(inputValue);
                        const [integerPart] = cleanValue.split(".");
                        if (integerPart.length > 10) {
                          return inputFormatter(formik.values.permit_fees)
                            .value;
                        }
                        if (isFocusPermitFees) {
                          return inputFormatter(unformatted(inputValue)).value;
                        }
                        return !!value
                          ? inputFormatter(Number(value).toFixed(2)).value
                          : "";
                      }}
                      parser={(value) => {
                        if (!value) {
                          formik.setFieldValue("permit_fees", "");
                          return "";
                        }
                        const inputValue = unformatted(value.toString());
                        const [integerPart, decimalPart = ""] =
                          inputValue.split(".");
                        if (integerPart.length > 10) {
                          return formik.values.permit_fees;
                        }
                        formik.setFieldValue("permit_fees", inputValue || "");
                        return inputValue;
                      }}
                      onKeyDown={(event) => {
                        onKeyDownCurrency(event, {
                          integerDigits: 10,
                          decimalDigits: 2,
                          unformatted,
                          decimalSeparator: inputFormatter().decimal_separator,
                        });
                      }}
                      onFocus={() => {
                        setIsFocusPermitFees(true);
                      }}
                      onBlur={() => {
                        setIsFocusPermitFees(false);
                      }}
                      prefix={inputFormatter().currency_symbol}
                    />
                  </div>
                  <div className="w-full overflow-hidden">
                    <ButtonField
                      label={_t("Agency")}
                      labelPlacement="top"
                      onClick={() => {
                        setDirectoryOpen(true);
                      }}
                      value={
                        formik.values.agency.user_id &&
                        HTMLEntities.decode(
                          sanitizeString(formik.values.agency.display_name)
                        )
                      }
                      addonBefore={
                        formik.values.agency.user_id ? (
                          <div className="flex items-center gap-1">
                            <ContactDetailsButton
                              onClick={(e) => {
                                e.stopPropagation();
                                setContactOpen(true);
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              className="!w-5 !h-5"
                              directoryId={
                                formik.values.agency.user_id.toString() || ""
                              }
                              directoryTypeKey={
                                formik?.values.agency?.type_key === "contact"
                                  ? formik?.values.agency?.parent_type_key || ""
                                  : formik.values.agency?.type
                                  ? getDirectaryKeyById(
                                      formik.values.agency?.type === 1
                                        ? 2
                                        : Number(formik.values.agency.type),
                                      gConfig
                                    )
                                  : formik.values.agency?.type_key || ""
                              }
                            />
                          </div>
                        ) : null
                      }
                    />
                  </div>
                </div>
              </SidebarCardBorder>
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              buttonText={_t("Create Permit")}
              className="w-full justify-center"
              disabled={isLoading}
              isLoading={isLoading}
              onClick={() => setIsSubmit(true)}
            />
          </div>
        </Form>
      </Drawer>
      {directoryOpen && (
        <SelectCustomerDrawer
          closeDrawer={() => {
            setDirectoryOpen(false);
          }}
          singleSelecte={true}
          openSelectCustomerSidebar={directoryOpen}
          options={[
            CFConfig.employee_key,
            "my_crew",
            CFConfig.customer_key,
            CFConfig.misc_contact_key,
            CFConfig.contractor_key,
            CFConfig.vendor_key,
            "by_service",
            "my_project",
          ]}
          setCustomer={(data) => {
            let selectedCustomer: Partial<CustomerSelectedData> = {};
            if (Array.isArray(data)) {
              if (data.length) {
                selectedCustomer = {
                  ...data[0],
                  company: "company" in data[0] ? data[0].company || "" : "",
                  contact_id:
                    "contact_id" in data[0] ? Number(data[0].contact_id) : 0,
                  stage: "stage" in data[0] ? Number(data[0].stage || "0") : 0,
                };
              }
            } else {
              selectedCustomer = data || {};
            }
            formik.setFieldValue("agency", selectedCustomer);
          }}
          selectedCustomer={
            formik.values.agency.user_id ? [formik.values.agency] : []
          }
          groupCheckBox={false}
          projectId={Number(formik.values.project.id) || undefined}
        />
      )}
      {contactOpen && (
        <ContactDetailsModal
          isOpenContact={contactOpen}
          contactId={formik.values.agency.user_id}
          onCloseModal={() => {
            setContactOpen(false);
          }}
          additional_contact_id={0}
        />
      )}
      {newTypeName.trim() && (
        <ConfirmModal
          isOpen={Boolean(newTypeName.trim())}
          modalIcon={`fa-regular ${icon}`}
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newTypeName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => setNewTypeName("")}
          onAccept={async () => {
            if (!isAddingCustomData && newTypeName) {
              setIsAddingCustomData(true);
              const response = await addNewPermitType(newTypeName);
              if (response.success && response.data) {
                response.data.item_id.toString();
                formik.setFieldValue(
                  "permit_type",
                  response.data.item_id.toString() || ""
                );
                setNewTypeName("");
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => setNewTypeName("")}
        />
      )}
      {isSelectProOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProOpen}
          setOpen={setIsSelectProOpen}
          selectedProjects={
            formik.values.project.id ? [formik.values.project] : []
          }
          onProjectSelected={(projects: IProject[]) => {
            let selectedProject: Partial<IProject>;
            if (Array.isArray(projects)) {
              selectedProject = projects[0];
            } else {
              selectedProject = projects;
            }
            formik.setFieldValue("project", selectedProject || {});
          }}
          module_key={module_key}
        />
      )}
    </>
  );
};

export default AddPermitSide;
