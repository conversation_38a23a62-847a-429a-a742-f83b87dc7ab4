import { useEffect, useMemo, useState } from "react";

// Hook
import { useIframe, useTranslation } from "~/hook";

// React
import { useNavigate, useSearchParams } from "@remix-run/react";

// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";

// Organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
import OnlyRequiredCustomFields from "~/shared/components/organisms/OnlyRequiredCustomFields/OnlyRequiredCustomFields";

// Shared
import { getCustomFieldAccess } from "~/shared/utils/helper/getCustomFieldAccess";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { useDirectoryKeyValue } from "~/shared/hooks/useCustomField/useDirectoryKeyValue";
import { useSideBarCustomField } from "~/shared/hooks/useCustomField/useSidebarCustomField";
import { formatCustomFieldForRequest } from "~/shared/utils/helper/customFieldSidebarFormat";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";

// Formik
import { FormikValues, useFormik } from "formik";
import * as Yup from "yup";

// Zustand
import { getGConfig, getGSettings } from "~/zustand";
import { getModuleAutoIncrementPrimaryId } from "~/zustand/module-auto-increment-primary-id/store";
import {
  resetModuleAutoIncrementId,
  setModuleAutoIncrementId,
} from "~/zustand/module-auto-increment-primary-id/actions";
import { getGlobalProject } from "~/zustand/global/config/slice";

// Other
import { addServiceTicketAPI } from "../../redux/action/dashboardAction";
import {
  getApiDefaultParams,
  Number,
  replaceDOMParams,
  sanitizeString,
} from "~/helpers/helper";
import { apiRoutes } from "~/route-services/routes";
import { sendMessageKeys } from "~/components/page/$url/data";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import {
  EVENT_LOGGER_ACTION,
  EVENT_LOGGER_NAME,
} from "~/shared/constants/event-logger";

const AddServicesTickets = ({
  drowerOpen,
  setDrowerOpen,
  editView,
  project,
}: IAddServicesTicketsProps) => {
  const { _t } = useTranslation();
  const navigate = useNavigate();

  const { getGlobalModuleByKey } = useGlobalModule();

  const { view_st_custom_tab }: Partial<IModule> = useMemo(() => {
    const STModule = getGlobalModuleByKey(CFConfig.service_ticket_module);

    return STModule || {};
  }, []);

  const gConfig: GConfig = getGConfig();

  const { module_singular_name, module_id, module_key }: GConfig = gConfig;
  const [customerOptions, setCustomerOptions] = useState<CustomerEmailTab[]>(
    []
  );
  const [isSelectProOpen, setIsSelectProOpen] = useState<boolean>(false);
  const [isOpenSelectCustomer, setIsOpenSelectCustomer] =
    useState<boolean>(false);
  const { directoryKeyValue, directory }: IDirectoryFormCustomField =
    useDirectoryKeyValue();
  const [searchparams, setSearchParams] = useSearchParams();
  // this is will NF once testing done it will be merge on dev
  // const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
  //   useState<boolean>(false);

  const { componentList, loadingCustomField } = useSideBarCustomField(
    { directory, directoryKeyValue } as IDirectoryFormCustomField,
    {
      moduleId: module_id,
      userType: "22",
    } as IRequestCustomFieldForSidebar
  );
  const {
    need_to_increment,
    last_primary_id,
  }: Partial<IResponseGetModuleAutoNumber> =
    getModuleAutoIncrementPrimaryId() || {};
  const { isNoAccessCustomField }: ICustomFieldAccess = getCustomFieldAccess();
  const gSettings: GSettings = getGSettings();
  const { is_custom_service_tickets_id, date_format } = gSettings;
  const [customerType, setCustomerType] = useState<string>("");
  const [contactDetailDialogOpen, setContactDetailDialogOpen] =
    useState<boolean>(false);
  const [isSubmit, setIsSubmit] = useState<boolean>(false);
  const [selectedContactId, setSelectedContactId] = useState<
    number | string | undefined
  >("");
  const [selectedAddContactId, setSelectedAddContactId] = useState<
    number | string | undefined
  >(0);

  const initialFormValues = componentList.length
    ? {
        custom_fields: componentList.reduce((acc, item) => {
          acc[item.name] = item?.value ?? "";
          return acc;
        }, {} as ICustomFieldInitValue),
      }
    : {};

  const defaultCustomID = useMemo(() => {
    if (
      Number(is_custom_service_tickets_id) === 2 &&
      need_to_increment &&
      last_primary_id
    ) {
      return (Number(need_to_increment) + Number(last_primary_id)).toString();
    } else if (Number(is_custom_service_tickets_id) === 0) {
      return (gSettings?.custom_service_tickets_id).toString();
    }
    return "";
  }, [is_custom_service_tickets_id, need_to_increment, last_primary_id]);

  useEffect(() => {
    const updateCustomId = () => {
      if (drowerOpen) {
        formik.setValues((prev) => ({
          ...prev,
          custom_id: defaultCustomID,
        }));
        setInitialValuesState((prevState: any) => ({
          ...prevState,
          custom_id: defaultCustomID,
        }));
      }
    };

    updateCustomId();
  }, [defaultCustomID, drowerOpen]);

  useEffect(() => {
    if (Number(is_custom_service_tickets_id) == 2 && drowerOpen) {
      setModuleAutoIncrementId(module_id, module_key);
      return () => {
        resetModuleAutoIncrementId();
      };
    }
  }, [is_custom_service_tickets_id, drowerOpen]);

  const { parentPostMessage } = useIframe();

  // submit
  // changes by Parth bhai 18-07-2024
  const handleSubmit = async ({ setSubmitting }: FormikValues) => {
    setIsSubmit(true);
    let isCustomFieldValid = true;
    if (
      componentList.length &&
      !isNoAccessCustomField &&
      view_st_custom_tab?.toString() === "1"
    ) {
      for (let index = 0; index < componentList.length; index++) {
        const value =
          formik?.values?.custom_fields?.[componentList[index].name];
        const multiple = componentList[index].multiple;
        const typeComponent = componentList[index].type;
        if (multiple || typeComponent === "checkbox-group") {
          if (!value?.length) {
            isCustomFieldValid = false;
            break;
          }
        } else if (!value) {
          isCustomFieldValid = false;
          break;
        }
      }
    }

    if (!formik.isValid || !isCustomFieldValid) {
      return;
    }

    const formData = {
      title: HTMLEntities.encode(formik.values.title).trim(),
      customer_id: Number(formik.values.customer.user_id),
      contact_id: Number(formik.values.customer.contact_id)
        ? Number(formik.values.customer.contact_id)
        : undefined,
      billed_to: Number(formik.values.invoiced_to.user_id),
      billed_to_contact: Number(formik.values.invoiced_to.contact_id)
        ? Number(formik.values.invoiced_to.contact_id)
        : undefined,
      project_id: formik.values.project.id
        ? Number(formik.values.project.id)
        : null,
      project_name: formik.values.project.project_name || "",
      customer_contract: formik.values.customer_contract || "",

      custom_service_ticket_id:
        is_custom_service_tickets_id != 0
          ? HTMLEntities.encode(formik.values.custom_id)
          : undefined,
      custom_fields:
        formik.values.custom_fields && !isNoAccessCustomField
          ? formatCustomFieldForRequest(
              formik.values.custom_fields,
              componentList,
              date_format
            ).custom_fields
          : undefined,
      access_to_custom_fields:
        formik.values.custom_fields &&
        !isNoAccessCustomField &&
        view_st_custom_tab?.toString() === "1"
          ? 1
          : 0,
      address_from: formik?.values?.address_from || "directory",
      cust_access_code: formik?.values?.customer?.access_code || "",
    };

    try {
      const response = await addServiceTicketAPI(getValuableObj(formData));
      if (response?.success) {
        EventLogger.log(
          EVENT_LOGGER_NAME.service_tickets + EVENT_LOGGER_ACTION.added,
          1
        );
        setIsSubmit(false);
        setDrowerOpen(!drowerOpen);
        if (window.ENV.PAGE_IS_IFRAME) {
          parentPostMessage(sendMessageKeys?.modal_change, {
            open: false,
          });
          return;
        } else {
          navigate(`${response?.data?.ticketId?.toString()}`);
        }
      } else {
        notification.error({
          description: response?.message,
        });
      }
    } catch (error) {
      setIsSubmit(false);
    } finally {
      setIsSubmit(false);
      setSubmitting(false);
    }
  };

  const globalProject: IInitialGlobalData["config"]["global_project"] =
    getGlobalProject();

  const defaultProject: IProject = useMemo(() => {
    if (globalProject) {
      const { project_id, project_name } = globalProject || {};
      const id = Number(project_id);
      if (id) {
        return { id, project_name };
      }
    }
    return {} as IProject;
  }, [JSON.stringify(globalProject || {})]);

  const dynamicValidationSchema = componentList.reduce((acc, fieldName) => {
    if (fieldName.multiple || fieldName.type === "checkbox-group") {
      acc[fieldName.name] = Yup.array()
        .of(Yup.string().required("This field is required."))
        .min(1, "This field is required.")
        .required("This field is required.");
    } else {
      acc[fieldName.name] = Yup.string()
        .trim()
        .required("This field is required.");
    }
    return acc;
  }, {} as Record<string, Yup.StringSchema | Yup.AnySchema>);

  // add formik
  const formik = useFormik({
    // enableReinitialize: true,
    initialValues: {
      title: "",
      project: defaultProject,
      customer: {} as CustomerEmail,
      invoiced_to: {} as CustomerEmail,
      custom_id: "",
      customer_contract: "",
      address_from: "directory",
      custom_fields:
        view_st_custom_tab?.toString() === "1" &&
        initialFormValues.custom_fields
          ? initialFormValues.custom_fields
          : {},
    },
    // enableReinitialize: true,
    validationSchema: Yup.object({
      project: Yup.object().shape({
        id: Yup.number().notOneOf([0], "Select any Project."),
      }),
      customer: Yup.object().shape({
        user_id: Yup.number()
          .required("This field is required.")
          .notOneOf([0], "Select any Customer."),
      }),
      custom_id:
        is_custom_service_tickets_id === 0
          ? Yup.string()
          : Yup.string().required("This field is required."),
      invoiced_to: Yup.object().shape({
        id: Yup.number().notOneOf([0], "Select any Project."),
      }),
      title: Yup.string().trim().required("This field is required."),
      custom_fields:
        view_st_custom_tab?.toString() === "1"
          ? Yup.object().shape(dynamicValidationSchema)
          : Yup.object(),
    }),
    onSubmit: handleSubmit,
  });

  const [initialValuesState, setInitialValuesState] = useState<any>(
    formik.values
  );

  const newProject = project || defaultProject;
  const getCustomer = async (usersIds: string, contactIDs?: string) => {
    let customer: Partial<CustomerSelectedData> = {};
    const data = await getApiDefaultParams({
      otherParams: {
        // apply filter status 0 customer coming only active so for both archive and active record
        // we need to remove for this bug:- https://app.clickup.com/t/86cyn3cjd
        // filter: {
        //   status: "0",
        // },
        directory: [2, 3, 4, 22, 23, 204],
        directories: usersIds,
        start: 0,
        sales_address_details: 1,
        limit: 10,
        app_users: false,
        service_details: undefined,
        contacts_on_demand: 1,
      },
    });

    const response = (await webWorkerApi<IgetTagsRes>({
      url: apiRoutes.COMMON.get_global_directory,
      method: "post",
      data: data,
    })) as IGetGlobalDirectoryRes;
    if (response?.success) {
      customer = response?.data[0] || {};
    }
    return customer;
  };
  const addCustomer = async (
    customer: Partial<CustomerEmail>,
    billedTo: Partial<CustomerEmail>,
    formikValue: Partial<typeof formik.values>
  ) => {
    let newCustomer: Partial<CustomerSelectedData> = {};
    let newBilledTo: Partial<CustomerSelectedData> = {};

    if (customer.user_id) {
      newCustomer = await getCustomer(
        customer.user_id?.toString(),
        customer.contact_id ? customer.contact_id?.toString() : undefined
      );
    }
    if (billedTo.user_id) {
      newBilledTo = await getCustomer(
        billedTo.user_id?.toString(),
        billedTo.contact_id ? billedTo.contact_id?.toString() : undefined
      );
    } else if (
      newCustomer.billed_to?.toString() &&
      Number(newCustomer.billed_to)
    ) {
      billedTo = {
        user_id: Number(newCustomer.billed_to),
        contact_id: Number(newCustomer.billed_to_contact),
      };
      if (billedTo.user_id) {
        newBilledTo = await getCustomer(
          billedTo.user_id?.toString(),
          billedTo.contact_id ? billedTo.contact_id?.toString() : undefined
        );
      }
    }
    if (newCustomer.user_id || newBilledTo.user_id) {
      if (Number(billedTo.user_id) && Number(newBilledTo.user_id)) {
        formik.setValues((prev) => ({
          ...prev,
          ...formikValue,
          customer: newCustomer,
          invoiced_to: newBilledTo,
        }));
        setInitialValuesState((prevState: any) => ({
          ...prevState,
          customer: newCustomer,
          invoiced_to: newBilledTo,
        }));
      } else if (Number(customer.billed_to)) {
        formik.setValues((prev) => ({
          ...prev,
          customer: newCustomer,
        }));
        setInitialValuesState((prevState: any) => ({
          ...prevState,
          customer: newCustomer,
        }));
        if (customer.billed_to) {
          const customerBilledTo = await getCustomer(
            customer.billed_to?.toString(),
            Number(customer.billed_to_contact)
              ? customer.billed_to_contact?.toString()
              : undefined
          );
          if (customerBilledTo.user_id) {
            formik.setValues((prev) => ({
              ...prev,
              ...formikValue,
              invoiced_to: customerBilledTo,
              customer: newCustomer,
            }));
          } else {
            formik.setValues((prev) => ({
              ...prev,
              ...formikValue,
              customer: newCustomer,
            }));
            setInitialValuesState((prevState: any) => ({
              ...prevState,
              invoiced_to: customerBilledTo,
            }));
          }
        }
      } else {
        formik.setValues((prev) => ({
          ...prev,
          ...formikValue,
          customer: newCustomer,
          invoiced_to: {},
        }));
        setInitialValuesState((prevState: any) => ({
          ...prevState,
          customer: newCustomer,
          invoiced_to: {},
        }));
      }
    } else {
      formik.setValues((prev) => ({
        ...prev,
        customer: {},
        ...formikValue,
        invoiced_to: {},
      }));
      setInitialValuesState((prevState: any) => ({
        ...prevState,
        customer: {},
        invoiced_to: {},
      }));
    }
  };

  const setCustomerInterLinkedData = (project: IProject) => {
    let customer = {
      user_id: Number(project.customer_id),
      contact_id: Number(project.customer_contact_id),
    };
    let billedTo = {
      user_id: Number(project.billed_to),
      contact_id: Number(project.billed_to_contact),
    };

    if (Number(formik.values.customer.user_id)) {
      customer = {
        user_id: Number(formik.values.customer.user_id),
        contact_id: Number(formik.values.customer.contact_id),
      };
    }

    if (!billedTo.user_id && Number(formik.values.customer.billed_to)) {
      billedTo = {
        user_id: Number(formik.values.customer.billed_to),
        contact_id: Number(formik.values.customer.billed_to_contact),
      };
    }
    const customerIds = [customer.user_id, billedTo.user_id]
      .filter(Boolean)
      .join(",");

    if (customerIds) {
      addCustomer(customer, billedTo, {
        project,
        address_from: project ? "project" : "directory",
        customer_contract: project.customer_contract || "",
      });
      setInitialValuesState((prevState: any) => ({
        ...prevState,
        project,
        customer_contract: project.customer_contract || "",
      }));
    } else {
      formik.setValues((prev) => ({
        ...prev,
        customer: {},
        address_from: project ? "project" : "directory",
        project,
        invoiced_to: {},
      }));
      setInitialValuesState((prevState: any) => ({
        ...prevState,
        customer: {},
        project,
        invoiced_to: {},
      }));
    }
  };

  useEffect(() => {
    if (!searchparams.get("customer_id")) {
      setCustomerInterLinkedData(newProject);
    }
  }, [JSON.stringify(newProject || {}), searchparams.get("customer_id")]);

  useEffect(() => {
    const customerId = Number(searchparams.get("customer_id") || "");
    if (customerId) {
      addCustomer({ user_id: customerId }, {}, (prev: IFormValues) => ({
        ...prev,
        custom_id: defaultCustomID,
        project: newProject,
      }));
    }
  }, [searchparams.get("customer_id")]);

  let projectCallApiCalled = false;
  useEffect(() => {
    if (
      !projectCallApiCalled &&
      formik.values.project.id &&
      Object.keys(formik.values.project).length <= 2
    ) {
      projectCallApiCalled = true;

      const fetchData = async () => {
        const fetchProject = async (
          is_completed: boolean | undefined = false
        ) => {
          try {
            const apiParams = await getWebWorkerApiParams<SetProjectsParams>({
              otherParams: {
                start: 0,
                limit: 1,
                is_completed,
                record_type: "project",
                projects: formik.values.project.id?.toString(),
                need_all_projects: 0,
                global_call: true,
                filter: { status: "0" },
              },
            });
            const response = (await webWorkerApi({
              url: apiRoutes.GET_PROJECTS.url,
              method: "post",
              data: apiParams,
            })) as IGetProjectsApiResponse;
            if (
              response &&
              response.data &&
              response.data.projects &&
              response.data.projects.length
            ) {
              return response.data.projects.find((a) => a);
            }
            if (!is_completed) {
              return await fetchProject(true);
            }
          } catch (error) {
            console.error(
              `\n File: #AddServicesTickets.tsx -> Line: #567 ->  `,
              error
            );
          }
        };
        const project = await fetchProject();
        if (project) {
          setCustomerInterLinkedData({
            ...project,
            view_in_calendar: Number(project.view_in_calendar),
            view_in_schedule: Number(project.view_in_schedule),
            allow_overbilling: Number(project.allow_overbilling),
            is_assigned_project: Number(project.is_assigned_project),
            show_client_access: Number(project.show_client_access),
            start_date: project.start_date?.toString(),
            end_date: project.end_date || undefined,
            customer_name: project.customer_name || undefined,
            customer_name_only: project.customer_name_only || undefined,
            cust_image: project.cust_image || undefined,
          });
        }
      };
      fetchData();
    }
  }, [formik.values.project.id]);

  return (
    <>
      <Drawer
        open={drowerOpen}
        rootClassName="drawer-open"
        width={718}
        maskClosable={false}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-ticket"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {editView
                ? _t("Service Ticket")
                : `Add ${replaceDOMParams(
                    sanitizeString(module_singular_name)
                  )}`}
            </Header>
          </div>
        }
        // this is will NF once testing done it will be merge on dev
        closeIcon={
          !window.ENV.PAGE_IS_IFRAME && (
            <CloseButton
              onClick={() => {
                setIsSubmit(false);
                setDrowerOpen(false);
                setSearchParams((prev) => {
                  prev.delete("action");
                  prev.delete("project");
                  prev.delete("customer_id");
                  return prev;
                });
              }}
            />
          )
        }
      >
        <form className="py-4" onSubmit={formik.handleSubmit} noValidate>
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="flex flex-col gap-5">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <ButtonField
                    label={_t("Project")}
                    name="project_id"
                    labelPlacement="top"
                    value={
                      formik.values.project.project_id &&
                      HTMLEntities.decode(
                        sanitizeString(formik.values.project.project_name)
                      )
                    }
                    onClick={() => {
                      setIsSelectProOpen(true);
                    }}
                    addonBefore={
                      Boolean(formik.values.project.id) && (
                        <ProjectFieldRedirectionIcon
                          projectId={formik.values.project.id?.toString() || ""}
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        />
                      )
                    }
                    errorMessage={
                      isSubmit ? formik.errors.project?.project_id : ""
                    }
                  />
                </div>
                <div className="w-full">
                  <InputField
                    label={_t("Title")}
                    name="title"
                    labelPlacement="top"
                    value={formik.values.title}
                    onChange={(e) =>
                      formik.setFieldValue("title", e.target.value)
                    }
                    isRequired={true}
                    errorMessage={isSubmit ? formik.errors.title : ""}
                  />
                </div>

                <div className="w-full">
                  <ButtonField
                    label={_t("Customer")}
                    name="customerName"
                    labelPlacement="top"
                    required={true}
                    value={
                      formik.values.customer.user_id &&
                      HTMLEntities.decode(
                        sanitizeString(formik.values.customer.display_name)
                      )
                    }
                    onClick={() => {
                      setCustomerType("customer");
                      setCustomerOptions([
                        CFConfig.customer_key,
                        CFConfig.lead_key,
                        "my_project",
                      ]);
                      setIsOpenSelectCustomer(true);
                    }}
                    avatarProps={
                      formik.values.customer.display_name
                        ? {
                            user: {
                              name: HTMLEntities.decode(
                                sanitizeString(
                                  formik.values.customer.display_name
                                )
                              ),
                              image: formik.values.customer.image,
                            },
                          }
                        : undefined
                    }
                    addonBefore={
                      <>
                        {Boolean(formik.values.customer.user_id) && (
                          <div className="flex items-center gap-1">
                            <ContactDetailsButton
                              onClick={(event) => {
                                event.stopPropagation();
                                setSelectedContactId(
                                  formik.values.customer.user_id
                                );
                                setSelectedAddContactId(
                                  formik.values.customer.contact_id || 0
                                );
                                setContactDetailDialogOpen(true);
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              directoryId={
                                formik.values.customer.user_id?.toString() || ""
                              }
                              directoryTypeKey={
                                formik?.values.customer?.type_key !== "contact"
                                  ? formik?.values.customer?.type_key || ""
                                  : formik?.values.customer?.parent_type_key ||
                                    ""
                              }
                            />
                          </div>
                        )}
                      </>
                    }
                    errorMessage={
                      isSubmit ? formik.errors.customer?.user_id : ""
                    }
                  />
                </div>

                <div className="grid md:grid-cols-2 gap-5">
                  <div className="w-full">
                    <InputField
                      label={_t("Ticket") + " #"}
                      name="custom_id"
                      labelPlacement="top"
                      value={
                        is_custom_service_tickets_id == 0
                          ? "Save To View"
                          : formik.values.custom_id
                      }
                      maxLength={21}
                      onChange={(e) => {
                        formik?.setFieldValue(
                          "custom_id",
                          e.target.value.trimStart()
                        );
                      }}
                      disabled={is_custom_service_tickets_id == 0}
                      isRequired={
                        is_custom_service_tickets_id === 0 ? false : true
                      }
                      onBlur={() => {
                        formik?.validateField("custom_work_order_id");
                      }}
                      errorMessage={isSubmit ? formik.errors.custom_id : ""}
                    />
                  </div>
                  <div className="w-full">
                    <ButtonField
                      label={_t("Invoiced To")}
                      name="billedTo"
                      labelPlacement="top"
                      value={
                        formik.values.invoiced_to.user_id &&
                        HTMLEntities.decode(
                          sanitizeString(formik.values.invoiced_to.display_name)
                        )
                      }
                      onClick={(e) => {
                        setCustomerType("billTo");
                        setCustomerOptions([
                          CFConfig.employee_key,
                          "my_crew",
                          CFConfig.customer_key,
                          CFConfig.contractor_key,
                          CFConfig.vendor_key,
                          CFConfig.misc_contact_key,
                          "by_service",
                          "my_project",
                        ]);
                        setIsOpenSelectCustomer(true);
                      }}
                      avatarProps={
                        formik.values.invoiced_to.display_name
                          ? {
                              user: {
                                name: HTMLEntities.decode(
                                  sanitizeString(
                                    formik.values.invoiced_to.display_name
                                  )
                                ),
                                image: formik.values.invoiced_to.image,
                              },
                            }
                          : undefined
                      }
                      addonBefore={
                        <>
                          {Boolean(
                            Number(formik.values.invoiced_to.user_id)
                          ) && (
                            <div className="flex items-center gap-1">
                              <ContactDetailsButton
                                onClick={(event) => {
                                  event.stopPropagation();
                                  setSelectedContactId(
                                    formik.values.invoiced_to.user_id
                                  );
                                  setSelectedAddContactId(
                                    formik.values.invoiced_to.contact_id || 0
                                  );
                                  setContactDetailDialogOpen(true);
                                }}
                              />
                              <DirectoryFieldRedirectionIcon
                                directoryId={
                                  formik.values.invoiced_to.user_id?.toString() ||
                                  ""
                                }
                                directoryTypeKey={
                                  formik?.values.invoiced_to?.type_key !==
                                  "contact"
                                    ? formik?.values.invoiced_to?.type_key || ""
                                    : formik?.values.invoiced_to
                                        ?.parent_type_key || ""
                                }
                              />
                            </div>
                          )}
                        </>
                      }
                    />
                  </div>
                </div>
              </SidebarCardBorder>
              {view_st_custom_tab?.toString() === "1" && (
                <OnlyRequiredCustomFields
                  componentList={componentList}
                  formik={formik}
                  isSubmit={isSubmit}
                  loadingCustomField={loadingCustomField}
                />
              )}
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <PrimaryButton
              type="primary"
              buttonText={_t(
                `Create ${
                  HTMLEntities.decode(sanitizeString(module_singular_name)) ??
                  "Service Ticket"
                }`
              )}
              className="w-full justify-center primary-btn"
              htmlType="submit"
              disabled={formik?.isSubmitting || loadingCustomField}
              isLoading={formik.isSubmitting}
              onClick={() => setIsSubmit(true)}
            />
          </div>
        </form>
      </Drawer>
      {isOpenSelectCustomer && (
        <SelectCustomerDrawer
          projectId={Number(formik.values.project.id)}
          openSelectCustomerSidebar={isOpenSelectCustomer}
          closeDrawer={() => {
            setCustomerType("");
            setIsOpenSelectCustomer(false);
            setCustomerOptions([]);
          }}
          singleSelecte={true}
          options={customerOptions?.length > 0 ? customerOptions : []}
          setCustomer={async (data) => {
            const selectedCustomer: CustomerEmail = data.length
              ? (data[0] as CustomerEmail)
              : {};

            if (customerType === "customer") {
              if (Number(selectedCustomer.billed_to)) {
                formik.setValues((prev) => ({
                  ...prev,
                  customer: selectedCustomer,
                  address_from: "directory",
                  project: {},
                }));

                const selectedCustomerData = await getCustomer(
                  selectedCustomer.billed_to?.toString() || ""
                );
                formik.setFieldValue("invoiced_to", selectedCustomerData);
              } else {
                formik.setValues((prev) => ({
                  ...prev,
                  customer: selectedCustomer,
                  address_from: "directory",
                  project: {},
                  invoiced_to: {},
                }));
              }
            } else if (customerType === "billTo") {
              formik.setFieldValue("invoiced_to", selectedCustomer);
            }

            setCustomerType("");
            setCustomerOptions([]);
            setIsOpenSelectCustomer(false);
          }}
          selectedCustomer={
            customerType === "customer"
              ? formik.values.customer.user_id
                ? [formik.values.customer]
                : []
              : customerType === "billTo"
              ? formik.values.invoiced_to.user_id
                ? [formik.values.invoiced_to]
                : []
              : []
          }
          groupCheckBox={true}
        />
      )}
      {isSelectProOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProOpen}
          setOpen={setIsSelectProOpen}
          selectedProjects={
            formik.values.project.id ? [formik.values.project] : []
          }
          onProjectSelected={(data) => {
            const selectedProjectData = data[0] || {};

            setCustomerInterLinkedData(selectedProjectData);
            setIsSelectProOpen(false);
          }}
          module_key={module_key}
          customer_id={formik.values.customer.user_id?.toString() ?? ""}
        />
      )}
      {contactDetailDialogOpen && Boolean(selectedContactId) && (
        <ContactDetailsModal
          isOpenContact={contactDetailDialogOpen}
          onCloseModal={() => {
            setSelectedAddContactId(0);
            setContactDetailDialogOpen(false);
          }}
          contactId={Number(selectedContactId)}
          additional_contact_id={selectedAddContactId}
        />
      )}
    </>
  );
};

export default AddServicesTickets;
