import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "~/hook";

// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { TimePickerField } from "~/shared/components/molecules/timePickerField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";

// Organisms
import OnlyRequiredCustomFields from "~/shared/components/organisms/OnlyRequiredCustomFields/OnlyRequiredCustomFields";

// Other
import { useAppSTDispatch, useAppSTSelector } from "../../../../redux/store";
import { filterOptionBySubstring } from "~/shared/utils/helper/common";
import { Number, sanitizeString } from "~/helpers/helper";
import {
  sendCheckInTimeCardApi,
  updateTimeCardApi,
} from "../../../../redux/action/serviceTicketDetailsAction";
import { useParams } from "@remix-run/react";
import { updateSTDetail } from "../../../../redux/slices/serviceTicketDetailsSlice";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";
import { apiRoutes } from "~/route-services/routes";
import isEmpty from "lodash/isEmpty";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import useDirectoryKeyValue from "~/shared/hooks/useCustomField/useDirectoryKeyValue";
import useSideBarCustomField from "~/shared/hooks/useCustomField/useSidebarCustomField";
import { useFormik } from "formik";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { getCostCode } from "~/redux/action/getCostCodeAction";
import { getGSettings } from "~/zustand";
import {
  EVENT_LOGGER_ACTION,
  EVENT_LOGGER_NAME,
} from "~/shared/constants/event-logger";
import { settimecardRed } from "../../../../utils/config";
import { getGlobalUser } from "~/zustand/global/user/slice";
import dayjs from "dayjs";
import { DateTime } from "luxon";

const EmployeeTimeCard = ({
  serviceTicketDetail,
  employeeTimeCard,
  currentTimeCardData,
  setCurrentTimeCardData,
  setEmployeeTimeCard,
}: IEmployeeTimeCardProps) => {
  const { checkGlobalModulePermissionByKey, getGlobalModuleByKey } =
    useGlobalModule();
  const module = getCurrentMenuModule();
  const { module_id } = module || {};
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const {
    company_id,
    user_id,
    cost_code_id,
    enable_default_cost_code,
    timezone_utc_tz_id,
  } = user || {};
  const appSettings = getGlobalAppSettings();
  const { allow_service_ticket_in_timecard, require_timecard_cost_code } =
    appSettings || {};

  const timeCardModule = useMemo(
    () => getGlobalModuleByKey(CFConfig.time_card_module),
    []
  );
  const customFieldsModule = useMemo(
    () => checkGlobalModulePermissionByKey(CFConfig.custom_fields_module),
    []
  );

  const { has_project_based_cost_codes }: GSettings = getGSettings();

  const { directoryKeyValue, directory }: IDirectoryFormCustomField =
    useDirectoryKeyValue();

  const { componentList, loadingCustomField, checkValidation } =
    useSideBarCustomField(
      { directory, directoryKeyValue } as IDirectoryFormCustomField,
      {
        moduleId: timeCardModule?.module_id,
        requiredFieldsOnly: 0,
        moduleKey: timeCardModule?.module_key,
        formListKey: "clockin",
      } as IRequestCustomFieldForSidebar
    );

  const [isSubmit, setIsSubmit] = useState<boolean>(false);

  const initialFormValues = componentList.length
    ? {
        custom_fields: componentList.reduce((acc, item) => {
          acc[item.name] = item?.value ?? "";
          return acc;
        }, {} as ICustomFieldInitValue),
      }
    : {};

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      custom_fields: initialFormValues.custom_fields
        ? initialFormValues.custom_fields
        : {},
    },
    onSubmit: () => {},
  });

  const { _t } = useTranslation();
  const dispatch = useAppSTDispatch();
  const params: RouteParams = useParams();
  const { codeCostData }: IGetCostCodeList = useAppSTSelector(
    (state) => state.costCode
  );
  const { details }: IServiceInitialState = useAppSTSelector(
    (state) => state.serviceDetails
  );

  const validTimezone = DateTime.local().setZone(timezone_utc_tz_id);
  const luxonTime = validTimezone.isValid ? validTimezone : DateTime.local();
  const formattedTime = luxonTime.toFormat("hh:mm a");
  const initialDayjsTime = dayjs(formattedTime, "hh:mm A");

  const [date, setDate] = useState(initialDayjsTime);

  const [dateChange, setDateChange] = useState(0);
  const [loading, setLoading] = useState(false);
  const [serviceTicketinfo, setServiceTicketInfo] =
    useState<IServiceTicketDetails | null>(null);
  const [selectedProject, setSelectedProject] = useState<IProject>({});
  useEffect(() => {
    setServiceTicketInfo(serviceTicketDetail);
  }, [JSON.stringify(serviceTicketDetail)]);

  useEffect(() => {
    if (!isEmpty(currentTimeCardData?.cost_code_id)) {
      setServiceTicketInfo((prev) => ({
        ...prev,
        cost_code_id: currentTimeCardData?.cost_code_id,
        cost_code_name: currentTimeCardData?.cost_code_name,
      }));
    } else {
      const existsInList = codeCostData.some(
        (item) => item.code_id?.toString() === user?.cost_code_id?.toString()
      );

      if (user?.enable_default_cost_code && existsInList) {
        setServiceTicketInfo((prev) => ({
          ...prev,
          cost_code_id: user?.cost_code_id,
          cost_code_name: user?.cost_code_name,
        }));
      } else {
        setServiceTicketInfo((prev) => ({
          ...prev,
          cost_code_id: "",
          cost_code_name: "Unassigned",
        }));
      }
    }
  }, [currentTimeCardData?.cost_code_id, user, codeCostData]);

  useEffect(() => {
    const costCodeId = serviceTicketinfo?.cost_code_id?.toString();

    if (
      require_timecard_cost_code?.toString() === "1" &&
      currentTimeCardData &&
      codeCostData?.length &&
      costCodeId &&
      costCodeId !== "Unassigned"
    ) {
      const existsInList = codeCostData.some(
        (item) => item.code_id?.toString() === costCodeId
      );

      if (!existsInList) {
        setServiceTicketInfo((prev) => {
          if (prev.cost_code_id !== "Unassigned") {
            return {
              ...prev,
              cost_code_id: "Unassigned",
              cost_code_name: "",
            };
          }
          return prev;
        });
      }
    }
  }, [
    codeCostData,
    serviceTicketinfo?.cost_code_id,
    require_timecard_cost_code,
    currentTimeCardData,
  ]);

  useEffect(() => {
    if (!serviceTicketDetail?.project_id) {
      const fetchData = async () => {
        const fetchProject = async (
          is_completed: boolean | undefined = false
        ) => {
          try {
            const apiParams = await getWebWorkerApiParams<SetProjectsParams>({
              otherParams: {
                start: 0,
                limit: 1,
                is_completed,
                record_type: "project,opportunity",
                category: "timecard_project",
                need_all_projects: 0,
                global_call: true,
                filter: { status: "0" },
              },
            });
            const response = (await webWorkerApi({
              url: apiRoutes.GET_PROJECTS.url,
              method: "post",
              data: apiParams,
            })) as IGetProjectsApiResponse;
            if (
              response &&
              response.data &&
              response.data.custom_generic_projects &&
              response.data.custom_generic_projects.length
            ) {
              return response.data.custom_generic_projects.find(
                (a: IGetCustomGenericProjectsProject) =>
                  a.key == "timecard_service_ticket"
              );
            }
          } catch (error) {
            console.error(
              `\n File: #EmployeeTimeCard.tsx -> Line: #183 ->  `,
              error
            );
          }
        };

        const project = await fetchProject();
        if (project) {
          setSelectedProject({
            project_id: project?.key,
            project_name: project?.name,
          });
        }
      };
      fetchData();
    } else {
      setSelectedProject({
        project_id: serviceTicketDetail?.project_id,
        project_name: serviceTicketDetail?.project_name,
      });
    }
  }, []);

  const codeCostList: ISelectList[] = useMemo(
    () => [
      { label: "Unassigned", value: "Unassigned" }, // Add Unassigned option
      ...(codeCostData?.map((item: ICostCode) => ({
        label: `${item.cost_code_name} ${
          item?.csi_code ? "(" + item?.csi_code + ")" : ""
        }`,
        value: item.code_id.toString(),
      })) || []),
    ],
    [codeCostData]
  );
  const handleUpdateTimecardData = async () => {
    setLoading(true);

    try {
      let reqParams: IUpdateTimeCardReq = {
        service_ticket_id:
          allow_service_ticket_in_timecard == 1
            ? Number(params?.id)
            : Number(currentTimeCardData.service_ticket_id),
        old_service_ticket_id: Number(currentTimeCardData.service_ticket_id),
        timecard_id: Number(currentTimeCardData?.timecard_id),
        allow_service_tickets: allow_service_ticket_in_timecard,
        project_id: selectedProject?.project_id?.toString(),
        is_dashboard_call: 1,
        module_id: module_id,
        type: currentTimeCardData?.type,
      };
      if (
        currentTimeCardData?.cost_code_id != serviceTicketinfo?.cost_code_id
      ) {
        reqParams.action = "cost_code";
      } else if (
        currentTimeCardData?.project_id != serviceTicketinfo?.project_id
      ) {
        reqParams.action = "project";
      } else if (
        currentTimeCardData?.service_ticket_id?.toString() !=
        params.id?.toString()
      ) {
        reqParams.action = "service_ticket";
      }
      if (serviceTicketinfo?.cost_code_id) {
        reqParams.cost_code_id = Number(serviceTicketinfo?.cost_code_id);
      }

      const res = (await updateTimeCardApi(reqParams)) as any;
      if (res.success) {
        var company_detail = {
          company_id: company_id,
          user_id: user_id,
          timecard_id: res.data.timecard_id,
          action: reqParams.action,
          type: res.data.type,
        };
        settimecardRed(company_detail);
        setLoading(false);
        const updateSTInfo: {
          current_timecard_service_ticket?: string | undefined;
          service_date_only?: string;
          job_status?: number;
        } = {
          current_timecard_service_ticket: params?.id,
        };
        if (!details?.service_date_only || !details?.service_time) {
          updateSTInfo.service_date_only = res?.data?.clock_in_date.toString();
        }
        updateSTInfo.job_status = 150;
        dispatch(updateSTDetail(updateSTInfo));
        setCurrentTimeCardData(null);
        setEmployeeTimeCard(false);
      } else {
        setEmployeeTimeCard(false);
        notification.error({
          description: res.message,
        });
      }
    } catch (err) {
      console.error(err);
      setLoading(false);
    }
  };

  const handleCheckIn = async () => {
    setLoading(true);

    if (
      customFieldsModule !== "no_access" &&
      customFieldsModule !== "read_only"
    ) {
      let isCustomFieldValid = checkValidation(
        formik.values.custom_fields,
        componentList
      );

      if (!isCustomFieldValid) {
        var company_detail = {
          company_id: company_id,
          user_id: user_id,
          // timecard_id: response.data.timecard_id,
          action: "start",
          // type: response.data.type,
        };
        settimecardRed(company_detail);
        setLoading(false);
        return;
      }
    }

    try {
      let reqParams: ICheckInTimeCardReq = {
        service_ticket_id: Number(params?.id),
        allow_service_tickets: 1,

        project_id: selectedProject?.project_id
          ? selectedProject?.project_id?.toString()
          : "",
        clockin: 1,
        clockinmultiple: 1,
        module_id: timeCardModule?.module_id,
        type: "employee",
        ...formik.values.custom_fields,
      };
      if (!currentTimeCardData) {
        reqParams.timecard_start_time_change = dateChange;
        reqParams.timecard_start_time = dayjs(date).format("hh:mm A");
      }
      if (serviceTicketinfo?.cost_code_id) {
        reqParams.cost_code_id = Number(serviceTicketinfo?.cost_code_id);
        reqParams.tb_timecard_costcode = Number(
          serviceTicketinfo?.cost_code_id
        );
      } else if (enable_default_cost_code == 1) {
        reqParams.cost_code_id = Number(cost_code_id);
        reqParams.tb_timecard_costcode = Number(cost_code_id);
      }
      const res = (await sendCheckInTimeCardApi(reqParams)) as ISericeTicketRes;
      if (res) {
        if (res.success) {
          EventLogger.log(
            EVENT_LOGGER_NAME.time_card + EVENT_LOGGER_ACTION.add,
            1
          );
          setLoading(false);
          const updateSTInfo: {
            current_timecard_service_ticket?: string | undefined;
            service_date_only?: string;
            job_status?: number;
          } = {
            current_timecard_service_ticket: params?.id,
          };
          if (!details?.service_date_only || !details?.service_time) {
            updateSTInfo.service_date_only =
              res?.data?.clock_in_date.toString();
          }
          updateSTInfo.job_status = 150;
          dispatch(updateSTDetail(updateSTInfo));
          setEmployeeTimeCard(false);
        } else {
          setLoading(false);
          setEmployeeTimeCard(false);
          notification.error({
            description: res.message,
          });
        }
      }
    } catch (err) {
      setLoading(false);
      console.error(err);
    }
  };

  const handleCheckInTimeCard = () => {
    setIsSubmit(true);
    const isCostCodeRequired = require_timecard_cost_code?.toString() === "1";
    const costCode = serviceTicketinfo?.cost_code_id?.toString()?.trim();
    const hasValidCostCode =
      (isCostCodeRequired && !costCode) ||
      costCode === "" ||
      costCode === "Unassigned";
    if (hasValidCostCode) {
      notification.error({
        description: _t("Please select Cost Code."),
      });
      return;
    }

    if (serviceTicketinfo?.service_ticket_id || selectedProject?.project_id) {
      if (currentTimeCardData) {
        if (
          (!serviceTicketinfo?.cost_code_id &&
            require_timecard_cost_code?.toString() === "1") ||
          (serviceTicketinfo?.cost_code_id === "Unassigned" &&
            require_timecard_cost_code?.toString() === "1")
        ) {
          notification.error({
            description: _t("Please select Cost Code."),
          });
          return;
        } else handleUpdateTimecardData();
      } else {
        handleCheckIn();
      }
    } else {
      notification.error({
        description: _t("Please select service ticket or project"),
      });
      return;
    }
  };

  useEffect(() => {
    dispatch(
      getCostCode({
        project_id: has_project_based_cost_codes
          ? details.project_id
          : undefined,
        timecard_cost_code: 1,
      })
    );
  }, [details.project_id, has_project_based_cost_codes]);

  return (
    <Drawer
      open={employeeTimeCard}
      rootClassName="drawer-open"
      width={718}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-calendar-clock"
            />
          </div>
          <Header
            level={5}
            className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
          >
            {_t("Employee Time Card")}
          </Header>
        </div>
      }
      closeIcon={<CloseButton onClick={() => setEmployeeTimeCard(false)} />}
    >
      <div className="py-4">
        <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
          <div className="grid gap-4">
            <SidebarCardBorder addGap={true}>
              {!currentTimeCardData && (
                <div className="w-full">
                  <TimePickerField
                    label={_t("Clock-In Time")}
                    use12Hours
                    name="time"
                    labelPlacement="top"
                    format="hh:mm A"
                    value={date}
                    allowClear={false}
                    onChange={(_, val) => {
                      const luxonTime =
                        DateTime.local().setZone(timezone_utc_tz_id);
                      const validTime = luxonTime.isValid
                        ? luxonTime
                        : DateTime.local();

                      const formattedTime = validTime.toFormat("hh:mm a");
                      const currentTime = dayjs(formattedTime, "hh:mm A");
                      if (val && dayjs(_).isAfter(currentTime, "minute")) {
                        notification.error({
                          description:
                            "Clock-In time should be less than or equal to current time.",
                        });
                        setDate(currentTime); // Reset to current time
                      } else {
                        setDate(_);
                        setDateChange(1);
                      }
                    }}
                  />
                </div>
              )}
              <div className="w-full">
                <InlineField
                  label={_t("Service Ticket")}
                  labelPlacement="top"
                  field={
                    <Tooltip
                      placement="topLeft"
                      title={`ST #${
                        serviceTicketinfo?.custom_service_ticket_id ??
                        serviceTicketinfo?.company_ticket_id
                      }: ${HTMLEntities.decode(
                        sanitizeString(serviceTicketinfo?.title)
                      )}`}
                    >
                      <InputField
                        label={_t("")}
                        name="service-ticket"
                        labelPlacement="top"
                        disabled={serviceTicketinfo?.title ? true : false}
                        value={`ST #${
                          serviceTicketinfo?.custom_service_ticket_id ??
                          serviceTicketinfo?.company_ticket_id
                        }: ${HTMLEntities.decode(
                          sanitizeString(serviceTicketinfo?.title)
                        )}`}
                        onChange={() => {}}
                        autoComplete="off"
                      />
                    </Tooltip>
                  }
                />
              </div>
              <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                <div className="w-full">
                  <ButtonField
                    label={_t("Project")}
                    name="project_id"
                    labelPlacement="top"
                    iconView={true}
                    required={true}
                    isDisabled={selectedProject?.project_id !== ""}
                    value={selectedProject?.project_name}
                    onClick={() => {}}
                  />
                </div>
                <div className="w-full">
                  <SelectField
                    label={_t("Cost Code")}
                    labelPlacement="top"
                    allowClear={true}
                    showSearch
                    isRequired={require_timecard_cost_code?.toString() === "1"}
                    disabled={false}
                    options={codeCostList}
                    value={
                      codeCostList.find((item) => {
                        if (serviceTicketinfo?.cost_code_id) {
                          return (
                            serviceTicketinfo.cost_code_id.toString() ===
                            item.value?.toString()
                          );
                        }
                        if (enable_default_cost_code == 1) {
                          return (
                            (cost_code_id?.toString() || "") ===
                            item.value?.toString()
                          );
                        }
                        return item.value === "Unassigned";
                      }) ||
                      codeCostList.find((item) => item.value === "Unassigned")
                    }
                    onChange={(value: string | string[]) => {
                      if (!Array.isArray(value)) {
                        const costCodeName = codeCostData.find(
                          (item) =>
                            item?.code_id?.toString() === value?.toString()
                        );
                        setServiceTicketInfo((prevValues) => ({
                          ...prevValues,
                          cost_code_id: value,
                          cost_code_name: `${costCodeName?.cost_code_name} ${
                            !!costCodeName?.csi_code
                              ? "(" + costCodeName?.csi_code + ")"
                              : ""
                          }`,
                        }));
                      }
                    }}
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                    onClear={() => {
                      setServiceTicketInfo((prevState) => ({
                        ...prevState,
                        cost_code_id: "",
                      }));
                    }}
                  />
                </div>
              </div>
            </SidebarCardBorder>
            {!Boolean(Number(currentTimeCardData?.timecard_id)) &&
              customFieldsModule !== "no_access" &&
              customFieldsModule !== "read_only" && (
                <OnlyRequiredCustomFields
                  componentList={componentList}
                  formik={formik}
                  isSubmit={isSubmit}
                  loadingCustomField={loadingCustomField}
                  title={_t("Clock-In Custom Fields (Required)")}
                  headerLeftContent={
                    <Tooltip
                      title={_t(
                        "A response must be provided before the user can start a new Time Card. (Example: Have you had a fever in the past 24 hours?)"
                      )}
                    >
                      <FontAwesomeIcon
                        className="w-3.5 h-3.5 text-primary-900 mt-0.5 dark:text-white/90 ml-1"
                        icon="fa-regular fa-info-circle"
                      />
                    </Tooltip>
                  }
                  module_name={timeCardModule?.module_name}
                />
              )}
          </div>
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
          <PrimaryButton
            type="primary"
            className="w-full justify-center success-btn"
            buttonText={_t("Clock-In")}
            disabled={loading || loadingCustomField}
            isLoading={loading}
            onClick={handleCheckInTimeCard}
          />
        </div>
      </div>
    </Drawer>
  );
};

export default EmployeeTimeCard;
