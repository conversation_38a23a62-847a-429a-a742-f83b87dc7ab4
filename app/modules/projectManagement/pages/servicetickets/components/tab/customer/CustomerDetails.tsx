// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// Organisms
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";

// Other
import { useAppSTDispatch, useAppSTSelector } from "../../../redux/store";
import { useEffect, useRef, useState } from "react";
import { defaultConfig } from "~/data";
import {
  getStatusActionFor<PERSON>ield,
  getStatus<PERSON><PERSON><PERSON>ield,
  validateEmail,
} from "~/shared/utils/helper/common";
import { fieldStatus } from "../../../utils/constants";
import { setActiveField } from "~/redux/slices/selectCustomerSlice";
import delay from "lodash/delay";
import { useParams } from "@remix-run/react";
import { updateSTCustomerApi } from "../../../redux/action/serviceTicketCustomerAction";
import { updateSTCustomerDetail } from "../../../redux/slices/serviceTicketCustomerSlice";
import { useTranslation } from "~/hook";
import { updateSTDetail } from "../../../redux/slices/serviceTicketDetailsSlice";
import { getDirectaryIdByName } from "~/components/sidebars/multi-select/customer/zustand/action";
import { sanitizeString } from "~/helpers/helper";
import { useExistingCustomers } from "~/components/sidebars/multi-select/customer/zustand";
import { updateSTDetailApi } from "../../../redux/action/serviceTicketDetailsAction";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { useFormik } from "formik";

const CustomerDetails = () => {
  const dispatch = useAppSTDispatch();

  const currentMenuModule = getCurrentMenuModule();
  const { module_access } = currentMenuModule || {};

  const params: RouteParams = useParams();

  const { customerDetails }: ISTCustomerInitialState = useAppSTSelector(
    (state) => state.serviceTicketCustomerDetails
  );

  const { details }: IServiceInitialState = useAppSTSelector(
    (state) => state.serviceDetails
  );

  let { getExistingUsersWithApi } = useExistingCustomers();

  const { _t } = useTranslation();

  const formik = useFormik({
    initialValues: {
      customer: {} as TselectedContactSendMail,
      location: "",
      cust_notes: "",
    },
    onSubmit: () => {},
  });

  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);
  const loadingStatusRef = useRef(fieldStatus);
  const [selectedAdiContactId, setSelectedAdiContactId] = useState<
    number | null | string
  >(null);
  const [selectedContactId, setSelectedContactId] = useState<
    number | string | undefined
  >("");
  const [customerFieldType, setCustomerFieldType] = useState<string>("");
  const [customerOptions, setCustomerOptions] = useState<CustomerEmailTab[]>(
    []
  );
  const [isOpenSelectCustomer, setIsOpenSelectCustomer] =
    useState<boolean>(false);
  const [contactDetailDialogOpen, setContactDetailDialogOpen] =
    useState<boolean>(false);

  useEffect(() => {
    if (details && customerDetails) {
      formik.setValues((prev) => ({
        ...prev,
        customer: {
          user_id: details.customer_id,
          display_name: details.customer_name,
          type_name: getDirectaryIdByName(Number(details?.dir_type)),
          contact_id: details.contact_id,
          image: details.contact_id
            ? ""
            : details.customer_data?.length && details.customer_data[0].image,
          type_key:
            details.customer_data?.length && details.customer_data?.length > 0
              ? details.customer_data[0].type_key
              : "",
          parent_type_key:
            details.customer_data?.length && details.customer_data?.length > 0
              ? details.customer_data[0].parent_type_key
                ? details.customer_data[0].parent_type_key
                : ""
              : "",
          company_name: customerDetails.cust_company,
          cust_email: customerDetails.cust_email || "",
        },
        location: customerDetails.location || "",
        cust_notes: customerDetails.cust_notes || "",
      }));
    } else {
      formik.resetForm();
    }

    return () => {
      formik.resetForm();
    };
  }, [JSON.stringify(details), JSON.stringify(customerDetails)]);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleUpdateField = async (
    data: ISTDetailFieldsBoolean,
    field: IFieldStatus["field"]
  ) => {
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });
    if (field === "customer_id") {
      ["cust_company", "cust_email"].forEach((fieldkey) =>
        handleChangeFieldStatus({
          field: fieldkey,
          status: "loading",
          action: "API",
        })
      );
    }
    const updateRes = (await updateSTDetailApi({
      service_ticket_id: params?.id,
      ...data,
    })) as IServiceTicketDetailApiRes;
    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      if (field === "customer_id") {
        ["cust_company", "cust_email"].forEach((fieldkey) =>
          handleChangeFieldStatus({
            field: fieldkey,
            status: "success",
            action: "API",
          })
        );
      }
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      if (field === "customer_id") {
        ["cust_company", "cust_email"].forEach((fieldkey) =>
          handleChangeFieldStatus({
            field: fieldkey,
            status: "error",
            action: "API",
          })
        );
      }
      if (updateRes.statusCode === 403) {
        notification.error({
          description: updateRes.message,
        });
      }
    }
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
      if (field === "customer_id") {
        const fields = ["cust_company", "cust_email"];
        const fieldActions = fields.map((f) =>
          getStatusActionForField(loadingStatusRef.current, f)
        );

        fields.forEach((fieldKey, idx) => {
          const action = fieldActions[idx];
          handleChangeFieldStatus({
            field: fieldKey,
            status: action === "FOCUS" ? "save" : "button",
            action: action || "API",
          });
        });
      }
    }, 3000);
    return updateRes;
  };

  const handleUpdateCustomerField = async (
    data: ISTDetailFieldsBoolean,
    field: IFieldStatus["field"]
  ) => {
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updateSTCustomerApi({
      service_ticket_id: params?.id,
      ...data,
    })) as IServiceTicketDetailApiRes;
    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      if (updateRes.statusCode === 403) {
        notification.error({
          description: updateRes.message,
        });
      }
    }
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
    return updateRes;
  };

  const selectedCustomerList = (): TselectedContactSendMail[] => {
    let selectedCustomer: TselectedContactSendMail[] = [];
    if (customerFieldType == "customer") {
      selectedCustomer = formik.values.customer?.user_id
        ? ([formik.values.customer] as TselectedContactSendMail[])
        : [];
    }
    return selectedCustomer;
  };

  const bracketValue =
    formik.values.customer.display_name?.match(/\(([^)]+)\)/)?.[1] ?? "";
  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Details")}
        iconProps={{
          icon: "fa-solid fa-file-user",
          containerClassName:
            "bg-[linear-gradient(180deg,#7FA3FF1a_0%,#3387FD1a_100%)]",
          id: "customer_details_icon",
          colors: ["#7FA3FF", "#3387FD"],
        }}
      />
      <div className="pt-2">
        <div className="flex lg:flex-row flex-col lg:gap-4 gap-2 mt-[3px] items-start">
          <ul className="w-full flex flex-col gap-1">
            <li className="overflow-hidden">
              <ButtonField
                label={_t("Customer")}
                placeholder={_t("Select Customer")}
                name="customer_id"
                labelPlacement="left"
                editInline={true}
                iconView={true}
                readOnly={module_access === "read_only"}
                value={formik.values.customer.display_name ?? ""}
                statusProps={{
                  status: getStatusForField(loadingStatus, "customer_id"),
                }}
                onClick={() => {
                  setCustomerFieldType("customer");
                  setCustomerOptions([
                    defaultConfig.customer_key,
                    defaultConfig.lead_key,
                    "my_project",
                  ]);
                  setIsOpenSelectCustomer(true);
                }}
                rightIcon={
                  <>
                    {formik.values.customer.display_name &&
                      formik.values.customer.user_id && (
                        <div className="flex gap-1 items-center">
                          <ContactDetailsButton
                            onClick={() => {
                              setSelectedContactId(
                                formik.values.customer.user_id
                              );
                              setSelectedAdiContactId(
                                formik.values.customer.contact_id || "0"
                              );
                              setContactDetailDialogOpen(true);
                            }}
                          />
                          <DirectoryFieldRedirectionIcon
                            directoryId={formik.values.customer.user_id?.toString()}
                            directoryTypeKey={
                              (formik.values.customer.type_key !== "contact"
                                ? formik.values.customer.type_key
                                : formik.values.customer.parent_type_key) || ""
                            }
                          />
                        </div>
                      )}
                  </>
                }
                disabled={
                  getStatusForField(loadingStatus, "customer_id") === "loading"
                }
              />
            </li>
            <li>
              <InputField
                label={_t("Company Name")}
                placeholder={_t("Company Name")}
                labelPlacement="left"
                editInline={true}
                iconView={true}
                readOnly={module_access === "read_only"}
                value={
                  HTMLEntities.decode(
                    sanitizeString(formik.values.customer.company_name || "")
                  ) ??
                  HTMLEntities.decode(sanitizeString(bracketValue)) ??
                  ""
                }
                name="customer.company_name"
                fixStatus={getStatusForField(loadingStatus, "cust_company")}
                onChange={formik.handleChange}
                onMouseEnter={() => {
                  handleChangeFieldStatus({
                    field: "cust_company",
                    status: "edit",
                    action: "ME",
                  });
                }}
                onMouseLeaveDiv={() => {
                  handleChangeFieldStatus({
                    field: "cust_company",
                    status: "button",
                    action: "ML",
                  });
                }}
                onFocus={() =>
                  handleChangeFieldStatus({
                    field: "cust_company",
                    status: "save",
                    action: "FOCUS",
                  })
                }
                onBlur={async (e) => {
                  const value = e.target.value.trim();
                  if ((value || "") !== (customerDetails.cust_company || "")) {
                    formik.setFieldValue("customer.company_name", value);
                    const response = await handleUpdateField(
                      { customer_company: HTMLEntities.encode(value) },
                      "cust_company"
                    );
                    if (response.success) {
                      dispatch(updateSTCustomerDetail({ cust_company: value }));
                    } else {
                      formik.setFieldValue(
                        "customer.company_name",
                        customerDetails.cust_company || ""
                      );
                    }
                  } else {
                    handleChangeFieldStatus({
                      field: "cust_company",
                      status: "button",
                      action: "BLUR",
                    });
                    formik.setFieldValue(
                      "customer.company_name",
                      customerDetails.cust_company || ""
                    );
                  }
                }}
              />
            </li>
            <li>
              <InputField
                label={_t("Location")}
                placeholder={_t("Location (If other than Address)")}
                labelPlacement="left"
                editInline={true}
                iconView={true}
                readOnly={module_access === "read_only"}
                value={formik.values.location}
                name="location"
                fixStatus={getStatusForField(loadingStatus, "location")}
                onChange={formik.handleChange}
                onMouseEnter={() => {
                  handleChangeFieldStatus({
                    field: "location",
                    status: "edit",
                    action: "ME",
                  });
                }}
                onMouseLeaveDiv={() => {
                  handleChangeFieldStatus({
                    field: "location",
                    status: "button",
                    action: "ML",
                  });
                }}
                onFocus={() =>
                  handleChangeFieldStatus({
                    field: "location",
                    status: "save",
                    action: "FOCUS",
                  })
                }
                onBlur={async (e) => {
                  const value = e.target.value.trim();
                  if ((value || "") !== (customerDetails.location || "")) {
                    formik.setFieldValue("location", value);
                    const response = await handleUpdateCustomerField(
                      { location: value },
                      "location"
                    );
                    if (response.success) {
                      dispatch(updateSTCustomerDetail({ location: value }));
                    } else {
                      formik.setFieldValue(
                        "location",
                        customerDetails.location || ""
                      );
                    }
                  } else {
                    handleChangeFieldStatus({
                      field: "location",
                      status: "button",
                      action: "BLUR",
                    });
                    formik.setFieldValue(
                      "location",
                      customerDetails.location || ""
                    );
                  }
                }}
              />
            </li>
            <li>
              <TextAreaField
                label={_t("Internal Notes")}
                placeholder={_t("Write some notes (if needed)")}
                labelPlacement="left"
                editInline={true}
                iconView={true}
                readOnly={module_access === "read_only"}
                value={formik.values.cust_notes}
                name="cust_notes"
                fixStatus={getStatusForField(loadingStatus, "cust_notes")}
                onChange={formik.handleChange}
                onMouseEnter={() => {
                  handleChangeFieldStatus({
                    field: "cust_notes",
                    status: "edit",
                    action: "ME",
                  });
                }}
                onMouseLeaveDiv={() => {
                  handleChangeFieldStatus({
                    field: "cust_notes",
                    status: "button",
                    action: "ML",
                  });
                }}
                onFocus={() =>
                  handleChangeFieldStatus({
                    field: "cust_notes",
                    status: "save",
                    action: "FOCUS",
                  })
                }
                onBlur={async (e) => {
                  const value = e.target.value.trim();
                  if ((value || "") !== (customerDetails.cust_notes || "")) {
                    formik.setFieldValue("cust_notes", value);
                    const response = await handleUpdateCustomerField(
                      { customer_notes: value },
                      "cust_notes"
                    );
                    if (response.success) {
                      dispatch(updateSTCustomerDetail({ cust_notes: value }));
                    } else {
                      formik.setFieldValue(
                        "cust_notes",
                        customerDetails.cust_notes || ""
                      );
                    }
                  } else {
                    handleChangeFieldStatus({
                      field: "cust_notes",
                      status: "button",
                      action: "BLUR",
                    });
                    formik.setFieldValue(
                      "cust_notes",
                      customerDetails.cust_notes || ""
                    );
                  }
                }}
              />
            </li>
            <li>
              <InputField
                label={_t("Email")}
                placeholder={_t("Email")}
                labelPlacement="left"
                editInline={true}
                iconView={true}
                readOnly={module_access === "read_only"}
                value={HTMLEntities.decode(
                  sanitizeString(formik.values.customer.cust_email || "")
                )}
                name="customer.cust_email"
                fixStatus={
                  !!formik.values.customer.cust_email
                    ? getStatusForField(loadingStatus, "cust_email")
                    : undefined
                }
                onChange={formik.handleChange}
                onMouseEnter={() => {
                  handleChangeFieldStatus({
                    field: "cust_email",
                    status: "edit",
                    action: "ME",
                  });
                }}
                onMouseLeaveDiv={() => {
                  handleChangeFieldStatus({
                    field: "cust_email",
                    status: "button",
                    action: "ML",
                  });
                }}
                onFocus={() =>
                  handleChangeFieldStatus({
                    field: "cust_email",
                    status: "save",
                    action: "FOCUS",
                  })
                }
                onBlur={async (e) => {
                  // setIsEmailEditing(false);
                  const value = e?.target?.value.trim();
                  if (value !== customerDetails?.cust_email) {
                    if (value && !validateEmail(value)) {
                      notification.error({
                        description: _t("Invalid email address."),
                      });
                      formik.setFieldValue("customer.cust_email", value);
                      return;
                    }
                    const response = await handleUpdateCustomerField(
                      { customer_email: value },
                      "cust_email"
                    );
                    if (response.success) {
                      dispatch(updateSTCustomerDetail({ cust_email: value }));
                    } else {
                      formik.setFieldValue(
                        "customer.cust_email",
                        customerDetails.cust_email || ""
                      );
                    }
                  } else {
                    handleChangeFieldStatus({
                      field: "cust_email",
                      status: "button",
                      action: "BLUR",
                    });
                    formik.setFieldValue(
                      "customer.customer_email",
                      customerDetails.cust_email || ""
                    );
                  }
                }}
                autoComplete="off"
              />
            </li>
          </ul>
        </div>
      </div>
      {isOpenSelectCustomer && (
        <SelectCustomerDrawer
          projectId={details?.project_id}
          openSelectCustomerSidebar={isOpenSelectCustomer}
          closeDrawer={() => {
            dispatch(setActiveField(customerOptions[0]));
            setIsOpenSelectCustomer(false);
            setCustomerFieldType("");
            setCustomerOptions([]);
          }}
          singleSelecte={true}
          options={customerOptions?.length > 0 ? customerOptions : []}
          setCustomer={async (cust) => {
            const data: any = cust;
            if (data.length > 0) {
              const customer = data?.length ? data[0] || {} : {};
              formik.setFieldValue("customer", customer);
              const formData = {
                customer_id: customer.user_id?.toString() || "",
                project_id: null,
                project_name: null,
                customer_street: customer?.address1 || "",
                customer_street2: customer?.address2 || "",
                customer_city: customer?.city || "",
                customer_state: customer?.state || "",
                customer_zip: customer?.zip || "",
                customer_email: customer?.email || "",
                contact_id: customer?.contact_id,
                customer_company: customer.company_name?.toString() || "",
                cust_access_code: customer?.access_code || null,

                // need to reset data of invoice To field
                billed_to: "",
                billed_to_display_name: "",
                billed_to_dir_type: "",
                billed_to_contact: 0,
              };

              let updatedData = {
                customer_first_name: customer.first_name,
                customer_id: customer.user_id,
                contact_id: customer.contact_id,
                customer_last_name: customer.last_name,
                customer_city: customer?.city || "",
                customer_state: customer?.state || "",
                cust_company: customer.company_name?.toString() || "",
                cust_email: customer?.email || "",
                cust_street: customer?.address1 || "",
                cust_street2: customer?.address2 || "",
                cust_zip: customer?.zip || "",
                cust_access_code: customer?.access_code || null,
                customer_name: customer?.display_name || "",
                project_id: null,
                project_name: null,
                dir_type: customer.type,
                customer_data: data.map((record: any) => ({
                  user_id: record.user_id,
                  first_name: record.first_name,
                  last_name: record.last_name,
                  image: record.image,
                  address1: record.address1,
                  address2: record.address2,
                  phone: record.phone,
                  cell: record.cell,
                  email: record.email,
                  city: record.city,
                  state: record.state,
                  zip: record.zip,
                  type: record.type,
                  company_name: record.company_name,
                  type_name: record.type_name,
                  type_key: record.type_key,
                  parent_type_key: record.parent_type_key,
                })),
                billed_to: "",
                billed_to_display_name: "",
                billed_to_dir_type: "",
                billed_to_contact: 0,
                invoiced_to_data: [],
              };

              let updatedCustomerData = {
                customer_id: customer.user_id,
                cust_name: customer?.display_name || "",
                cust_company: customer.company_name?.toString() || "",
                contact_id: customer.contact_id,
                customer_state: customer?.state || "",
                customer_city: customer?.city || "",
                customer_zip: customer?.zip || "",
                customer_email: customer?.email || "",
                cust_address1: customer?.address1 || "",
                cust_address2: customer?.address2 || "",
                cust_street: customer?.address1 || "",
                cust_street2: customer?.address2 || "",
                cust_city: customer?.city || "",
                cust_state: customer?.state || "",
                cust_zip: customer?.zip || "",
                prj_address1: null,
                prj_address2: null,
                project_city: null,
                project_state: null,
                project_zip: null,
                cust_email: customer?.email || "",
                cust_access_code: customer?.access_code || null,
                dir_type: customer?.type,
              };

              const formikCustomer = {
                user_id: details.customer_id,
                display_name: details.customer_name,
                type_name: getDirectaryIdByName(Number(details?.dir_type)),
                contact_id: details.contact_id || 0,
                image: details.contact_id
                  ? ""
                  : details.customer_data?.length &&
                    details.customer_data[0].image,
                type_key:
                  details.customer_data?.length &&
                  details.customer_data?.length > 0
                    ? details.customer_data[0].type_key
                    : "",
                company_name: customerDetails.cust_company,
              };

              const errorMessage = await formik.validateField("customer");

              if (errorMessage) {
                notification.error({
                  description: _t(errorMessage),
                });
                formik.setFieldValue("customer", formikCustomer);
              } else if (Number(customer.billed_to)) {
                handleChangeFieldStatus({
                  field: "customer_id",
                  status: "loading",
                  action: "API",
                });
                handleChangeFieldStatus({
                  field: "cust_company",
                  status: "loading",
                  action: "API",
                });
                getExistingUsersWithApi({
                  usersIds: customer.billed_to?.toString() || "",
                  apiDataReturn: async (
                    customers: Partial<CustomerSelectedData>[]
                  ) => {
                    if (customers?.length) {
                      const invoiceTo = customers[0] || {};
                      const response = await handleUpdateField(
                        {
                          ...formData,
                          billed_to: invoiceTo.user_id?.toString() || "",
                          billed_to_display_name:
                            invoiceTo.display_name?.toString() || "",
                          billed_to_dir_type:
                            invoiceTo.orig_type?.toString() || "",
                          billed_to_contact: invoiceTo.contact_id || 0,
                        },
                        "billed_to"
                      );
                      if (response.success) {
                        handleChangeFieldStatus({
                          field: "customer_id",
                          status: "success",
                          action: "API",
                        });
                        handleChangeFieldStatus({
                          field: "cust_company",
                          status: "success",
                          action: "API",
                        });
                        dispatch(
                          updateSTDetail({
                            ...updatedData,
                            billed_to: invoiceTo.user_id?.toString() || "",
                            billed_to_display_name:
                              invoiceTo.display_name?.toString() || "",
                            billed_to_dir_type:
                              invoiceTo.orig_type?.toString() || "",
                            billed_to_contact: invoiceTo.contact_id || 0,
                            invoiced_to_data: data.map((record: any) => ({
                              user_id: record.user_id,
                              first_name: record.first_name,
                              last_name: record.last_name,
                              image: record.image,
                              address1: record.address1,
                              address2: record.address2,
                              phone: record.phone,
                              cell: record.cell,
                              email: record.email,
                              city: record.city,
                              state: record.state,
                              zip: record.zip,
                              type: record.type,
                              company_name: record.company_name,
                              type_name: record.type_name,
                              type_key: record.type_key,
                            })),
                          })
                        );
                        dispatch(updateSTCustomerDetail(updatedCustomerData));
                      } else {
                        handleChangeFieldStatus({
                          field: "customer_id",
                          status: "error",
                          action: "API",
                        });
                        handleChangeFieldStatus({
                          field: "cust_company",
                          status: "error",
                          action: "API",
                        });
                      }
                      delay(() => {
                        const fieldAction = getStatusActionForField(
                          loadingStatusRef.current,
                          "customer_id"
                        );
                        handleChangeFieldStatus({
                          field: "customer_id",
                          status: "FOCUS" === fieldAction ? "save" : "button",
                          action: fieldAction || "API",
                        });
                        const companyFieldAction = getStatusActionForField(
                          loadingStatusRef.current,
                          "cust_company"
                        );
                        handleChangeFieldStatus({
                          field: "cust_company",
                          status:
                            "FOCUS" === companyFieldAction ? "save" : "button",
                          action: companyFieldAction || "API",
                        });
                      }, 3000);
                    } else {
                      const response = await handleUpdateField(
                        formData,
                        "customer_id"
                      );
                      if (response.success) {
                        dispatch(updateSTDetail(updatedData));
                        dispatch(updateSTCustomerDetail(updatedCustomerData));
                      } else {
                        formik.setFieldValue("customer", formikCustomer);
                      }
                    }
                  },
                });
              } else {
                const response = await handleUpdateField(
                  formData,
                  "customer_id"
                );
                if (response.success) {
                  dispatch(updateSTDetail(updatedData));
                  dispatch(updateSTCustomerDetail(updatedCustomerData));
                } else {
                  formik.setFieldValue("customer", formikCustomer);
                }
              }
            } else {
              notification.error({
                description: _t("Customer field is required."),
              });
            }
          }}
          selectedCustomer={selectedCustomerList()}
          groupCheckBox={true}
        />
      )}
      {contactDetailDialogOpen && selectedContactId && (
        <ContactDetailsModal
          isOpenContact={contactDetailDialogOpen}
          onCloseModal={() => setContactDetailDialogOpen(false)}
          contactId={Number(selectedContactId)}
          readOnly={module_access === "read_only"}
          additional_contact_id={selectedAdiContactId as string} // as per PHP additional contact will not select from here
        />
      )}
    </>
  );
};
export default CustomerDetails;
