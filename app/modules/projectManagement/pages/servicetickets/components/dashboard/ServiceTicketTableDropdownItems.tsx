import { useEffect, useMemo, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Molecules
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { PDFFilePreview } from "~/shared/components/molecules/pdfFilePreview";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import CustomContextMenu from "~/shared/components/molecules/CustomContextMenu";
import { ShareInternalLinkModal } from "~/shared/components/molecules/shareInternalLinkModal";
// Other
import DirSendEmail from "~/modules/people/directory/components/DirSendEmail";
import { SERVICE_TICKET_OPTIONS } from "../../utils/constants";
import {
  downloadPdfServiceTicketApi,
  fetchDashData,
  setCreateModifyInvoice<PERSON>pi,
  setServiceNot<PERSON><PERSON><PERSON>,
  setService<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "../../redux/action/dashboardAction";
import { apiRoutes, routes } from "~/route-services/routes";
import { defaultConfig } from "~/data";
import { getApiData } from "~/helpers/axios-api-helper";
import { getApiDefaultParams, sanitizeString } from "~/helpers/helper";
import { sendCommonEmailApi } from "~/redux/action/sendEmailAction";
import { removeFirstSlash } from "~/shared/utils/helper/common";
import { getGConfig, getGModuleByKey, useGModules } from "~/zustand";
import { useAppSTDispatch, useAppSTSelector } from "../../redux/store";
import { sendMessageKeys } from "~/components/page/$url/data";
import { fetchServiceTicketDetails } from "../../redux/action/serviceTicketDetailsAction";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { getDirectaryIdByName } from "~/components/sidebars/multi-select/customer/zustand/action";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { faArrowUpRightFromSquare } from "@fortawesome/pro-regular-svg-icons";

interface IServiceTicketTableDropdownItemsProps {
  data: Partial<IServiceTicketData>;
  refreshTable: () => void;
  className?: string;
  iconClassName?: string;
  contentClassName?: string;
  icon?: string;
  isDetailView?: boolean;
}

export const ServiceTicketTableDropdownItems = ({
  data,
  refreshTable,
  className,
  iconClassName,
  contentClassName,
  icon,
  isDetailView = false,
}: IServiceTicketTableDropdownItemsProps) => {
  const { _t } = useTranslation();
  const dispatch = useAppSTDispatch();
  const gConfig: GConfig = getGConfig();
  const { checkModuleAccessByKey } = useGModules();
  const { getGlobalModuleByKey } = useGlobalModule();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { module_name, page_is_iframe, module_singular_name } = gConfig;
  const { allow_delete_module_items = "0" } = user || {};
  const woModuleName: IModule | undefined = getGlobalModuleByKey(
    defaultConfig.work_order_module || "Work Orders"
  );
  const modulePLName =
    _t(HTMLEntities.decode(sanitizeString(module_name))) ||
    _t("Service Tickets");
  const { module_name: paymentModuleName = "Payment" } =
    (getGModuleByKey(defaultConfig.payment_module) as GModule) || {};
  const { module_name: invoiceModuleName = "Invoice" } =
    (getGModuleByKey(defaultConfig.invoice_merge_module_key) as GModule) || {};
  const { details }: IServiceInitialState = useAppSTSelector(
    (state) => state.serviceDetails
  );
  const [billsDetailPdfViewOpen, setBillsDetailPdfViewOpen] =
    useState<boolean>(false);
  const [isShareOpen, setIsShareOpen] = useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [delArchConfirmOpen, setDelArchConfirmOpen] = useState<string>("");
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [notifyTechnicians, setNotifyTechnicians] = useState<{
    id: string | number;
    flag: boolean;
  }>({ id: "", flag: false });
  const [iframeData, setIframeData] = useState<{
    url: string;
    title: string;
  }>({ url: "", title: "" });
  const [shareLink, setShareLink] = useState<string>("");
  const [pdfTempId, setPdfTempId] = useState<string>("");
  const [selectedCustomer, setSelectedCustomer] = useState<any[]>([]);
  const [sendNotificationLoader, setSendNotificationLoader] = useState(false);
  const [pageReload, setPageReload] = useState(false);

  const options: CustomerEmailTab[] = [
    defaultConfig.employee_key,
    "my_crew",
    defaultConfig.customer_key,
    defaultConfig.contractor_key,
    defaultConfig.vendor_key,
    defaultConfig.misc_contact_key,
    "by_service",
    "my_project",
  ];

  const paymentModuleNoAccess = useMemo(
    () =>
      checkModuleAccessByKey(defaultConfig.payment_module) === "read_only" ||
      checkModuleAccessByKey(defaultConfig.payment_module) === "no_access",
    []
  );

  const invoiceModuleNoAccess = useMemo(
    () =>
      checkModuleAccessByKey(defaultConfig.invoice_merge_module_key) ===
        "read_only" ||
      checkModuleAccessByKey(defaultConfig.invoice_merge_module_key) ===
        "no_access",
    []
  );

  const handleInvoiceMiddleClick = async (data: any) => {
    try {
      const resData: Partial<ISTCreateModifyApiRes> =
        await setCreateModifyInvoiceApi(Number(data?.service_ticket_id));
      if (resData?.statusCode === 200 && resData.data) {
        const newURL = new URL(
          routes.MANAGE_INVOICE.url +
            "/" +
            (resData?.data?.id?.toString() || ""),
          window.location.origin
        );
        window.open(newURL.toString(), "_blank");
      }
    } catch (err) {
      console.error(
        `\n File: ServiceTicketTableDropdownItems.tsx -> Line: Error -> `,
        err
      );
    }
  };

  const handleDirDelArch = async () => {
    try {
      setIsDeleting(true);
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "update_module_item_status",
          user,
          otherParams: {
            primary_key: data.service_ticket_id,
            module_key: "service_tickets",
            status: data.is_deleted?.toString() === "0" ? 1 : 0,
          },
        }),
        success: (response: { message: string }) => {
          setIsDeleting(false);
          refreshTable();
          dispatch(fetchDashData());
          setDelArchConfirmOpen("");
        },
        callComplete: () => {
          setIsDeleting(false);
          setDelArchConfirmOpen("");
        },
        error: (description) => {
          setIsDeleting(false);
          notification.error({
            description,
          });
        },
      });
    } catch (error) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const handleDirDelActive = async () => {
    try {
      setIsDeleting(true);
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "delete_module_record",
          user,
          otherParams: {
            primary_key: data.service_ticket_id,
            module_key: "service_tickets",
            remove_associated_data: 0,
          },
        }),
        success: (response: { message: string }) => {
          setIsDeleting(false);
          refreshTable();
          dispatch(fetchDashData());
          setDelArchConfirmOpen("");
        },
        error: (description) => {
          notification.error({
            description,
          });
        },
        callComplete: () => {
          setIsDeleting(false);
          setDelArchConfirmOpen("");
        },
      });
    } catch (error: unknown) {
      setIsDeleting(false);
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const handleNotificationData = async () => {
    if (!data?.assigned_to?.some((item) => item.email)) {
      notification.error({
        description: "No email found for service technician.",
      });
      setNotifyTechnicians({ id: "", flag: false });
      return;
    }
    setSendNotificationLoader(true);
    try {
      const resData: Partial<ISTNotificationApiRes> =
        await setServiceNotificationApi(Number(data?.service_ticket_id));
      setNotifyTechnicians({ id: "", flag: false });
      if (resData.statusCode !== 200) {
        notification.error({
          description: "Please select service technician.",
        });
      }
    } catch (err) {
      console.error(
        `\n File: ServiceTicketTableDropdownItems.tsx -> Line: Error -> `,
        err
      );
    }
    setSendNotificationLoader(false);
  };

  const emailApiCall = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean
  ) => {
    const formData: IServiceSendEmailForm = {
      ...tempFormData,
      send_me_copy: ccMailCopy ? 1 : 0,
      send_custom_email: 0,
      record_id: data.service_ticket_id,
      module_id: gConfig?.module_id,
      module_key: gConfig?.module_key,
      t_id: pdfTempId,
      service_ticket_id: data.service_ticket_id,
      action: "send",
      op: "pdf_service_ticket",
    };
    try {
      const res = (await sendCommonEmailApi(formData)) as ISendEmailCommonRes;
      if (res) {
        if (res.success) {
          closeSendMailSidebar();
        } else {
          notification.error({
            description: res.message,
          });
        }
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    }
  };

  const downloadPdf = async (tId: string) => {
    const res = (await downloadPdfServiceTicketApi({
      service_ticket_id: data.service_ticket_id,
      action: "download",
      t_id: tId,
      op: "pdf_service_ticket",
    })) as IDownloadSericeTicketRes;
    if (res) {
      if (res.success) {
        const link = document.createElement("a");
        link.href = res.base64_encode_pdf ?? "";
        link.download =
          res.data.pdf_name.toString() || res.base64_encode_pdfUrl || "";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        notification.error({
          description: res.message,
        });
      }
    }
  };

  useEffect(() => {
    if (billsDetailPdfViewOpen) {
      dispatch(
        fetchServiceTicketDetails({
          id: Number(data?.service_ticket_id),
        })
      );
    }
  }, [billsDetailPdfViewOpen]);

  useEffect(() => {
    if (details) {
      const formikCustomer = {
        user_id: details.customer_id,
        display_name: details.customer_name,
        type_name: getDirectaryIdByName(Number(details?.dir_type)),
        contact_id: details.contact_id || 0,
        email: details.cust_email
          ? details.cust_email
          : details.customer_data?.length && details.customer_data[0].email,
        image: details.contact_id
          ? ""
          : details.customer_data?.length && details.customer_data[0].image,
        type_key:
          details.customer_data?.length && details.customer_data?.length > 0
            ? details.customer_data[0].type_key
            : "",
      };

      const formikInvoiceTo = {
        user_id: details.billed_to,
        display_name: details.billed_to_display_name,
        billed_to_dir_type: details.billed_to_dir_type,
        contact_id: details.billed_to_contact,
        email:
          details.invoiced_to_data?.length && details.invoiced_to_data[0].email,
        type_name:
          details.invoiced_to_data?.length &&
          details.invoiced_to_data[0].type_name,
        image: details.billed_to_contact
          ? ""
          : details.invoiced_to_data?.length &&
            details.invoiced_to_data[0].image,
        type_key:
          details.invoiced_to_data?.length &&
          details.invoiced_to_data[0].type_key,
      };

      const selectedCustomers = [];
      if (details.customer_id && details.customer_data?.length) {
        selectedCustomers.push(formikCustomer);
      }
      if (details.billed_to && details.invoiced_to_data?.length) {
        selectedCustomers.push(formikInvoiceTo);
      }

      setSelectedCustomer(selectedCustomers);
    }
  }, [details]);

  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    visible: boolean;
  }>({ x: 0, y: 0, visible: false });

  const contextMenuItems = [
    {
      label: "Open in new tab",
      icon: faArrowUpRightFromSquare,
      onClick: () => {
        handleInvoiceMiddleClick(data);
      },
    },
  ];

  const getOptions = () => {
    return SERVICE_TICKET_OPTIONS.filter((option) => {
      if (!data) {
        return false;
      }
      switch (option.key) {
        case "service-reminder":
          if (
            !data.service_format_date ||
            !["149", "263"].includes(data.job_status?.toString() || "")
          ) {
            return false;
          }
          break;
        case "archive":
          if (data.is_deleted === 1) {
            return false;
          }
          break;
        case "active":
          if (data.is_deleted === 0) {
            return false;
          }
          break;
      }
      return true;
    }).map((option) => {
      return {
        ...option,
        label:
          option.key === "post-payment"
            ? `Post ${paymentModuleName}`
            : option.key === "invoice"
            ? `Create/Modify ${invoiceModuleName}`
            : option.key === "work-order"
            ? `${modulePLName} vs ${woModuleName?.plural_name}`
            : option.label,
        disabled:
          (option.key === "delete" &&
            (allow_delete_module_items === "0" || page_is_iframe)) ||
          (option.key === "post-payment" && paymentModuleNoAccess) ||
          (option.key === "invoice" && invoiceModuleNoAccess),
        onClick: async (e: { domEvent: any }) => {
          switch (option.key) {
            case "email-pdf":
              setBillsDetailPdfViewOpen(true);
              await dispatch(
                fetchServiceTicketDetails({
                  id: Number(data?.service_ticket_id),
                })
              ).unwrap();
              break;
            case "post-payment":
              let tempAuthorization = gConfig?.authorization;
              const isExpired = isExpiredAuthorization();
              if (isExpired) {
                const response = (await webWorkerApi({
                  url: "/api/auth/token",
                })) as IGetTokenFromNode;
                if (response.success) {
                  tempAuthorization = response.data.accessToken;
                  setAuthorizationExpired(response.data.accessTokenExpired);
                }
              }
              const baseUrl = `${window?.location?.protocol}//${window?.location?.host}`;
              const pathname = routes.MANAGE_PAYMENT.url;
              const newURL = new URL(baseUrl + pathname, window.ENV.PANEL_URL);
              newURL.searchParams.set("authorize_token", tempAuthorization);
              newURL.searchParams.set("action", "new");
              newURL.searchParams.set("iframecall", "1");
              newURL.searchParams.set("from_remix", "1");
              newURL.searchParams.set("module_key", "service_tickets");
              newURL.searchParams.set(
                "customer_id",
                data?.customer_id?.toString() || ""
              );
              newURL.searchParams.set("iframe_close", "1");
              setIframeData({ url: newURL.toString(), title: "Post Payment" });
              break;
            case "invoice":
              try {
                const resData: Partial<ISTCreateModifyApiRes> =
                  await setCreateModifyInvoiceApi(
                    Number(data?.service_ticket_id)
                  );
                if (resData?.statusCode === 200 && resData.data) {
                  let tempAuthorization = gConfig?.authorization;
                  const isExpired = isExpiredAuthorization();
                  if (isExpired) {
                    const response = (await webWorkerApi({
                      url: "/api/auth/token",
                    })) as IGetTokenFromNode;
                    if (response.success) {
                      tempAuthorization = response.data.accessToken;
                      setAuthorizationExpired(response.data.accessTokenExpired);
                    }
                  }
                  const newURL = new URL(
                    routes.MANAGE_INVOICE.url +
                      "/" +
                      (resData?.data?.id?.toString() || ""),
                    window.location.origin
                  );
                  const event = e.domEvent;

                  if (
                    Number(window.ENV.ENABLE_ALL_CLICK) &&
                    (event.ctrlKey || event.metaKey)
                  ) {
                    window.open(newURL.toString(), "_blank");
                  } else {
                    newURL.searchParams.set(
                      "authorize_token",
                      tempAuthorization
                    );
                    newURL.searchParams.set("iframecall", "1");
                    newURL.searchParams.set("from_remix", "1");
                    setIframeData({
                      url: newURL.toString(),
                      title: "Create Invoice",
                    });
                  }
                }
              } catch (err) {
                console.error(
                  `\n File: ServiceTicketTableDropdownItems.tsx -> Line: Error -> `,
                  err
                );
              }
              break;
            case "service-reminder":
              try {
                const resData: Partial<ISTReminderApiRes> =
                  await setServiceReminderApi(Number(data?.service_ticket_id));
                refreshTable();
                if (!resData.success) {
                  notification.error({
                    description: resData.message || "Something went wrong.",
                  });
                }
              } catch (err) {
                console.error(
                  `\n File: ServiceTicketTableDropdownItems.tsx -> Line: Error -> `,
                  err
                );
              }
              break;
            case "notify":
              setNotifyTechnicians({
                id: Number(data?.service_ticket_id),
                flag: true,
              });
              break;
            case "work-order":
              window.open("https://vimeo.com/399295862", "_blank");
              break;
            case "share":
              setIsShareOpen(true);
              break;
            case "archive":
              setDelArchConfirmOpen("archive");
              break;
            case "active":
              setDelArchConfirmOpen("active");
              break;
            case "delete":
              setDelArchConfirmOpen("delete");
              break;
            default:
              break;
          }
        },
        ...(Number(window.ENV.ENABLE_ALL_CLICK) && {
          onAuxClick: (e: any) => {
            if (option.key === "invoice" && e.button === 1) {
              e.preventDefault();
              e.stopPropagation();
              handleInvoiceMiddleClick(data);
            }
          },
          onContextMenu: (e: React.MouseEvent<any>) => {
            if (option.key === "invoice") {
              e.preventDefault();
              e.stopPropagation();
              setContextMenu({ x: e.clientX, y: e.clientY, visible: true });
            }
          },
        }),
      };
    });
  };

  return (
    <>
      <DropdownMenu
        icon={icon ? icon : "fa-regular fa-ellipsis-vertical"}
        options={getOptions()}
        buttonClass={
          className
            ? className
            : "active-button hover:!bg-primary-900/10 !rounded"
        }
        iconClassName={iconClassName}
        contentClassName={contentClassName}
        {...((user?.allow_delete_module_items === "0" ||
          paymentModuleNoAccess ||
          invoiceModuleNoAccess) && {
          footerText: _t(
            "Some actions might be unavailable depending on your privilege."
          ),
        })}
      />

      {billsDetailPdfViewOpen && (
        <PDFFilePreview
          projectId={data?.project_id}
          isOpen={billsDetailPdfViewOpen}
          onCloseModal={() => setBillsDetailPdfViewOpen(false)}
          moduleId={gConfig?.module_id}
          op="pdf_service_ticket"
          idName="service_ticket_id"
          isLoading={false}
          id={data?.service_ticket_id?.toString() || ""}
          options={options}
          emailSubject={data?.email_subject || ""}
          handleEmailApiCall={emailApiCall}
          handleDownload={downloadPdf}
          isViewAttachment={false}
          moduleName={module_singular_name}
          setPdfTempId={setPdfTempId}
          selectedCustomer={selectedCustomer}
        />
      )}

      {isShareOpen && (
        <ShareInternalLinkModal
          isOpen={isShareOpen}
          shareLinkParams={{
            record_id: Number(data.service_ticket_id),
            module_key: gConfig?.module_key,
            module_page: removeFirstSlash(
              routes.MANAGE_SERVICE_TICKETS.url || ""
            ),
          }}
          onEmailLinkClick={(data) => {
            setIsSendEmailSidebarOpen(true);
            setShareLink(data);
            setIsShareOpen(false);
          }}
          onCloseModal={() => {
            setIsShareOpen(false);
            setShareLink("");
          }}
        />
      )}

      <DirSendEmail
        isOpen={isSendEmailSidebarOpen}
        canWrite={shareLink ? false : true}
        recordId={data?.user_id}
        options={
          shareLink
            ? undefined
            : [
                defaultConfig.employee_key,
                "my_crew",
                defaultConfig.customer_key,
                defaultConfig.contractor_key,
                defaultConfig.vendor_key,
                defaultConfig.misc_contact_key,
                "by_service",
              ]
        }
        emailData={{
          subject: shareLink ? "Shared Link" : "",
          body: shareLink
            ? `A link to a record within Contractor Foreman has been shared with you. <a href="${shareLink}">View Details.</a>`
            : "",
        }}
        onSendResponse={() => {
          setShareLink("");
        }}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setShareLink("");
        }}
        appUsers={true}
        contactId={0}
      />

      {delArchConfirmOpen !== "" && (
        <ConfirmModal
          isOpen={delArchConfirmOpen !== ""}
          modaltitle={
            delArchConfirmOpen === "delete"
              ? _t("Delete")
              : delArchConfirmOpen === "active"
              ? _t("Active")
              : _t("Archive")
          }
          description={
            delArchConfirmOpen === "delete"
              ? _t("Are you sure you want to delete this Item?")
              : delArchConfirmOpen === "archive"
              ? _t(
                  "Are you sure you want to Archive this item? To view it or Activate it later, set the filter to show Archived items."
                )
              : _t("Are you sure you want to Activate this data?")
          }
          withConfirmText={delArchConfirmOpen === "delete"}
          modalIcon={
            delArchConfirmOpen === "delete"
              ? "fa-regular fa-trash-can"
              : delArchConfirmOpen === "archive"
              ? "fa-regular fa-box-archive"
              : "fa-regular fa-regular-active"
          }
          isLoading={isDeleting}
          onAccept={() => {
            if (delArchConfirmOpen === "delete") {
              handleDirDelActive();
            } else {
              handleDirDelArch();
            }
          }}
          onDecline={() => setDelArchConfirmOpen("")}
          onCloseModal={() => setDelArchConfirmOpen("")}
        />
      )}

      {notifyTechnicians.flag && (
        <ConfirmModal
          isOpen={notifyTechnicians.flag}
          modalIcon="fa-regular fa-bell"
          descriptionclassName="text-primary-900"
          modaltitle={_t("Notify Technicians")}
          isLoading={sendNotificationLoader}
          description={_t(
            `Do you want to send a notification to the assigned contacts? Notifications will be sent based on the preferences within the user's account.`
          )}
          onCloseModal={() => setNotifyTechnicians({ id: "", flag: false })}
          onAccept={() => {
            handleNotificationData();
          }}
          onDecline={() => setNotifyTechnicians({ id: "", flag: false })}
        />
      )}

      {iframeData?.url && (
        <IframeModal
          isOpen={iframeData?.url ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            if (pageReload) {
              window.location.reload();
            } else {
              setIframeData({ url: "", title: "" });
            }
          }}
          modalBodyClass="p-0"
          header={{
            closeIcon: true,
          }}
          iframeProps={{
            src: iframeData.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            if (key === sendMessageKeys.module_name_change) {
              setPageReload(true);
            } else if (key === sendMessageKeys.modal_change) {
              setIframeData({ url: "", title: "" });
            }
          }}
        />
      )}

      <CustomContextMenu
        position={{ x: contextMenu.x, y: contextMenu.y }}
        visible={contextMenu.visible}
        items={contextMenuItems}
        onClose={() => setContextMenu((c) => ({ ...c, visible: false }))}
      />
    </>
  );
};
