import { useMemo, useState } from "react";

// custom hook
import { useIframe, useTranslation } from "~/hook";

// static const
import { apiRoutes, routes } from "~/route-services/routes";
import { defaultConfig } from "~/data";

// zustand
import {
  getGConfig,
  getGModuleByKey,
  getGSettings,
  useGModules,
} from "~/zustand";

import { setSendEmailOpenStatus } from "~/components/sidebars/multi-select/customer/zustand/action";

// helpers
import { getApiDefaultParams, Number, sanitizeString } from "~/helpers/helper";
import { getApiData } from "~/helpers/axios-api-helper";
// molecules
import { ShareInternalLinkModal } from "~/shared/components/molecules/shareInternalLinkModal";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { SendEmailDrawer } from "~/shared/components/molecules/sendEmailDrawer";
// Other
import { useNavigate, useParams } from "@remix-run/react";

import { removeFirstSlash } from "~/shared/utils/helper/common";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { sendMessageKeys } from "~/components/page/$url/data";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { useBoolean } from "../../hook/use-boolean";
import ImportProjectTemplate from "./ImportProjectTemplate";
import {
  downloadProjectStatement,
  exportScheduleOfValues,
} from "../../redux/action/proDashAction";
import { faArrowUpRightFromSquare } from "@fortawesome/pro-regular-svg-icons";
import CustomContextMenu from "~/shared/components/molecules/CustomContextMenu";

export const ProjectListTableDropdownItems = ({
  data,
  refreshTable,
  className,
  iconClassName,
  contentClassName,
  icon,
  isDetailView = false,
  tooltipcontent,
}: IProjectListTableDropdownItemsProps) => {
  const { _t } = useTranslation();

  const navigate = useNavigate();
  const { parentPostMessage } = useIframe();

  const templateSelector = useBoolean();

  const {
    module_id,
    module_key,
    module_access,
    module_name,
    module_singular_name,
  }: GConfig = getGConfig();
  const [shareLinkUrl, setSharelinkUrl] = useState<string>("");
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { allow_delete_module_items = "0" } = user || {};
  const [isShareOpen, setIsShareOpen] = useState<boolean>(false); // Updated state
  const { date_format, image_resolution, save_a_copy_of_sent_pdf }: GSettings =
    getGSettings();
  const { checkModuleAccessByKey } = useGModules();
  const [isHandleStatusLoading, setIsHandleStatusLoading] =
    useState<boolean>(false);
  const [sendEmailOpen, setSendEmailOpen] = useState<boolean>(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState<boolean>(false);
  const [deleteRecordLoading, setDeleteRecordLoading] =
    useState<boolean>(false);
  const [confirmArchiveDialogOpen, setConfirmArchiveDialogOpen] =
    useState<boolean>(false);
  const [downLoadingstatement, setDownLoadingstatement] =
    useState<boolean>(false);
  const dataIsActive = useMemo(
    () => data?.is_deleted?.toString() === "0",
    [data?.is_deleted]
  );
  const { authorization }: GConfig = getGConfig();

  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    visible: boolean;
    selectedOption: string | null;
  }>({ x: 0, y: 0, visible: false, selectedOption: null });

  const handleOpenInNewTab = (url: string) => {
    window.open(url, "_blank");
  };

  const getOptionUrl = (key: string) => {
    const projectId = Number(id) === 0 ? Number(data?.id) : Number(id);
    switch (key) {
      case "note":
        return `${window.ENV.PANEL_URL}manage_notes.php?action=new&project=${projectId}`;
      case "inspection":
        return `/manage-inspections?action=new&project=${projectId}`;
      case "todo":
        return `${routes.MANAGE_TODO.url}?action=new&project=${projectId}`;
      case "invoice":
        return `/manage-invoice?action=new&project=${projectId}`;
      case "punchlist":
        return `${routes.MANAGE_PUNCHLISTS.url}?action=new&project=${projectId}`;
      case "dailylog":
        return `${routes.MANAGE_DAILYLOG.url}?action=new&project=${projectId}`;
      default:
        return "";
    }
  };

  const contextMenuItems = [
    {
      label: "Open in new tab",
      icon: faArrowUpRightFromSquare,
      onClick: () => {
        if (contextMenu.selectedOption) {
          const url = getOptionUrl(contextMenu.selectedOption);
          if (url) handleOpenInNewTab(url);
        }
      },
    },
  ];

  const { id: id }: RouteParams = useParams();

  const handleEmailApiCall = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean
  ) => {
    try {
      const response = (await webWorkerApi({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: {
          ...tempFormData,
          send_me_copy: ccMailCopy ? 1 : 0,
          send_custom_email: 0,
        },
      })) as Omit<IApiCallResponse, "success"> & { success: boolean | string };
      if (
        (typeof response?.success === "boolean" && response?.success) ||
        (typeof response?.success !== "boolean" &&
          response?.success?.toString() === "1")
      ) {
        closeSendMailSidebar();
        setIsShareOpen(false); // Set the share link modal to close
        setSendEmailOpenStatus(false);
      } else {
        notification.error({
          description: response?.message,
        });
      }
    } catch (error) {
      console.error(
        `\n File: #header-link.tsx -> Line: #47 -> `,
        (error as Error)?.message
      );
      notification.error({
        description: "Something went wrong. Please try again.",
      });
    }
  };

  const handleDelete = (deleteAllData: boolean) => {
    try {
      setDeleteRecordLoading(true);
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "delete_module_record",
          user,
          otherParams: {
            primary_key: Number(id) === 0 ? Number(data?.id) : Number(id),
            module_key: module_key,
            remove_associated_data: deleteAllData ? 1 : 0,
          },
        }),
        success: (response: { message: string }) => {
          setConfirmDialogOpen(false);

          if (window.ENV.PAGE_IS_IFRAME) {
            parentPostMessage(sendMessageKeys?.modal_change, {
              open: false,
            });
          } else {
            if (Number(id) !== 0) {
              navigate(`${routes.MANAGE_PROJECT.url}`);
              if (isDetailView) return;
            }
          }

          refreshTable();
        },
        error: (description) => {
          notification.error({
            description,
          });
        },
        callComplete: () => setDeleteRecordLoading(false),
      });
    } catch (error: unknown) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const handleStatus = () => {
    setIsHandleStatusLoading(true);
    try {
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "update_module_item_status",
          user,
          otherParams: {
            primary_key: Number(id) === 0 ? Number(data?.id) : Number(id),
            module_key: module_key,
            status: dataIsActive ? 1 : 0,
          },
        }),
        success: (response: { message: string }) => {
          refreshTable();
          if (Number(id) !== 0) {
            navigate(`${routes.MANAGE_PROJECT.url}`);
          }
          setIsHandleStatusLoading(false);
          setConfirmArchiveDialogOpen(false);
        },
        error: (description) => {
          notification.error({
            description,
          });
        },
      });
    } catch (error) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const handleDownloadProjectStatements = async () => {
    try {
      setDownLoadingstatement(true);
      const paramsData = getApiDefaultParams({
        op: "project_report_spreadsheet",
        user,
        otherParams: {
          project_id: Number(id) === 0 ? Number(data?.id) : Number(id),
          need_more_data: 2,
        },
      });

      await downloadProjectStatement({
        op: paramsData.op || "",
        project_id: paramsData.project_id,
        company_id: paramsData.company_id || 0,
        tz: paramsData.tz,
        tzid: paramsData.tzid || "",
        need_more_data: paramsData.need_more_data,
        user_id: paramsData.user_id || 0,
        from: paramsData.from,
      });
    } catch (error: unknown) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const handleExportScheduleOfValues = async () => {
    try {
      setDownLoadingstatement(true);

      await exportScheduleOfValues({
        project_id: Number(id) === 0 ? Number(data?.id) : Number(id),
        projectName:
          HTMLEntities.decode(sanitizeString(data?.project_name)) || "",
      });
    } catch (error: unknown) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const hasNoteAccess = useMemo(() => {
    const mAccess = checkModuleAccessByKey(defaultConfig.notes_module);

    return mAccess === "full_access" || mAccess === "own_data_access";
  }, []);

  const hasInspectionAccess = useMemo(() => {
    const mAccess = checkModuleAccessByKey(defaultConfig.inspection_module);

    return mAccess === "full_access" || mAccess === "own_data_access";
  }, []);

  const hasToDoAccess = useMemo(() => {
    const mAccess = checkModuleAccessByKey(defaultConfig.todo_module);

    return mAccess === "full_access" || mAccess === "own_data_access";
  }, []);

  const hasInvoiceAccess = useMemo(() => {
    const mAccess = checkModuleAccessByKey(
      defaultConfig.invoice_merge_module_key
    );

    return mAccess === "full_access" || mAccess === "own_data_access";
  }, []);

  const hasPunchListAccess = useMemo(() => {
    const mAccess = checkModuleAccessByKey(defaultConfig.punch_list_module);

    return mAccess === "full_access" || mAccess === "own_data_access";
  }, []);

  const hasDailyLogAccess = useMemo(() => {
    const mAccess = checkModuleAccessByKey(defaultConfig.daily_log_module);

    return mAccess === "full_access" || mAccess === "own_data_access";
  }, []);

  const hasValidTemplateOptAccess = useMemo(() => {
    return (
      Number(data?.project_template_id) === 0 &&
      data?.project_status_key === "pending"
    );
  }, [data]);

  const moduleNotes: GModule | undefined = getGModuleByKey(
    defaultConfig.notes_module
  );

  const moduleInspections: GModule | undefined = getGModuleByKey(
    defaultConfig.inspection_module
  );

  const moduleTodos: GModule | undefined = getGModuleByKey(
    defaultConfig.todo_module
  );

  const moduleInvoices: GModule | undefined = getGModuleByKey(
    defaultConfig.invoice_merge_module_key
  );

  const modulePunchlists: GModule | undefined = getGModuleByKey(
    defaultConfig.punch_list_module
  );

  const moduleDailylogs: GModule | undefined = getGModuleByKey(
    defaultConfig.daily_log_module
  );

  const createAddOption = (
    label: string,
    icon: string,
    key: string,
    disabled: boolean
  ) => ({
    label,
    icon,
    onClick: (e: { domEvent: React.MouseEvent }) => {
      const url = getOptionUrl(key);
      if (
        Number(+window.ENV.ENABLE_ALL_CLICK) &&
        (e.domEvent.ctrlKey || e.domEvent.metaKey)
      ) {
        handleOpenInNewTab(url);
      } else {
        if (key === "note") {
          window.location.href = url;
        } else {
          navigate(url);
        }
      }
    },
    onAuxClick: (e: React.MouseEvent) => {
      if (Number(+window.ENV.ENABLE_ALL_CLICK) && e.button === 1) {
        e.preventDefault();
        e.stopPropagation();
        handleOpenInNewTab(getOptionUrl(key));
      }
    },
    onContextMenu: (e: React.MouseEvent) => {
      if (Number(+window.ENV.ENABLE_ALL_CLICK)) {
        e.preventDefault();
        e.stopPropagation();
        setContextMenu({
          x: e.clientX,
          y: e.clientY,
          visible: true,
          selectedOption: key,
        });
      }
    },
    disabled,
  });

  const ProjectListViewTableOptions = [
    createAddOption(
      `Add ${moduleNotes?.module_name || "Note"}`,
      "fa-regular fa-notes",
      "note",
      !hasNoteAccess
    ),
    createAddOption(
      `Add ${moduleInspections?.module_name || "Inspection"}`,
      "fa-regular fa-neuter",
      "inspection",
      !hasInspectionAccess
    ),
    createAddOption(
      `Add ${moduleTodos?.module_name || "To-Do"}`,
      "fa-regular fa-list-ul",
      "todo",
      !hasToDoAccess
    ),
    createAddOption(
      `Add ${moduleInvoices?.module_name || "Invoice"}`,
      "fa-regular fa-usd",
      "invoice",
      !hasInvoiceAccess
    ),
    createAddOption(
      `Add ${modulePunchlists?.module_name || "Punchlist"}`,
      "fa-regular fa-square-check",
      "punchlist",
      !hasPunchListAccess
    ),
    createAddOption(
      `Add ${moduleDailylogs?.module_name || "Daily log"}`,
      "fa-regular fa-calendar",
      "dailylog",
      !hasDailyLogAccess
    ),
    {
      label: `${module_singular_name || "Project"} Statement`,
      icon: "fa-regular fa-house",
      onClick: () => {
        handleDownloadProjectStatements();
      },
    },
    {
      label: "Export Schedule of Values",
      icon: "fa-regular fa-calendar-lines",
      onClick: () => {
        handleExportScheduleOfValues();
      },
    },
    ...(hasValidTemplateOptAccess
      ? [
          {
            label: "Import Project Template",
            icon: "fa-regular fa-file-import",
            onClick: () => {
              templateSelector.onTrue();
            },
          },
        ]
      : []),
    {
      content: _t("Share Internal Link"),
      icon: "fa-regular fa-share-nodes",
      onClick: () => {
        setIsShareOpen(true);
      },
      onlyIconView: true,
    },
    {
      content:
        data?.is_deleted?.toString() === "1" ? (
          <>
            {_t("Status: Archived")} <br />
            {_t("Click to Activate the item")}
          </>
        ) : (
          <>
            {_t("Status: Active")} <br />
            {_t("Click to Archive the item")}
          </>
        ),
      icon:
        data?.is_deleted?.toString() === "1"
          ? "fa-regular fa-regular-active"
          : "fa-regular fa-box-archive",
      onClick: () => setConfirmArchiveDialogOpen(true),
      onlyIconView: true,
      disabled: module_access === "read_only",
    },
    {
      content: _t("Delete"),
      icon: "fa-regular fa-trash-can",
      onClick: () => setConfirmDialogOpen(true),
      disabled:
        allow_delete_module_items === "0" || module_access === "read_only",
      onlyIconView: true,
      hide: module_access === "read_only",
    },
  ];

  return (
    <>
      <DropdownMenu
        icon={icon ? icon : "fa-regular fa-ellipsis-vertical"}
        options={ProjectListViewTableOptions}
        tooltipcontent={tooltipcontent}
        buttonClass={
          className
            ? className
            : "active-button hover:!bg-primary-900/10 !rounded"
        }
        iconClassName={iconClassName && iconClassName}
        contentClassName={contentClassName && contentClassName}
        {...((!hasNoteAccess ||
          !hasInspectionAccess ||
          !hasToDoAccess ||
          !hasInvoiceAccess ||
          !hasPunchListAccess ||
          !hasDailyLogAccess ||
          allow_delete_module_items === "0") && {
          footerText: _t(
            "Some actions might be unavailable depending on your privilege."
          ),
        })}
      />

      {isShareOpen && (
        <ShareInternalLinkModal
          isOpen={isShareOpen}
          shareLinkParams={{
            record_id: Number(id) === 0 ? Number(data?.id) : Number(id), // Adjust the key name for your data structure
            module_key: module_key,
            module_page: removeFirstSlash(routes.MANAGE_PROJECT.url || ""),
          }}
          onEmailLinkClick={(shareLinkData) => {
            setSendEmailOpenStatus(true); // Open the email modal
            setSendEmailOpen(true);
            setIsShareOpen(false);
            setSharelinkUrl(shareLinkData);
          }}
          onCloseModal={() => {
            setIsShareOpen(false); // Close the modal
          }}
        />
      )}

      {sendEmailOpen && (
        <SendEmailDrawer
          closeDrawer={() => {
            setSendEmailOpen(false);
            setSendEmailOpenStatus(false);
          }}
          isViewAttachment={false}
          app_access={true}
          appUsers={true}
          openSendEmailSidebar={sendEmailOpen}
          options={[defaultConfig.employee_key, defaultConfig.contractor_key]}
          singleSelecte={false}
          emailApiCall={handleEmailApiCall}
          customEmailData={{
            body: `A link to a record within Contractor Foreman has been shared with you. <a href = ${shareLinkUrl}>View Details</a>.`,
            subject: "Shared Link",
          }}
          canWrite={shareLinkUrl ? false : true}
          validationParams={{
            date_format,
            file_support_module_access: checkModuleAccessByKey(
              defaultConfig.file_support_key
            ),
            image_resolution,
            module_key,
            module_id,
            module_access,
            save_a_copy_of_sent_pdf,
          }}
        />
      )}

      {confirmDialogOpen && (
        <ConfirmModal
          isOpen={confirmDialogOpen}
          projectModule={true}
          modaltitle={_t("Delete")}
          description={_t("Are you sure you want to delete this Item?")}
          isLoading={deleteRecordLoading}
          withConfirmText={true}
          assocDelCheckBoxProps={{
            view: true,
            children: `${_t("Delete all project related data?")}`,
          }}
          onAccept={(data) => {
            handleDelete(data as boolean);
          }}
          onDecline={() => setConfirmDialogOpen(false)}
          onCloseModal={() => setConfirmDialogOpen(false)}
        />
      )}

      <ConfirmModal
        isLoading={isHandleStatusLoading}
        isOpen={confirmArchiveDialogOpen}
        modaltitle={
          data?.is_deleted?.toString() === "1" ? _t("Active") : _t("Archive")
        }
        description={_t(
          data?.is_deleted?.toString() === "1"
            ? "Are you sure you want to Activate this data?"
            : `Are you sure you want to Archive this item?  ${
                dataIsActive
                  ? "To view it or Activate it later, set the filter to show Archived items."
                  : ""
              }`
        )}
        modalIcon={
          dataIsActive
            ? "fa-regular fa-box-archive"
            : "fa-regular fa-regular-active"
        }
        onAccept={() => {
          handleStatus();
        }}
        onDecline={() => {
          setConfirmArchiveDialogOpen(false);
        }}
        onCloseModal={() => {
          setConfirmArchiveDialogOpen(false);
        }}
      />

      {templateSelector.bool && (
        <ImportProjectTemplate
          isOpen={templateSelector.bool}
          onClose={() => templateSelector.onFalse()}
          id={data?.id ?? ""}
        />
      )}

      <CustomContextMenu
        position={{ x: contextMenu.x, y: contextMenu.y }}
        visible={contextMenu.visible}
        items={contextMenuItems}
        onClose={() =>
          setContextMenu((c) => ({
            ...c,
            visible: false,
            selectedOption: null,
          }))
        }
      />
    </>
  );
};
