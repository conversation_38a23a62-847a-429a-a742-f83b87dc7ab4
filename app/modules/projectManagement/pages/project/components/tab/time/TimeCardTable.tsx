import { useEffect, useMemo, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useAppProDispatch, useAppProSelector } from "../../../redux/store";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { sendMessageKeys } from "~/components/page/$url/data";
import { useParams } from "@remix-run/react";
import { getGConfig } from "~/zustand";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { generateCostCodeLabel } from "~/shared/utils/helper/common";
import {
  GridReadyEvent,
  IServerSideGetRowsParams,
  SortChangedEvent,
  ValueGetterParams,
  ValueSetterParams,
} from "ag-grid-community";
import { sanitizeString } from "~/helpers/helper";
import { getModuleAccess } from "~/shared/utils/helper/module";
import {
  updateProjectCostCode,
  fetchProjectTimecards,
} from "../../../redux/action/ProjectTimeAction";
import { fetchProjectDetailsApi } from "../../../redux/action/projectDetailsAction";
import { faArrowUpRightFromSquare } from "@fortawesome/pro-regular-svg-icons";
import CustomContextMenu from "~/shared/components/molecules/CustomContextMenu";
let timeout: NodeJS.Timeout;

const TimeCardTable = (props: TimeCardTableProps) => {
  const { dataLimit, fetchData } = props;
  const { _t } = useTranslation();
  const { id } = useParams();
  const { authorization }: GConfig = getGConfig();
  const { timeData } = useAppProSelector((state) => state.proDashTime);
  const { details }: IProjectDetailsInitialState = useAppProSelector(
    (state) => state.proDetails
  );
  const timecardDetail = details?.timecardDetail;
  const { timecardData } = useAppProSelector((state) => state.proDashTime);
  const { getGlobalModuleByKey } = useGlobalModule();
  const timeCardModule = getGlobalModuleByKey(CFConfig.time_card_module);
  const timeCardTable = timecardData ?? [];
  const [isShowingMore, setIsShowingMore] = useState<boolean>(false);
  const [collapse, setCollapse] = useState<string[]>([]);
  const [iframeData, setIframeData] = useState<{
    url?: string;
    title: string;
    addUrl?: string;
  }>({ url: "", title: "", addUrl: "" });
  const [displayLimit, setDisplayLimit] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const totalCount = Number(timeCardTable?.length);
  const totalWorkMins = timeData?.timecards_count?.[0]?.total_work_mins ?? 0;
  const [gridRowParams, setGridRowParams] = useState<IGridParamsCus | null>(
    null
  );
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    visible: boolean;
  }>({ x: 0, y: 0, visible: false });

  // Add context menu items
  const contextMenuItems = [
    {
      label: "Open in new tab",
      icon: faArrowUpRightFromSquare,
      onClick: () => {
        const url = `${window.ENV.PANEL_URL}manage_timecards.php?action=new&project=${id}`;
        window.open(url, "_blank", "noopener,noreferrer");
      },
    },
  ];
  const { formatter } = useCurrencyFormatter();
  const CURRENCY_SYMBOL = formatter().currency_symbol;
  // const isReadOnly = useMemo(
  //   () => checkModuleAccessByKey(CFConfig.project_module) === "read_only",
  //   [CFConfig.project_module]
  // );

  const isReadOnly = useMemo(
    () => getModuleAccess(timeCardModule),
    [timeCardModule]
  );
  const { codeCostTimeData }: IGetTimeCostCodeList = useAppProSelector(
    (state) => state.timeCostCode
  );
  const dispatch = useAppProDispatch();

  const employeeModule = useMemo(
    () => getGlobalModuleByKey(CFConfig.time_card_module),
    [getGlobalModuleByKey]
  );
  const employee_module_access = useMemo(
    () => getModuleAccess(employeeModule),
    [employeeModule]
  );

  const crewCardModule = useMemo(
    () => getGlobalModuleByKey(CFConfig.crew_card_module),
    [getGlobalModuleByKey]
  );
  const crew_card_module_access = useMemo(
    () => getModuleAccess(crewCardModule),
    [crewCardModule]
  );

  const crewSheetModule = useMemo(
    () => getGlobalModuleByKey(CFConfig.crew_sheet_module),
    [getGlobalModuleByKey]
  );
  const crew_sheet_module_access = useMemo(
    () => getModuleAccess(crewSheetModule),
    [crewSheetModule]
  );

  const filteredCodeCostData = codeCostTimeData
    ?.filter(
      (item) =>
        (item?.cost_code_name?.toString() !== "" ||
          item?.csi_code?.toString() !== "") &&
        Number(item?.parent_id) > 0
    )
    ?.map((el) => ({
      ...el,
      label: generateCostCodeLabel({
        name: el?.cost_code_name,
        code: el?.csi_code,
        isArchived: false,
        isAllowCodeWithoutName: true,
      }),
      value: el?.code_id,
    }));

  useEffect(() => {
    if (timeCardTable.length) {
      setCollapse(["1"]);
    }
  }, [timeCardTable]);

  const fetchProDetailApis = async () => {
    await dispatch(
      fetchProjectDetailsApi({
        project_id: Number(id),
        record_type: "project",
        skipLoading: true,
      })
    );
  };

  const columnDefs = [
    {
      headerName: "Employee",
      field: "employee",
      minWidth: 180,
      flex: 1,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const htmlDecode = HTMLEntities.decode(sanitizeString(value));

        return value ? (
          <Tooltip title={htmlDecode}>
            <Typography className="table-tooltip-text">{htmlDecode}</Typography>
          </Tooltip>
        ) : (
          <div className="flex items-center gap-1">
            <span> - </span>
            <ButtonWithTooltip
              tooltipTitle={_t("Team Member Deleted")}
              tooltipPlacement="top"
              icon="fa-regular fa-circle-info"
              onClick={() => {}}
            />
          </div>
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "record_date",
      maxWidth: 135,
      minWidth: 135,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ value }: { value: string }) => {
        return value ? <DateTimeCard format="date" date={value} /> : "-";
      },
    },
    {
      headerName: _t("Cost Code"),
      field: "cost_code_id",
      flex: 2,
      minWidth: 130,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellEditor: "agRichSelectCellEditor",
      editable: ({ data }: { data: TimeCardData }) => {
        // Disable editing if any of the conditions are met
        if (
          (data?.time_card_type == "employee" &&
            employee_module_access === "read_only") ||
          (data?.time_card_type == "employee" &&
            data?.entry_type == "auto" &&
            data?.modified_detail_id == 0) ||
          (data?.time_card_type == "crew" &&
            crew_card_module_access === "read_only") ||
          (data?.time_card_type == "crew_sheet" &&
            crew_sheet_module_access === "read_only")
        ) {
          return false; // Make the cell non-editable
        }
        return true; // Otherwise, allow editing
      },
      cellEditorParams: {
        values: filteredCodeCostData.map((item) => item.code_id),
        filterList: true,
        searchType: "matchAny",
        allowTyping: true,
        // need to check
        valueListMaxHeight:
          filteredCodeCostData?.length == 1
            ? 60
            : filteredCodeCostData?.length == 2
            ? 90
            : filteredCodeCostData?.length == 3
            ? 120
            : filteredCodeCostData?.length == 4
            ? 150
            : 180,
        formatValue: (value: string) => {
          const selectedOption = filteredCodeCostData.find(
            (item) => item.code_id == value
          );
          return selectedOption
            ? `${selectedOption.csi_name}${
                selectedOption.csi_code ? ` (${selectedOption.csi_code})` : ""
              }`
            : "Unassigned";
        },
      },
      valueGetter: (params: ValueGetterParams) => {
        return params.data?.cost_code_name
          ? generateCostCodeLabel({
              name: params.data?.cost_code_name,
              code: params.data?.cost_code,
              isArchived: false,
              isAllowCodeWithoutName: true,
            })
          : "Unassigned";
      },
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          const costCodeId = params?.newValue;

          const costCode = filteredCodeCostData?.find(
            (el) => el?.code_id === costCodeId
          );

          if (costCode) {
            const updatedData = {
              ...params.data,
              cost_code_name: costCode?.cost_code_name,
              cost_code_id: costCode?.code_id,
              cost_code: costCode?.csi_code,
              code_is_deleted: 0,
            };
            params.node.setData(updatedData);
            dispatch(
              updateProjectCostCode({
                detail_id: params?.data?.detail_id,
                cost_code_id: costCode?.code_id,
              })
            )
              .then(() => fetchProDetailApis())
              .catch((error) => {
                console.error("Error updating cost code:", error);
              });
          } else {
            params.node.setData({
              ...params.data,
              cost_code_name: "Unassigned",
              cost_code_id: null,
              cost_code: "",
              code_is_deleted: 0,
            });
          }
        }
        return true;
      },
      cellRenderer: ({ data }: { data: TimeCardData }) => {
        let typeWiseTooltip = "";

        if (
          data?.time_card_type === "employee" &&
          employee_module_access === "read_only"
        ) {
          typeWiseTooltip = "Time Card - Read Only Access";
        } else if (
          data?.time_card_type === "employee" &&
          data?.entry_type === "auto" &&
          data?.modified_detail_id === 0
        ) {
          typeWiseTooltip = "The Time Card does not have modified history";
        } else if (
          data?.time_card_type === "crew" &&
          crew_card_module_access === "read_only"
        ) {
          typeWiseTooltip = "Crew Card - Read Only Access";
        } else if (
          data?.time_card_type === "crew_sheet" &&
          crew_sheet_module_access === "read_only"
        ) {
          typeWiseTooltip = "Crew Sheet - Read Only Access";
        }

        const costCodeFind = filteredCodeCostData.find(
          (item) => item.code_id == data?.cost_code_id
        );

        const costCode = costCodeFind
          ? generateCostCodeLabel({
              name: costCodeFind.csi_name || "",
              code: costCodeFind.csi_code || "",
              isArchived: data?.code_is_deleted == 1 || !costCodeFind,
              isAllowCodeWithoutName: true,
            })
          : "Unassigned";
        const htmlDecode = HTMLEntities.decode(sanitizeString(costCode));
        return costCode ? (
          <div className="flex items-center gap-1">
            <Tooltip title={htmlDecode}>
              <Typography className="table-tooltip-text">
                {htmlDecode}
              </Typography>
            </Tooltip>
            {typeWiseTooltip && (
              <ButtonWithTooltip
                tooltipTitle={_t(typeWiseTooltip)}
                tooltipPlacement="top"
                icon="fa-regular fa-circle-info"
                onClick={() => {}}
              />
            )}
          </div>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Hours Worked"),
      field: "hours_worked",
      minWidth: 150,
      maxWidth: 150,
      suppressMovable: false,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: ({ data }: { data: TimeCardData }) => {
        const hours = Math.floor(Number(data?.work_mins) / 60);
        const remainingMinutes = Number(data?.work_mins) % 60;

        const formateHours = `${hours
          .toString()
          .padStart(2, "0")}:${remainingMinutes.toString().padStart(2, "0")}`;
        return data?.work_mins ? (
          <div className="flex items-center justify-end gap-1">
            <Tooltip title={formateHours}>
              <Typography className="table-tooltip-text">
                {formateHours}
              </Typography>
            </Tooltip>

            {data?.has_estimate_item?.toString() !== "1" && (
              <ButtonWithTooltip
                tooltipTitle={_t(
                  "Within Project > Settings you have defined the Labor rate to be taken from the Approved Estimate. The cost code associated with this item was not used within an Approved Estimate or Change Order and therefore it cannot be calculated properly."
                )}
                tooltipPlacement="top"
                icon="fa-solid fa-exclamation"
                onClick={() => {}}
              />
            )}
          </div>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "timecard_id",
      maxWidth: 50,
      minWidth: 50,
      suppressMenu: true,
      cellRenderer: ({ data }: { data: TimeCardData }) => {
        const timeCardType = data?.time_card_type;
        const entryType = data?.entry_type;
        return timeCardType === "employee" && entryType === "manual" ? (
          ""
        ) : (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={async () => {
                let tempAuthorization = authorization;
                const isExpired = isExpiredAuthorization();
                if (isExpired) {
                  const response = (await webWorkerApi({
                    url: "/api/auth/token",
                  })) as IGetTokenFromNode;
                  if (response.success) {
                    tempAuthorization = response.data.accessToken;
                    setAuthorizationExpired(response.data.accessTokenExpired);
                  }
                }
                const pathname = "manage_timecards.php";
                const newURL = new URL("/" + pathname, window.ENV.PANEL_URL);

                newURL.searchParams.set("authorize_token", tempAuthorization);
                newURL.searchParams.set("iframecall", "1");
                newURL.searchParams.set("from_remix", "1");
                newURL.searchParams.set("id", data?.timecard_id?.toString());
                setIframeData({
                  url: newURL.toString(),
                  title: String(id),
                });
              }}
            />
          </div>
        );
      },
    },
  ];

  const datasource = {
    async getRows(gridParams: IServerSideGetRowsParams) {
      const request = gridParams?.request;
      let changeGridParams: ChangeGridParams = {
        start: request?.startRow ?? 0,
        length: (request?.endRow ?? 0) - (request?.startRow ?? 0),
        order_by_name: "",
        order_by_dir: "",
      };
      const sortModel = request?.sortModel;
      if (sortModel?.length) {
        const { colId, sort } = sortModel?.[0];
        changeGridParams = {
          ...changeGridParams,
          order_by_name: colId,
          order_by_dir: sort,
        };
      }
      setGridRowParams({
        changeGridParams,
        gridParams,
      });
    },
  };

  const fetchTimeCardList = async () => {
    let gridData: {
      rowCount?: number;
      rowData: Partial<IProjectTimeCardTableData>[];
    } = {
      rowData: [],
    };
    const { changeGridParams, gridParams } = gridRowParams ?? {};
    const length = changeGridParams?.length ?? 0;
    const { start } = changeGridParams || {};
    const limit = length;
    // const startPagination = !!start ? Math.floor(start) : 0;
    const page = changeGridParams?.start
      ? Math.floor(changeGridParams?.start / length)
      : 0;

    try {
      gridParams?.api.hideOverlay();

      const resData = (await dispatch(
        fetchProjectTimecards({
          id: Number(id),
          limit,
          ...(page > 0 && { start: page * limit }),
        })
      ).unwrap()) as IProjectTimeCardApiRes;

      const rowCount = gridParams?.api?.getDisplayedRowCount() ?? 0;

      const estimatesArr =
        Array.isArray(resData?.data) && resData?.data?.length > 0
          ? resData?.data
          : [];
      if (estimatesArr?.length < length) {
        gridData = {
          ...gridData,
          rowCount: rowCount + (estimatesArr?.length ?? 0) - 1,
        };
      }
      gridData = { ...gridData, rowData: estimatesArr };

      gridParams?.success(gridData);
      if ((!resData?.success || gridData?.rowData?.length <= 0) && page === 0) {
        gridParams?.api.showNoRowsOverlay();
      } else if (resData?.success && gridData?.rowData?.length > 0) {
        gridParams?.api.hideOverlay();
      }
    } catch (err) {
      gridParams?.success({ rowCount: 0, rowData: [] });
      gridParams?.api.showNoRowsOverlay();
      gridParams?.fail();
    }
  };

  useEffect(() => {
    if (gridRowParams?.changeGridParams) {
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(() => {
        fetchTimeCardList();
      }, 500);
    }
  }, [gridRowParams?.changeGridParams]);

  const onGridReady = (gridParams: GridReadyEvent) => {
    gridParams?.api?.setServerSideDatasource(datasource);
  };
  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource({ getRows: () => {} });
    params.api.setServerSideDatasource(datasource);
  };

  return (
    <>
      <CollapseSingleTable
        title={timeCardModule?.plural_name ?? "Time Cards"}
        addButton={timeCardModule?.module_name ?? "Time Cards"}
        totalRecord={`${CURRENCY_SYMBOL}${timecardDetail?.total_cost}`}
        total={timecardDetail?.total_timecard_counts}
        totalRecordIcon={true}
        defaultActiveKey={totalCount ? ["1"] : []}
        activeKey={collapse}
        onChange={setCollapse}
        addButtonDisabled={timeCardModule?.can_write !== "1"}
        onClickAdd={async (e: any) => {
          const url = `${window.ENV.PANEL_URL}manage_timecards.php?action=new&project=${id}`;
          if (
            Number(window.ENV.ENABLE_ALL_CLICK) &&
            (e?.ctrlKey || e?.metaKey)
          ) {
            window.open(url, "_blank", "noopener,noreferrer");
          } else {
            window.location.href = url;
          }
        }}
        addButtonEvents={
          Number(window.ENV.ENABLE_ALL_CLICK)
            ? {
                onMouseDown: (e: React.MouseEvent) => {
                  if (e.button === 1) {
                    e.preventDefault();
                    const url = `${window.ENV.PANEL_URL}manage_timecards.php?action=new&project=${id}`;
                    window.open(url, "_blank", "noopener,noreferrer");
                  }
                },
                onContextMenu: (e: React.MouseEvent) => {
                  e.preventDefault();
                  setContextMenu({
                    x: e.clientX,
                    y: e.clientY,
                    visible: true,
                  });
                },
              }
            : {}
        }
        extraInfo={
          <div className="flex items-center gap-1.5 bg-primary-8 text-primary-900 py-[3px] px-[9px] rounded-sm dark:bg-dark-800 dark:text-white/90 w-fit">
            <FontAwesomeIcon
              className="w-3.5 h-3.5"
              icon="fa-duotone fa-solid fa-clock"
            />
            <Typography className="!mb-0 !text-sm !text-primary-900 leading-4 !font-semibold dark:!text-white/90 ">
              {`${timecardDetail?.total_minutes} Hrs`}
            </Typography>
          </div>
        }
        children={
          <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
            <div className="ag-theme-alpine customs-table-height customs-vertical-scroll-auto max-h-[680px]">
              <DynamicTable
                columnDefs={columnDefs}
                onGridReady={onGridReady}
                onSortChanged={onSortChanged}
                noRowsOverlayComponent={() => (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-time-cards.svg`}
                  />
                )}
              />
            </div>
          </div>
        }
      />

      {(iframeData?.url || iframeData?.addUrl) && (
        <IframeModal
          isOpen={iframeData?.url || iframeData?.addUrl ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            fetchData();
            fetchProDetailApis();
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
          modalBodyClass="p-0"
          header={{
            // icon,
            closeIcon: true,
          }}
          iframeProps={{
            src: !iframeData.url ? iframeData?.addUrl : iframeData?.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            if (key === sendMessageKeys.modal_change) {
              // reload data
              // fetchAllProjectTimeCardsModules(false, currentPage);
            }
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
        />
      )}
      <CustomContextMenu
        position={{ x: contextMenu.x, y: contextMenu.y }}
        visible={contextMenu.visible}
        items={contextMenuItems}
        onClose={() => setContextMenu((c) => ({ ...c, visible: false }))}
      />
    </>
  );
};

export default TimeCardTable;
