// Hook
import { useTranslation } from "~/hook";

// atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useAppProSelector } from "../../../../redux/store";
import {
  addCOWOItemsToProject,
  getChangeOrdersItems,
  listChangeOrders,
} from "../../../../redux/action/projectSovAction";
import { Number, sanitizeString } from "~/helpers/helper";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { filterOptionBySubstring } from "~/shared/utils/helper/common";
import { useBoolean } from "../../../../hook/use-boolean";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import {
  CheckboxSelectionCallbackParams,
  GridApi,
  GridReadyEvent,
  IRowNode,
  SelectionChangedEvent,
} from "ag-grid-community";
import debounce from "lodash/debounce";

const ImportItemsFromCo = ({
  importCoItem,
  setImportCoItem,
  callSummaryApi,
  callProjectSOVItemsApi,
  importedCOItems,
}: IImportItemsFromCoProps) => {
  const { _t } = useTranslation();
  const { getGlobalModuleByKey } = useGlobalModule();
  const { formatter } = useCurrencyFormatter();
  const { details } = useAppProSelector((state) => state.proDetails);
  const [listSection, setListSection] = useState<IProjectChangeOrder[]>([]);

  const [selectedItems, setSelectedItems] = useState<IProjectChangeOrderItem[]>(
    []
  );

  const moduleCo: IModule | undefined = getGlobalModuleByKey(
    CFConfig.change_order_module
  );
  const [isImportCheckListLoading, setIsImportCheckListLoading] =
    useState<boolean>(false);
  const [changeOrderSections, setChangeOrderSections] = useState<ICOSections[]>(
    []
  );
  const [isAddChangeOrderItemLoading, setIsAddChangeOrderItemLoading] =
    useState(false);
  const [selectTemplate, setSelectTemplate] = useState<string>("");
  const [isSubmit, setIsSubmit] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState<boolean>(false);
  const [updateKey, setUpdateKey] = useState<number>(0);

  const listBool = useBoolean();
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { module_id = 0 } = currentModule || {};

  const gridApiRef = useRef<{ [key: string]: GridApi | null }>({});

  const allItems = useMemo(() => {
    return changeOrderSections?.flatMap((sec) => sec.items);
  }, [changeOrderSections]);

  const defaultSelectedCOItems = useMemo(() => {
    const selected = allItems.filter((item) => {
      return importedCOItems.some((coItem) => {
        return (
          coItem.reference_item_id === Number(item.item_id) &&
          coItem.reference_item_type_id === Number(item.reference_item_id)
        );
      });
    });

    return selected;
  }, [importedCOItems, allItems, importCoItem]);

  useEffect(() => {
    if (defaultSelectedCOItems.length > 0) {
      setSelectedItems(defaultSelectedCOItems);
    }
  }, [defaultSelectedCOItems]);

  const colDefs = useMemo(
    () => [
      {
        headerName: "",
        field: "checkbox",
        minWidth: 38,
        maxWidth: 38,
        suppressMenu: true,
        showDisabledCheckboxes: false,
        cellClass: "ag-cell-center",
        headerClass: "ag-header-center",
        flex: 2,
        checkboxSelection: (
          params: CheckboxSelectionCallbackParams<IProjectChangeOrderItem>
        ) => {
          const node = params.node;

          const { data } = node;

          let currentSection = importedCOItems?.find(
            (item) => item.reference_item_id == Number(data?.item_id)
          )?.reference_item_id;

          return !currentSection && Number(data?.is_optional_item) === 0;
        },
        cellRenderer: (params: { data: IProjectChangeOrderItem }) => {
          const { data } = params;

          let currentSection = importedCOItems?.find(
            (item) => item.reference_item_id == Number(data?.item_id)
          )?.reference_item_id;

          const disabledTooltip = !!currentSection
            ? ""
            : "This is Optional item and will not be included into the SOV";

          const isSelectable = data?.is_optional_item !== 1;

          return (
            <>
              <Tooltip title={_t(isSelectable ? "" : disabledTooltip)}>
                <div
                  className={`ag-wrapper ag-input-wrapper ag-checkbox-input-wrapper ag-disabled ${
                    currentSection ? "ag-checked" : ""
                  }`}
                >
                  <input className="ag-input-field-input ag-checkbox-input" />
                </div>
              </Tooltip>
            </>
          );
        },
      },
      {
        headerName: _t("Item Name"),
        field: "item_name",
        minWidth: 120,
        suppressMenu: true,
        flex: 2,
        cellClass: "ag-cell-left",
        headerClass: "ag-header-left",
        cellRenderer: (params: { data: IProjectChangeOrderItem }) => {
          const { data } = params;
          const name = HTMLEntities.decode(sanitizeString(data.subject));

          return data.subject ? (
            <Tooltip title={name}>
              <Typography className="table-tooltip-text">
                {name}
                {data.is_optional_item === 1 ? (
                  <span className="text-gray-500 ml-1">(Optional) </span>
                ) : (
                  <></>
                )}
              </Typography>
            </Tooltip>
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t("QTY"),
        field: "qty",
        minWidth: 80,
        maxWidth: 80,
        suppressMenu: true,
        cellClass: "ag-cell-right",
        headerClass: "ag-header-right",
        cellRenderer: (params: { data: IProjectChangeOrderItem }) => {
          const { data } = params;
          return data.quantity ? (
            <Tooltip title={data.quantity}>
              <Typography className="table-tooltip-text">
                {data.quantity}
              </Typography>
            </Tooltip>
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t("Unit Cost"),
        field: "unit_cost",
        minWidth: 120,
        maxWidth: 120,
        suppressMenu: true,
        cellClass: "ag-cell-right",
        headerClass: "ag-header-right",
        cellRenderer: (params: { data: IProjectChangeOrderItem }) => {
          const { data } = params;

          return data.unit_cost ? (
            <Tooltip
              title={
                formatter(((Number(data.unit_cost) || 0) / 100).toFixed(2))
                  .value_with_symbol
              }
            >
              <Typography className="table-tooltip-text">
                {
                  formatter(((Number(data.unit_cost) || 0) / 100).toFixed(2))
                    .value_with_symbol
                }
              </Typography>
            </Tooltip>
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t("Total"),
        field: "total",
        minWidth: 120,
        maxWidth: 120,
        suppressMenu: true,
        cellClass: "ag-cell-right",
        headerClass: "ag-header-right",
        cellRenderer: (params: { data: IProjectChangeOrderItem }) => {
          const { data } = params;
          return data.total ? (
            <Tooltip
              title={
                formatter(
                  (
                    (Number(roundUp(Number(data.total) / 100, 2)) || 0) / 100
                  ).toFixed(2)
                ).value_with_symbol
              }
            >
              <Typography className="table-tooltip-text">
                {
                  formatter(
                    (
                      (Number(roundUp(Number(data.total) / 100, 2)) || 0) / 100
                    ).toFixed(2)
                  ).value_with_symbol
                }
              </Typography>
            </Tooltip>
          ) : (
            "-"
          );
        },
      },
    ],
    []
  );

  useEffect(() => {
    const getCompletedCOList = async () => {
      listBool.onTrue();
      try {
        const { success, data, message } = (await listChangeOrders({
          filter: {
            status: "0",
            project: !!details?.id
              ? details?.id?.toString() != "0"
                ? details?.id.toString()
                : undefined
              : undefined,
            billing_status: "140,139",
          },
          ignore_filter: 1,
          module_id: module_id,
        })) as IProjectChangeOrderListApiRes;
        if (success) {
          setListSection(data?.changeOrders ?? []);
        } else {
          notification.error({
            description: message,
          });
        }
      } catch (error) {
        notification.error({
          description: "Something went wrong!",
        });
      } finally {
        listBool.onFalse();
      }
    };
    getCompletedCOList();
  }, [importCoItem]);

  const changeOrderTamplateList = useMemo(
    () =>
      listSection?.map((item: IProjectChangeOrder) => {
        const changeOrderSubject = HTMLEntities.decode(
          sanitizeString(item.subject)
        );
        const projectName = HTMLEntities.decode(
          sanitizeString(item.project_name)
        );

        // Apply condition for project_name starting with "-"
        const label =
          !!changeOrderSubject && !!projectName
            ? `CO. #${item.company_order_id} - ${changeOrderSubject} - ${projectName}`
            : `CO. #${item.company_order_id} - ${changeOrderSubject}`;

        return {
          label,
          value: item.change_order_id.toString(),
        };
      }),
    [listSection]
  );

  const onGridReady = useCallback(
    (params: GridReadyEvent, sectionId: string | number) => {
      const gridApi = params.api;

      gridApiRef.current[sectionId] = params.api as GridApi;

      const section = changeOrderSections.find(
        (s) => Number(s.section_id) === Number(sectionId)
      );
      if (!section) return;

      // Deselect all first
      gridApi.deselectAll();

      defaultSelectedCOItems.forEach((row) => {
        if (row.section_id === sectionId) {
          const node = gridApi.getRowNode(String(row.item_id));
          if (node) {
            node.setSelected(true, false); // suppressEvent = true to avoid recursion
          }
        }
      });
    },
    [defaultSelectedCOItems, changeOrderSections]
  );

  const getChangeOrderTableList = async (value: string) => {
    setIsImportCheckListLoading(true);
    try {
      const { success, data, message } = (await getChangeOrdersItems({
        change_order_id: Number(value),
        need_section: 1,
      })) as IProjectChangeOrderItemsApiRes;
      if (success) {
        setChangeOrderSections(data.items);
      } else {
        notification.error({
          description: message,
        });
      }
    } catch (error) {
      notification.error({
        description: "Something went wrong!",
      });
    } finally {
      setIsImportCheckListLoading(false);
    }
  };

  const onChange = async (value: string | string[]) => {
    if (!!value) {
      setSelectTemplate(value as string);
      getChangeOrderTableList(value as string);
    } else {
      setSelectTemplate("");
      setChangeOrderSections([]);
    }
  };

  const handleCOSectionChanged = (
    e: CheckboxChangeEvent,
    rowData: IProjectChangeOrderItem[],
    sectionId: string | number
  ) => {
    setUpdateKey((prev) => prev + 1);

    const gridToConsider = gridApiRef.current[sectionId];
    if (!gridToConsider) return;

    if (e.target.checked) {
      setSelectedItems((prev) => {
        const updatedSelection = [
          ...prev,
          ...rowData.filter(
            (newItem) => !prev.some((item) => item.item_id === newItem.item_id)
          ),
        ];
        return updatedSelection;
      });

      rowData.forEach((row) => {
        const node = gridToConsider.getRowNode(String(row.item_id));
        if (node) {
          node.setSelected(true, false); // suppress event
        }
      });
    } else {
      setSelectedItems((prev) => {
        const updatedSelection = prev.filter(
          (item) => !rowData.some((row) => row.item_id === item.item_id)
        );
        return updatedSelection;
      });

      rowData.forEach((row) => {
        const getCurrentSection = importedCOItems?.find(
          (item) => item.reference_item_id === Number(row.item_id)
        )?.reference_item_id;

        const node = gridToConsider.getRowNode(String(row.item_id));
        if (node && !getCurrentSection) {
          node.setSelected(false, false); // suppress event
        }
      });
    }
  };

  const roundUp = (value: number, decimals: number) => {
    const multiplier = Math.pow(10, decimals);
    return Math.round(value * multiplier);
  };

  const areItemsSame = useCallback(
    (arr1: IProjectChangeOrderItem[], arr2: IProjectChangeOrderItem[]) => {
      if (arr1.length !== arr2.length) return false; // Check length first

      const itemIds1 = new Set(arr1.map((item) => item.item_id));
      const itemIds2 = new Set(arr2.map((item) => item.item_id));

      return (
        itemIds1.size === itemIds2.size &&
        [...itemIds1].every((id) => itemIds2.has(id))
      );
    },
    []
  );

  const handleCoItemSelectionChanged = useCallback(
    debounce((event: SelectionChangedEvent) => {
      const selectedNodes = event.api.getSelectedNodes();
      const unSelectedNodes = event.api
        .getRenderedNodes()
        .filter((node: IRowNode) => node.isSelected() === false)
        .map((node) => node.data);

      const selected = selectedNodes.map((node: IRowNode) => node.data);

      setSelectedItems((prev) => {
        const updatedSelection = [
          ...prev,
          ...selected.filter(
            (newItem) => !prev.some((item) => item.item_id === newItem.item_id)
          ),
        ];
        const selectedItem = updatedSelection.filter(
          (node) =>
            !unSelectedNodes.some((item) => item.item_id === node.item_id)
        );

        return [...selectedItem];
      });
    }, 50),
    []
  );

  const handleAddChangeOrderItemsToProject = async () => {
    setIsAddChangeOrderItemLoading(true);
    setIsSubmit(true);
    const itemsToBeAdd = selectedItems.filter(
      (jobItem) =>
        !(importedCOItems || []).some((item) => {
          return Number(item.reference_item_id) === Number(jobItem.item_id);
        })
    );
    const itemsToBeAddIds = itemsToBeAdd.map((item) => item.item_id);
    if (selectedItems.length !== 0) {
      try {
        const resData = await addCOWOItemsToProject({
          id: Number(details.id || 0),
          // change_order_id: Number(selectTemplate),
          item_ids: itemsToBeAddIds.join(","),
          request_from: "change-order",
        });

        if (resData.success) {
          await Promise.all([callSummaryApi(), callProjectSOVItemsApi()]);
          setIsAddChangeOrderItemLoading(false);
          setIsSubmit(false);
          setImportCoItem(false);
          setConfirmDialogOpen(false);
        } else {
          notification.error({
            description: resData.message || "Something went wrong!",
          });
          setIsAddChangeOrderItemLoading(false);
          setIsSubmit(false);
          setConfirmDialogOpen(false);
        }
      } catch (error) {
        notification.error({
          description: (error as Error).message || "Something went wrong!",
        });
        setIsAddChangeOrderItemLoading(false);
        setIsSubmit(false);
        setImportCoItem(false);
        setConfirmDialogOpen(false);
      }
    } else {
      notification.error({
        description: "Please select atleast one item.",
      });
      setIsAddChangeOrderItemLoading(false);
      setIsSubmit(false);
      setConfirmDialogOpen(false);
    }
  };

  return (
    <>
      <Drawer
        open={importCoItem}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-arrow-down-arrow-up"
              />
            </div>
            <div className="flex flex-col max-w-[calc(100%-40px)]">
              <Header
                level={5}
                className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
              >
                {_t(`Import Items from ${moduleCo?.module_name}`)}
              </Header>
              <Typography className="text-xs font-normal text-[#808697] leading-3">
                {_t(
                  `${moduleCo?.module_name} with a Billing Status of Approved/Unbilled will show in dropdown.`
                )}
              </Typography>
            </div>
          </div>
        }
        closeIcon={<CloseButton onClick={() => setImportCoItem(false)} />}
      >
        <form className="py-4">
          <div className="sidebar-body overflow-y-auto sm:h-[calc(100vh-139px)] h-[calc(100vh-148px)] px-4">
            <div className="grid gap-4">
              <Typography className="text-13 font-normal text-primary-900">
                {_t(
                  "Not finding the CO you are looking for? Archived COs will not appear for import. If you need to use an archived CO you will need to make it active again from your Change Orders Module"
                )}
              </Typography>
              <SidebarCardBorder addGap={true}>
                <div className="grid gap-3.5">
                  <div className="w-full max-w-full overflow-hidden">
                    <SelectField
                      label={_t(
                        `${HTMLEntities.decode(
                          sanitizeString(moduleCo?.module_name)
                        )}`
                      )}
                      placeholder={`Select Approved ${moduleCo?.module_name} (If Any)`}
                      labelPlacement="top"
                      isRequired={true}
                      options={changeOrderTamplateList}
                      showSearch
                      filterOption={(input, option) =>
                        filterOptionBySubstring(input, option?.label as string)
                      }
                      allowClear
                      onChange={onChange}
                      errorMessage={
                        isSubmit
                          ? !selectTemplate
                            ? "This field is required."
                            : ""
                          : ""
                      }
                    />
                  </div>
                  {isImportCheckListLoading ? (
                    <Spin className="w-full h-[150px] flex items-center justify-center" />
                  ) : (
                    <div className="flex flex-col gap-1.5">
                      <Typography className="text-sm text-[#343a40e3] font-semibold">
                        {_t(`${moduleCo?.module_name} Items`)}
                      </Typography>
                      <div className="mt-4">
                        {changeOrderSections?.map(
                          (sections: ICOSections, index: number) => {
                            const isAllItemsSelected =
                              sections.is_optional_section !== 1 &&
                              areItemsSame(
                                sections.items.filter(
                                  (item) => item.is_optional_item !== 1
                                ),
                                selectedItems.filter(
                                  (item) =>
                                    item.section_id.toString() ===
                                    sections.section_id.toString()
                                )
                              );

                            const checkBoxDisabled = areItemsSame(
                              sections.items.filter(
                                (item) => item.is_optional_item !== 1
                              ),
                              defaultSelectedCOItems.filter(
                                (item) =>
                                  item.section_id.toString() ===
                                  sections.section_id.toString()
                              )
                            );

                            return (
                              <div
                                className={`flex flex-col gap-2 ${
                                  index !== 0 && "mt-3"
                                }`}
                                key={sections.section_id}
                              >
                                <CheckBox
                                  key={updateKey}
                                  checked={
                                    !sections.items.length
                                      ? false
                                      : isAllItemsSelected
                                  }
                                  disabled={
                                    checkBoxDisabled ||
                                    Number(sections.is_optional_section) === 1
                                  }
                                  className="gap-1.5"
                                  onChange={(e) => {
                                    handleCOSectionChanged(
                                      e,
                                      sections.items.filter(
                                        (item) => item.is_optional_item !== 1
                                      ),
                                      sections.section_id
                                    );
                                  }}
                                >
                                  {_t(sections.section_name)}

                                  {sections.is_optional_section === 1 && (
                                    <Tooltip
                                      className="ml-2"
                                      title={HTMLEntities.decode(
                                        sanitizeString(
                                          "This is an Optional section and will not be included in the SOV"
                                        )
                                      )}
                                    >
                                      <FontAwesomeIcon
                                        icon="fa-regular fa-circle-info"
                                        className="text-primary-900 ml-1.5"
                                      />
                                    </Tooltip>
                                  )}
                                </CheckBox>
                                <div className="p-2 common-card">
                                  <div className="ag-theme-alpine customs-table-height customs-vertical-scroll-auto max-h-[680px] sov_change_order_table">
                                    <StaticTable
                                      key={sections.section_id?.toString()}
                                      className="static-table checkbox-ml-none"
                                      rowSelection={"multiple"}
                                      columnDefs={colDefs}
                                      rowMultiSelectWithClick={true}
                                      suppressRowClickSelection={true}
                                      onSelectionChanged={
                                        handleCoItemSelectionChanged
                                      }
                                      onGridReady={(params) =>
                                        onGridReady(params, sections.section_id)
                                      }
                                      isRowSelectable={(node) =>
                                        node.data.is_optional_item !== 1
                                      }
                                      rowData={sections.items}
                                      rowBuffer={10}
                                      cacheBlockSize={50}
                                      suppressMovableColumns={true}
                                      getRowId={(params) =>
                                        String(params.data?.item_id)
                                      }
                                      noRowsOverlayComponent={() => (
                                        <NoRecords
                                          image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                                        />
                                      )}
                                    />
                                  </div>
                                </div>
                              </div>
                            );
                          }
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </SidebarCardBorder>
            </div>
          </div>
          <div className="sidebar-footer w-full px-4 pt-4">
            <PrimaryButton
              htmlType="button"
              disabled={isAddChangeOrderItemLoading || listBool.bool}
              loading={isAddChangeOrderItemLoading}
              onClick={() => {
                if (selectTemplate) {
                  setConfirmDialogOpen(true);
                } else {
                  setIsSubmit(true);
                }
              }}
              buttonText={_t(`Add Items to List`)}
            />
          </div>
        </form>
      </Drawer>

      {confirmDialogOpen && (
        <ConfirmModal
          isOpen={confirmDialogOpen}
          modaltitle={_t("Confirmation")}
          description={_t(
            "Are you sure you want to add these items to Schedule of Values?"
          )}
          isLoading={isAddChangeOrderItemLoading}
          modalIcon="fa-regular fa-file-check"
          onAccept={() => {
            handleAddChangeOrderItemsToProject();
          }}
          onDecline={() => setConfirmDialogOpen(false)}
          onCloseModal={() => setConfirmDialogOpen(false)}
        />
      )}
    </>
  );
};

export default ImportItemsFromCo;
