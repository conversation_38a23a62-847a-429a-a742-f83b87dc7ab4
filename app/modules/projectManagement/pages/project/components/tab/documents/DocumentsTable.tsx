import { useEffect, useState, useMemo } from "react";

// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { DocumentWriterFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/documentWriterFieldRedirectionIcon";
// Other
import { useAppProSelector } from "../../../redux/store";
import { useParams } from "@remix-run/react";
import {
  getDefaultStatuscolor,
  isJson,
  sanitizeString,
} from "~/helpers/helper";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { getGConfig } from "~/zustand";
import { sendMessageKeys } from "~/components/page/$url/data";
import { DocumentDetail } from "~/shared/components/organisms/documentDetail";
import {
  getDocumentTemplatePdf,
  getDocumentView,
} from "~/modules/people/directory/redux/action/dirDocumentsAction";
import DirSendEmail from "~/modules/people/directory/components/DirSendEmail";
import { defaultConfig } from "~/data";

const DocumentsTable = (props: DocumentTableProps) => {
  const { fetchAllProjectDocumentsModules, dataLimit } = props;
  const { _t } = useTranslation();
  const { documentsData } = useAppProSelector((state) => state.proDashDocument);
  const documents = documentsData?.document_writer ?? [];
  const { id } = useParams();
  const { getGlobalModuleByKey } = useGlobalModule();
  const documentsModule = getGlobalModuleByKey(CFConfig.document_writer_module);
  const [isShowingMore, setIsShowingMore] = useState<boolean>(false);
  const [allDocuments, setAllDocuments] = useState(documents);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [collapse, setCollapse] = useState<string[]>([]);
  const displayedDocuments = isShowingMore
    ? allDocuments
    : allDocuments.slice(0, dataLimit);
  const totalCount = Number(
    documentsData?.document_writers_count?.[0]?.number_of_document_writer ?? 0
  );
  // const { authorization } = useLoaderData<typeof loader>();
  const { authorization }: GConfig = getGConfig();
  const [iframeData, setIframeData] = useState<{
    url?: string;
    title: string;
    addUrl?: string;
  }>({ url: "", title: "", addUrl: "" });
  const [isViewOpen, setIsViewOpen] = useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [viewData, setViewData] = useState<Partial<DocumentsData>>({});
  const [selectedData, setSelectedData] = useState<Partial<DocumentsData>>({});
  const [isViewLoading, setIsViewLoading] = useState<boolean>(false);
  const [isDwnlLoading, setIsDwnlLoading] = useState<boolean>(false);

  useEffect(() => {
    if (isInitialLoad || !isShowingMore) {
      setAllDocuments(documents);
      setIsInitialLoad(false);
    } else {
      const newDocuments = documents.filter(
        (item) => !allDocuments.some((p) => p.document_id === item.document_id)
      );
      if (newDocuments.length > 0) {
        setAllDocuments((prev) => [...prev, ...newDocuments]);
      } else {
        setAllDocuments(documents);
      }
    }
  }, [documents, isInitialLoad]);

  useEffect(() => {
    if (documents?.length) {
      setCollapse(["1"]);
    }
  }, [documents]);

  const handleShowMore = () => {
    if (allDocuments.length === totalCount) {
      setIsShowingMore(true);
    } else {
      setIsShowingMore(true);
      fetchAllProjectDocumentsModules(true, ["document_writer", "counts"]);
    }
  };

  const handleShowLess = () => {
    setIsShowingMore(false);
  };

  const handleViewClick = async (Id: number) => {
    setIsViewOpen(true);
    if (!isViewLoading) {
      setIsViewLoading(true);
      setViewData({});
      const viewRes = (await getDocumentView({
        formData: { get_html_only: 1, replace_fields: 1 },
        paramsData: {
          directoryId: id || "",
          companyDocumentId: Id.toString() || "",
        },
      })) as IDirDocViewApiRes;
      if (viewRes?.success) {
        setViewData(viewRes?.data);
      } else {
        notification.error({
          description: viewRes?.message,
        });
      }
      setIsViewLoading(false);
    }
  };

  const handleDownloadClick = async () => {
    const id: string = selectedData?.company_document_id?.toString() || "";
    if (!isDwnlLoading) {
      setIsDwnlLoading(true);

      const dwnlRes = (await getDocumentTemplatePdf({
        formData: {
          get_html_only: 0,
          document_html: viewData?.document_html,
          document_id: Number(viewData?.document_id) || 0,
        },
        paramsData: {
          directoryId: id || "",
          companyDocumentId: id.toString() || "",
        },
      })) as IDirDocViewApiRes;

      if (dwnlRes?.success) {
        if (isJson(dwnlRes?.preview_doc || "")) {
          const jsonObject = JSON.parse(dwnlRes.preview_doc || "");
          const url = jsonObject?.url;
          if (url) {
            window.open(url, "_blank");
          }
        }
      } else {
        notification.error({
          description: dwnlRes?.message,
        });
      }
      setIsDwnlLoading(false);
    }
  };

  const transformAttachedFiles = (attachedFiles?: string) => {
    if (!attachedFiles) return [];
    try {
      const files = JSON.parse(attachedFiles);
      return files.map((file: IFile) => ({ file_path: file.file_url }));
    } catch (error) {
      console.error("Error parsing attached files JSON", error);
      return [];
    }
  };

  const emailDataFiles = useMemo(
    () => transformAttachedFiles(selectedData?.attached_files),
    [selectedData]
  );

  const columnDefs = [
    {
      headerName: _t("Document Name"),
      field: "document_name",
      minWidth: 135,
      maxWidth: 135,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const document_name = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={document_name}>
            <Typography className="table-tooltip-text">
              {document_name}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },

    {
      headerName: _t("Module Name"),
      field: "module_name",
      minWidth: 130,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const module_name = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={module_name}>
            <Typography className="table-tooltip-text">
              {module_name}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },

    {
      headerName: _t("Date"),
      field: "date_added",
      maxWidth: 270,
      minWidth: 270,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ value }: { value: string }) => {
        return value ? <DateTimeCard format="date" date={value} /> : "-";
      },
    },

    {
      headerName: "Status",
      maxWidth: 120,
      minWidth: 120,
      field: "status",
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-center-aligned-cell",
      cellRenderer: ({ data }: { data: DocumentsData }) => {
        const status = data.status;
        let color = "";
        let textColor = "";

        const hexToRgb = (hex: string): string => {
          let cleanHex = hex.replace("#", "");

          if (cleanHex.length === 3) {
            cleanHex = cleanHex
              .split("")
              .map((c) => c + c)
              .join("");
          }

          const bigint = parseInt(cleanHex, 16);
          const r = (bigint >> 16) & 255;
          const g = (bigint >> 8) & 255;
          const b = bigint & 255;

          return `${r}, ${g}, ${b}`;
        };

        if (data?.document_status_key == 2) {
          color = "#45897e";
        } else if (data?.document_status_key == 1) {
          color = "#ff5400";
        } else {
          color = "#343a40";
        }
        textColor = `rgba(${hexToRgb(color)}, 0.18)`;

        return status ? (
          <Tooltip title={status}>
            <div className="text-center overflow-hidden">
              <Tag
                color={color}
                style={{
                  backgroundColor: textColor,
                  color: color,
                }}
                className={`${
                  textColor === "" && "!text-primary-900"
                } mx-auto text-13 type-badge common-tag max-w-24`}
              >
                {status}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          <Typography className="table-tooltip-text">-</Typography>
        );
      },
    },

    {
      headerName: "",
      field: "document_id",
      maxWidth: 70,
      minWidth: 70,
      suppressMenu: true,
      cellRenderer: ({ data }: { data: DocumentsData }) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={() => {
                setSelectedData(data);
                handleViewClick(Number(data?.company_document_id) || 0);
              }}
            />
            <div className="w-6">
              <DocumentWriterFieldRedirectionIcon
                iconClassName="!w-3.5 !h-3.5"
                documentWriterId={Number(data?.company_document_id)}
              />
            </div>
          </div>
        );
      },
    },
  ];

  return (
    <>
      <CollapseSingleTable
        // title={_t(documentsModule?.plural_name || "Documents")}
        title="Documents"
        addButton={_t(
          HTMLEntities.decode(sanitizeString(documentsModule?.module_name)) ||
            "Document Writer"
        )}
        defaultActiveKey={totalCount ? ["1"] : []}
        activeKey={collapse}
        onChange={setCollapse}
        addButtonDisabled={documentsModule?.can_write !== "1"}
        onClickAdd={async () => {
          if (!id) {
            return;
          }
          let tempAuthorization = authorization;
          const isExpired = isExpiredAuthorization();
          if (isExpired) {
            const response = (await webWorkerApi({
              url: "/api/auth/token",
            })) as IGetTokenFromNode;
            if (response.success) {
              tempAuthorization = response.data.accessToken;
              setAuthorizationExpired(response.data.accessTokenExpired);
            }
          }
          const pathname = "manage_document_writer.php";
          const newURL = new URL("/" + pathname, window.ENV.PANEL_URL);

          newURL.searchParams.set("authorize_token", tempAuthorization);
          newURL.searchParams.set("iframecall", "1");
          newURL.searchParams.set("from_remix", "1");
          newURL.searchParams.set("action", "new");
          newURL.searchParams.set("project", id?.toString());
          setIframeData({
            addUrl: newURL.toString(),
            title: String(id),
          });
        }}
        rightsideContant={
          dataLimit < totalCount && (
            <ButtonWithTooltip
              tooltipTitle={isShowingMore ? _t("Show Less") : _t("Show More")}
              tooltipPlacement="top"
              icon={
                isShowingMore
                  ? "fa-regular fa-arrow-up"
                  : "fa-regular fa-arrow-down"
              }
              className="hover:!bg-primary-900/20"
              onClick={isShowingMore ? handleShowLess : handleShowMore}
            />
          )
        }
        children={
          <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
            <div className="ag-theme-alpine">
              <StaticTable
                className="static-table"
                columnDefs={columnDefs}
                rowData={displayedDocuments}
                noRowsOverlayComponent={() => (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-froala-editor.svg`}
                  />
                )}
              />
            </div>
          </div>
        }
      />

      {(iframeData?.url || iframeData?.addUrl) && (
        <IframeModal
          isOpen={iframeData?.url || iframeData?.addUrl ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            fetchAllProjectDocumentsModules(false, [
              "document_writer",
              "counts",
            ]);
            setIframeData({ url: "", title: "", addUrl: "" });
            handleShowLess();
          }}
          modalBodyClass="p-0"
          header={{
            // icon,
            closeIcon: true,
          }}
          iframeProps={{
            src: !iframeData.url ? iframeData?.addUrl : iframeData?.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            if (key === sendMessageKeys.iframe_change) {
              // reload data
              fetchAllProjectDocumentsModules(false, [
                "document_writer",
                "counts",
              ]);
              handleShowLess();
            }
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
        />
      )}

      {isViewOpen && (
        <DocumentDetail
          isOpen={isViewOpen}
          docDetail={viewData}
          isLoading={isViewLoading || isDwnlLoading}
          onDownloadClick={() => {
            handleDownloadClick();
          }}
          onEmailClick={() => {
            setIsSendEmailSidebarOpen(true);
          }}
          onClose={() => setIsViewOpen(false)}
        />
      )}

      <DirSendEmail
        isOpen={isSendEmailSidebarOpen}
        isViewAttachment={false}
        options={[
          defaultConfig.employee_key,
          "my_crew",
          defaultConfig.customer_key,
          defaultConfig.contractor_key,
          defaultConfig.vendor_key,
          defaultConfig.misc_contact_key,
          "by_service",
        ]}
        emailData={{
          subject: selectedData?.document_name || "Document",
          body: "",
          files: emailDataFiles,
        }}
        op="get_dw_template_pdf"
        otherPerams={{
          sendEmail: "1",
          document_id: viewData?.document_id || "",
          company_document_id: viewData?.company_document_id || "",
          document_html: viewData?.document_html || "",
        }}
        app_access={false}
        onSendResponse={() => {}}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
        }}
        groupCheckBox={true}
      />
    </>
  );
};

export default DocumentsTable;
