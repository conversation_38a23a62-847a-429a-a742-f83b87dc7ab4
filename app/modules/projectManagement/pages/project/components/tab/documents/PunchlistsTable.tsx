import { useEffect, useState } from "react";

// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { PunchlistFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/punchlistFieldRedirectionIcon";
// Other
import { useParams } from "@remix-run/react";
import { useAppProSelector } from "../../../redux/store";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { webWorker<PERSON>pi } from "~/shared/services/apiService/web-worker";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { getGConfig } from "~/zustand";
import { sendMessageKeys } from "~/components/page/$url/data";
import { sanitizeString } from "~/helpers/helper";
import { routes } from "~/route-services/routes";
import { remixCompletePages } from "~/data/pages";
import { getMenuData } from "~/zustand/global/menuModules/slice";

const PunchlistsTable = (props: DocumentTableProps) => {
  const { fetchAllProjectDocumentsModules, dataLimit } = props;
  const { _t } = useTranslation();
  const { id } = useParams();
  const { getGlobalModuleByKey } = useGlobalModule();
  const punchlistModule = getGlobalModuleByKey(CFConfig.punch_list_module);
  const { documentsData } = useAppProSelector((state) => state.proDashDocument);
  const punchLists = documentsData?.punchlists ?? [];
  const [isShowingMore, setIsShowingMore] = useState<boolean>(false);
  const [allPunchLists, setAllPunchLists] = useState(punchLists);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [collapse, setCollapse] = useState<string[]>([]);
  const [isRemixCompletePage, setIsRemixCompletePage] =
    useState<boolean>(false);
  const displayedPunchLists = isShowingMore
    ? allPunchLists
    : allPunchLists.slice(0, dataLimit);
  const totalCount = Number(
    documentsData?.punchlists_count?.[0]?.number_of_punchlist ?? 0
  );
  const { authorization }: GConfig = getGConfig();
  const [iframeData, setIframeData] = useState<{
    url?: string;
    title: string;
    addUrl?: string;
  }>({ url: "", title: "", addUrl: "" });

  useEffect(() => {
    if (isInitialLoad || !isShowingMore) {
      setAllPunchLists(punchLists);
      setIsInitialLoad(false);
    } else {
      const newPermits = punchLists.filter(
        (item) =>
          !allPunchLists.some((p) => p.punchlist_id === item.punchlist_id)
      );
      if (newPermits.length > 0) {
        setAllPunchLists((prev) => [...prev, ...newPermits]);
      } else {
        setAllPunchLists(punchLists);
      }
    }
  }, [punchLists, isInitialLoad]);

  useEffect(() => {
    if (punchLists.length) {
      setCollapse(["1"]);
    }
  }, [punchLists]);

  const handleShowMore = () => {
    if (allPunchLists.length === totalCount) {
      setIsShowingMore(true);
    } else {
      setIsShowingMore(true);
      fetchAllProjectDocumentsModules(true, ["punchlists", "counts"]);
    }
  };

  const handleShowLess = () => {
    setIsShowingMore(false);
  };

  useEffect(() => {
    let remixComplete =
      remixCompletePages.findIndex(
        (item) => item.key === CFConfig.punch_list_module
      ) !== -1;
    setIsRemixCompletePage(remixComplete);
  }, []);

  const columnDefs = [
    {
      headerName: _t("Title"),
      field: "punchlist_name",
      minWidth: 135,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const punchlist_name = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={punchlist_name}>
            <Typography className="table-tooltip-text">
              {punchlist_name}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("# of Items"),
      field: "item_count",
      minWidth: 180,
      maxWidth: 180,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
    },
    {
      headerName: _t("# Complete"),
      field: "item_completed",
      minWidth: 180,
      maxWidth: 180,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
    },
    {
      headerName: _t("# Incomplete"),
      field: "item_incompleted",
      minWidth: 180,
      maxWidth: 180,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
    },
    {
      headerName: "",
      field: "punchlist_id",
      maxWidth: 70,
      minWidth: 70,
      suppressMenu: true,
      cellRenderer: ({ value }: { value: number }) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={async () => {
                let tempAuthorization = authorization;
                const isExpired = isExpiredAuthorization();
                if (isExpired) {
                  const response = (await webWorkerApi({
                    url: "/api/auth/token",
                  })) as IGetTokenFromNode;
                  if (response.success) {
                    tempAuthorization = response.data.accessToken;
                    setAuthorizationExpired(response.data.accessTokenExpired);
                  }
                }

                let urlObj: URL;

                if (isRemixCompletePage) {
                  urlObj = new URL(
                    routes.MANAGE_PUNCHLISTS.url +
                      "/" +
                      (value?.toString() || ""),
                    window.location.origin
                  );
                } else {
                  const pathname = "manage_punchlists.php";
                  urlObj = new URL("/" + pathname, window.ENV.PANEL_URL);
                }

                urlObj.searchParams.set("id", value?.toString() ?? "");
                urlObj.searchParams.set("authorize_token", tempAuthorization);
                urlObj.searchParams.set("iframecall", "1");
                urlObj.searchParams.set("from_remix", "1");

                setIframeData({
                  url: urlObj.toString(),
                  title: String(id),
                });
              }}
            />
            <div className="w-6">
              <PunchlistFieldRedirectionIcon
                iconClassName="!w-3.5 !h-3.5"
                punchlistId={value}
              />
            </div>
          </div>
        );
      },
    },
  ];
  return (
    <>
      <CollapseSingleTable
        title={_t(
          HTMLEntities.decode(sanitizeString(punchlistModule?.plural_name)) ||
            "Punchlists"
        )}
        addButton={_t(
          HTMLEntities.decode(sanitizeString(punchlistModule?.module_name)) ||
            "Punchlist"
        )}
        defaultActiveKey={totalCount ? ["1"] : []}
        activeKey={collapse}
        onChange={setCollapse}
        addButtonDisabled={punchlistModule?.can_write !== "1"}
        onClickAdd={async () => {
          if (!id) {
            return;
          }
          let tempAuthorization = authorization;
          const isExpired = isExpiredAuthorization();
          if (isExpired) {
            const response = (await webWorkerApi({
              url: "/api/auth/token",
            })) as IGetTokenFromNode;
            if (response.success) {
              tempAuthorization = response.data.accessToken;
              setAuthorizationExpired(response.data.accessTokenExpired);
            }
          }

          let urlObj: URL;

          if (isRemixCompletePage) {
            urlObj = new URL(
              routes.MANAGE_PUNCHLISTS.url + "/",
              window.location.origin
            );
          } else {
            const pathname = "manage_punchlists.php";
            urlObj = new URL("/" + pathname, window.ENV.PANEL_URL);
          }

          urlObj.searchParams.set("authorize_token", tempAuthorization);
          urlObj.searchParams.set("iframecall", "1");
          urlObj.searchParams.set("from_remix", "1");
          urlObj.searchParams.set("action", "new");
          urlObj.searchParams.set("project", id?.toString() ?? "");

          setIframeData({
            addUrl: urlObj.toString(),
            title: String(id),
          });
        }}
        rightsideContant={
          dataLimit < totalCount && (
            <ButtonWithTooltip
              tooltipTitle={isShowingMore ? _t("Show Less") : _t("Show More")}
              tooltipPlacement="top"
              icon={
                isShowingMore
                  ? "fa-regular fa-arrow-up"
                  : "fa-regular fa-arrow-down"
              }
              className="hover:!bg-primary-900/20"
              onClick={isShowingMore ? handleShowLess : handleShowMore}
            />
          )
        }
        children={
          <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
            <div className="ag-theme-alpine">
              <StaticTable
                className="static-table"
                columnDefs={columnDefs}
                rowData={displayedPunchLists}
                noRowsOverlayComponent={() => (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-punchlist.svg`}
                  />
                )}
              />
            </div>
          </div>
        }
      />

      {(iframeData?.url || iframeData?.addUrl) && (
        <IframeModal
          isOpen={iframeData?.url || iframeData?.addUrl ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            fetchAllProjectDocumentsModules(false, ["punchlists", "counts"]);
            setIframeData({ url: "", title: "", addUrl: "" });
            handleShowLess();
          }}
          modalBodyClass="p-0"
          header={{
            // icon,
            closeIcon: true,
          }}
          iframeProps={{
            src: !iframeData.url ? iframeData?.addUrl : iframeData?.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            if (
              key ===
              (isRemixCompletePage
                ? sendMessageKeys.modal_change
                : sendMessageKeys.iframe_change)
            ) {
              // reload data
              fetchAllProjectDocumentsModules(false, ["punchlists", "counts"]);
              handleShowLess();
            }
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
        />
      )}
    </>
  );
};

export default PunchlistsTable;
