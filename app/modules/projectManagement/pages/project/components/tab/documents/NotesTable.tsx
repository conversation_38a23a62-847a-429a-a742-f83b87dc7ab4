import { useEffect, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { NotesFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/notesFieldRedirectionIcon";
// Other
import { useParams } from "@remix-run/react";
import { useAppProSelector } from "../../../redux/store";
import { getDefaultStatuscolor, sanitizeString } from "~/helpers/helper";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { getGConfig } from "~/zustand";
import { sendMessageKeys } from "~/components/page/$url/data";

const NotesTable = (props: DocumentTableProps) => {
  const { fetchAllProjectDocumentsModules, dataLimit } = props;
  const { _t } = useTranslation();
  const { id } = useParams();
  const { getGlobalModuleByKey } = useGlobalModule();
  const notesModule = getGlobalModuleByKey(CFConfig.notes_module);
  const { documentsData } = useAppProSelector((state) => state.proDashDocument);
  const notes = documentsData?.notes ?? [];
  const [isShowingMore, setIsShowingMore] = useState<boolean>(false);
  const [allNotes, setAllNotes] = useState(notes);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [collapse, setCollapse] = useState<string[]>([]);
  const displayedNotes = isShowingMore
    ? allNotes
    : allNotes.slice(0, dataLimit);
  const totalCount = Number(
    documentsData?.notes_count?.[0]?.number_of_note ?? 0
  );
  // const { authorization } = useLoaderData<typeof loader>();
  const { authorization }: GConfig = getGConfig();
  const [iframeData, setIframeData] = useState<{
    url?: string;
    title: string;
    addUrl?: string;
  }>({ url: "", title: "", addUrl: "" });

  useEffect(() => {
    if (isInitialLoad || !isShowingMore) {
      setAllNotes(notes);
      setIsInitialLoad(false);
    } else {
      const newNotes = notes.filter(
        (item) => !allNotes.some((p) => p.note_id === item.note_id)
      );
      if (newNotes.length > 0) {
        setAllNotes((prev) => [...prev, ...newNotes]);
      } else {
        setAllNotes(notes);
      }
    }
  }, [notes, isInitialLoad]);

  useEffect(() => {
    if (notes.length) {
      setCollapse(["1"]);
    }
  }, [notes]);

  const handleShowMore = () => {
    if (allNotes.length === totalCount) {
      setIsShowingMore(true);
    } else {
      setIsShowingMore(true);
      fetchAllProjectDocumentsModules(true, ["notes", "counts"]);
    }
  };

  const handleShowLess = () => {
    setIsShowingMore(false);
  };

  const columnDefs = [
    {
      headerName: _t("Title"),
      field: "subject",
      minWidth: 135,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const subject = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={subject}>
            <Typography className="table-tooltip-text">{subject}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Created By"),
      field: "created_by",
      minWidth: 200,
      maxWidth: 200,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: ({ data }: { data: NotesData }) => {
        const name = HTMLEntities.decode(sanitizeString(data.created_by));
        return name ? (
          <Tooltip title={name}>
            <div className="w-fit mx-auto">
              <AvatarProfile
                user={{
                  name: name,
                  image: data.user_avtar,
                }}
                className="m-auto"
                iconClassName="text-[11px] font-semibold"
              />
            </div>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "date",
      maxWidth: 270,
      minWidth: 270,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ data }: { data: NotesData }) => {
        return data?.date_added ? (
          <DateTimeCard format="date" date={data?.date_added} />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "Status",
      maxWidth: 120,
      minWidth: 120,
      field: "status",
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-center-aligned-cell",
      cellRenderer: ({ data }: { data: NotesData }) => {
        const status = data.status_name;
        const { color, textColor } = getDefaultStatuscolor(
          data.default_status_color || ""
        );
        return status ? (
          <Tooltip title={status}>
            <div className="text-center overflow-hidden">
              <Tag
                color={color}
                style={{
                  color: `${textColor || ""}`,
                }}
                className={`${
                  textColor === "" && "!text-primary-900"
                } mx-auto text-13 type-badge common-tag max-w-24`}
              >
                {status}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          <Typography className="table-tooltip-text">-</Typography>
        );
      },
    },
    {
      headerName: "",
      field: "note_id",
      maxWidth: 70,
      minWidth: 70,
      suppressMenu: true,
      cellRenderer: ({ value }: { value: number }) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={async () => {
                let tempAuthorization = authorization;
                const isExpired = isExpiredAuthorization();
                if (isExpired) {
                  const response = (await webWorkerApi({
                    url: "/api/auth/token",
                  })) as IGetTokenFromNode;
                  if (response.success) {
                    tempAuthorization = response.data.accessToken;
                    setAuthorizationExpired(response.data.accessTokenExpired);
                  }
                }
                const pathname = "manage_notes.php";
                const newURL = new URL("/" + pathname, window.ENV.PANEL_URL);

                newURL.searchParams.set("authorize_token", tempAuthorization);
                newURL.searchParams.set("iframecall", "1");
                newURL.searchParams.set("from_remix", "1");
                newURL.searchParams.set("id", value?.toString());
                setIframeData({
                  url: newURL.toString(),
                  title: String(id),
                });
              }}
            />
            <div className="w-6">
              <NotesFieldRedirectionIcon
                iconClassName="!w-3.5 !h-3.5"
                notesId={value}
              />
            </div>
          </div>
        );
      },
    },
  ];
  return (
    <>
      <CollapseSingleTable
        title={_t(
          HTMLEntities.decode(sanitizeString(notesModule?.plural_name)) ||
            "Notes"
        )}
        addButton={_t(
          HTMLEntities.decode(sanitizeString(notesModule?.module_name)) ||
            "Notes"
        )}
        defaultActiveKey={totalCount ? ["1"] : []}
        activeKey={collapse}
        onChange={setCollapse}
        addButtonDisabled={notesModule?.can_write !== "1"}
        onClickAdd={async () => {
          if (!id) {
            return;
          }
          let tempAuthorization = authorization;
          const isExpired = isExpiredAuthorization();
          if (isExpired) {
            const response = (await webWorkerApi({
              url: "/api/auth/token",
            })) as IGetTokenFromNode;
            if (response.success) {
              tempAuthorization = response.data.accessToken;
              setAuthorizationExpired(response.data.accessTokenExpired);
            }
          }
          const pathname = "manage_notes.php";
          const newURL = new URL("/" + pathname, window.ENV.PANEL_URL);

          newURL.searchParams.set("authorize_token", tempAuthorization);
          newURL.searchParams.set("iframecall", "1");
          newURL.searchParams.set("from_remix", "1");
          newURL.searchParams.set("action", "new");
          newURL.searchParams.set("project", id?.toString());
          setIframeData({
            addUrl: newURL.toString(),
            title: String(id),
          });
        }}
        rightsideContant={
          dataLimit < totalCount && (
            <ButtonWithTooltip
              tooltipTitle={isShowingMore ? _t("Show Less") : _t("Show More")}
              tooltipPlacement="top"
              icon={
                isShowingMore
                  ? "fa-regular fa-arrow-up"
                  : "fa-regular fa-arrow-down"
              }
              className="hover:!bg-primary-900/20"
              onClick={isShowingMore ? handleShowLess : handleShowMore}
            />
          )
        }
        children={
          <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
            <div className="ag-theme-alpine">
              <StaticTable
                className="static-table"
                columnDefs={columnDefs}
                rowData={displayedNotes}
                noRowsOverlayComponent={() => (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-notes-table.svg`}
                  />
                )}
              />
            </div>
          </div>
        }
      />

      {(iframeData?.url || iframeData?.addUrl) && (
        <IframeModal
          isOpen={iframeData?.url || iframeData?.addUrl ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            fetchAllProjectDocumentsModules(false, ["notes", "counts"]);
            setIframeData({ url: "", title: "", addUrl: "" });
            handleShowLess();
          }}
          modalBodyClass="p-0"
          header={{
            // icon,
            closeIcon: true,
          }}
          iframeProps={{
            src: !iframeData.url ? iframeData?.addUrl : iframeData?.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            if (key === sendMessageKeys.iframe_change) {
              // reload data
              fetchAllProjectDocumentsModules(false, ["notes", "counts"]);
              handleShowLess();
            }
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
        />
      )}
    </>
  );
};

export default NotesTable;
