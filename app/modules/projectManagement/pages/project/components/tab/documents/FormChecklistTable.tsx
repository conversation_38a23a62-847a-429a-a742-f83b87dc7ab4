import { useEffect, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { FormsChecklistsFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/formsChecklistsFieldRedirectionIcon";
// Other
import { useParams } from "@remix-run/react";
import { useAppProSelector } from "../../../redux/store";
import { getDefaultStatuscolor, sanitizeString } from "~/helpers/helper";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { getGConfig } from "~/zustand";
import { PDFFilePreviewWithOP } from "~/shared/components/molecules/pdfFilePreviewwithOP";
import { sendMessageKeys } from "~/components/page/$url/data";
import { routes } from "~/route-services/routes";

const FormChecklistTable = (props: DocumentTableProps) => {
  const { fetchAllProjectDocumentsModules, dataLimit } = props;
  const { _t } = useTranslation();
  const { id } = useParams();
  const { getGlobalModuleByKey } = useGlobalModule();
  const formChecklistModule = getGlobalModuleByKey(
    CFConfig.forms_checklist_module
  );

  const { documentsData } = useAppProSelector((state) => state.proDashDocument);
  const formChecklist = documentsData?.checklists ?? [];
  const [isShowingMore, setIsShowingMore] = useState<boolean>(false);
  const [selectedId, setSelectedId] = useState<number>(0);
  const [allFormMChecklist, setAllFormMChecklist] = useState(formChecklist);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [collapse, setCollapse] = useState<string[]>([]);
  const [selectedAddId, setSelectedAddId] = useState<number>(0);
  const displayedFormChecklist = isShowingMore
    ? allFormMChecklist
    : allFormMChecklist.slice(0, dataLimit);
  const totalCount = Number(
    documentsData?.checklists_count?.[0]?.number_of_checklist ?? 0
  );
  // const { authorization } = useLoaderData<typeof loader>();
  const { authorization }: GConfig = getGConfig();
  const [checklistUrl, setChecklistUrl] = useState<string>("");
  const [isPOPdfViewOpen, setIsPOPdfViewOpen] = useState<boolean>(false);
  const gConfig: GConfig = getGConfig();
  const [pdfTempId, setPdfTempId] = useState<string>("");
  const [iframeData, setIframeData] = useState<{
    url?: string;
    title: string;
    addUrl?: string;
  }>({ url: "", title: "", addUrl: "" });

  useEffect(() => {
    if (isInitialLoad || !isShowingMore) {
      setAllFormMChecklist(formChecklist);
      setIsInitialLoad(false);
    } else {
      const newFormChecklist = formChecklist.filter(
        (item) =>
          !allFormMChecklist.some((p) => p.checklist_id === item.checklist_id)
      );
      if (newFormChecklist.length > 0) {
        setAllFormMChecklist((prev) => [...prev, ...newFormChecklist]);
      } else {
        setAllFormMChecklist(formChecklist);
      }
    }
  }, [formChecklist, isInitialLoad]);

  useEffect(() => {
    if (formChecklist.length) {
      setCollapse(["1"]);
    }
  }, [formChecklist]);

  useEffect(() => {
    const handleSetChecklistIframeUrl = async () => {
      if (
        authorization &&
        typeof window !== "undefined" &&
        window.ENV.PANEL_URL
      ) {
        let tempAuthorization = authorization;
        const isExpired = isExpiredAuthorization();
        if (isExpired) {
          const response = (await webWorkerApi({
            url: "/api/auth/token",
          })) as IGetTokenFromNode;
          if (response.success) {
            tempAuthorization = response.data.accessToken;
            setAuthorizationExpired(response.data.accessTokenExpired);
          }
        }

        const pathname = "manage_forms.php";
        const newURL = new URL("/" + pathname, window.ENV.PANEL_URL);

        newURL.searchParams.set("iframecall", "1");
        newURL.searchParams.set("authorize_token", tempAuthorization);
        newURL.searchParams.set("from_remix", "1");
        newURL.searchParams.set("web_page", pathname);

        setChecklistUrl(newURL.toString());
        return;
      }
      setChecklistUrl("");
    };
    handleSetChecklistIframeUrl();
  }, [authorization, id]);

  const handleShowMore = () => {
    if (allFormMChecklist.length === totalCount) {
      setIsShowingMore(true);
    } else {
      setIsShowingMore(true);
      fetchAllProjectDocumentsModules(true, ["forms_checklist", "counts"]);
    }
  };

  const handleShowLess = () => {
    setIsShowingMore(false);
  };
  const options: CustomerEmailTab[] = [
    CFConfig.employee_key,
    "my_crew",
    CFConfig.customer_key,
    CFConfig.contractor_key,
    CFConfig.vendor_key,
    CFConfig.misc_contact_key,
    "by_service",
    "my_project",
  ];

  const columnDefs = [
    {
      headerName: "#",
      field: "emp_init_name",
      minWidth: 135,
      maxWidth: 135,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: { data: ChecklistData }) => {
        const emp_init_name = `${data?.emp_init_name}#${data?.company_checklist_id}`;

        return emp_init_name ? (
          <Tooltip title={emp_init_name}>
            <Typography className="table-tooltip-text">
              {emp_init_name}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Form Name"),
      field: "form_name",
      minWidth: 130,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const form_name = HTMLEntities?.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={form_name}>
            <Typography className="table-tooltip-text">{form_name}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "date_added",
      maxWidth: 270,
      minWidth: 270,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ value }: { value: string }) => {
        return value ? <DateTimeCard format="date" date={value} /> : "-";
      },
    },
    {
      headerName: "Status",
      maxWidth: 120,
      minWidth: 120,
      field: "status",
      headerClass: "ag-header-center",
      cellClass: "ag-center-aligned-cell",
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ data }: { data: ChecklistData }) => {
        const status = data.status_name;
        const { color, textColor } = getDefaultStatuscolor(
          data.default_status_color || ""
        );
        return status ? (
          <Tooltip title={status}>
            <div className="text-center overflow-hidden">
              <Tag
                color={color}
                style={{
                  color: `${textColor || ""}`,
                }}
                className={`${
                  textColor === "" && "!text-primary-900"
                } mx-auto text-13 type-badge common-tag max-w-24`}
              >
                {status}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          <Typography className="table-tooltip-text">-</Typography>
        );
      },
    },
    {
      headerName: "",
      field: "checklist_id",
      maxWidth: 70,
      minWidth: 70,
      suppressMenu: true,
      cellRenderer: ({ value }: { value: number }) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={() => {
                setSelectedId(value || 0);
                setIsPOPdfViewOpen(true);
              }}
            />
            <div className="w-6">
              <FormsChecklistsFieldRedirectionIcon
                iconClassName="!w-3.5 !h-3.5"
                formsChecklistsId={value}
              />
            </div>
          </div>
        );
      },
    },
  ];

  const downloadPdf = async () => {};

  const emailApiCall = async () => {};

  const handleSubmitForApproval = async () => {};

  return (
    <>
      <CollapseSingleTable
        title={_t(
          HTMLEntities.decode(
            sanitizeString(formChecklistModule?.plural_name)
          ) || "Forms & Checklists"
        )}
        addButton={_t(
          HTMLEntities.decode(
            sanitizeString(formChecklistModule?.module_name)
          ) || "Forms & Checklists"
        )}
        defaultActiveKey={totalCount ? ["1"] : []}
        activeKey={collapse}
        onChange={setCollapse}
        addButtonDisabled={formChecklistModule?.can_write !== "1"}
        onClickAdd={async () => {
          if (!id) {
            return;
          }
          let tempAuthorization = authorization;
          const isExpired = isExpiredAuthorization();
          if (isExpired) {
            const response = (await webWorkerApi({
              url: "/api/auth/token",
            })) as IGetTokenFromNode;
            if (response.success) {
              tempAuthorization = response.data.accessToken;
              setAuthorizationExpired(response.data.accessTokenExpired);
            }
          }
          const pathname = "manage_forms.php";
          const newURL = new URL("/" + pathname, window.ENV.PANEL_URL);

          newURL.searchParams.set("authorize_token", tempAuthorization);
          newURL.searchParams.set("iframecall", "1");
          newURL.searchParams.set("from_remix", "1");
          newURL.searchParams.set("action", "new");
          newURL.searchParams.set("project", id?.toString());
          setIframeData({
            addUrl: newURL.toString(),
            title: String(id),
          });
        }}
        rightsideContant={
          dataLimit < totalCount && (
            <ButtonWithTooltip
              tooltipTitle={isShowingMore ? _t("Show Less") : _t("Show More")}
              tooltipPlacement="top"
              icon={
                isShowingMore
                  ? "fa-regular fa-arrow-up"
                  : "fa-regular fa-arrow-down"
              }
              className="hover:!bg-primary-900/20"
              onClick={isShowingMore ? handleShowLess : handleShowMore}
            />
          )
        }
        children={
          <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
            <div className="ag-theme-alpine">
              <StaticTable
                className="static-table"
                columnDefs={columnDefs}
                rowData={displayedFormChecklist}
                noRowsOverlayComponent={() => (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-forms-checklists.svg`}
                  />
                )}
              />
            </div>
          </div>
        }
      />
      {isPOPdfViewOpen && selectedId && (
        <PDFFilePreviewWithOP
          projectId={Number(id)}
          isOpen={isPOPdfViewOpen}
          onCloseModal={() => setIsPOPdfViewOpen(false)}
          moduleId={formChecklistModule?.module_id ?? 0}
          op="pdf_checklist"
          idName="checklist_id"
          isLoading={false}
          id={selectedId?.toString() || ""}
          options={options}
          emailSubject={""}
          handleEmailApiCall={emailApiCall}
          handleDownload={downloadPdf}
          isViewAttachment={false}
          moduleName={"Singular MName"}
          // setPdfTempId={setPdfTempId}
          selectedCustomer={[]}
          hideTemplateDropdown={true}
          daynamicOp={false}
          headerButton={{
            text: "Edit",
            onClick: () => {
              window.location.href = `/manage_forms.php?id=${selectedId}`;
            },
            visible: true,
            isLoading: false,
          }}
        />
      )}

      {(iframeData?.url || iframeData?.addUrl) && (
        <IframeModal
          isOpen={iframeData?.url || iframeData?.addUrl ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
          modalBodyClass="p-0"
          header={{
            // icon,
            closeIcon: true,
          }}
          iframeProps={{
            src: !iframeData.url ? iframeData?.addUrl : iframeData?.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            if (key === sendMessageKeys.iframe_change) {
              // reload data
              fetchAllProjectDocumentsModules(false, [
                "forms_checklist",
                "counts",
              ]);
            }
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
        />
      )}
    </>
  );
};

export default FormChecklistTable;
