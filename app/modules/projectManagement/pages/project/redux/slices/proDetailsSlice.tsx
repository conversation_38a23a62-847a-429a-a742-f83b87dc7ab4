// This is the demo
import { createSlice } from "@reduxjs/toolkit";
import {
  fetchProjectActionItemsApi,
  fetchProjectContactsApi,
  fetchProjectDetailsApi,
  fetchProjectFilesApi,
} from "../action/projectDetailsAction";
import { ProjectDetailsField, ProjectfieldStatus } from "../../utils/constants";
import { RootState } from "../store";

const initialState: IProjectDetailsInitialState = {
  details: ProjectDetailsField,
  isDetailLoading: true,
  noDetailsAvailable: false,
  noSummaryDetailsAvailable: true,
  isSummaryDetailsLoading: true,
  summaryFiles: [],
  isSummaryFilesLoading: true,
  isSummaryActionItemsLoading: true,
  isSummaryLoaded: false,
  isActionItemsLoaded: false,
  summaryActionItems: {},
  loadingStatus: ProjectfieldStatus,
  defaultSettingSaved: false,
};

export const projectDetailsSlice = createSlice({
  name: "proDetails",
  initialState,
  reducers: {
    updateProjectDetail: (state, { payload }) => {
      state.details = { ...state.details, ...payload };
    },
    setNoDetailsAvail: (state, { payload }) => {
      state.noDetailsAvailable = payload;
    },
    setLoadingStatus: (state, { payload }: { payload: IFieldStatus }) => {
      state.loadingStatus = state.loadingStatus.map((item) =>
        item.field === payload.field
          ? { ...item, status: payload.status }
          : item
      );
    },
    resetSummarynActionuItemsStates: (state) => {
      state.isSummaryLoaded = false;
      state.isActionItemsLoaded = false;
    },
    setDefaultSettingSaved: (state, { payload }) => {
      state.defaultSettingSaved = payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchProjectDetailsApi.pending, (state, action) => {
      if (!action.meta.arg?.skipLoading) {
        state.isDetailLoading = true;
        state.details = initialState.details;
      }
    });
    builder.addCase(fetchProjectDetailsApi.fulfilled, (state, { payload }) => {
      const { success, data, message } = payload as IprojectDetailsApiRes;
      if (success) {
        state.details = { ...state.details, ...data };
      } else {
        state.noDetailsAvailable = true;
      }
      state.isDetailLoading = false;
    });
    builder.addCase(fetchProjectDetailsApi.rejected, (state, { payload }) => {
      state.noDetailsAvailable = true;
      state.isDetailLoading = false;
    });
    builder.addCase(fetchProjectFilesApi.pending, (state, action) => {
      state.summaryFiles = initialState.summaryFiles;
      state.isSummaryFilesLoading = true;
    });
    builder.addCase(fetchProjectFilesApi.fulfilled, (state, { payload }) => {
      const { success, data, message } = payload as IProjectFilesApiRes;
      if (success) {
        state.summaryFiles = data.aws_files;
        state.isSummaryLoaded = true;
      }
      state.isSummaryFilesLoading = false;
    });
    builder.addCase(fetchProjectFilesApi.rejected, (state, { payload }) => {
      state.noSummaryDetailsAvailable = true;
      state.isSummaryFilesLoading = false;
    });
    builder.addCase(fetchProjectActionItemsApi.pending, (state, action) => {
      state.summaryActionItems = initialState.summaryActionItems;
      state.isSummaryActionItemsLoading = true;
    });
    builder.addCase(
      fetchProjectActionItemsApi.fulfilled,
      (state, { payload }) => {
        const { success, data, message } = payload as IProjectActionItemsApiRes;
        if (success) {
          state.summaryActionItems = data.open_incomplete_items;
          state.isActionItemsLoaded = true;
        }
        state.isSummaryActionItemsLoading = false;
      }
    );
    builder.addCase(
      fetchProjectActionItemsApi.rejected,
      (state, { payload }) => {
        state.noSummaryDetailsAvailable = true;
        state.isSummaryActionItemsLoading = false;
      }
    );
    builder.addCase(fetchProjectContactsApi.pending, (state, action) => {
      state.details = state.details;
    });
    builder.addCase(fetchProjectContactsApi.fulfilled, (state, { payload }) => {
      const { success, data, message } = payload as IprojectDetailsApiRes;
      if (success) {
        state.details = { ...state.details, ...data };
      }
    });
    builder.addCase(
      fetchProjectContactsApi.rejected,
      (state, { payload }) => {}
    );
  },
});

export const {
  updateProjectDetail,
  setNoDetailsAvail,
  setLoadingStatus,
  resetSummarynActionuItemsStates,
  setDefaultSettingSaved,
} = projectDetailsSlice.actions;

export const getUpdatedProjectDetails = (): ((
  dispatch: unknown,
  getState: () => RootState
) => IProjectDetails) => {
  return (_, getState) => {
    const state = getState();
    return state.proDetails.details;
  };
};

export default projectDetailsSlice.reducer;
