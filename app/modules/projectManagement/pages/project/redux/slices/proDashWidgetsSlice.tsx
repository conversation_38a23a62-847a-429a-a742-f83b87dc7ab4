// This is the demo
import { createSlice } from "@reduxjs/toolkit";
import {
  fetchProDashboardApi,
  fetchProDashboardScheduleApi,
} from "../action/proDashAction";
import { fetchProjectType } from "../action/addProjectAction";
import { stateValidation } from "~/modules/people/directory/components/sidebar/utils";

const initialProDashboardStates = {
  projects_by_status: [],
  action_items: { cnt: [], name: [] },
  labor: {
    project_id: [],
    estimated_per: [],
    actual_per: [],
    budgeted_labor: [],
    actual_labor: [],
  },
  unpaid_items: {
    bills_per: [],
    bills_amount: [],
    invoices_per: [],
    invoices_amount: [],
    bills_total: { bill_count: "", total_bills_amount: "" },
    invoices_total: {
      invoices_count: "",
      total_invoices_amount: "",
    },
  },
  projectSchedule: [],

  action_items_last_refresh_time: "",
  labor_last_refresh_time: "",
  project_status_last_refresh_time: "",
  unpaid_items_last_refresh_time: "",
};

const initialDashLoadingStates = {
  project_status: false,
  action_items: false,
  labor: false,
  unpaid_items: false,
};

const initialState: IProDashboardInitialState = {
  dashboardData: initialProDashboardStates,
  dashLoadingKey: initialDashLoadingStates,
  reloadKeys: ["project_status", "action_items", "labor", "unpaid_items"],
  isInitialLoad: true,
  projectTypes: [],
  isLoadingProjectSchedule: true,
  proSearchValue: "",
};

export const proDashWidgetsSlice = createSlice({
  name: "proDashboard",
  initialState,
  reducers: {
    setIsInitialLoad: (state, action) => {
      state.isInitialLoad = action.payload;
    },
    setProSearchValue: (state, { payload }) => {
      state.proSearchValue = payload;
    },
    addCustomProjectType: (state, { payload }) => {
      const data = payload;
      // ! Convert to string because in get api item_type data type is string and add response item_type data type is number
      data.item_type = data.item_type.toString();
      data.item_id = data.item_id.toString();
      state.projectTypes = [data, ...state.projectTypes];
    },
    setIsLoadingProjectSchedule: (state, { payload }) => {
      state.isLoadingProjectSchedule = payload;
    },
    setThisWeekTask: (state, { payload }) => {
      state.dashboardData.projectSchedule = payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchProDashboardApi.pending, (state, action) => {
      const refresh_type = action.meta.arg.refresh_type as TProWidgetKeys;
      if (!!refresh_type) {
        state.reloadKeys = [...state.reloadKeys, refresh_type];
      }
    });
    builder.addCase(
      fetchProDashboardApi.fulfilled,
      (state, { payload, meta }) => {
        const { success, data } = payload as IProDashBoradApiRes;

        if (success) {
          state.dashboardData = { ...state.dashboardData, ...data };
        }

        const refresh_type = meta.arg.refresh_type as TProWidgetKeys;
        if (!!refresh_type) {
          state.reloadKeys = state.reloadKeys.filter((k) => k !== refresh_type);
        } else {
          state.reloadKeys = [];
        }
      }
    );
    builder.addCase(fetchProDashboardApi.rejected, (state, action) => {
      const refresh_type = action.meta.arg.refresh_type as TProWidgetKeys;
      if (!!refresh_type) {
        state.reloadKeys = state.reloadKeys.filter((k) => k !== refresh_type);
      } else {
        state.reloadKeys = [];
      }
    });
    builder.addCase(fetchProjectType.pending, (state, action) => {
      state.projectTypes = [];
    });
    builder.addCase(fetchProjectType.fulfilled, (state, { payload, meta }) => {
      const { success, data } = payload as IProjectTypeListApiRes;
      state.projectTypes = data;
    });
    builder.addCase(fetchProjectType.rejected, (state) => {
      state.projectTypes = [];
    });
    builder.addCase(fetchProDashboardScheduleApi.pending, (state, action) => {
      state.dashboardData.projectSchedule = [];
      state.isLoadingProjectSchedule = true;
    });
    builder.addCase(
      fetchProDashboardScheduleApi.fulfilled,
      (state, { payload, meta }) => {
        const { success, data } = payload as IProjectDashScheduleApiRes;
        state.dashboardData.projectSchedule = data;
        state.isLoadingProjectSchedule = false;
      }
    );
    builder.addCase(fetchProDashboardScheduleApi.rejected, (state) => {
      state.dashboardData.projectSchedule = [];
      state.isLoadingProjectSchedule = true;
    });
  },
});

export const {
  setIsInitialLoad,
  addCustomProjectType,
  setIsLoadingProjectSchedule,
  setThisWeekTask,
  setProSearchValue,
} = proDashWidgetsSlice.actions;
export default proDashWidgetsSlice.reducer;
