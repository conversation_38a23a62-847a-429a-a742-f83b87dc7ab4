interface IProjectTempFil {
  project_status?: string;
  project_status_kanban?: string;
  project_type?: string;
  start_date?: string;
  end_date?: string;
  status?: string;
  customer?: string;
  customer_contact_id?: string;
  project_contacts?: string;
  record_type?: string;
  customer_names?: string;
  project_contacts_names?: string;
  project_status_names?: string;
  project_type_names?: string;
  project_manager?: string;
  project_manager_names?: string;
  sales_rep?: string | number;
  sales_rep_names?: string;
  tab?: string;
}

interface IProjectListFilter {
  project_status?: string;
  project_status_kanban?: string;
  project_type?: string;
  start_date?: string;
  end_date?: string;
  status?: string;
  customer?: string;
  project_contacts?: string;
  record_type?: string;
  customer_names?: string;
  project_contacts_names?: string;
  project_status_names?: string;
  project_type_names?: string;
  project_manager?: string;
  project_manager_names?: string;
  sales_rep?: string | number;
  sales_rep_names?: string;
  tab?: string;
}

interface IProjectListParmas {
  start?: number;
  limit?: number;
  get_projects_id?: number;
  is_kanban?: boolean;
  is_completed?: boolean;
  search?: string;
  filter?: IProjectListFilter;
  order_by_name?: string;
  order_by_dir?: string;
  page: number;
}
interface IProjectFileAndPhotoViewSlice {
  currentView: "folder" | "timeline";
  folderView: {
    viewAllFiles: boolean;
    fileStructure: "thumbnail" | "detail";
  };
  timelineView: {
    fileStructure: "thumbnail" | "detail";
  };
}
interface GetCustomDataTypesParams {
  moduleId: number;
  types: Array<number>;
}
interface GenerateAutoNumber {
  module_id: number;
  module_key: string;
}

interface IProjectKanbanListParmas {
  page?: number;
  limit?: number;
  record_type?: string;
  filter?: IProjectListFilter;
  any_status?: string;
  search?: string;
  ignore_filter?: number;
}

interface IProjectData {
  id: number;
  company_id: number;
  project_id: string;
  customer_id: number;
  project_name: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zip: string;
  location_map: string | number;
  description: string;
  project_type: string;
  project_status: string;
  project_status_key: string;
  site_manager_id: number;
  safety_contact_id: number;
  notes: string;
  image: string | number;
  start_date: string;
  end_date: string;
  prepared_by: number;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  parent_project_id: string | number;
  demo_data: number;
  completion_date: string;
  warranty_start_date: string | number;
  contract_amount: string;
  budget_amount: string | number;
  create_file_folder: number;
  view_in_schedule: number;
  view_in_calendar: number;
  view_in_geofence: number;
  progress: string;
  assigned_to: string;
  task_user_id: string;
  permit_details: string | number;
  retention: string;
  project_type_key: string;
  notice_to_proceed: string | number;
  latitude: string;
  longitude: string;
  billed_to: number;
  qb_date: string;
  quickbook_project_id: number;
  qbc_id: string;
  is_updated: number;
  view_in_timecard: number;
  add_wo_total_to_revised_contract_amount: number;
  customer_contact_id: number;
  wbs: string | number;
  budget_is: string;
  original_retention: string;
  project_color: string;
  geo_image: string;
  geo_radius: string;
  type: string;
  referral_source: string | number;
  referral_source_key: string | number;
  stage: string | number;
  stage_key: string | number;
  score: string | number;
  est_sales_date: string | number;
  company_project_id: string;
  project_prefix: string;
  is_notes_convert: number;
  allow_overbilling: number;
  billing_option: string;
  bid_due_date: string | number;
  budget_row_from: string;
  show_client_access: number;
  client_access_html: string;
  project_manager_id: number;
  show_client_phone: number;
  show_client_email: number;
  site_manager_contact_id: number;
  safety_additional_contact_id: number | string;
  client_manager_id: number;
  billed_to_contact: number;
  client_cover_image: string;
  duration: number;
  customer_contract: string;
  client_dashboard_message: string;
  enable_client_access: number;
  associated_estimate_id: number;
  default_tax_rate_id: number;
  allow_online_payment: number;
  companycam_project_id: number;
  companycam_project_updated: number;
  companycam_label_id: number;
  companycam_updated_at: string;
  wip_progress: number;
  wip_notes: string;
  companycam_creator_name: string;
  budgeted: string;
  budgeted_labor: string;
  original_contract_amount: string;
  original_budget_amount: string;
  revised_contract_amount: string;
  invoiced: string;
  gross_profit_total: string;
  revised_contract_amount_w_o_tax: string;
  commited: string;
  actual: string;
  actual_labor: string;
  tax_id: number;
  total: string;
  revenue: string;
  cost: string;
  sales_rep: number;
  estimator: number;
  no_tax_contract_amount: string;
  save_as_template: number;
  project_template_id: number;
  project_default_material_markup_percent: string;
  project_default_equipment_markup_percent: string;
  project_default_labor_markup_percent: string;
  project_default_sub_contractor_markup_percent: string;
  project_default_other_item_markup_percent: string;
  project_default_undefined_markup_percent: string;
  project_enable_labor_markup_wage_rate: number;
  project_enable_labor_markup_billing_rate: number;
  project_enable_labor_markup_burden_rate: number;
  is_converted: number;
  origin: number;
  markup_preference: string;
  need_adjust_invoice: number;
  project_type_name: string;
  project_status_name: string;
  status_name: string;
  billed_to_name: string | number;
  customer_name: string;
  customer_company: string;
  cust_type: number;
  customer_name_only: string;
  cust_image: string;
  contract_amount_held: string;
  site_manager_name: string | number;
  safety_contact_name: string | number;
  email_subject: string;
  is_assigned_project: number;
  sov_items: string;
  time_added: string;
  time_modified: string;
  qb_date_added: string;
  qb_time_added: string;
  kanban_date_modified: string;
  date_added_sorting: string;
  start_date_sorting: string;
  end_date_sorting: string;
  added_by_name: string;
  is_available: string;
  unread_msg_tbl1: string;
  unread_msg_tbl2: string;
  stage_name: string | number;
  assignee_name: string | number;
  assignee_id: number;
  project_manager_name: string;
  enable_procurement_tab: number;
  approved_estimates: string;
  paid_amount: string;
  profile_image: string;
}

interface ProjectListData {
  module_id: number;
  global_project: {
    project_id: number;
    project_name: string;
    view_in_timecard: string;
    project_selection: string;
  };
  project_status: string;
  project_file_sortorder: string;
  sso: string;
  module: {
    module_id: number;
    module_key: string;
    module_name: string;
    web_page: string;
    can_write: number;
    can_read: number;
    has_student_permission: string;
    access_to_all_data: string;
    allow_view_all_activities: string;
    prompt_generate_daily_log: string;
    hide_tab_in_directory_popup: string;
    allow_as_project_manager: string;
  };
  assigned_projects: string | number;
  kanban_project_selected: string[];
  kanban_setting: [];
  data: IProjectData[];
  dashboard_shortcuts: string;
  company_number_format: string;
}

interface IProjectListApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ProjectListData;
}

interface IProjectType {
  item_id: string;
  is_custom_item: string;
  name: string;
  item_type: string;
  qb_full_name: string;
  qb_account_type: string;
  qb_account_sub_type: string;
  quickbook_category_id: string;
  date_added: string;
  date_modified: string;
  status_name: string;
  is_deleted: string;
  status_color: string;
  sort_order: string;
  key: string;
  does_sync_qb: number;
  orig_name: string;
  is_status: number;
  sorting_id: number;
  type: string;
  show_in_progress_bar: number;
  module_id?: string | number;
  value: number | string;
}

interface IProjectTypeListApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IProjectType[];
}

interface IProjectTemplate {
  id: number;
  project_name: string;
  project_id: string;
  save_as_template: number;
  project_status_key: string;
}

interface IProjectTemplatesListApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IProjectTemplate[];
}

interface IProjectTableCellRenderer {
  data: Partial<IProjectData>;
}

interface IProjectFilterProps {
  kanbanView?: boolean;
  beforeFilterUpdateCallback?: () => void;
}

interface IProjectStatus {
  label: string;
  value: string;
}

interface IProjectListTableDropdownItemsProps {
  data:
    | Partial<IProjectData>
    | Partial<IPorjectKanbanData>
    | Partial<IProjectDetails>
    | null;
  refreshTable: Function;
  className?: string;
  iconClassName?: string;
  contentClassName?: string;
  icon?: IFontAwesomeIconProps["icon"];
  isDetailView?: boolean;
  tooltipcontent?: string;
}

interface IProjectDashApiParams {
  refresh_type?: TProWidgetKeys | string;
  is_refresh: number;
}

// need to change after RIGHT api response
type TResProjectStatus = {
  sort_order: string;
  status_color: string;
  item_id: number;
  key: string;
  column_id: string | number;
  is_custom_item: string;
  name: string;
  orig_name: string;
  item_type: string;
  qb_full_name: string;
  qb_account_type: string;
  qb_account_sub_type: string;
  quickbook_category_id: string;
  status_name: string;
  is_deleted: string;
  is_status: string;
  does_sync_qb: string;
  sorting_id: number;
  show_in_progress_bar: string;
};

interface IResProjectStatesData {
  project_status_name: string;
  status_color: string;
  total: string;
  total_record: string;
  sort_order: string;
  is_status: number;
  p_key: string;
  per: string;
  status_color_code: string;
}

interface IResActionItems {
  cnt: string[];
  name: string[];
}
interface IResLabor {
  project_id: string[];
  estimated_per: number[];
  actual_per: number[];
  budgeted_labor: number[];
  actual_labor: number[];
}

type TBillCount = {
  bill_count: string;
  total_bills_amount: string;
};

type TInvoiceTotal = {
  invoices_count: string;
  total_invoices_amount: string;
};
interface IResUnpaidItems {
  bills_per: number[];
  bills_amount: number[];
  invoices_per: number[];
  invoices_amount: number[];
  bills_total: TBillCount;
  invoices_total: TInvoiceTotal;
}

interface IDashboardProjectByStatus {
  sort_order: string;
  status_color: string;
  key: string;
  column_id: string;
  is_custom_item: string;
  name: string;
  orig_name: string;
  item_type: string;
  qb_full_name: string;
  qb_account_type: string;
  qb_account_sub_type: string;
  quickbook_category_id: string;
  status_name: string;
  is_deleted: string;
  is_status: string;
  does_sync_qb: string;
  sorting_id: number;
  show_in_progress_bar: string;
}

interface IProDashBoradData {
  projects_by_status: IResProjectStatesData[];
  action_items: IResActionItems;
  labor: IResLabor;
  unpaid_items: IResUnpaidItems;
  projectSchedule: IProjectDashScheduleData[];
  action_items_last_refresh_time: string;
  labor_last_refresh_time: string;
  unpaid_items_last_refresh_time: string;
  project_status_last_refresh_time: string;
  projects_status?: { [key: string]: IDashboardProjectByStatus };
}

interface IProDashBoradApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IProDashBoradData;
}

type TProWidgetKeys =
  | "project_status"
  | "action_items"
  | "labor"
  | "unpaid_items";

type TDashLoadingKey = {
  [key in TProWidgetKeys]: boolean;
};

interface IProDashboardInitialState {
  dashboardData: IProDashBoradData;
  dashLoadingKey: TDashLoadingKey;
  reloadKeys: Array<Partial<TProWidgetKeys>>;
  isInitialLoad: boolean;
  projectTypes: IProjectType[];
  isLoadingProjectSchedule: boolean;
  proSearchValue: string;
}

interface IProFinancialInitialState {
  financialData: {
    bills: IProjectBillsData[];
    bills_count: IProjectBillsCountData[];
    change_orders: IProjectChangeOrdersData[];
    change_orders_count: IProjectChangeOrdersCountData[];
    estimates: IProjectEstimatesData[];
    estimates_count: IProjectEstimatesCountData[];
    expenses: IProjectExpensesData[];
    expenses_count: IProjectExpensesCountData[];
    invoices: IProjectInvoicesData[];
    invoices_count: IProjectInvoicesCountData[];
    payments: IProjectPaymentsData[];
    payments_count: IProjectPaymentsCountData[];
    purchase_orders: IProjectPurchaseOrdersData[];
    purchase_orders_count: IProjectPurchaseOrdersCountData[];
    sub_contracts: IProjectSubContractsData[];
    sub_contracts_count: IProjectSubContractsCountData[];
    work_orders: IProjectWorkOrdersData[];
    work_orders_count: IProjectWorkOrdersCountData[];
  };
  isInitialLoad?: boolean;
  projectTypes: IProjectTypes[];
  isloading?: boolean;
  isfirsttime?: boolean;
}

interface IProDocumentInitialState {
  documentsData: {
    daily_logs: IProjectDailyLogsData[];
    daily_logs_count: IProjectDailyLogsCountData[];
    document_writer: IProjectDocumentWriterData[];
    document_writers_count: IProjectDocumentWriterCountData[];
    checklists: IProjectChecklistsData[];
    checklists_count: IProjectChecklistsCountData[];
    incidents: IProjectIncidentsData[];
    incidents_count: IProjectIncidentsCountData[];
    inspections: IProjectInspectionsData[];
    inspections_count: IProjectInspectionsCountData[];
    notes: IProjectNotesData[];
    notes_count: IProjectNotesCountData[];
    permits: IProjectPermitsData[];
    permits_count: IProjectPermitsCountData[];
    punchlists: IProjectPunchListsData[];
    punchlists_count: IProjectPunchListsCountData[];
    correspondences: IProjectCorrespondencesData[];
    correspondences_count: IProjectCorrespondencesCountData[];
    safety_meeting: IProjectSafetyMeetingData[];
    safety_meetings_count: IProjectSafetyMeetingCountData[];
    service_ticket: IProjectServiceTicketData[];
    service_tickets_count: IProjectServiceTicketCountData[];
    submittals: IProjectSubmittalsData[];
    submittals_count: IProjectSubmittalsCountData[];
    todos: IProjectTodosData[];
    todos_count: IProjectTodosCountData[];
  };
  isInitialLoad?: boolean;
  projectTypes: IProjectTypes[];
}

interface IProTimeInitialState {
  timeData: {
    time_cards: IProjectTimeCardTableData[];
    timecards_count: IProjectTimeCardTableCountData[];
    time_card_prev_week_status: IProjectTimeCardPrevWeekStatusData[];
    time_card_week_status: IProjectTimeCardWeekStatusData[];
  };
  timecardData: IProjectTimeCardTableData[];
  isTimeInitialLoad?: boolean;
  projectTypes: IProjectTypes[];
}

interface IProReportInitialState {
  reportData: {
    job_cost: IProjectJobCostData[];
    job_cost_actual: IProjectJobCostActualData[];
    job_cost_detail: IProjectJobCostDetailsData[];
    labor_budget: IProjectLaborBudgetData[];
  };
  isReportInitialLoad?: boolean;
  projectTypes: IProjectTypes[];
}

interface IProjectJobCostData {
  commited_total: string;
  cost_code: string;
  cost_code_id: string;
  cost_code_name: string;
  count_in_contract_amount: string;
  is_markup_percentage: string;
  item_total: string;
  item_type: string;
  item_type_name: string;
  markup: string;
  markup_total: string;
  module_company_id: string;
  module_id: string;
  module_name: string;
  module_primary_id: string;
  module_web_page: string;
  parent_cost_code: string;
  parent_cost_code_name: string;
  quantity: string;
  record_date: string;
  subject: string;
  timecard_hours: string;
  total_to_show: string;
  type: string;
  unit_cost: string;
}

interface IProjectJobCostActualData {
  bill_total: number;
  budget_cost: number;
  commited_total: number;
  contract_minus_cost: number;
  contract_value: number;
  cost_budget_minus_cost: number;
  cost_code: string;
  cost_code_id: string;
  cost_code_name: string;
  count_in_contract_amount: string;
  equip_labor_total: number;
  expense_total: number;
  invoice_total: number;
  invoiced_minus_cost: number;
  is_markup_percentage: string;
  item_total: number;
  item_type: number;
  item_type_name: string;
  markup: string;
  markup_total: number;
  module_company_id: string;
  module_id: string;
  module_name: string;
  module_primary_id: number;
  module_web_page: string;
  parent_cost_code: string;
  parent_cost_code_name: string;
  quantity: number;
  record_date: string;
  subject: string;
  timecard_hours: string;
  timecard_total: number;
  total_actual_cost: number;
  total_to_show: number;
  type: string;
  unit_cost: string;
}

interface IProjectJobCostDetailsData {
  commited_total: number;
  cost_code: string;
  cost_code_id: string;
  cost_code_name: string;
  count_in_contract_amount: string;
  is_markup_percentage: string;
  item_total: number;
  item_type: number;
  item_type_name: string;
  markup: string;
  markup_total: number;
  module_company_id: string;
  module_id: string;
  module_name: string;
  module_primary_id: number;
  module_web_page: string;
  parent_cost_code: string;
  parent_cost_code_name: string;
  quantity: number;
  record_date: string;
  subject: string;
  timecard_hours: string;
  total_to_show: number;
  type: string;
  unit_cost: string;
}

interface IProjectLaborBudgetData {
  commited_total: number;
  cost_code: string;
  cost_code_id: string;
  cost_code_name: string;
  count_in_contract_amount: string;
  is_markup_percentage: string;
  item_total: number;
  item_type: number;
  item_type_name: string;
  labor_markup_total: number;
  markup: string;
  markup_total: number;
  module_company_id: string;
  module_id: string;
  module_name: string;
  module_primary_id: number;
  module_web_page: string;
  parent_cost_code: string;
  parent_cost_code_name: string;
  quantity: number;
  record_date: string;
  subject: string;
  timecard_hours: string;
  total_to_show: number;
  type: string;
  unit_cost: string;
}
interface IProjectProcurementInitialState {
  procurementData: IProcurement[];
  procurementGenerateData: IProcurementGenerate | {};
  isProcurementInitialLoad?: boolean;
  projectTypes: IProjectTypes[];
}

interface IProcurement {
  apply_global_tax: string;
  assigned_to: string;
  assigned_to_company_name: string;
  assigned_to_contact_id: string;
  assigned_to_name_only: string;
  assignee_name: string;
  assignee_type: string;
  bidder_item_id: string;
  company_id: string;
  cost_code: string;
  cost_code_id: string;
  cost_code_name: string;
  date_added: string;
  date_modified: string;
  description: string;
  directory_id: string;
  equipment_id: string;
  estimate_id: string;
  estimate_item_no: string;
  hidden_markup: string;
  internal_notes: string;
  is_deleted: string;
  is_markup_percentage: string;
  is_optional_item: string;
  is_project_template: string;
  is_temaplate: string;
  item_id: string;
  item_on_database: string;
  item_type: string;
  item_type_display_name: string;
  item_type_name: string;
  markup: string;
  markup_amount: string;
  material_id: string;
  one_build_id: string;
  origin: string;
  parent_item_id: string;
  po_ref_id: string;
  project_template_id: string;
  qbc_id: string;
  quantity: string;
  quickbook_estimateitem_id: string;
  quickbook_item_id: string;
  ref_id: string;
  reference_item_id: string;
  sc_ref_id: string;
  section_id: string;
  selected_reference_module_id: string;
  subject: string;
  tax_id: string;
  total: string;
  unit: string;
  unit_cost: string;
  variation_id: string;
}

interface IProcurementGenerate {
  assignee_id: string;
  company_id: string;
  date_added: string;
  date_modified: string;
  estimate_id: string;
  module_key: string;
  module_name_with_id: string;
  project_id: string;
  purchase_order_id: string;
  ref_id: string;
  ref_record_id: string;
  ref_web_page: string;
  sub_contract_id: string;
  user_id: string;
}

interface IGetTimeCostCodeList {
  codeCostTimeData: ITimeCostCode[];
  codeCostLoading: boolean;
}

interface IGetTimeCostCodeRes extends Omit<IDefaultAPIRes, "data"> {
  data: ITimeCostCode[];
}

interface ITimeCostCode {
  code_id: string;
  original_code_id: string;
  csi_code: string;
  parent_id: string;
  csi_name: string;
  parent_code_id: string;
  cost_code_name: string;
  is_deleted: string;
  is_managed_level: string;
  has_no_child: string;
}

interface IProjectTimeCardTableData {
  cost_code_name: string;
  detail_id: string;
  employee: string;
  entry_type: string;
  hours_worked: string;
  ot_hours: string;
  overtime_code: string;
  tc_date: string;
  timecard_date: string;
  timecard_id: string;
  total_wage: string;
  type: string;
  wage: string;
  work_mins: string;
  length: number;
}

interface IProjectTimeCardTableCountData {
  number_of_timecard: string;
  total_work_mins: string;
}

interface IProjectTimeCardPrevWeekStatusData {
  regular_hours: string;
  regular_second: string;
  ot_hours: string;
  ot_second: string;
  timecard_date: string;
  day_of_week: string;
}

interface IProjectTimeCardWeekStatusData {
  regular_hours: string;
  regular_second: string;
  ot_hours: string;
  ot_second: string;
  timecard_date: string;
  day_of_week: string;
}

interface IProjectDailyLogsData {
  arrival_date: string;
  arrival_time: string;
  generated_by: string;
  log_id: string;
  project_id: string;
  task_performed: string;
  user_avtar: string;
  weather_notes: string;
}

interface IProjectDailyLogsCountData {
  number_of_daily_log: string;
}

interface IProjectDocumentWriterData {
  document_name: string;
  module_name: string;
  date_added: string;
  status: string;
  document_id: string;
}

interface IProjectDocumentWriterCountData {
  number_of_document_writer: string;
}

interface IProjectChecklistsData {
  emp_init_name: string;
  form_name: string;
  date_added: string;
  status: string;
  checklist_id: string;
}

interface IProjectChecklistsCountData {
  number_of_checklist: string;
}

interface IProjectIncidentsData {
  company_incident_id: string;
  description: string;
  type_name: string;
  incident_type_name: string;
  incident_date: string;
  notified_date: string;
  incident_id: string;
}

interface IProjectIncidentsCountData {
  number_of_incidents: string;
}

interface IProjectInspectionsData {
  inspection_id: string;
  inspection_type: string;
  inspection_status_name: string;
  inspection_date: string;
  inspection_time: string;
  status_color: string;
  inspection_key: string;
}

interface IProjectInspectionsCountData {
  number_of_inspection: string;
}

interface IProjectNotesData {
  subject: string;
  created_by: string;
  date: string;
  status: string;
  note_id: string;
}

interface IProjectNotesCountData {
  number_of_note: string;
}

interface IProjectPermitsData {
  permit_id: string;
  project_id: string;
  permission: string;
  permit_type_key: string;
  permit_type: string;
  permit_date: string;
  approval_date: string;
  expire_date: string;
  pulled_by_date: string;
  permit_fees: string;
  agency: string;
  inspection_id: string;
  is_deleted: string;
  user_id: string;
  company_id: string;
  date_added: string;
  date_modified: string;
  notes: string;
  is_project_template: string;
  project_template_id: string;
  parent_permit_id: string;
  demo_data: string;
  is_notes_convert: string;
  agency_contact_id: string;
  agency_name: string;
  permit_type_name: string;
  company_inspection_id: string;
}

interface IProjectPermitsCountData {
  number_of_permit: string;
}

interface IProjectPunchListsData {
  punchlist_name: string;
  item_count: string;
  item_completed: string;
  item_incompleted: string;
  punchlist_id: string;
}

interface IProjectPunchListsCountData {
  number_of_punchlist: string;
}

interface IProjectCorrespondencesData {
  correspondence_id: string;
  type_name: string;
  type_key: string;
  type: string;
  correspondence_date: string;
  status_name: string;
  correspondence_key: string;
}

interface IProjectCorrespondencesCountData {
  number_of_correspondence: string;
}

interface IProjectSafetyMeetingData {
  subject: string;
  type_name: string;
  item_date: string;
  meeting_id: string;
}

interface IProjectSafetyMeetingCountData {
  number_of_safety_meeting: string;
}

interface IProjectServiceTicketData {
  service_ticket_id: string;
  title: string;
  job_status: string;
  st_status: string;
  service_date: string;
  service_time: string;
  total: string;
  service_ticket_status_type: string;
  status_color: string;
  count_in_contract_amount: string;
}

interface IProjectServiceTicketCountData {
  number_of_service_ticket: string;
}

interface IProjectSubmittalsData {
  submittal_id: string;
  title: string;
  date_received: string;
  due_date: string;
  date_added: string;
  company_submittal_id: string;
  prefix_company_submittal_id: string;
  status_name: string;
  submittal_status_key: string;
  status_color: string;
}

interface IProjectSubmittalsCountData {
  number_of_submittal: string;
}

interface IProjectTodosData {
  task_name: string;
  priority_name: string;
  todo_date: string;
  due_date: string;
  status_name: string;
  todo_id: string;
}

interface IProjectTodosCountData {
  number_of_todo: string;
}

interface IProjectTypeListParmas {
  types?: number[];
  module_id?: (number | string | null)[];
}

interface IPorjectKanbanData {
  id: number;
  project_name: string;
  project_id: string;
  customer_id: number;
  is_deleted: number;
  stage: number;
  stage_key: string;
  project_template_id: number;
  project_status_key?: string;
  assignee_id: number;
  project_status_name: string;
  project_status: string;
  status_name: string;
  customer_name: string;
  start_date: string;
  completion_date: string;
  bid_due_date: string;
  est_sales_date: string;
  date_added: string;
  date_modified: string;
  kanban_date_modified: string;
  enable_procurement_tab: number;
  approved_estimates: string;
  total_count: string;
}

interface IProjectKanbanViewListData {
  sort_order: string;
  status_color: string;
  item_id: number;
  key: string;
  column_id: string;
  is_custom_item: string;
  name: string;
  orig_name: string;
  item_type: string;
  qb_full_name: string;
  qb_account_type: string;
  qb_account_sub_type: string;
  quickbook_category_id: string;
  status_name: string;
  is_deleted: string;
  is_status: string;
  does_sync_qb: string;
  sorting_id: number;
  show_in_progress_bar: string;
  is_collapse_card: number;
  total_count: string;
  projects: IPorjectKanbanData[];
}

interface IProjectKanbanListData {
  status_data: {
    sort_order: string;
    status_color: string;
    item_id: number;
    key: string;
    column_id: string;
    is_custom_item: string;
    name: string;
    orig_name: string;
    item_type: string;
    qb_full_name: string;
    qb_account_type: string;
    qb_account_sub_type: string;
    quickbook_category_id: string;
    status_name: string;
    is_deleted: string;
    is_status: string;
    does_sync_qb: string;
    sorting_id: number;
    show_in_progress_bar: string;
    is_collapse_card: number;
    total_count: string;
    projects: IPorjectKanbanData[];
  };
}

interface IProjectKanbanSettings {
  kanban_id: number;
  company_id: number;
  user_id: number;
  module_id: number;
  module_field_id: string;
  default_view: number;
  co_type: string;
  date_modified: string;
  default_view_back: number;
}

interface IProjectKanbanListApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    kanban_data: IProjectKanbanListData[];
    kanban_project_selected: string[];
    kanban_setting: IprojectKanbanSettings;
  };
}

interface IProjectDashScheduleParmas {
  request_for?: string;
  start_date_range?: string;
  project?: number;
  is_refresh?: number;
}

interface IProjectDashScheduleApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IProjectDashScheduleData[];
  projectDashboardLastRefreshTime: string;
}

interface IProjectFinancialApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IProjectFinancialData[];
}

interface IProjectDocumentApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IProDocumentInitialState["documentsData"];
}

interface IProjectTimeApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IProjectTimeData[];
}

interface IProjectDashScheduleData {
  id: number;
  title: string;
  text: string;
  wbs: string;
  progress: string;
  duration: number;
  start_date: string;
  end_date: string;
  start_date_only: string;
  end_date_only: string;
  start_time_only: string;
  end_time_only: string;
  assignee_name: string;
  textColor: string;
  type: string;
  color: string;
  days_span: string;
  rec_type: string;
  event_pid: string;
  event_length: string;
  full_day: string;
  company_id: number;
  user_id: number;
  event_id: string;
  event_days: string;
  etag: string;
  display_name: string;
  description: string;
  project_id: number;
  rec_pattern: string;
  assigned_to: string;
  module_id: string;
  project_name: string;
}

interface IProjectEventsListParmas {
  filter?: IProjectListFilter;
  ignore_filter?: number;
}

interface IprojectEventsListData {
  id: number;
  full_day_event: string;
  start_date: string;
  end_date: string;
  start_date_only: string;
  end_date_only: string;
  project_status_name: string;
  project_status: string;
  lat?: number;
  lng?: number;
  event_location: string;
  customer_name: string;
  project_name: string;
  street1: string;
  street2: string;
  state_zip: string;
  city: string;
  project_address1: string;
  project_address2: string;
  project_city: string;
  project_state: string;
  project_zip: string;
  location: string;
}

interface IProjectEventsListApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    events_data: IprojectEventsListData[];
  };
}

interface ICompanyAddressData {
  latitude: number;
  longitude: number;
  city: string;
  street: string;
  state: string;
  zip_code: string;
}

interface ICompanyAddressApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ICompanyAddressData;
}

interface IProjectUpdateApiParams {
  id?: string | number;
  project_id?: string;
  stage?: string;
  module_key?: string;
  project_name?: string;
  customer_id?: number | string;
  latitude?: number | string;
  longitude?: number | string;
  project_type?: string;
  company_id?: number | string;
  notes?: string;
  description?: string;
  budget_row_from?: string;
  allow_overbilling?: number | string;
  billing_option?: string;
  customer_contact_id?: number | string;
  project_color?: string;
  customer_contract?: string;
  site_manager_id?: number | string;
  site_manager_contact_id?: number | string;
  project_manager_id?: number | string;
  safety_contact_id?: number | string;
  safety_additional_contact_id?: number | string;
  project_status?: string;
  contract_amount?: number | string;
  budget_amount?: number | string;
  retention?: number | string;
  view_in_schedule?: number | string;
  view_in_calendar?: number | string;
  allow_online_payment?: number | string;
  billed_to?: number | string;
  billed_to_contact?: number | string;
  view_in_timecard?: number | string;
  add_wo_total_to_revised_contract_amount?: number | string;
  budget_is?: string;
  default_tax_rate_id?: number | string;
  view_in_geofence?: number | string;
  geo_radius?: string;
  geo_image?: string;
  client_cover_image?: string;
  client_dashboard_message?: string;
  notice_to_proceed?: string | null;
  start_date?: string;
  end_date?: string;
  completion_date?: string | null;
  warranty_start_date?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  zip?: string;
  show_client_access?: number | string;
  client_access_html?: string;
  show_client_phone?: number | string;
  show_client_email?: number | string;
  wip_progress?: number | string;
  wip_notes?: string;
  sales_rep?: number | string;
  prepared_by?: number | string;
  estimator?: number | string;
  tax_id?: number | string;
  user_id?: number | string;
  from?: string;
  force_login?: number | string;
  markup_preference?: string;
  save_as_template?: number | string;
  project_default_material_markup_percent?: string;
  project_default_equipment_markup_percent?: string;
  project_default_labor_markup_percent?: string;
  project_default_sub_contractor_markup_percent?: string;
  project_default_other_item_markup_percent?: string;
  project_default_undefined_markup_percent?: string;
  project_enable_labor_markup_wage_rate?: number | string;
  project_enable_labor_markup_billing_rate?: number | string;
  project_enable_labor_markup_burden_rate?: number | string;
  reference_company_id?: number | string;
}

interface IProjectAddressApi {
  id?: string | number;
}

interface IProjectAddressApiData {
  id: number;
  company_id: number;
  project_id: string;
  project_name: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zip: string;
  latitude: string;
  longitude: string;
  view_in_timecard: number;
  cust_name: string;
  cust_phone: string;
  cust_cell: string;
  cust_email: string;
  cust_address1: string;
  cust_address2: string;
  cust_city: string;
  cust_state: string;
  cust_zip: string;
  cust_company_name: string;
}

interface IProjectAddressApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IProjectAddressApiData;
}

interface IProjectDetailsApiParams {
  project_id: number;
  record_type: string;
  skipLoading?: boolean;
}

interface IProjectContactData {
  user_id: number;
  directory_id: number;
  full_name: string;
  title: string;
  company_name: string;
  access_role: string;
  phone: string;
  cell: string;
  email: string;
  type: number;
  type_name: string;
  services: string;
  service_names: string;
  image: string;
}

interface IProjectAdditionalContactData {
  contact_id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  phone: string;
  cell: string;
  email: string;
  title: string;
  notes: string;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  company_id: number;
  parent_contact_id: number;
  phone_ext: string;
  company_name: string;
  image: string;
}

interface IProjectDetails {
  id: number | string;
  company_id: number | string;
  project_id: string;
  customer_id: number | string;
  project_name: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zip: string;
  latitude: string;
  longitude: string;
  geo_radius: string;
  location_map: string;
  description: string;
  project_type: string;
  project_status_key: string;
  prj_tax_name?: string;
  prj_tax_rate?: string;
  customer_company?: string;
  original_contract_amount?: string | number;
  original_budget_amount?: string | number;
  view_in_timecard?: number | string;
  view_in_geofence?: number | string;
  save_as_template?: number | string;
  allow_online_payment?: number | string;
  project_color?: string;
  project_default_material_markup_percent?: string;
  project_default_equipment_markup_percent?: string;
  project_default_labor_markup_percent?: string;
  project_default_sub_contractor_markup_percent?: string;
  project_default_other_item_markup_percent?: string;
  project_default_undefined_markup_percent?: string;
  project_enable_labor_markup_wage_rate?: number | string;
  project_enable_labor_markup_billing_rate?: number | string;
  project_enable_labor_markup_burden_rate?: number | string;
  progress?: string;
  project_type_name?: string;
  project_status_name?: string;
  project_status?: string;
  prj_type_name?: string;
  status_name?: string;
  customer_name?: string;
  cust_address1?: string;
  cust_address2?: string;
  cust_city?: string;
  cust_state?: string;
  cust_zip?: string;
  cust_type?: number | string;
  cust_phone?: string;
  cust_cell?: string;
  cust_title?: string;
  cust_image?: string;
  access_code?: string;
  customer_billed_to?: number | string;
  customer_type_key?: number | string;
  customer_type_name?: string;
  customer_name_only?: string;
  customer_contact_phone?: string;
  customer_contact_cell?: string;
  customer_contact_title?: string;
  cust_email?: string;
  customer_fax?: string;
  start_date: string;
  end_date: string;
  warranty_start_date: string;
  date_added: string;
  date_modified: string;
  time_added: string;
  time_modified: string;
  qb_date_added?: string;
  qb_time_added?: string;
  assignee_name?: string;
  assignee_image?: string;
  assignee_name_only?: string;
  assignee_company_name?: string;
  project_manager_name?: string;
  project_manager_name_only?: string;
  profile_image?: string;
  default_tax_name?: string;
  default_tax_rate?: string;
  sales_rep_company_name?: string;
  is_deleted: string | number;
  contract_amount: number | string;
  completion_date: string | null;
  sov_items?: string;
  approved_estimate_total?: string;
  approved_estimate_item_total?: string | number;
  approved_estimate_markup_total?: string | number;
  site_manager_id?: number | string;
  safety_contact_id?: number | string;
  safety_contact_type?: number | string | null;
  notes?: string;
  image?: string;
  prepared_by?: number | string;
  parent_project_id?: number | string;
  demo_data?: number | string;
  budget_amount?: number | string;
  create_file_folder?: number | string;
  view_in_schedule?: number | string;
  view_in_calendar?: number | string;
  assigned_to?: string;
  task_user_id?: string;
  permit_details?: string;
  retention?: string;
  project_type_key?: string;
  notice_to_proceed?: string | null;
  latitude?: string;
  longitude?: string;
  billed_to?: number | string;
  qb_date?: string;
  quickbook_project_id?: number | string;
  qbc_id?: string;
  is_updated?: number | string;
  add_wo_total_to_revised_contract_amount?: number | string;
  customer_contact_id?: number | string;
  wbs?: number | string;
  budget_is?: string;
  original_retention?: number | string;
  geo_image?: string;
  geo_radius?: string;
  type?: string;
  referral_source?: string;
  referral_source_key?: string;
  stage?: string;
  stage_key?: string;
  score?: number | string;
  est_sales_date?: string;
  company_project_id?: string;
  project_prefix?: string;
  is_notes_convert?: number | string;
  allow_overbilling?: number | string;
  billing_option?: string;
  bid_due_date?: string;
  budget_row_from?: string;
  show_client_access?: number | string;
  client_access_html?: string;
  project_manager_id?: number | string;
  show_client_phone?: number | string;
  show_client_email?: number | string;
  site_manager_contact_id?: number | string;
  safety_additional_contact_id?: number | string;
  client_manager_id?: number | string;
  billed_to_contact?: number | string;
  billed_to_type?: number | string;
  billed_to_image?: string;
  client_cover_image?: string;
  duration?: number | string;
  customer_contract?: string;
  client_dashboard_message?: string;
  enable_client_access?: number | string;
  associated_estimate_id?: number | string;
  default_tax_rate_id?: number | string;
  companycam_project_id?: number | string;
  companycam_project_updated?: number | string;
  companycam_label_id?: number | string;
  companycam_updated_at?: number | string;
  wip_progress?: number | string;
  wip_notes?: string;
  companycam_creator_name?: string;
  budgeted?: string;
  budgeted_labor?: string;
  revised_contract_amount?: string;
  invoiced?: string;
  gross_profit_total?: string;
  revised_contract_amount_w_o_tax?: string;
  commited?: string;
  actual?: string;
  actual_labor?: string;
  tax_id?: number | string;
  total?: string;
  revenue?: string;
  cost?: string;
  sales_rep?: string | number;
  estimator?: number | string;
  no_tax_contract_amount?: string;
  project_template_id?: number | string;
  is_converted?: number | string;
  origin?: number | string;
  markup_preference?: string;
  need_adjust_invoice?: number | string;
  is_custom_data_copy?: number | string;
  xero_project_id?: number | string;
  xero_sync_date?: number | string;
  assignee_contact_id?: number | string;
  invoice_item_count?: string;
  last_read_msg_id?: number | string;
  client_last_read_msg_id?: number | string;
  billed_to_name?: string;
  billed_to_name_only?: string;
  billed_to_company_name?: string;
  budget_items_amount?: string;
  sov_total?: string;
  sov_item_total?: number | string;
  sov_taxable_total?: string;
  approved_co_total?: string;
  approved_co_item_total?: string | number;
  wo_total?: string;
  wo_item_total?: number | string;
  contract_amount_held?: string;
  billed_amount?: string;
  client_portal_billed_amount?: string;
  paid_amount?: string;
  invoice_retainage?: string;
  row_cost_invoices?: number | string;
  status_name?: string;
  customer_company?: string;
  site_manager_type?: string | number;
  safety_manager_type?: string;
  is_assigned_project?: string;
  site_manager_name?: string;
  site_manager_name_only?: number | string;
  site_manager_company_name?: string | null;
  site_manager_title?: string;
  site_manager_phone?: string;
  site_manager_cell?: string;
  site_manager_email?: string;
  site_manager_access_role?: string;
  site_manager_services?: string;
  site_manager_app_access?: string;
  site_manager_type_name?: number | string;
  safety_contact_name?: string;
  email_subject?: string;
  safety_contact_name_only?: number | string;
  safety_contact_company_name?: string;
  safety_contact_title?: string;
  safety_contact_phone?: string;
  safety_contact_cell?: string;
  safety_contact_email?: string;
  safety_contact_access_role?: string;
  safety_contact_services?: string;
  safety_contact_app_access?: string;
  safety_contact_type_name?: number | string;
  time_added?: string;
  time_modified?: string;
  qb_date_added?: string;
  qb_time_added?: string;
  companycam_date_added?: number | string;
  companycam_time_added?: number | string;
  origin_date_modified?: string;
  added_by_name?: string;
  estimator_company_name?: string;
  estimator_title?: string;
  estimator_phone?: string;
  estimator_cell?: string;
  estimator_email?: string;
  estimator_name?: string;
  estimator_name_only?: string;
  estimator_access_role?: string;
  estimator_services?: string;
  estimator_app_access?: string;
  estimator_type?: string | number;
  estimator_type_name?: string;
  is_available?: string;
  unread_msg_tbl1?: string;
  unread_msg_tbl2?: string;
  is_client_available?: string;
  client_unread_msg_tbl1?: string;
  client_unread_msg_tbl2?: string;
  previous_invoice_total?: string;
  current_invoice_total?: string;
  outstanding_invoice_total?: number | string;
  past_due_invoice_total?: string;
  upcoming_due_invoice_total?: string;
  project_expenses?: string;
  referral_source_name?: string;
  stage_name?: string;
  assignee_name_only?: string;
  assignee_company_name?: string;
  assignee_type?: string;
  assignee_type_name?: string;
  assignee_type_key?: string;
  project_manager_name?: string;
  project_manager_name_only?: string;
  profile_image?: string;
  project_manager_company_name?: string;
  project_manager_type_name?: string;
  project_manager_title?: string;
  project_manager_phone?: string;
  project_manager_cell?: string;
  project_manager_email?: string;
  project_manager_access_role?: string;
  project_manager_services?: string;
  project_manager_app_access?: string;
  project_manager_type?: string | number;
  enable_procurement_tab?: string;
  approved_estimates?: string;
  default_tax_name?: string;
  default_tax_rate?: string;
  is_reversible?: string | number;
  prj_tax_name?: string;
  prj_tax_rate?: string;
  is_prj_reversible_tax?: string | number;
  sales_rep_title?: string;
  sales_rep_phone?: string;
  sales_rep_cell?: string;
  sales_rep_fax?: string;
  sales_rep_email?: string;
  sales_rep_address1?: string;
  sales_rep_address2?: string;
  sales_rep_city?: string;
  sales_rep_state?: string;
  sales_rep_zip?: string;
  sales_rep_name?: string;
  sales_rep_company_name?: string;
  sales_rep_name_only?: string;
  sales_rep_services?: string;
  sales_rep_app_access?: string;
  sales_rep_type?: string | number;
  sales_rep_type_name?: string;
  sales_rep_access_role?: string;
  customer_tax_id?: string;
  sov_tax_amount?: number | string;
  revised_original_contract_amount?: number | string;
  original_contract_amount_no_tax?: number | string;
  finance_summary?: IProjectFinanceSummary;
  site_manager_image?: string;
  project_manager_image?: string;
  safety_contact_image?: string;
  sales_rep_image?: string;
  estimator_image?: string;
  all_item_total: {
    total: {
      estimated_total: number;
      commited_total: number;
      actual_total: number;
      remain_commited: number;
      remain_actual: number;
      remain_commited_percent: number;
      remain_actual_percent: number;
      due: number;
    };
    material: {
      estimated_total: number;
      commited_total: number;
      actual_total: number;
      remain_commited: number;
      remain_actual: number;
      remain_commited_percent: number;
      remain_actual_percent: number;
      due: number;
    };
    equipment: {
      estimated_total: number;
      commited_total: number;
      actual_total: number;
      remain_commited: number;
      remain_actual: number;
      remain_commited_percent: number;
      remain_actual_percent: number;
      due: number;
    };
    labor: {
      estimated_total: number;
      commited_total: number;
      actual_total: number;
      remain_commited: number;
      remain_actual: number;
      remain_commited_percent: number;
      remain_actual_percent: number;
      due: number;
    };
    sub_contractor: {
      estimated_total: number;
      commited_total: number;
      actual_total: number;
      remain_commited: number;
      remain_actual: number;
      remain_commited_percent: number;
      remain_actual_percent: number;
      due: number;
    };
    other_item: {
      estimated_total: number;
      commited_total: number;
      actual_total: number;
      remain_commited: number;
      remain_actual: number;
      remain_commited_percent: number;
      remain_actual_percent: number;
      due: number;
    };
    unassigned: {
      estimated_total: number;
      commited_total: number;
      actual_total: number;
      remain_commited: number;
      remain_actual: number;
      remain_commited_percent: number;
      remain_actual_percent: number;
      due: number;
    };
  };
  referenceModuleDs: {
    sc: number;
    po: number;
    expense: number;
    bill: number;
    log_item: number;
    daily_log_equipment: number;
    equipment_log: number;
  };
  finance_summary: IProjectFinanceSummary;
  billing_vs_actual: {
    original_contract_amount: number;
    amount_invoiced: number;
    remain_to_invoice: number;
    total_actual_cost: number;
    gross_profit: number;
  };
  wip_widget: {
    original_contract_amount: number;
    amount_invoiced: number;
    current_cost_budget: number;
    total_actual_cost: number;
    cost_completed: number;
    forcast_completed: number;
    earned_revenue: number;
    over_billing: number;
    under_billing: number;
  };
  project_summary: {
    original_contract_amount: number;
    change_orders: number;
    work_orders: number;
    service_tickets: number;
    total_project_amount: number;
    invoice_payments: number;
    unpaid_invoices: number;
    contract_amount_held: number;
    remain_invoices: number;
  };
  timecardAry: TimeCardData[];
  estimatedAry: EstimatedCardData[];
  customer_additional_contacts: IProjectAdditionalContactData[];
  internal_contacts: IProjectContactData[];
  external_contacts: IProjectContactData[];
  timecardDetail: {
    total_cost: string;
    total_minutes: string;
    total_timecard_counts: string;
  };
  contacts: TProjectContacts[];
  client_access_users: TClientAccessUsers[];
  client_access: TClientAccessModules[];
  client_access_contacts: TClientAccessContacts[];
}

type TClientAccessContacts = {
  contact_id: 3963470;
  customer_name: "janat savaniiiiii";
  is_additional_contact: "0";
  phone: "";
  cell: "";
  email: "<EMAIL>";
};

type TProjectContacts = {
  contact_id: string | number;
  type_id: string | number;
  directory_id: string | number;
  project_id: string | number;
  date_added: string;
  date_modified: string;
  company_id: string | number;
  directory_contact_id: string | number;
  first_name: string;
  last_name: string;
  dir_type: string | number;
  type_name: string;
  company_name: string;
  phone: string;
  email: string;
  title: string;
  cell: string;
  is_deleted: string | number;
  company: string;
  display_name: string;
  image: string;
};

type TClientAccessUsers = {
  access_id: string | number;
  project_id: string | number;
  contact_id: string | number;
  is_additional_contact: number;
  access_type: number;
  username: string;
  password: string;
  has_access: number;
  last_logged_in: string;
  last_login_track: number;
  company_id: number;
  user_id: number;
  date_added: string;
  date_modified: string;
  last_login_date: string;
  last_login_time: string;
  customer_name: string;
  customer_email: string;
};

type TClientAccessModules = {
  portal_access_id: string | number;
  company_id: string | number;
  user_id: string | number;
  project_id: string | number;
  module_key: string;
  is_access: number;
  template_id: string | number;
  pdf_key: string;
  date_added: string;
  date_modified: string;
  mdl_additional_options: string;
  custom_templates: string | number;
  default_templates: string | number;
  module_id: string | number;
};

interface IprojectDetailsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IProjectDetails;
}

interface IProjectDetailsInitialState {
  details: IProjectDetails;
  isDetailLoading: boolean;
  noDetailsAvailable: boolean;
  isSummaryDetailsLoading: boolean;
  summaryFiles: IProjectFilesData[];
  noSummaryDetailsAvailable: boolean;
  isSummaryFilesLoading: boolean;
  isSummaryActionItemsLoading: boolean;
  isSummaryLoaded: boolean;
  isActionItemsLoaded: boolean;
  summaryActionItems: IProjectActionItemsData;
  loadingStatus: IFieldStatus[];
  defaultSettingSaved: boolean;
}

interface IProjectStatusList {
  label?: string;
  value?: string;
  default_color?: string;
  icon?: IFontAwesomeIconProps["icon"];
  show_in_progress_bar?: string;
  index?: number;
}

interface IProjectKanbanSortingArray {
  column_id: number | string;
  sort_order: number | string;
  sorting_id: number | string;
  column_name: number | string;
  type_id: number | string;
}

interface IProjectKanbanSorting {
  module_id: number;
  kanban_sorting: IProjectKanbanSortingArray[];
}

interface IProjectSummaryApiParams {
  project_id: number;
  record_type: string;
}

interface IProjectFinanceSummaryCell {
  est_revenue: number;
  est_cost: number;
  est_profit: number;
  est_markup: number;
  est_margin: number;
  sov_tax: number;
  project_amount_w_tax: number;
  amount_invoiced: number;
  amount_paid: number;
  amount_unpaid: number;
  remaining_to_invoice: number;
  past_due: number;
  due_30_days: number;
  retention_held: number;
  name: string;
}

type TEstimateFinance = {
  est_revenue: number;
  total_revenue: number;
  est_cost: number;
  est_profit: number;
  est_markup: number;
  est_margin: number;
};

type IProjectFinanceSummary = {
  sov: TEstimateFinance;
  change_orders: TEstimateFinance;
  work_orders: TEstimateFinance;
  service_tickets: TEstimateFinance;
  total: TEstimateFinance;
  invoices: {
    amount_invoiced: number;
    amount_paid: number;
    amount_unpaid: number;
    retention_held: number;
    remaining_to_invoice: number;
    due_30_days: number;
    past_due: number;
  };
  sov_tax: number;
  project_amount_w_tax: number;
};
interface IProjectFilesApiParams {
  page?: number;
  limit?: number;
  type?: number;
  onlyImage?: number;
  filter?: {
    primaryId: number;
  };
  record_id?: number;
  module_key?: string;
  start?: number;
}

interface IProjectFilesData {
  image_id: number;
  project_id: number;
  type: string;
  type_id: string;
  file_ext: string;
  file_path: string;
  title: string;
  notes: string;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  module_id: number;
  primary_id: number;
  file_tags: string;
  child_item_id: number;
  user_id: number;
  company_id: number;
  file_type: number;
  is_image: number;
  parent_image_id: number;
  demo_data: number;
  height: number;
  width: number;
  size: string;
  mode: string;
  child_item_backup: string;
  child_item_backup12: string;
  upload_from: string;
  image_res: string;
  camera_res: string;
  file_name: string;
  thumb_flag: number;
  large_flag: number;
  static_folder: number;
  folder_id: number;
  refrence_img_id: number;
  quickbook_attachable_id: string;
  is_common_notes: number;
  is_bidding_file: number;
  child_item_name: string;
  file_save_when_send_email: number;
  is_file_shared: number;
  annotation_data: string;
  original_file_path: string;
  is_google_drive_file: number;
  companycam_photo_id: string;
  companycam_creator_name: string;
  original_image_id: number;
  is_project_template: number;
  project_template_id: string;
  xero_attachable_id: string;
  date_added_files: string;
  time_added_files: string;
}

interface IProjectBillsData {
  bill_id: string;
  company_bill_id: string;
  due_date: string;
  notes: string;
  order_date: string;
  payments: string;
  supplier_company_name: string;
  total: string;
}

interface IProjectBillsCountData {
  number_of_bill: string;
  total: string;
}

interface IProjectChangeOrdersData {
  billing_status: string;
  billing_status_name: string;
  change_order_id: string;
  company_order_id: string;
  count_in_contract_amount: string;
  order_date: string;
  status_color: string;
  subject: string;
  total: string;
}

interface IProjectChangeOrdersCountData {
  number_of_change_order: string;
  total: string;
}

interface IProjectEstimatesData {
  approval_type_key: string;
  approval_type_name: string;
  company_estimate_id: string;
  customer_name: string;
  description: string;
  estimate_date: string;
  estimate_id: string;
  status_color: string;
  title: string;
  total: string;
}

interface IProjectEstimatesCountData {
  total: string;
  number_of_estimate: string;
}

interface IProjectExpensesData {
  amount: string;
  expense_date: string;
  expense_id: string;
  expense_name: string;
  total: string;
  vendor_name: string;
}

interface IProjectExpensesCountData {
  amount: string;
  number_of_expense: string;
}

interface IProjectInvoicesData {
  approval_type_name: string;
  company_invoice_id: string;
  customer_name: string;
  description: string;
  due_date: string;
  invoice_date: string;
  invoice_id: string;
  is_advance_invoice: string;
  paymentTotal: string;
  prefix_company_invoice_id: string;
  status_color: string;
  total: string;
}

interface IProjectInvoicesCountData {
  number_of_invoice: string;
  total: string;
}

interface IProjectPaymentsData {
  amount: string;
  approval_status_key: string;
  approval_status_name: string;
  company_invoice_id: string;
  invoice_id: string;
  payment_date: string;
  payment_id: string;
  payment_type_name: string;
  prefix_company_invoice_id: string;
  status_color: string;
}

interface IProjectPaymentsCountData {
  amount: string;
  number_of_payment: string;
}

interface IProjectPurchaseOrdersData {
  billing_status_name: string;
  company_purchase_order_id: string;
  order_date: string;
  purchase_order_id: string;
  status_color: string;
  subject: string;
  supplier_company_name: string;
  total: string;
}

interface IProjectPurchaseOrdersCountData {
  number_of_purchase_order: string;
  total: string;
}

interface IProjectSubContractsData {
  company_sub_contract_id: string;
  contractor_id: string;
  contractor_name: string;
  order_date: string;
  response: string;
  response_name: string;
  sub_contract_id: string;
  subject: string;
  total: string;
}

interface IProjectSubContractsCountData {
  number_of_sub_contract: string;
  total: string;
}

interface IProjectWorkOrdersData {
  company_order_id: string;
  work_order: string;
  work_order_status: string;
  work_order_id: string;
  total: string;
  no_markup_total: string;
  order_date: string;
  status_color: string;
  status_name: string;
}

interface IProjectWorkOrdersCountData {
  number_of_work_order: string;
  total: string;
}

interface IProjectFilesApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    assigned_projects: string;
    sortOrder: string;
    filesCount: string;
    aws_files: IProjectFilesData[];
  };
}

interface IProjectUpdateCostCodeParams {
  detail_id: string;
  cost_code_id: string;
}

interface IProjectTimesApiParams {
  need_more_data?: number;
  id?: number;
  module_key?: string[];
}

interface IProjectReportParams {
  project_id: string;
  filter_by: string;
  generate_from: string;
  factor_by: string;
  show_all: number;
}

interface IProjectDownloadReportParams {
  project_id: string;
  filter_by: string;
  // start_date: string;
  // end_date: string;
  generate_from: string;
  factor_by: string;
  show_all: number;
}

interface IProjectProcurementApiParams {
  project_id: number;
}

interface IProjectProcurementGenerateApiParams {
  assignee_id: number;
  selected_ref: string;
  ref_id: number;
  id?: number;
}
interface IProjectActionTimeCardApiParams {
  id?: number;
  limit?: number;
  start?: number;
}

interface IProjectActionItems {
  total_due: string;
  total_open: string;
  total_close: string;
}

interface IProjectActionItemsBills {
  bill_status: string;
  bill_count: string;
}

interface IProjectActionItemsData {
  open_incomplete_todo?: IProjectActionItems[];
  open_incomplete_punchlist?: IProjectActionItems[];
  open_incomplete_purchase_order?: IProjectActionItems[];
  open_incomplete_invoice?: IProjectActionItems[];
  open_incomplete_bills?: IProjectActionItemsBills[];
  open_incomplete_compliance?: IProjectActionItems[];
  open_incomplete_rfi?: IProjectActionItems[];
}

interface IProjectActionItemsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    open_incomplete_items: IProjectActionItemsData;
  };
}

interface IProjectReportApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    open_incomplete_items: IProjectActionItemsData;
  };
}
interface IProjectProcurementItemsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: [];
}

interface IProjectDetailUpdateApiRes extends Omit<IDefaultAPIRes, "data"> {
  data?: IProjectDetails | {};
}

interface IProjectTimeCardApiRes extends Omit<IDefaultAPIRes, "data"> {
  data?: IProjectTimeCardTableData[] | [];
}

interface IProjectFileUploadApiParams {
  userId?: string;
  companyId?: string;
  moduleName?: string;
  fileType?: string;
  fileName?: string;
  fileUrl?: string;
  saveAsNew?: number;
  imageResolution?: string;
}

interface IProjectCustomSignature {
  key: string;
  value: string;
}

interface IUpdateProjectCustomField {
  id?: string | number;
  record_id?: number;
  module_id?: number;
  custom_field_id?: number;
  custom_signature?: IProjectCustomSignature[];
  custom_fields?: {
    [key: string]: string | string[];
  };
  user_id?: number;
  company_id?: number;
}

interface IUpdateProjectCustomFieldApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    notification_sent: boolean;
  };
}

// SOV

interface IBilledRemainSovSumamry {
  material: string;
  equipment: string;
  labor: string;
  sub_contractor: string;
  other_item: string;
  unassigned: string;
}

interface ISovSummary {
  billed: IBilledRemainSovSumamry;
  remain: IBilledRemainSovSumamry;
  total_billed: string;
  total_remain: string;
  total_profit: string;
  profit_percentage: string | number;
  item_total: string;
  sub_total: string;
  total_quantity: string;
  total_billed_unit: string;
  total_remained_unit: string;
}

interface ISovFinalState {
  estimate: ISovSummary;
  change_order: ISovSummary;
  work_order: ISovSummary;
  project_item: ISovSummary;
  all: ISovSummary;
  [key: string]: ISovSummary;
}

interface ISOVBudgetItemsData {
  item_id: number;
  project_id: number;
  directory_id: number;
  company_id: number;
  equipment_id: number;
  material_id: number;
  cost_code_id: number;
  tax_id: number;
  subject: string;
  quantity: number;
  unit: string;
  unit_cost: string | number;
  hidden_markup: number;
  markup: string;
  description: string;
  estimate_id: number;
  change_order_id: number;
  co_item_section_id: string | number;
  co_item_section_name: string;
  work_order_id: number;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  total: string | number;
  total_tax: string | mumber;
  reference_item_id: number;
  budget_item_no: number;
  parent_budget_item_id: number;
  item_type: string | number;
  reference_item_type_id: number;
  item_assigned_to: number;
  item_assigned_to_contact_id: number;
  apply_global_tax: number;
  tax_amount: string;
  is_markup_percentage: number;
  markup_amount: string;
  section_id: number;
  item_no: number;
  internal_notes: string;
  is_project_template: number;
  project_template_id: number;
  item_on_database: number;
  formated_unit_cost: string;
  formated_total: string;
  modified_unit_cost: string;
  tax_rate: string;
  company_estimate_id: string | mumber;
  estimate_approval_type: string | mumber;
  estimate_item_no: string | mumber;
  est_internal_notes: string | mumber;
  custom_section_id: string | mumber;
  est_item_section_id: string | mumber;
  section_name: string | mumber;
  order_item_no: string | mumber;
  item_total: number;
  assignee_name: string | mumber;
  assigned_to_name_only: string;
  assignee_type: string | mumber;
  item_type_display_name: string;
  item_type_name: string;
  item_type_key: string;
  wo_item_no: string | mumber;
  company_order_id: string;
  wo_company_order_id: string | mumber;
  co_company_order_id: string | mumber;
  total_billed: number;
  bill_paid: number;
  bill_paid_amount: number;
  bill_remain: number;
  cost_code_name: string | mumber;
  cost_code: string | mumber;
  source_name: string;
  source: string;
  previous_paid_bill: number;
  current_paid_bill: number;
  previous_amount: string;
  current_amount: string;
  item_section_id: string;
  final_markup: string;
  markup_type: string;
  assigned_to_company_name: string;
  assigned_to_image: string;
  variation_name: string;
  variation_id: string;
  is_group_item: number;
  no_mu_total: number;
  retainage: number;
  current_retainage: number;
}

interface IProjectSOVItemsApiResData {
  budget_items: ISOVBudgetItemsData[];
}

interface IProjectSOVItemsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IProjectSOVItemsApiResData;
}

interface IgetApprovedEstimatesApiParams {
  filter: {
    status: string;
    customer?: string | number;
    project?: string | number;
    approval_type: string;
  };
  limited_fields: number;
  ignore_filter: number;
}

interface IgetApprovedEstimatesData {
  estimate_id: number;
  title: string;
  is_template: number;
  project_name: string;
  company_estimate_id: string;
  customer_name: string;
  estimate_date: string;
}

interface IgetApprovedEstimatesApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: { estimates: IgetApprovedEstimatesData[] };
}

interface IGetApprovedEstimatesItemsApiParams {
  estimate_id: string;
  module_id: number;
  need_section_response: number;
}

interface IGetApprovedEstimatesItemsApiResSections {
  section_id: string;
  estimate_id: string;
  custom_section_id: string;
  section_name: string;
  cost_code_id: string;
  description: string;
  user_id: string;
  company_id: string;
  modify_by: string;
  parent_section_id: string;
  demo_data: string;
  date_added: string;
  date_modified: string;
  is_optional_section: string;
  is_project_template: string;
  project_template_id: string;
  code_id: string;
  csi_code: string;
  parent_id: string;
  csi_name: string;
  is_deleted: string;
  parent_code_id: string;
  demo_parent_code_id: string;
  test_id: string;
  parent_id_back: string;
  qb_date: string;
  quickbook_costcode_id: string;
  quickbook_parent_id: string;
  qbc_id: string;
  is_updated: string;
  quickbook_level: string;
  is_managed_level: string;
  import_id: string;
  show_on_timecard: string;
  income_account_id: string;
  expense_account_id: string;
  has_no_child: string;
  qb_type: string;
  deleted_archived: string;
  is_sample_parent: string;
  cost_code_name: string;
  section_total: string;
  section_total_with_optional_item: string;
  taxable_total: string;
  markup_total: string;
  markup_items_total: string;
  total_w_o_tax_markup: string;
  taxable_total_new: string;
  items: IGetApprovedEstimatesItemsApiResSectionsItems[];
}

interface IGetApprovedEstimatesItemsApiResSectionsItems {
  item_id: string;
  estimate_id: string;
  directory_id: string;
  company_id: string;
  equipment_id: string;
  material_id: string;
  cost_code_id: string;
  tax_id: string;
  subject: string;
  quantity: string;
  unit: string;
  unit_cost: string;
  hidden_markup: string;
  markup: string;
  description: string;
  is_deleted: string;
  date_added: string;
  date_modified: string;
  total: string;
  estimate_item_no: string;
  parent_item_id: string;
  item_type: string;
  reference_item_id: string;
  assigned_to: string;
  assigned_to_contact_id: string;
  is_temaplate: string;
  quickbook_estimateitem_id: string;
  quickbook_item_id: string;
  qbc_id: string;
  item_on_database: string;
  apply_global_tax: string;
  section_id: string;
  is_markup_percentage: string;
  markup_amount: string;
  bidder_item_id: string;
  is_optional_item: string;
  variation_id: string;
  internal_notes: string;
  one_build_id: string;
  is_project_template: string;
  project_template_id: string;
  origin: string;
  modified_unit_cost: string;
  item_type_display_name: string;
  item_type_name: string;
  item_type_key: string;
  company_estimate_id: string;
  assignee_type: string;
  assignee_name: string;
  assigned_to_name_only: string;
  assigned_to_company_name: string;
  section_name: string;
  custom_section_id: string;
  source_name: string;
  item_total: string;
  cost_code_name: string;
  cost_code: string;
  code_id: string;
  updated_unit_cost: string;
  quickbook_costcode_id: string;
  tax_rate: string;
  origin_date_modified: string;
  variation_name: string;
  no_mu_total: string;
}

interface IGetApprovedEstimatesItemsApiRes
  extends Omit<IDefaultAPIRes, "data"> {
  data: {
    sections: IGetApprovedEstimatesItemsApiResSections[];
  };
}

interface IAddProjectItemApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    reference_item_id: number;
    budget_items?: ISOVBudgetItemsData[];
  };
}

interface IDeleteSOVItemParams {
  formData: {
    item_id: string;
  };
  paramsData: {
    id: string;
  };
}

// SOV

interface IBilledRemainSovSumamry {
  material: string;
  equipment: string;
  labor: string;
  sub_contractor: string;
  other_item: string;
  unassigned: string;
}

interface ISovSummaryInitialState {
  sov_summary: ISovFinalState;
  taxable_total: string | number;
  isSovInitialLoading: boolean;
  noSovAvailable: boolean;
  current_filter_by: string;
  isSOVItemsLoading: boolean;
  noSOVItemsAvailable: boolean;
  projectSOVItems: IProjectSOVItemsApiResData;
  unitData: IUnitData[];
}

interface ISovSummaryApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: Pick<ISovSummaryInitialState, "sov_summary" | "taxable_total">;
}

interface IAddApprovedEstimatesItemsToProjectApiParams {
  project_id?: number;
  items_from?: string;
  item_ids?: string;
  from_section?: number;
  estimate_id?: string;
}

interface IProjectChangeOrderListParams {
  page?: number;
  limit?: number;
  filter_primary_id?: number;
  global_project?: number;
  ignore_filter: number;
  any_status?: number;
  search?: string;
  filter?: Partial<IChangeOrderListFilters>;
  module_id?: number;
}

interface IProjectChangeOrder {
  employee: string;
  customer_name: string;
  customer_image: string;
  customer_short_name: string;
  customer_id: number;
  project_id: number;
  project_name: string;
  billing_status_name_old: string;
  billing_status_key: string;
  change_order_id: number;
  total: string;
  subject: string;
  is_deleted: number;
  billing_status: number;
  signature: string;
  email_subject: string;
  company_order_id: string;
  projectPrefix: string;
  prefix_company_order_id: string;
  billing_status_name: string;
  order_date: Date;
  date_added: Date;
  time_added: string;
  date_modified: Date;
  default_color: string;
  type: string;
  billed_to_name: string;
  billed_to_email: string;
  billed_to_dir_type: number;
  billed_to: number;
  billed_to_status: null;
  billed_to_contact: number;
  day_delay: string;
  time_delay_name: null;
  notes: string;
  owner_co: string;
  rfi_id: number;
  c_rfi_id: null;
  count_in_contract_amount: number;
  estimate_id: string;
  items_count: string;
  ref_po_id?: number;
}

interface IProjectChangeOrderListApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    changeOrders: IProjectChangeOrder[];
  };
}

interface IGetChangeOrdersItems {
  change_order_id: number;
  need_section: number;
}

interface IProjectChangeOrderItem {
  item_id?: number;
  change_order_id?: number;
  user_id?: number;
  company_id?: number;
  cost_code_id?: number | string;
  tax_id?: string;
  subject: string;
  quantity: number;
  unit?: string;
  unit_cost?: number | string;
  hidden_markup?: number;
  markup?: number;
  description?: string;
  is_deleted?: number;
  date_added?: string;
  date_modified?: string;
  total?: number;
  order_item_no?: number;
  parent_item_id?: number;
  item_type: number | string;
  reference_item_id?: number | string;
  item_on_database?: number;
  assigned_to?: number | string;
  contractor_id?: number | string;
  contractor_contact_id?: number;
  is_markup_percentage?: number;
  markup_amount?: string;
  is_discount_item?: number;
  source?: string;
  cor_item_id?: number;
  apply_global_tax?: number;
  assigned_to_backup?: number;
  assigned_to_contact_id?: number;
  estimate_id?: number;
  reference_module_item_id?: number;
  internal_notes?: string;
  modified_unit_cost?: string;
  item_type_display_name?: string;
  item_type_name?: string;
  item_type_key?: string;
  assignee_type?: string;
  assignee_name?: string;
  assigned_to_name_only?: string;
  assigned_to_company_name?: string;
  cor_i_id?: number;
  cor_id?: number;
  cor_change_order_id?: number;
  company_order_id?: string;
  source_name?: string;
  is_imported?: string;
  cost_code_name?: string;
  cost_code?: string;
  tax_rate?: number;
  contractor_name?: string;
  no_mu_total?: number;
  add_item_to_database?: number;
  section_id: string | number;
  section_name: string;
  is_optional_item?: number;
}

interface ICOSections {
  section_name: string;
  section_id: number;
  section_id: number;
  custom_section_id: number;
  description: string | null;
  reference_section_id: string | number;
  is_optional_section: number;
  items: IProjectChangeOrderItem[];
}

interface IProjectChangeOrderItemsApiResData {
  items: ICOSections[];
}

interface IProjectChangeOrderItemsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IProjectChangeOrderItemsApiResData;
  success: boolean;
}

interface IAddCOWOItemsToProjectApiParams {
  id?: number;
  work_order_id?: number;
  request_from: string;
  change_order_id?: number;
  item_ids?: string;
}

interface IProjectWorkOrderListParmas {
  limit?: number;
  page?: number;
  filter?: IWorkOrderTempFil;
  search?: string;
  start?: number;
  order_by_name?: string;
  order_by_dir?: string;
  order_direction?: string;
  ignore_filter?: number;
}

interface IProjectWorkOrderParamsData {
  id?: string;
}

interface IProjectWorkorderItem {
  item_id?: number | string;
  work_order_id?: number | string;
  user_id?: number | string | undefined;
  company_id?: number | string;
  cost_code_id?: number | string;
  subject?: string;
  quantity?: number | string;
  unit?: string;
  unit_cost?: number | string;
  hidden_markup?: number | string;
  description?: string;
  total?: string | number;
  date_added?: string;
  date_modified?: string;
  order_item_no?: number | string;
  tax_id?: number | string;
  markup?: string;
  item_type?: number | string;
  reference_item_id?: number;
  assigned_to?: number | string | undefined;
  parent_item_id?: number | string;
  demo_data?: number | string;
  contractor_id?: number | string;
  contractor_contact_id?: number | string;
  is_markup_percentage?: string | number | undefined;
  markup_amount?: string | number;
  estimate_id?: number | string;
  reference_module_item_id?: number | string;
  assigned_to_backup?: number | string;
  assigned_to_contact_id?: number | string;
  internal_notes?: string;
  item_on_database?: number | string;
  item_type_display_name?: string;
  item_type_name?: string;
  item_type_key?: string;
  assignee_type?: number | string;
  assignee_name?: string;
  assigned_to_name_only?: string;
  assigned_to_company_name?: string;
  cost_code_name?: string;
  csi_code?: string;
  is_deleted?: number;
  cost_code?: number | string;
  contractor_name?: number | string;
  contractor_dir_type?: number | string;
  add_item_to_database?: number | string;
  type_key?: string | number | undefined;
  type_name?: string;
  type?: string | number | undefined;
  apply_global_tax?: string | number;
}

interface IProjectWorkorderItemApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    items: IProjectWorkorderItem[];
  }; // Change this when use
}

interface IProjectWorkOrderData {
  work_order_id: number;
  description: string;
  subject: string;
  assigned_to: string;
  response: number;
  project_name: string;
  project_id: number;
  issued_by_name: string;
  billed_to_email: string;
  assignee_name: string;
  assignee_dir_type: number;
  company_order_id: string;
  work_order_status_name: string;
  email_subject: string;
  order_date: string;
  end_date: string;
  projectPrefix: string;
  prefix_company_work_order_id: string;
  date_added: string;
  work_order_status_name_old: string;
  work_order_status: number;
  assignes_to: Assignedto[];
  assignees_to: IAssigneesTo[];
  is_deleted?: string;
  customer_name?: string;
  total: number;
}

interface IProjectWorkOrderListApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    workOrders: IProjectWorkOrderData[];
  };
}

interface IAddSOVItemsFromCSVFileParams {
  csvFile: string;
  project_id: number;
}

interface IAddCIDBItemsData extends ISOVBudgetItemsData {
  hidden_cost: number;
  account_id: number;
  import_item_type: string;
  contractor_id: number;
}

interface ISovItemsForAdd extends ISOVBudgetItemsData {
  section?: string;
}

interface IAddSOVItemsFromCSVFile {
  project_id: number;
  sov_items: Partial<ISovItemsForAdd>[];
  is_markup_from_csv: boolean;
}

type TUpdateProjContactsApiParams = {
  id?: string | number;
  key_name: string;
  project_contacts: string;
  user_id: string;
  company_id: string;
};

interface TExtendedProjectModuleStatus extends ModuleStatus {
  is_status?: string | number;
}

interface IClientPrefsModuleData {
  modules: TClientAccessModules[];
}

interface IUpdateClientPrefsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IClientPrefsModuleData;
}

type TClientAccessArr = {
  module_key: string;
  preference_id: number;
  is_access: number;
  pdf_template: number;
  pdf_key: string;
};

interface IClientPortalModuleSettingApiParams {
  module_id: number;
  show_phone_to_client_portal: number;
  show_email_to_client_portal: number;
  client_access_ary: TClientAccessArr[];
}

interface IShareFilesForProjectApiParams {
  module_key?: string;
  record_id: number;
  start: number;
  limit: number;
}

interface IShareFilesForProjectApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    aws_files: IFile[];
    project_file_sortorder: string;
  };
}

interface IGetClientLogsApiParams {
  start?: number;
  length?: number;
  search?: string;
  searchByUser?: string;
  force_login?: number;
  searchByModule?: string;
  order_by_name?: string;
  order_by_dir?: string;
}

interface IClientLog {
  id: number;
  company_id: number;
  user_id: number;
  access_id: number;
  action: string;
  record_id: string;
  module_id: string;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  status: 1;
  from: string;
  operation: string;
  module_key: string;
  report_module_id: number;
  subject: string;
  email_record_id: number;
  postmark_message_id: number;
  custom_text: string;
  ip_address: string;
  custom_tag: string;
  reference_company_id: number;
  date_insert: string;
  user_name: string;
  module_name: string;
  web_page: string;
  status_name: string;
  l_date_insert: string;
  l_time_insert: string;
}

interface IGetClientLogsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    client_log: IClientLog[];
  };
}

type TShareWithClientApiParams = {
  image_id: number;
  is_file_shared: number;
};

interface IProjectContactsApiParams {
  project_id: number;
  record_type?: string;
  is_webhook?: number;
  invoice_id?: number;
  basic_info?: string;
}

interface IProjectContactsApiRes {
  contacts: TProjectContacts[];
  customer_additional_contacts: IProjectAdditionalContactData[];
  internal_contacts: IProjectContactData[];
  external_contacts: IProjectContactData[];
}

interface IUpdateClientAccessContactApiRes
  extends Omit<IDefaultAPIRes, "data"> {
  data: {
    client_users: TClientAccessUsers;
  };
}

type TTaskContractor = {
  display_name: string;
  user_id: string;
  type: string;
};

interface ICopyProjectDataApiParams {
  project_template_id: number;
  copy_templates_module: string[];
  project_id: number;
  is_from_action_menu: boolean;
}

interface IDownloadProjectStatementApiParams {
  op: string;
  project_id: number;
  company_id: number;
  tz: string;
  tzid: string;
  need_more_data: number;
  user_id: number;
  from: string;
}

interface IExportScheduleOfValuesApiParams {
  project_id: number;
  projectName: string;
}
