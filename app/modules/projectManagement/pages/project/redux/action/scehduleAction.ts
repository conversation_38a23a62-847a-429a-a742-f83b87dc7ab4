import { createAsyncThunk } from "@reduxjs/toolkit";
import { handleApiCatchError } from "~/helpers/helper";
import { projectRoutes } from "~/route-services/project.routes";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";

export const fetchGanttScheduleTasksPro = createAsyncThunk(
  "projectScheduleTasksPro",
  async (paramsData: IGetScheduleTasksApiParams) => {
    try {
      const data = await getWebWorkerApiParams({
        otherParams: {
          ...paramsData,
        },
      });

      const response = (await webWorkerApi({
        url: projectRoutes.get_schedule_tasks,
        method: "post",
        data: data,
      })) as IGetScheduleTasksApiRes;
      return response;
    } catch (error) {
      return handleApiCatchError(error as Error);
    }
  }
);
