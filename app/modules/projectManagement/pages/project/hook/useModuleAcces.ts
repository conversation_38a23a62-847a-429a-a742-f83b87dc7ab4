import { useMemo } from "react";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { USER_PLANE_IDS } from "../utils/constants";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { useAppProSelector } from "../redux/store";

export const useModuleAccess = () => {
  const { details }: IProjectDetailsInitialState = useAppProSelector(
    (state) => state.proDetails
  );

  const { checkGlobalModulePermissionByKey, getGlobalModuleByKey } =
    useGlobalModule();

  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();

  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();

  const { module_access } = currentModule || {};

  const { disable_client_when_project_completed, module_group_id = -1 } =
    appSettings || {};

  const clientAccessPortal = getGlobalModuleByKey(
    CFConfig.client_portal_module
  );

  const procurementModule = getGlobalModuleByKey(CFConfig.procurement_module);

  const ganttModule = getGlobalModuleByKey(
    CFConfig.project_schedule_gantt_module
  );

  const projectReportModule = getGlobalModuleByKey(
    CFConfig.project_report_module
  );

  const projectFileTab = getGlobalModuleByKey(CFConfig.project_file_module);

  const projectDocumentTab = getGlobalModuleByKey(
    CFConfig.project_document_tab
  );

  const projectScheduleTab = getGlobalModuleByKey(CFConfig.project_budget_tab);
  const timeCardModule = getGlobalModuleByKey(CFConfig.time_card_module);

  const restrictedPlanIds: number[] = useMemo(() => {
    return [
      USER_PLANE_IDS.basic_v5,
      USER_PLANE_IDS.standard_v5,
      USER_PLANE_IDS.plus_v5,
    ];
  }, []);

  const isReadOnly = useMemo(
    () => module_access === "read_only",
    [module_access]
  );

  const hasEnoughAccessofProjectFinanceTabModule = useMemo(() => {
    const access: TModuleAccessStatus = checkGlobalModulePermissionByKey(
      CFConfig.project_finance_tab_module
    );

    return ["full_access", "own_data_access", "read_only"].includes(access);
  }, [CFConfig.project_finance_tab_module]);

  const projectContractAmountModuleAccess = useMemo(() => {
    const access: TModuleAccessStatus = checkGlobalModulePermissionByKey(
      CFConfig.project_contract_amount_module
    );

    return ["full_access", "own_data_access"].includes(access);
  }, [CFConfig.project_contract_amount_module]);

  const projectBudgetAmountModuleAccess = useMemo(() => {
    const access: TModuleAccessStatus = checkGlobalModulePermissionByKey(
      CFConfig.project_budget_amount_module
    );

    return ["full_access", "own_data_access"].includes(access);
  }, [CFConfig.project_contract_amount_module]);

  const enoughProcurementAccess = useMemo(() => {
    return (
      procurementModule?.can_read === "1" &&
      details?.enable_procurement_tab == "1"
    );
  }, [procurementModule?.can_read, details?.enable_procurement_tab]);

  const enoughGanttModuleAccess = useMemo(() => {
    return (
      ganttModule?.can_read === "1" &&
      ![...restrictedPlanIds].slice(0, 2).includes(module_group_id)
    );
  }, [ganttModule?.can_read, restrictedPlanIds, module_group_id]);

  const enoughClientModuleAccess = useMemo(() => {
    return (
      clientAccessPortal?.can_read?.toString() === "1" &&
      !restrictedPlanIds.includes(module_group_id) &&
      !(
        disable_client_when_project_completed?.toString() === "1" &&
        details?.project_status === "completed"
      )
    );
  }, [
    clientAccessPortal?.can_read,
    restrictedPlanIds,
    module_group_id,
    disable_client_when_project_completed,
    details?.project_status,
  ]);

  const enoughReportModuleAccess = useMemo(() => {
    return (
      projectReportModule?.can_read === "1" &&
      ![...restrictedPlanIds].slice(0, 2).includes(module_group_id)
    );
  }, [projectReportModule?.can_read, restrictedPlanIds, module_group_id]);

  const enoughDocumentTabAccess = useMemo(() => {
    return (
      projectDocumentTab?.can_read === "1" ||
      projectDocumentTab?.can_write === "1"
    );
  }, [projectDocumentTab]);

  const enoughTabFileAccess = useMemo(() => {
    return (
      projectFileTab?.can_read === "1" || projectFileTab?.can_write === "1"
    );
  }, [projectFileTab]);

  const enoughTabScheduleAccess = useMemo(() => {
    return (
      projectScheduleTab?.can_read === "1" ||
      projectScheduleTab?.can_write === "1"
    );
  }, [projectScheduleTab]);

  const timeTabNoAccess = useMemo(() => {
    const access: TModuleAccessStatus = checkGlobalModulePermissionByKey(
      CFConfig.time_card_module
    );

    return access === "no_access";
  }, [CFConfig.time_card_module]);

  return {
    hasEnoughAccessofProjectFinanceTabModule,
    projectContractAmountModuleAccess,
    projectBudgetAmountModuleAccess,
    module_access,
    isReadOnly,
    enoughProcurementAccess,
    enoughGanttModuleAccess,
    enoughClientModuleAccess,
    enoughReportModuleAccess,
    enoughDocumentTabAccess,
    enoughTabFileAccess,
    enoughTabScheduleAccess,
    timeTabNoAccess,
  };
};
