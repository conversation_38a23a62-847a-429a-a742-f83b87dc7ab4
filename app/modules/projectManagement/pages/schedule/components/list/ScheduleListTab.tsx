import { AgGridReact } from "ag-grid-react";
import { useCallback, useMemo, useRef } from "react";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { useScheduleAppSelector } from "../../redux/store";
import { ICellRendererParams } from "ag-grid-community";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { Number, sanitizeString } from "~/helpers/helper";
import { CellClickedEvent } from "ag-grid-community";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { useTranslation } from "~/hook";
import { AvatarGroup } from "~/shared/components/atoms/avatarGroup";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";

const ScheduleListTab = () => {
  const gridRef = useRef<AgGridReact | null>(null);
  const { _t } = useTranslation();
  const { listGanttTasks } = useScheduleAppSelector(
    (state) => state.scheduleDash
  );

  const buildPathData = (flatRows: TGanttTaskTypes[]) => {
    const idMap = new Map(flatRows.map((item) => [item.id?.toString(), item]));
    return flatRows.map((item) => {
      const path = [];
      let current = item;

      while (current) {
        path.unshift(`${current.id}_${current.text}`);
        if (!current.parent || Number(current.parent) === 0) break;
        current = idMap.get(current.parent?.toString()) as TGanttTaskTypes;
      }

      return { ...item, path };
    });
  };

  const processedRowData = useMemo(
    () => buildPathData(listGanttTasks),
    [listGanttTasks]
  );

  const assigneeCell = useCallback((users: IGanttTaskContact[]) => {
    return (
      <>
        {users.length ? (
          <span>
            {users
              ?.map((u) =>
                _t(
                  HTMLEntities.decode(
                    sanitizeString(u.assigned_to_name_only || u.assignee_name)
                  )
                )
              )
              ?.join(", ")}
          </span>
        ) : (
          <div className="text-center table-tooltip-text">-</div>
        )}
      </>
      // <>
      //   {users?.length > 0 ? (
      //     <AvatarGroup
      //       max={{
      //         count: 1,
      //         style: {
      //           color: "#223558",
      //           backgroundColor: "#ECF1F9",
      //         },
      //       }}
      //       size={24}
      //       className="flex justify-center"
      //       prefixCls="multi-avatar-scroll"
      //     >
      //       {users?.map((user, index: number) => {
      //         const dName = HTMLEntities.decode(
      //           sanitizeString(user.assignee_name)
      //         );

      //         const assignedToNameOnlyName = HTMLEntities.decode(
      //           sanitizeString(user.assigned_to_name_only)
      //         );

      //         return (
      //           <div
      //             key={index}
      //             className={`flex items-center ${
      //               index === 0 ? "" : "gap-2 py-0.5 px-1"
      //             }`}
      //           >
      //             {index === 0 ? (
      //               <Tooltip title={dName} placement="top">
      //                 <div>
      //                   <AvatarProfile
      //                     user={{
      //                       name: dName,
      //                       image: user?.user_image ?? "",
      //                     }}
      //                     iconClassName="text-[11px] font-semibold"
      //                   />
      //                 </div>
      //               </Tooltip>
      //             ) : (
      //               <Tooltip title={dName} placement="top">
      //                 <div className="p-1 flex items-center gap-1">
      //                   <AvatarProfile
      //                     user={{
      //                       name: dName,
      //                       image: user?.user_image ?? "",
      //                     }}
      //                     iconClassName="text-[11px] font-semibold"
      //                   />
      //                   <Typography className="max-w-[160px] truncate block">
      //                     {user.assigned_to_name_only?.trim() !== ""
      //                       ? assignedToNameOnlyName
      //                       : dName}
      //                   </Typography>
      //                 </div>
      //               </Tooltip>
      //             )}
      //           </div>
      //         );
      //       })}
      //     </AvatarGroup>
      //   ) : (
      //     <div className="text-center table-tooltip-text">-</div>
      //   )}
      // </>
    );
  }, []);

  const columnDefs = useMemo(
    () => [
      {
        headerName: "#",
        field: "custom_toggle",
        minWidth: 60,
        maxWidth: 60,
        cellClass: "ag-cell-left !cursor-pointer",
        headerClass: "ag-header-left",
        onCellClicked: (params: CellClickedEvent) => {
          const { node } = params;

          node.setExpanded(!node.expanded);

          setTimeout(() => {
            params.api.refreshCells({
              rowNodes: [node],
              columns: ["custom_toggle"],
              force: true,
            });
          }, 30);
        },

        cellRenderer: (params: ICellRendererParams) => {
          const { node } = params;

          const hasChildren =
            Array.isArray(node.childrenAfterGroup) &&
            node.childrenAfterGroup.length > 0;

          if (hasChildren) {
            return (
              <Tooltip title={_t(node.expanded ? "Hide" : "Show")}>
                <Typography className="table-tooltip-text text-center">
                  {node.expanded ? "−" : "+"}
                </Typography>
              </Tooltip>
            );
          }
          return null;
        },
        suppressMovable: false,
        suppressMenu: true,
      },
      {
        headerName: "WBS",
        field: "wbs",
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        minWidth: 130,
      },
      {
        headerName: "Project/Task",
        field: "text",
        minWidth: 150,
        flex: 1,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        cellRenderer: (params: ICellRendererParams<TGanttTaskTypes>) => {
          const { data } = params;
          const text = _t(HTMLEntities.decode(sanitizeString(data?.text)));

          return (
            <Tooltip title={text}>
              <Typography className="table-tooltip-text text-center">
                {text ? text : <div className="text-center">-</div>}
              </Typography>
            </Tooltip>
          );
        },
        cellRendererParams: {
          suppressCount: true,
        },
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
      },
      {
        headerName: "Employee",
        field: "task_user_name",
        minWidth: 130,
        flex: 1,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left !cursor-pointer",
        suppressMovable: false,
        suppressMenu: true,
        cellRenderer: (params: ICellRendererParams<TGanttTaskTypes>) => {
          const { data } = params;
          return assigneeCell(data?.task_users ?? []);
        },
      },
      {
        headerName: "Assigned Contractor",
        field: "assignee_name",
        minWidth: 130,
        flex: 1,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left !cursor-pointer",
        suppressMovable: false,
        suppressMenu: true,
        cellRenderer: (params: ICellRendererParams<TGanttTaskTypes>) => {
          const { data } = params;
          return assigneeCell(data?.assignees_to ?? []);
        },
      },
      {
        headerName: "Duration",
        field: "duration",
        minWidth: 130,
        maxWidth: 130,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        suppressMovable: false,
        suppressMenu: true,
      },
      {
        headerName: "Scheduled Date",
        field: "start_date_only",
        minWidth: 130,
        maxWidth: 130,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        suppressMovable: false,
        suppressMenu: true,
      },
      {
        headerName: "End Date",
        field: "end_date_only",
        minWidth: 130,
        maxWidth: 130,
        suppressMovable: false,
        suppressMenu: true,
      },
    ],
    []
  );

  return (
    <div className="ag-theme-alpine">
      <StaticTable
        ref={gridRef}
        className="static-table"
        rowData={processedRowData}
        columnDefs={columnDefs}
        groupDisplayType="custom"
        treeData={true}
        getDataPath={(data) => data.path}
        noRowsOverlayComponent={() => (
          <NoRecords
            image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
          />
        )}
        getRowClass={(params) => {
          return !params.data?.parent || Number(params.data?.parent) === 0
            ? "ag-task-parent-row"
            : "";
        }}
      />
    </div>
  );
};

export default ScheduleListTab;
