import { lazy, Suspense, useEffect } from "react";
import {
  useScheduleAppDispatch,
  useScheduleAppSelector,
} from "../../redux/store";
import { fetchGanttScheduleTasks } from "~/redux/action/scehdulerAction";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { Spin } from "~/shared/components/atoms/spin";

const GanttChartView = lazy(
  () =>
    import(
      "~/shared/components/organisms/schedulerGanttChart/GanttChartView.client"
    )
);

const ScheduleGanttTab = () => {
  const dispatch = useScheduleAppDispatch();

  const currentModule = getCurrentMenuModule();

  const { module_key = 0 } = currentModule || {};

  const { isGanttDataLoading, isGanttDataLoaded } = useScheduleAppSelector(
    (state) => state.proSchedule
  );

  useEffect(() => {
    if (module_key && !isGanttDataLoaded) {
      dispatch(
        fetchGanttScheduleTasks({
          project: "",
          module_key: module_key?.toString() || "",
        })
      );
    }
  }, [module_key, isGanttDataLoaded]);

  return (
    <div className="h-full">
      <Suspense
        fallback={
          <Spin className="w-full h-[calc(100dvh-320px)] flex items-center justify-center" />
        }
      >
        {isGanttDataLoading ? (
          <Spin className="w-full h-[calc(100dvh-320px)] flex items-center justify-center" />
        ) : (
          <GanttChartView />
        )}
      </Suspense>
    </div>
  );
};

export default ScheduleGanttTab;
