import { handleApiCatchError } from "~/helpers/helper";
import { scheduleRoutes } from "~/route-services/schedule.route";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";

export const updateScheduleProjects = async (
  paramsData: IUpdateScheduleProjectsApiParams
) => {
  try {
    const data = await getWebWorkerApiParams({
      otherParams: {
        ...paramsData,
      },
    });

    const response = (await webWorkerApi({
      url: scheduleRoutes.update_schedule_projects,
      method: "post",
      data: data,
    })) as IGetScheduleTasksApiRes;
    return response;
  } catch (error) {
    return handleApiCatchError(error as Error);
  }
};

export const updateProjectStartEndDate = async (
  paramsData: IUpdateProStartEndDateApiParams
) => {
  try {
    const data = await getWebWorkerApiParams({
      otherParams: {
        ...paramsData,
      },
    });

    const response = (await webWorkerApi({
      url: scheduleRoutes.update_project_dates,
      method: "post",
      data: data,
    })) as DefaultResponse;
    return response;
  } catch (error) {
    return handleApiCatchError(error as Error);
  }
};

export const updateWeekendDays = async (paramsData: { option: string }) => {
  try {
    const data = await getWebWorkerApiParams({
      otherParams: {
        ...paramsData,
      },
    });

    const response = (await webWorkerApi({
      url: scheduleRoutes.update_weekend_config,
      method: "post",
      data: data,
    })) as DefaultResponse;
    return response;
  } catch (error) {
    return handleApiCatchError(error as Error);
  }
};
