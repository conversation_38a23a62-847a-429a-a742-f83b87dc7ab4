import { createSlice } from "@reduxjs/toolkit";

const initialState: IScheduleSliceInitState = {
  activeTab: "gantt",
  listGanttTasks: [],
};

export const scheduleSlice = createSlice({
  name: "scheduleDash",
  initialState,
  reducers: {
    setActiveTab: (state, action) => {
      state.activeTab = action.payload;
    },
    setListGanttTasks: (state, action) => {
      state.listGanttTasks = action.payload;
    },
  },
});

export const { setActiveTab, setListGanttTasks } = scheduleSlice.actions;

export default scheduleSlice.reducer;
