import { ReactNode } from "react";
import { Provider } from "react-redux";
import { store, useScheduleAppDispatch, useScheduleAppSelector } from "./store";
import { buildConnectedGanttProvider } from "~/context/buildConnectedGanttProvider";

const ScheduleStoreProvider = ({ children }: { children: ReactNode }) => {
  const ConnectorContextProvider = buildConnectedGanttProvider(
    () => useScheduleAppSelector((s) => s.proSchedule),
    useScheduleAppDispatch
  );

  return (
    <Provider store={store}>
      <ConnectorContextProvider>{children}</ConnectorContextProvider>
    </Provider>
  );
};

export default ScheduleStoreProvider;
