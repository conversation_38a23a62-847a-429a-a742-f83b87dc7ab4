import { configureStore } from "@reduxjs/toolkit";
import { useDispatch, useSelector } from "react-redux";
import scheduleDash from "./slices/scheduleSlice";
import schedulerSlice from "~/redux/slices/schedulerSlice";

export const store = configureStore({
  reducer: {
    scheduleDash: scheduleDash,
    proSchedule: schedulerSlice,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export const useScheduleAppDispatch = useDispatch.withTypes<AppDispatch>();
export const useScheduleAppSelector = useSelector.withTypes<RootState>();
