import { useEffect, useState } from "react";
import { useParams } from "@remix-run/react";

// atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { AddButton } from "~/shared/components/molecules/addButton";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { CidbItemDrawer } from "~/shared/components/molecules/cidbItemDrawer";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";

// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

// AG Grid
import { GridOptions, ValueSetterParams } from "ag-grid-community";

// Hooks and helpers
import { useTranslation } from "~/hook";
import { replaceDOMParams, sanitizeString } from "~/helpers/helper";

// Redux action, slice and store
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import {
  deleteEquipmentsTimecardDetail,
  getDLMaterialItemsApi,
  updateDLMaterialDetailApi,
} from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import {
  resetDLMaterial,
  resetDLMaterialItemsAct,
} from "~/modules/projectManagement/pages/dailyLogs/redux/slices/materialsSlice";
import { qtyNumberCheck } from "~/shared/utils/helper/common";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";
import { apiRoutes } from "~/route-services/routes";
import { ColDef } from "ag-grid-community";

const MaterialItemUsedCard = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const { id: DLId }: RouteParams = useParams();
  const dispatch = useAppDLDispatch();
  const { details }: IDLDetailsInitialState = useAppDLSelector(
    (state) => state.dailyLogDetails
  );
  const [isOpenSelectMaterial, setIsOpenSelectMaterial] =
    useState<boolean>(false);
  const [itemTypes, setItemTypes] = useState<IItemTypes[]>([]);
  const [subMaterial, setSubMaterial] = useState<Partial<ICIDBMaterial[]>>([]);
  const [initialSubMaterial, setInitialSubMaterial] = useState<
    Partial<ICIDBMaterial[]>
  >([]);
  const { materialsData, isMaterialTabLoading }: IDLMaterialsInitialState =
    useAppDLSelector((state) => state.dailyLogMaterials);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);
  const [selectedMaterialUsediId, setSelectedMaterialUsediId] =
    useState<number>();

  useEffect(() => {
    if (materialsData?.length) {
      const jobsiteDataArr: Partial<ICIDBMaterial[]> = [];

      materialsData.forEach((data: IMaterialsData) => {
        jobsiteDataArr.push({
          name: data.name || "",
          unit: data.unit || "",
          notes: data.notes || "",
          material_id: data.item_primary_id || "",
          item_type: data.item_type || "",
          reference_item_id: data.item_primary_id || "",
        });
      });
      setSubMaterial(jobsiteDataArr);
      setInitialSubMaterial(jobsiteDataArr);
    } else {
      setSubMaterial([]);
      setInitialSubMaterial([]);
    }
  }, [materialsData]);

  useEffect(() => {
    const fetchCompanyItems = async () => {
      if (details?.projectId) {
        try {
          const data = await getWebWorkerApiParams({
            otherParams: details?.projectId,
          });

          const response = (await webWorkerApi({
            url: apiRoutes.COMMON.get_company_items,
            method: "post",
            data: data,
          })) as IStatusRes;

          setItemTypes(response?.data as unknown as IItemTypes[]);
        } catch (error) {
          console.error(error);
        }
      }
    };

    fetchCompanyItems();
  }, [details?.projectId]);

  const updateCellValueApi = async (
    params: ValueSetterParams,
    fieldName: keyof IMaterialsData
  ) => {
    const { data, node, oldValue, newValue } = params;
    try {
      const formData: IDeliveryUsedFormData = {
        materials: [
          {
            quantity: data?.quantity || "",
            notes: data?.notes || "",
            itemName: data?.name,
            unit: data?.unit || "",
            itemId: data?.item_id?.toString(),
          },
        ],
      };
      if (
        fieldName === "quantity" &&
        newValue != null &&
        newValue.toString().includes(".")
      ) {
        formData.materials[0][fieldName] = newValue.toFixed(2);
      } else {
        formData.materials[0][fieldName] = newValue == null ? "" : newValue;
      }

      if (fieldName === "notes") {
        formData.materials[0][fieldName] = newValue.trim();
      }
      const response = (await updateDLMaterialDetailApi({
        ...formData,
        logId: DLId,
      })) as IMaterialUsedAddUpRes;

      if (response?.success) {
        dispatch(resetDLMaterial());
      } else {
        notification.error({
          description:
            response?.message || _t("Something went wrong. Please try again."),
        });
        if (node) {
          node.setData({
            ...data,
            [fieldName]: oldValue,
          });
        }
      }
    } catch (e) {
      notification.error({
        description: _t("Error fetching"),
      });
      if (node) {
        node.setData({
          ...data,
          [fieldName]: oldValue,
        });
      }
    }
  };

  const handleSaveMaterial = async (data: ICIDBMaterial[]) => {
    const materialUsedFrm: IDLMaterialUsed[] = [];

    const removedItemIds = materialsData
      .filter(
        (jobItem) =>
          !data.some((item) => item.material_id == jobItem.material_id)
      )
      .map((item) => Number(item.item_id));

    if (removedItemIds.length) {
      const deleteRes = (await deleteEquipmentsTimecardDetail({
        logId: Number(DLId),
        itemId: removedItemIds,
      })) as IDLPeopleDetailsUpdateApiRes;
      if (!deleteRes?.success) {
        notification.error({
          description: deleteRes?.message,
        });
      }
    }

    const addedItems = data.filter(
      (item) =>
        !materialsData.some(
          (jobItem) => jobItem.material_id == item.material_id
        )
    );

    if (addedItems.length) {
      data.forEach((item) => {
        if (
          item?.material_id &&
          !materialsData.some(
            (jobItem) => jobItem.material_id == item?.material_id
          )
        ) {
          materialUsedFrm.push({
            quantity: 0,
            notes: item?.notes || "",
            itemPrimaryId: item?.material_id?.toString() || "",
            unit: item?.unit?.toString() || "",
            itemName: item?.name || "",
            itemId: 0,
          });
        }
      });

      const addSubsMaterialRes = (await updateDLMaterialDetailApi({
        materials: materialUsedFrm,
        logId: Number(DLId),
      })) as IMaterialUsedAddUpRes;

      if (addSubsMaterialRes?.success) {
        setIsOpenSelectMaterial(false);
      } else {
        notification.error({
          description: addSubsMaterialRes?.message,
        });
      }
    }

    const resApi = (await getDLMaterialItemsApi({
      id: DLId || "",
    })) as IDLMaterialsApiRes;

    if (resApi?.success) {
      dispatch(
        resetDLMaterialItemsAct({
          data: resApi?.data?.materialsData,
        })
      );
    }
  };

  const handleDeleteMaterialDeli = async () => {
    if (!isDeleting) {
      setIsDeleting(true);
      const deleteRes = (await deleteEquipmentsTimecardDetail({
        logId: Number(DLId),
        itemId: [selectedMaterialUsediId || 0],
      })) as IDLPeopleDetailsUpdateApiRes;

      if (deleteRes?.success) {
        const resApi = (await getDLMaterialItemsApi({
          id: DLId || "",
        })) as IDLMaterialsApiRes;

        if (resApi?.success) {
          dispatch(
            resetDLMaterialItemsAct({
              data: resApi?.data?.materialsData,
            })
          );
        }
      } else {
        notification.error({
          description: deleteRes?.message,
        });
      }
      setIsDeleting(false);
      setIsDeleteConfirmOpen(false);
    }
  };

  const handleDragAndDrop = async (items: IMaterialsData[]) => {
    const tempItems = items?.map((row: IMaterialsData, index: number) => ({
      quantity: row.quantity,
      notes: row.notes || "",
      itemPrimaryId: row.material_id || "",
      unit: row.unit || "",
      name: row.name || "",
      itemId: row.item_id || "",
      logItemNo: (index + 1).toString(),
    }));

    const addSubsMaterialRes = (await updateDLMaterialDetailApi({
      materials: tempItems,
      logId: Number(DLId),
    })) as IMaterialUsedAddUpRes;

    if (addSubsMaterialRes?.success) {
      dispatch(resetDLMaterial());
    } else {
      notification.error({
        description: addSubsMaterialRes?.message,
      });
    }
  };

  const gridOptions: GridOptions = {
    onRowDragEnd: function (event) {
      if (isReadOnly) {
        return;
      }
      // ag grid community provide "RowNode" But not working that's why use any
      const { node, overIndex } = event as { node: any; overIndex: number };
      if (!gridOptions.api || !node) return;

      const rowData: IMaterialsData[] = [];
      gridOptions.api.forEachNode((node) => rowData.push(node.data));
      rowData.splice(overIndex, 0, rowData.splice(node.rowIndex, 1)[0]);
      handleDragAndDrop(rowData);
    },
  };

  const suppressKeyboardEvent: ColDef<
    CostItemTableCellRenderer["data"]
  >["suppressKeyboardEvent"] = (params) => {
    const { event, api } = params;

    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
      event.preventDefault();
      event.stopPropagation();
      return true;
    }

    if (event.key === "Enter") {
      event.preventDefault();
      event.stopPropagation();

      // Call valueSetter or custom logic for saving the value
      api.stopEditing(); // Stop editing to trigger valueSetter
      return true;
    }

    return true;
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Material Items Used")}
        headerProps={{
          containerClassName: "!flex-row !items-center",
        }}
        iconProps={{
          icon: "fa-solid fa-file-lines",
          containerClassName:
            "bg-[linear-gradient(180deg,#81bbfc1a_0%,#33a9ff1a_100%)]",
          id: "material_item_used_file_lines",
          colors: ["#81BBFC", "#33A9FF"],
        }}
        headerRightButton={
          <AddButton
            onClick={() => {
              if (isReadOnly) {
                return;
              }
              setIsOpenSelectMaterial(true);
            }}
            disabled={isReadOnly}
          >
            {_t("Item")}
          </AddButton>
        }
        children={
          <div className="pt-2">
            {isMaterialTabLoading ? (
              <Spin className="w-full h-10 flex items-center justify-center" />
            ) : (
              <div className="ag-theme-alpine">
                <StaticTable
                  className="static-table"
                  rowDragManaged={!isReadOnly}
                  gridOptions={gridOptions}
                  columnDefs={[
                    {
                      headerName: "",
                      field: "move",
                      minWidth: 35,
                      maxWidth: 35,
                      suppressMenu: true,
                      cellStyle: { textAlign: "center" },
                      rowDrag: true,
                      cellClass:
                        "ag-cell-center ag-move-cell custom-move-icon-set",
                      cellRenderer: () => {
                        return (
                          <div className="w-6 h-6 flex items-center justify-center absolute top-1 left-1 !z-0 opacity-100">
                            <FontAwesomeIcon
                              className="w-4 h-4 text-[#4b5a76]"
                              icon="fa-solid fa-grip-dots"
                            />
                          </div>
                        );
                      },
                    },
                    {
                      headerName: _t("Name"),
                      field: "name",
                      minWidth: 230,
                      flex: 2,
                      headerClass: "ag-header-left",
                      cellClass: "ag-cell-left",
                      resizable: true,
                      suppressMovable: false,
                      suppressMenu: true,
                      cellRenderer: (params: IMatrialUsedTableCellRenderer) => {
                        const materialName = replaceDOMParams(
                          sanitizeString(params?.data?.name)
                        );
                        return materialName ? (
                          <Tooltip title={materialName}>
                            <Typography className="table-tooltip-text">
                              {materialName}
                            </Typography>
                          </Tooltip>
                        ) : (
                          "-"
                        );
                      },
                    },
                    {
                      headerName: _t("QTY"),
                      field: "quantity",
                      minWidth: 100,
                      maxWidth: 100,
                      suppressMovable: false,
                      suppressMenu: true,
                      headerClass: "ag-header-right",
                      cellClass: "ag-cell-right quantity-column",
                      suppressKeyboardEvent,
                      editable: !isReadOnly,
                      cellEditor: "agNumberCellEditor",
                      valueGetter: (params: IMatrialUsedTableCellRenderer) =>
                        params?.data?.quantity,
                      valueSetter: (params: ValueSetterParams) => {
                        if (params && params.node) {
                          let nVal = params.newValue;

                          if (!nVal || nVal == null) {
                            const updatedData = {
                              ...params.data,
                              quantity: "",
                            };
                            params.node.setData(updatedData);
                            updateCellValueApi(params, "quantity");
                            return false;
                          }
                          const checkNum = qtyNumberCheck(nVal);
                          const integerPartLength = nVal
                            .toString()
                            .split(".")[0].length;

                          if (integerPartLength > 6 || !checkNum) {
                            notification.error({
                              description:
                                "Quantity should be less than or equal to 6 digits.",
                            });
                            return false;
                          }
                          if (nVal.toString().includes(".")) {
                            nVal = nVal.toFixed(2);
                          }

                          const updatedData = {
                            ...params.data,
                            quantity: nVal,
                          };
                          params.node.setData(updatedData);
                          updateCellValueApi(params, "quantity");
                        }
                        return true;
                      },
                      cellRenderer: (params: IMatrialUsedTableCellRenderer) => {
                        const quantityUnit =
                          Number(params?.data.quantity) % 1 === 0
                            ? Number(params?.data.quantity).toFixed(0)
                            : formatter(
                                Number(params?.data.quantity).toFixed(2)
                              ).value || 0;
                        const tooltipCont = `${quantityUnit} ${
                          params?.data.quantity !== null
                            ? params?.data?.unit
                              ? ` / ${params?.data?.unit}`
                              : ""
                            : ""
                        }`;
                        return tooltipCont ? (
                          <Tooltip title={tooltipCont}>
                            <Typography className="table-tooltip-text">
                              {quantityUnit}
                            </Typography>
                          </Tooltip>
                        ) : (
                          "-"
                        );
                      },
                    },
                    {
                      headerName: "",
                      field: "",
                      minWidth: 100,
                      maxWidth: 100,
                      cellClass: "qty-unit-column",
                      suppressMovable: false,
                      suppressMenu: true,
                      cellRenderer: (params: IMatrialUsedTableCellRenderer) => {
                        const unitCode = `${
                          params?.data.quantity !== null
                            ? params?.data?.unit
                              ? ` /${params?.data?.unit}`
                              : ""
                            : ""
                        }`;

                        return unitCode ? (
                          <Typography className="table-tooltip-text">
                            {unitCode}
                          </Typography>
                        ) : (
                          "-"
                        );
                      },
                    },
                    {
                      headerName: _t("Description"),
                      field: "notes",
                      minWidth: 230,
                      flex: 2,
                      headerClass: "ag-header-left",
                      cellClass: "ag-cell-left",
                      resizable: true,
                      suppressMovable: false,
                      suppressMenu: true,
                      editable: !isReadOnly,

                      valueGetter: (params: IMatrialUsedTableCellRenderer) =>
                        params?.data?.notes?.trim(),
                      valueSetter: (params: ValueSetterParams) => {
                        if (params && params.node) {
                          const updatedData = {
                            ...params.data,
                            notes: params.newValue.trim(),
                          };
                          params.node.setData(updatedData);
                          updateCellValueApi(params, "notes");
                        }
                        return true;
                      },
                      cellRenderer: (params: IMatrialUsedTableCellRenderer) => {
                        const materialDec = HTMLEntities.decode(
                          sanitizeString(params.data.notes)
                        );
                        return materialDec ? (
                          <Tooltip title={materialDec}>
                            <Typography className="table-tooltip-text">
                              {materialDec}
                            </Typography>
                          </Tooltip>
                        ) : (
                          "-"
                        );
                      },
                    },
                    {
                      headerName: "",
                      field: "",
                      maxWidth: 45,
                      minWidth: 45,
                      suppressMenu: true,
                      cellRenderer: (params: IMatrialUsedTableCellRenderer) => {
                        return (
                          <div className="flex items-center gap-2 justify-center">
                            <ButtonWithTooltip
                              tooltipTitle={_t("Delete")}
                              disabled={isReadOnly}
                              tooltipPlacement="top"
                              icon="fa-regular fa-trash-can"
                              onClick={() => {
                                setSelectedMaterialUsediId(
                                  Number(params?.data?.item_id) || 0
                                );
                                setIsDeleteConfirmOpen(true);
                              }}
                            />
                          </div>
                        );
                      },
                    },
                  ]}
                  rowData={materialsData || []}
                  stopEditingWhenCellsLoseFocus={true}
                  noRowsOverlayComponent={() => (
                    <NoRecords
                      image={`${window.ENV.CDN_URL}assets/images/no-records-material-items-used.svg`}
                    />
                  )}
                />
              </div>
            )}
          </div>
        }
      />
      {isOpenSelectMaterial && (
        <CidbItemDrawer
          closeDrawer={() => {
            setIsOpenSelectMaterial(false);
          }}
          options={[CFConfig.material_key]}
          singleSelecte={false}
          itemTypes={itemTypes}
          addItem={(data) => {
            setSubMaterial(data as ICIDBMaterial[]);
            handleSaveMaterial(data as ICIDBMaterial[]);
          }}
          openSendEmailSidebar={isOpenSelectMaterial}
          data={subMaterial as ICIDBMaterial[]}
          initialSubMaterial={initialSubMaterial as ICIDBMaterial[]}
          cidbModuleVIseIdAndValue={{
            [CFConfig.material_key]: {
              id: CFConfig.material_module_id,
              value: CFConfig.material_key,
            },
          }}
        />
      )}
      {isDeleteConfirmOpen && (
        <ConfirmModal
          isOpen={isDeleteConfirmOpen}
          modaltitle={_t("Delete")}
          description={_t("Are you sure you want to delete this Item?")}
          modalIcon="fa-regular fa-trash-can"
          isLoading={isDeleting}
          onAccept={handleDeleteMaterialDeli}
          onDecline={() => {
            setIsDeleteConfirmOpen(false);
          }}
          onCloseModal={() => {
            setIsDeleteConfirmOpen(false);
          }}
        />
      )}
    </>
  );
};

export default MaterialItemUsedCard;
