import { useState } from "react";
import isEmpty from "lodash/isEmpty";
import { useParams } from "@remix-run/react";

//Helper
import {
  getDefaultStatuscolor,
  replaceDOMParams,
  sanitizeString,
} from "~/helpers/helper";
import { useTranslation } from "~/hook";

//Redux
import { useAppDLSelector } from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { resetDash } from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dashboardSlice";
import { useAppDLDispatch } from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { getDLNotesAction } from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import { resetJobsiteInspectionAct } from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dLNotesSlice";

// Common
import Iframe from "~/components/page/$url/iframe";

// atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Spin } from "~/shared/components/atoms/spin";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";

// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { AddButton } from "~/shared/components/molecules/addButton";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
// Other
import { Inspection } from "~/modules/projectManagement/pages/dailyLogs/components/tab/sidebar/inspection";
import { routes } from "~/route-services/routes";
import { faArrowUpRightFromSquare } from "@fortawesome/pro-regular-svg-icons";
import CustomContextMenu from "~/shared/components/molecules/CustomContextMenu";

const JobsiteInspectionsCard = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const { _t } = useTranslation();
  const { id: DLId }: RouteParams = useParams();
  const dispatch = useAppDLDispatch();
  const [inspectionOpen, setInspectionOpen] = useState<boolean>(false);
  const [isSafetyMettingLoding, setIsSafetyMettingLoding] =
    useState<boolean>(false);
  const [selectedId, setSelectedId] = useState<number>(0);
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    visible: boolean;
  }>({ x: 0, y: 0, visible: false });

  // Add context menu items
  const contextMenuItems = [
    {
      label: "Open in new tab",
      icon: faArrowUpRightFromSquare,
      onClick: () => {
        const url = `${routes.MANAGE_INSPECTION.url}?action=new&project=${details?.projectId}`;
        window.open(url, "_blank", "noopener,noreferrer");
      },
    },
  ];
  const { isNoteTabLoading, noteData }: IDLNotesInitialState = useAppDLSelector(
    (state) => state.dailyLogNotes
  );

  const { details }: IDLDetailsInitialState = useAppDLSelector(
    (state) => state.dailyLogDetails
  );

  const handleGetSafetyMeetings = async () => {
    setIsSafetyMettingLoding(true);
    const resApi = (await getDLNotesAction({
      id: DLId || "",
    })) as IDLNotesApiRes;
    setIsSafetyMettingLoding(false);
    if (resApi.success) {
      dispatch(resetJobsiteInspectionAct(resApi.data));
    }
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Jobsite Inspections")}
        headerProps={{
          containerClassName: "!flex-row !items-center",
        }}
        iconProps={{
          icon: "fa-solid fa-user-tie",
          containerClassName:
            "bg-[linear-gradient(180deg,#ADD1001a_0%,#7B920A1a_100%)]",
          id: "jobsite_inspections_user_helmet_safety",
          colors: ["#ADD100", "#7B920A"],
        }}
        headerRightButton={
          <AddButton
            disabled={isReadOnly || isNoteTabLoading}
            onClick={(e: any) => {
              if (isReadOnly) {
                return;
              }
              const url = `${routes.MANAGE_INSPECTION.url}?action=new&project=${details?.projectId}`;
              if (
                Number(window.ENV.ENABLE_ALL_CLICK) &&
                (e?.ctrlKey || e?.metaKey)
              ) {
                window.open(url, "_blank", "noopener,noreferrer");
              } else {
                setInspectionOpen(true);
              }
            }}
            {...(Number(+window.ENV.ENABLE_ALL_CLICK)
              ? {
                  onMouseDown: (e: React.MouseEvent) => {
                    if (e.button === 1) {
                      e.preventDefault();
                      const url = `${routes.MANAGE_INSPECTION.url}?action=new&project=${details?.projectId}`;
                      window.open(url, "_blank", "noopener,noreferrer");
                    }
                  },
                  onContextMenu: (e: React.MouseEvent) => {
                    e.preventDefault();
                    setContextMenu({
                      x: e.clientX,
                      y: e.clientY,
                      visible: true,
                    });
                  },
                }
              : {})}
          >
            {_t("Inspection")}
          </AddButton>
        }
        children={
          <div className="pt-2">
            {isNoteTabLoading || isSafetyMettingLoding ? (
              <Spin className="w-full h-10 flex items-center justify-center" />
            ) : (
              <div className="ag-theme-alpine">
                <StaticTable
                  className="static-table"
                  columnDefs={[
                    {
                      headerName: _t("Inspection Type"),
                      field: "inspection_type",
                      minWidth: 150,
                      flex: 2,
                      headerClass: "ag-header-left",
                      cellClass: "ag-cell-left",
                      resizable: true,
                      suppressMovable: false,
                      suppressMenu: true,
                      cellRenderer: ({
                        data,
                      }: IDLJobsiteInspectionTablCellRenderer) => {
                        const inspectionType = replaceDOMParams(
                          sanitizeString(data?.inspection_type)
                        );
                        return inspectionType ? (
                          <Tooltip title={inspectionType}>
                            <Typography className="table-tooltip-text">
                              {inspectionType}
                            </Typography>
                          </Tooltip>
                        ) : (
                          "-"
                        );
                      },
                    },
                    {
                      headerName: "Status",
                      field: "inspection_status_name",
                      maxWidth: 120,
                      minWidth: 120,
                      suppressMovable: false,
                      suppressMenu: true,
                      headerClass: "ag-header-center",
                      cellRenderer: ({
                        data,
                      }: IDLJobsiteInspectionTablCellRenderer) => {
                        const statusName = data?.inspection_status_name;
                        const { default_color } = data;
                        const { color, textColor } = getDefaultStatuscolor(
                          default_color || ""
                        );
                        return statusName
                          ? !isEmpty(statusName) && (
                              <Tooltip title={statusName}>
                                <div className="text-center">
                                  <Tag
                                    color={color}
                                    style={{
                                      color: `${textColor || ""}`,
                                    }}
                                    className={`${
                                      textColor === "" && "!text-primary-900"
                                    } mx-auto text-13 type-badge common-tag`}
                                  >
                                    {statusName}
                                  </Tag>
                                </div>
                              </Tooltip>
                            )
                          : "-";
                      },
                    },
                    {
                      headerName: "",
                      field: "",
                      maxWidth: 80,
                      minWidth: 80,
                      suppressMenu: true,
                      cellRenderer: ({
                        data,
                      }: IDLJobsiteInspectionTablCellRenderer) => {
                        return (
                          <div className="flex items-center gap-2 justify-center">
                            <ButtonWithTooltip
                              tooltipTitle={_t("View")}
                              tooltipPlacement="top"
                              icon="fa-solid fa-eye"
                              onClick={() => {
                                setSelectedId(data.inspection_id);
                              }}
                            />
                          </div>
                        );
                      },
                    },
                  ]}
                  rowData={noteData?.inspections || []}
                  noRowsOverlayComponent={() => (
                    <NoRecords
                      image={`${window.ENV.CDN_URL}assets/images/no-records-contact.svg`}
                    />
                  )}
                />
              </div>
            )}
          </div>
        }
      />
      {/* {inspectionOpen && (
        <Inspection
          isOpen={inspectionOpen}
          isViewOnly={isReadOnly}
          onClose={setInspectionOpen}
          onAddedRec={(data) => {
            setInspectionOpen(false);
          }}
        />
      )} */}

      {Boolean(selectedId) && (
        <CommonModal
          isOpen={Boolean(selectedId)}
          widthSize="98%"
          draggable={false}
          onCloseModal={() => {
            setSelectedId(0);
          }}
          modalBodyClass="p-0"
          header={{
            title: "",
            closeIcon: true,
          }}
        >
          <div className="p-5">
            {selectedId ? (
              <Iframe
                path={`${routes.MANAGE_INSPECTION.url}/${selectedId}`}
                isRemixUrl={true}
                otherParams={`?iframecall=1`}
                onIframeClose={() => {
                  handleGetSafetyMeetings();
                  setSelectedId(0);
                  dispatch(resetDash());
                }}
              />
            ) : (
              "Something went wrong!"
            )}
          </div>
        </CommonModal>
      )}

      {inspectionOpen && (
        <CommonModal
          isOpen={inspectionOpen}
          widthSize="98%"
          draggable={false}
          onCloseModal={() => {
            setInspectionOpen(false);
          }}
          modalBodyClass="p-0"
          header={{
            title: "",
            closeIcon: true,
          }}
        >
          <div className="p-5">
            <Iframe
              path={`${routes.MANAGE_INSPECTION.url}`}
              isRemixUrl={true}
              otherParams={`?iframecall=1&action=new&arrival_date=${details?.arrivalDate}&project=${details?.projectId}`}
              onIframeClose={() => {
                handleGetSafetyMeetings();
                setInspectionOpen(false);
              }}
            />
          </div>
        </CommonModal>
      )}
      <CustomContextMenu
        position={{ x: contextMenu.x, y: contextMenu.y }}
        visible={contextMenu.visible}
        items={contextMenuItems}
        onClose={() => setContextMenu((c) => ({ ...c, visible: false }))}
      />
    </>
  );
};

export default JobsiteInspectionsCard;
