import { useState } from "react";
import { useParams } from "@remix-run/react";

// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Spin } from "~/shared/components/atoms/spin";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";

// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { AddButton } from "~/shared/components/molecules/addButton";
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";

// Other
import Iframe from "~/components/page/$url/iframe";
import { routes } from "~/route-services/routes";

// Hooks and Helper
import { useTranslation } from "~/hook";
import { getDefaultStatuscolor, sanitizeString } from "~/helpers/helper";

// Redux store, action and slice
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { getDLNotesAction } from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import { addSafetyMeetingAct } from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dLNotesSlice";
import { useGModules } from "~/zustand";
import { defaultConfig } from "~/data";
import { faArrowUpRightFromSquare } from "@fortawesome/pro-regular-svg-icons";
import CustomContextMenu from "~/shared/components/molecules/CustomContextMenu";

const SafetyMeetingsCard = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const { _t } = useTranslation();
  const { id: DLId }: RouteParams = useParams();
  const { getGModuleByKey } = useGModules();
  const safetyMeeting: GModule | undefined = getGModuleByKey(
    defaultConfig.safety_meeting_module
  );
  const dispatch = useAppDLDispatch();

  const { isNoteTabLoading, noteData }: IDLNotesInitialState = useAppDLSelector(
    (state) => state.dailyLogNotes
  );
  const { details }: IDLDetailsInitialState = useAppDLSelector(
    (state) => state.dailyLogDetails
  );

  const [selectedId, setSelectedId] = useState<number>(0);
  const [selectedProId, setSelectedProId] = useState<number>(0);
  const [isSafetyMettingLoding, setIsSafetyMettingLoding] =
    useState<boolean>(false);
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    visible: boolean;
  }>({ x: 0, y: 0, visible: false });

  // Add context menu items
  const contextMenuItems = [
    {
      label: "Open in new tab",
      icon: faArrowUpRightFromSquare,
      onClick: () => {
        const url = `${routes.MANAGE_SAFETY_MEETINGS.url}?action=new&project=${details?.projectId}`;
        window.open(url, "_blank", "noopener,noreferrer");
      },
    },
  ];
  const handleGetSafetyMeetings = async () => {
    setIsSafetyMettingLoding(true);
    const resApi = (await getDLNotesAction({
      id: DLId || "",
    })) as IDLNotesApiRes;
    setIsSafetyMettingLoding(false);
    if (resApi.success) {
      dispatch(addSafetyMeetingAct(resApi.data));
    }
  };
  return (
    <>
      <CrudCommonCard
        headerTitle={_t(`${safetyMeeting?.plural_name ?? "Safety Meetings"}`)}
        headerProps={{
          containerClassName: "!flex-row !items-center",
        }}
        iconProps={{
          icon: "fa-solid fa-user-helmet-safety",
          containerClassName:
            "bg-[linear-gradient(180deg,#F275941a_0%,#85489E1a_100%)]",
          id: "safety_meeting_table",
          colors: ["#F27594", "#85489E"],
        }}
        headerRightButton={
          <AddButton
            disabled={isReadOnly || isNoteTabLoading}
            onClick={(e: any) => {
              if (isReadOnly) {
                return;
              }
              const url = `${routes.MANAGE_SAFETY_MEETINGS.url}?action=new&project=${details?.projectId}`;
              if (
                Number(window.ENV.ENABLE_ALL_CLICK) &&
                (e?.ctrlKey || e?.metaKey)
              ) {
                window.open(url, "_blank", "noopener,noreferrer");
              } else {
                setSelectedProId(Number(details?.projectId));
              }
            }}
            {...(Number(+window.ENV.ENABLE_ALL_CLICK)
              ? {
                  onMouseDown: (e: React.MouseEvent) => {
                    if (e.button === 1) {
                      e.preventDefault();
                      const url = `${routes.MANAGE_SAFETY_MEETINGS.url}?action=new&project=${details?.projectId}`;
                      window.open(url, "_blank", "noopener,noreferrer");
                    }
                  },
                  onContextMenu: (e: React.MouseEvent) => {
                    e.preventDefault();
                    setContextMenu({
                      x: e.clientX,
                      y: e.clientY,
                      visible: true,
                    });
                  },
                }
              : {})}
          >
            {_t(
              `${
                HTMLEntities.decode(
                  sanitizeString(safetyMeeting?.module_name)
                ) ?? "Safety Meeting"
              }`
            )}
          </AddButton>
        }
        children={
          <div className="pt-2">
            {isNoteTabLoading || isSafetyMettingLoding ? (
              <Spin className="w-full h-10 flex items-center justify-center" />
            ) : (
              <div className="ag-theme-alpine">
                <StaticTable
                  className="static-table"
                  columnDefs={[
                    {
                      headerName: _t("Safety Topic"),
                      field: "safety_topic",
                      minWidth: 150,
                      flex: 1,
                      headerClass: "ag-header-left",
                      cellClass: "ag-cell-left",
                      resizable: true,
                      suppressMovable: false,
                      suppressMenu: true,
                      cellRenderer: ({
                        data,
                      }: IDLSafetyMeetingTablCellRenderer) => {
                        const meetingTitle = HTMLEntities.decode(
                          sanitizeString(data?.title)
                        );
                        return meetingTitle ? (
                          <Tooltip title={meetingTitle}>
                            <Typography className="table-tooltip-text">
                              {meetingTitle}
                            </Typography>
                          </Tooltip>
                        ) : (
                          "-"
                        );
                      },
                    },
                    {
                      headerName: _t("Leader"),
                      field: "leader_name",
                      minWidth: 100,
                      maxWidth: 100,
                      suppressMovable: false,
                      suppressMenu: true,
                      cellStyle: { textAlign: "center" },
                      headerClass: "ag-header-center",
                      cellRenderer: ({
                        data,
                      }: IDLSafetyMeetingTablCellRenderer) => {
                        const leaderName = HTMLEntities.decode(
                          sanitizeString(data?.leader_name)
                        );
                        const leaderImage = data?.leader_image;
                        return leaderName ? (
                          <Tooltip title={leaderName}>
                            <div>
                              <AvatarProfile
                                user={{
                                  name: leaderName,
                                  image: leaderImage,
                                }}
                                className="m-auto"
                                iconClassName="text-[11px] font-semibold"
                              />
                            </div>
                          </Tooltip>
                        ) : (
                          "-"
                        );
                      },
                    },
                    {
                      headerName: "# of Attendees",
                      field: "assignee_count",
                      maxWidth: 180,
                      minWidth: 180,
                      suppressMovable: false,
                      suppressMenu: true,
                      cellStyle: { textAlign: "right" },
                      headerClass: "ag-header-right",
                    },
                    {
                      headerName: "",
                      field: "",
                      maxWidth: 150,
                      minWidth: 150,
                      suppressMenu: true,
                    },
                    {
                      headerName: "Meeting Status",
                      field: "meeting_status_name",
                      maxWidth: 120,
                      minWidth: 120,
                      suppressMovable: false,
                      suppressMenu: true,
                      headerClass: "ag-header-center",
                      cellRenderer: ({
                        data,
                      }: IDLSafetyMeetingTablCellRenderer) => {
                        const {
                          meeting_status_name: meetingStatus,
                          default_color,
                        } = data;
                        const { color, textColor } = getDefaultStatuscolor(
                          default_color || ""
                        );
                        return meetingStatus ? (
                          <Tooltip title={meetingStatus}>
                            <div className="text-center">
                              <Tag
                                color={color}
                                style={{
                                  color: `${textColor || ""}`,
                                }}
                                className={`${
                                  textColor === "" && "!text-primary-900"
                                } mx-auto text-13 type-badge common-tag`}
                              >
                                {meetingStatus}
                              </Tag>
                            </div>
                          </Tooltip>
                        ) : (
                          "-"
                        );
                      },
                    },
                    {
                      headerName: "",
                      field: "",
                      maxWidth: 80,
                      minWidth: 80,
                      suppressMenu: true,
                      cellRenderer: ({
                        data,
                      }: IDLSafetyMeetingTablCellRenderer) => {
                        return (
                          <div className="flex items-center gap-2 justify-center">
                            <ButtonWithTooltip
                              tooltipTitle={_t("View")}
                              tooltipPlacement="top"
                              icon="fa-solid fa-eye"
                              onClick={() => {
                                setSelectedId(data.meeting_id);
                              }}
                            />
                          </div>
                        );
                      },
                    },
                  ]}
                  rowData={noteData?.safetyMeeting || []}
                  noRowsOverlayComponent={() => (
                    <NoRecords
                      image={`${window.ENV.CDN_URL}assets/images/no-records-safety-meetings.svg`}
                    />
                  )}
                />
              </div>
            )}
          </div>
        }
      />
      {Boolean(selectedId) && (
        <CommonModal
          isOpen={Boolean(selectedId)}
          widthSize="98%"
          draggable={false}
          onCloseModal={() => {
            setSelectedId(0);
          }}
          modalBodyClass="p-0"
          header={{
            title: "",
            closeIcon: true,
          }}
        >
          <div className="p-5">
            {selectedId ? (
              <Iframe
                path={`${routes.MANAGE_SAFETY_MEETINGS.url}/${selectedId}`}
                isRemixUrl={true}
                otherParams={`?iframecall=1`}
                onIframeClose={() => {
                  handleGetSafetyMeetings();
                  setSelectedId(0);
                }}
              />
            ) : (
              "Something went wrong!"
            )}
          </div>
        </CommonModal>
      )}

      {Boolean(selectedProId) && (
        <CommonModal
          isOpen={Boolean(selectedProId)}
          widthSize="98%"
          draggable={false}
          onCloseModal={() => {
            setSelectedProId(0);
          }}
          modalBodyClass="p-0"
          header={{
            title: "",
            closeIcon: true,
          }}
        >
          <div className="p-5">
            {selectedProId ? (
              <Iframe
                path={`${routes.MANAGE_SAFETY_MEETINGS.url}`}
                isRemixUrl={true}
                otherParams={`?iframecall=1&action=new&project=${selectedProId}&meeting_date=${details?.arrivalDate}&meeting_time=${details?.arrivalTime}`}
                onIframeClose={() => {
                  handleGetSafetyMeetings();
                  setSelectedProId(0);
                }}
              />
            ) : (
              "Something went wrong!"
            )}
          </div>
        </CommonModal>
      )}
      <CustomContextMenu
        position={{ x: contextMenu.x, y: contextMenu.y }}
        visible={contextMenu.visible}
        items={contextMenuItems}
        onClose={() => setContextMenu((c) => ({ ...c, visible: false }))}
      />
    </>
  );
};

export default SafetyMeetingsCard;
