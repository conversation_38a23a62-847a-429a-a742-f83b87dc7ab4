import { useParams } from "@remix-run/react";
import { useEffect, useState, useMemo } from "react";
import delay from "lodash/delay";
// Hooks + Redux + helper
import { getGConfig, useGModules } from "~/zustand";
import TimeCardData from "./details/items-list/TimeCardData";
import ItemsFilter from "./details/ItemsFilter";
import InvoiceCalcCard from "./details/InvoiceCalcCard";
import { formatAmount, Number, sanitizeString } from "~/helpers/helper";
import { formatTaxRate, getStatusForField } from "~/shared/utils/helper/common";
import { updateIVDetailApi } from "~/modules/financials/pages/invoice/redux/action";
import { updateIVDetail } from "~/modules/financials/pages/invoice/redux/slices/iVDetailsSlice";
import { useTranslation } from "~/hook";
import {
  useAppIVDispatch,
  useAppIVSelector,
} from "~/modules/financials/pages/invoice/redux/store";
import {
  fetchInvoiceItemsList,
  fetchInvoiceItemsToAdjust,
  fetchSOVItemsList,
  fetchTimeAndMaterialItemsList,
} from "~/modules/financials/pages/invoice/redux/action/InvoiceItemsActions";
import { getCostCode } from "~/redux/action/getCostCodeAction";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import EquipmentLogsItems from "./details/items-list/EquipmentLogsItems";
import BillItem from "./details/items-list/BillItem";
import ChangeOrderBillItems from "./details/items-list/ChangeOrderBillItems";
import ChangeOrderSOVItems from "./details/items-list/ChangeOrderItems";
import ExpensesItems from "./details/items-list/ExpensesItems";
import PurchaseOrderItems from "./details/items-list/PurchaseOrderItems";
import { EquipmentLogItem, ExpenseItem, InvoiceItem } from "./sidebar";
import {
  IIVDetailsField,
  INVOICE_ITEM,
  upadateIVFieldStatus,
} from "~/modules/financials/pages/invoice/utils/constants";
import ItemsToBill from "./details/items-list/ItemsToBill";
import ApprovedEstimatesItems from "./details/items-list/ApprovedEstimatesItems";
import WorkOrderItems from "./details/items-list/WorkOrderItems";
import LumpSumItems from "./details/items-list/LumpSumItems";
import EstimateItems from "./details/items-list/EstimateItems";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { InvoiceTabsItem } from "./sidebar/invoiceTabsItem";
import OldUIItemsList from "./details/items-list/OldUIItemsList";
import { defaultConfig } from "~/data";
import { updateTimeCardGroupingFilter } from "../../redux/slices/InvoiceItemsSlice";
import SubContractItems from "./details/items-list/SubContractItems";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { CF_PLANS } from "~/data/pages";

const DetailsTab = () => {
  const { _t } = useTranslation();
  const params = useParams();
  const dispatch = useAppIVDispatch();
  const { module_access, module_id, module_singular_name }: GConfig =
    getGConfig();
  const { formatter } = useCurrencyFormatter();
  const { getGModuleByKey } = useGModules();

  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const { module_group_id = 0 } = appSettings || {};

  const allowRetainagePayment = useMemo(() => {
    return ![CF_PLANS.basic_v5, CF_PLANS.standard_v5_planID].includes(
      module_group_id
    );
  }, [appSettings?.module_group_id]);

  const [invoiceItem, setInvoiceItem] = useState<boolean>(false);
  const [isItemTabsOpen, setIsItemTabsOpen] = useState<boolean>(false);
  const [isAddInvoiceItem, setIsAddInvoiceItem] = useState<boolean>(false);
  const [equipmentLogItem, setEquipmentLogItem] = useState<boolean>(false);
  const [expenseItem, setExpenseItem] = useState<boolean>(false);
  const [itemTabsType, setItemTabsType] = useState<string>("");
  const [itemToBeUpdate, setItemToBeUpdate] =
    useState<IInvoiceItemData>(INVOICE_ITEM);
  const [equipmentItemToBeUpdate, setEquipmentItemToBeUpdate] =
    useState<IInvoiceItemData>(INVOICE_ITEM);
  const [expenseItemToBeUpdate, setExpenseItemToBeUpdate] =
    useState<IInvoiceItemData>(INVOICE_ITEM);
  const [inputValues, setInputValues] =
    useState<Partial<IIVDetails>>(IIVDetailsField);
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(upadateIVFieldStatus);
  const [invoiceRecordList, setInvoiceRecordList] = useState<
    IInvoiceItemData[]
  >([]);
  const [sumItemsRecordList, setSumItemsRecordList] = useState<
    IInvoiceItemData[]
  >([]);

  const invoiceDetails = useAppIVSelector(
    (state) => state.invoiceDetails.details
  );

  const { invoiceItemsLoading, invoiceItems, invoiceItemsToAdjust } =
    useAppIVSelector((state) => state.proInvoiceItemList);

  const { taxDetailsList }: ITaxDetailsInitialState = useAppIVSelector(
    (state) => state.taxDetails
  );
  const { filter }: IIVCommonInitialState = useAppIVSelector(
    (state) => state.invoiceCommonData
  );

  const isReadOnly = useMemo(
    () => module_access === "read_only",
    [module_access]
  );

  useEffect(() => {
    setInputValues(invoiceDetails);
  }, [invoiceDetails.user_id]);

  useEffect(() => {
    dispatch(updateTimeCardGroupingFilter(invoiceDetails.tm_tc_group_by));
  }, [invoiceDetails.tm_tc_group_by]);

  useEffect(() => {
    if (params.id) {
      if (invoiceDetails.is_new_tm_invoice === 1) {
        dispatch(
          fetchInvoiceItemsList({
            id: Number(params.id),
            data_for: "all",
            need_section: 1,
            is_separate_estimate_sections: false,
          })
        );
      } else {
        dispatch(
          fetchInvoiceItemsList({
            id: Number(params.id),
            data_for: "all",
            need_section: 0,
            is_separate_estimate_sections: false,
            is_separate_change_order_sections: true,
          })
        );
      }
    }
  }, [params.id, invoiceDetails.is_new_tm_invoice]);

  useEffect(() => {
    if (invoiceDetails.project_id) {
      dispatch(
        fetchTimeAndMaterialItemsList({
          filter: {
            project: invoiceDetails.project_id.toString(),
            start_date: "",
            end_date: "",
          },
        })
      );

      dispatch(
        fetchSOVItemsList({
          project_id: invoiceDetails.project_id,
          module_id: module_id,
          need_section: 1,
        })
      );

      dispatch(
        fetchInvoiceItemsToAdjust({
          project_id: invoiceDetails.project_id.toString(),
        })
      );
    }

    if (invoiceDetails.project_id) {
      dispatch(getCostCode({ project_id: invoiceDetails.project_id }));
    }
  }, [invoiceDetails.project_id, invoiceDetails.invoice_id]);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) =>
        prevState.map((item) =>
          item.field === field ? { ...item, status: status } : item
        )
      );
    }
  };

  const handleUpdateField = async (data: IIVDetailFields) => {
    const field = Object.keys(data)[0] as keyof IIVDetails;
    setInputValues({ ...inputValues, ...data });
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    let newData = data;
    const updateRes = (await updateIVDetailApi({
      invoice_id: invoiceDetails?.invoice_id || "",
      ...newData,
    })) as IIVDetailsUpdateApiRes;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      dispatch(updateIVDetail(data));
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({ ...inputValues, [field]: invoiceDetails[field] });
      notification.error({
        description: updateRes?.message,
      });
    }

    // Remove success icon after 3 some second that's why use delay function.
    delay(() => {
      handleChangeFieldStatus({
        field: field,
        status: "button",
        action: "API",
      });
    }, 3000);
  };

  // Calculate total

  const calculateTotal = (
    calData: any,
    filter: string[],
    taxPercentage: number
  ) => {
    let grandTotal = 0;
    let totalTax = 0;

    let data: any = {
      // ...calData,
      items: [
        {
          section_id: 15424000,
          section_name: "items",
          items: calData?.items,
        },
      ],
      bill_sections: calData?.bill_sections,
      change_order_sections: calData?.change_order_sections,
      purchase_order_sections: calData?.purchase_order_sections,
      work_order_sections: calData?.work_order_sections,
      estimate_section: calData?.estimate_section,
      import_from_estimate_section: calData?.import_from_estimate_section,
      equip_log_section: [
        {
          section_id: 15424000,
          section_name: "equip_log_section",
          items: calData?.equip_log_section,
        },
      ],
      expense_section: [
        {
          section_id: 15424001,
          section_name: "expense_section",
          items: calData?.expense_section,
        },
      ],
      timecard_section: [
        {
          section_id: 15424002,
          section_name: "timecard_section",
          items: calData?.timecard_section,
        },
      ],
      itemsSOV: [
        {
          section_id: 15424002,
          section_name: "itemsSOV",
          items: calData?.itemsSOV,
        },
      ],

      // Old sections
      billSection: [
        {
          section_id: 15424002,
          section_name: "billSection",
          items: calData?.billSection,
        },
      ],
      changeOrderSection: [
        {
          section_id: 15424002,
          section_name: "changeOrderSection",
          items: calData?.changeOrderSection,
        },
      ],
      SOVBudgetItems: [
        {
          section_id: 15424002,
          section_name: "SOVBudgetItems",
          items: calData?.SOVBudgetItems,
        },
      ],
      workOrderSection: [
        {
          section_id: 15424002,
          section_name: "workOrderSection",
          items: calData?.workOrderSection,
        },
      ],
      equipmentLogSection: [
        {
          section_id: 15424002,
          section_name: "equipmentLogSection",
          items: calData?.equipmentLogSection,
        },
      ],
      expenseSection: [
        {
          section_id: 15424002,
          section_name: "expenseSection",
          items: calData?.expenseSection,
        },
      ],
      purchaseOrderSection: [
        {
          section_id: 15424002,
          section_name: "purchaseOrderSection",
          items: calData?.purchaseOrderSection,
        },
      ],
      timeCardSection: [
        {
          section_id: 15424002,
          section_name: "timeCardSection",
          items: calData?.timeCardSection,
        },
      ],
    };

    const calculateSectionTotal = (
      sections: {
        items: {
          total: string;
          apply_global_tax: number;
          item_type_key: string;
        }[];
      }[],
      applyTax: boolean,
      sectionName: string
    ) => {
      return sections?.reduce(
        (acc, section) => {
          const sectionResult = section?.items?.reduce(
            (itemAcc, item) => {
              const includeItem =
                sectionName === "items" ||
                sectionName === "import_from_estimate_section"
                  ? filter.length == 0 || filter.includes(item?.item_type_key)
                  : true;
              if (includeItem) {
                const itemTotal = Number(item.total);
                const itemTax =
                  applyTax && item.apply_global_tax === 1
                    ? (itemTotal * taxPercentage) / 100
                    : 0;

                const itemTotalTax =
                  applyTax &&
                  item.apply_global_tax === 1 &&
                  invoiceDetails?.taxable_retainage != "1" &&
                  !isNaN(taxPercentage) &&
                  !!Number(invoiceDetails?.invoice_retainage) &&
                  (sectionName === "items" ||
                    sectionName === "import_from_estimate_section")
                    ? itemTotal
                    : 0;

                itemAcc.total += itemTotal;
                itemAcc.tax += itemTax;
                itemAcc.totalTax += itemTotalTax;
              }

              return itemAcc;
            },
            { total: 0, tax: 0, totalTax: 0 }
          );

          acc.total += sectionResult?.total || 0;
          acc.totalTax += sectionResult?.totalTax || 0;

          if (
            !!Number(invoiceDetails?.invoice_retainage) &&
            !isNaN(taxPercentage) &&
            invoiceDetails?.taxable_retainage != "1" &&
            taxPercentage > 0 &&
            (sectionName === "items" ||
              sectionName === "import_from_estimate_section")
          ) {
            let nonTaxableRetainage =
              (acc.totalTax * Number(invoiceDetails?.invoice_retainage)) / 100;
            const newTax = Number(
              ((acc.totalTax - nonTaxableRetainage) * taxPercentage) / 100
            ).toFixed(0);
            acc.tax = Number(newTax) || 0;
          } else {
            acc.tax += sectionResult?.tax || 0;
          }

          return acc;
        },
        { total: 0, tax: 0, totalTax: 0 }
      );
    };

    // Calculate totals and taxes for other sections
    const sectionKeys: any[] =
      invoiceDetails.is_new_tm_invoice == 0
        ? [
            "items",
            "itemsSOV",
            "import_from_estimate_section",
            "change_order_sections",
            "estimate_section",
            // Old sections
            "billSection",
            // "changeOrderSection",
            "SOVChangeOrderSection",
            "SOVBudgetItems",
            "equipmentLogSection",
            "expenseSection",
            "purchaseOrderSection",
            "timeCardSection",
            "workOrderSection",
          ]
        : [
            "items",
            "itemsSOV",
            "import_from_estimate_section",
            "bill_sections",
            "change_order_sections",
            "equip_log_section",
            "purchase_order_sections",
            "expense_section",
            "timecard_section",
            "work_order_sections",
            "estimate_section",
          ];

    sectionKeys.forEach((key) => {
      const sections = data[key] as any[];
      const sectionResult = calculateSectionTotal(
        sections?.length > 0 ? sections : [],
        true,
        key
      );
      grandTotal += sectionResult?.total;
      totalTax += sectionResult?.tax;
    });

    return { grandTotal, totalTax };
  };

  const invoiceItemsList = useMemo(() => {
    //! The same code is used in the file below. If you want to make any changes to this code, apply the same changes to the file below:
    // app\modules\financials\pages\invoice\components\tab\details\items-list\LumpSumItems.tsx

    const BILL_MODULE_ID = 78;
    const ESTIMATE_MODULE_ID = 15;
    const is_new_tm_invoice = invoiceDetails?.is_new_tm_invoice;

    const items = invoiceItems?.items?.filter((obj: IInvoiceItemData) => {
      const estimateId = Number(obj.estimate_id || 0);
      const projectBudgetItemId = Number(obj.project_budget_item_id || 0);
      const changeOrderId = Number(obj.change_order_id || 0);
      const workOrderId = Number(obj.work_order_id || 0);
      const isRetainageItem = obj.is_retainage_item?.toString() === "1";
      const referenceModuleId = Number(obj.reference_module_id || 0);
      const referencePrimaryId = Number(obj.reference_primary_id || 0);
      const referenceModuleItemId = Number(obj.reference_module_item_id || 0);

      if (
        (estimateId > 0 && projectBudgetItemId > 0) ||
        changeOrderId > 0 ||
        workOrderId > 0 ||
        projectBudgetItemId > 0 ||
        isRetainageItem ||
        (referenceModuleId === BILL_MODULE_ID &&
          (!obj.reference_module_item_id || referenceModuleItemId === 0)) ||
        (is_new_tm_invoice === 1 &&
          referenceModuleId === BILL_MODULE_ID &&
          (!!obj.reference_module_item_id || referenceModuleItemId > 0)) ||
        (referencePrimaryId > 0 &&
          referenceModuleId !== ESTIMATE_MODULE_ID &&
          referenceModuleId !== BILL_MODULE_ID) ||
        isRetainageItem
      ) {
        return false; // DON'T RENDER
      }
      return true; // Include this item
    });
    const itemsSOV = invoiceItems?.items?.filter(
      (item) =>
        item.change_order_id == 0 &&
        item.work_order_id == 0 &&
        Number(item.project_budget_item_id) > 0
    );
    return { items, itemsSOV: is_new_tm_invoice == 1 ? itemsSOV : [] };
  }, [JSON.stringify(invoiceItems?.items), invoiceDetails?.is_new_tm_invoice]);

  const invoiceItemsBySections: IInvoiceItemsBySection = useMemo(() => {
    let billSection: IInvoiceItemData[] = [];
    let changeOrderSection: IInvoiceItemData[] = [];
    let equipmentLogSection: IInvoiceItemData[] = [];
    let expenseSection: IInvoiceItemData[] = [];
    let purchaseOrderSection: IInvoiceItemData[] = [];
    let timeCardSection: IInvoiceItemData[] = [];
    let SOVBudgetItems: IInvoiceItemData[] = [];
    let workOrderSection: IInvoiceItemData[] = [];
    let SOVChangeOrderSection: IInvoiceItemData[] = [];
    let lumpSumItems: IInvoiceItemData[] = [];
    let retainageItem: IInvoiceItemData[] = [];

    invoiceItems.items.map((item) => {
      if (
        Number(item.change_order_id) > 0 &&
        Number(item.is_tm_item === 0) &&
        Number(item.project_budget_item_id) > 0
      ) {
        SOVChangeOrderSection.push(item);
      } else if (Number(item.work_order_id) > 0) {
        workOrderSection.push(item);
      } else if (Number(item.project_budget_item_id) > 0) {
        SOVBudgetItems.push(item);
      } else if (
        invoiceDetails.is_new_tm_invoice === 0 &&
        item.reference_module_id === defaultConfig.purchase_order_module_id
      ) {
        purchaseOrderSection.push(item);
      } else if (item.reference_module_id === defaultConfig.expense_module_id) {
        expenseSection.push(item);
      } else if (
        item.reference_module_id ===
          getGModuleByKey(defaultConfig.change_order_module)?.module_id &&
        Number(item.change_order_id) > 0 &&
        item.is_tm_item === 1
      ) {
        changeOrderSection.push(item);
      } else if (
        item.reference_module_id ===
        getGModuleByKey(defaultConfig.time_card_module)?.module_id
      ) {
        timeCardSection.push(item);
      } else if (item.reference_module_id === defaultConfig.bill_module_id) {
        billSection.push(item);
      } else if (
        item.reference_module_id ===
        getGModuleByKey(defaultConfig.equipment_log_module)?.module_id
      ) {
        equipmentLogSection.push(item);
      } else if (
        Number(item.is_retainage_item) > 0 &&
        Number(item.total) !== 0
      ) {
        retainageItem.push(item);
      } else {
        lumpSumItems.push(item);
      }
    });

    return {
      billSection: billSection,
      changeOrderSection: changeOrderSection,
      equipmentLogSection: equipmentLogSection,
      expenseSection: expenseSection,
      purchaseOrderSection: purchaseOrderSection,
      timeCardSection: timeCardSection,
      SOVBudgetItems: SOVBudgetItems,
      workOrderSection: workOrderSection,
      SOVChangeOrderSection: SOVChangeOrderSection,
      lumpSumItems: lumpSumItems,
      retainageItem: retainageItem,
    };
  }, [invoiceItems?.items]);

  const selectedTax = useMemo(() => {
    const taxData = taxDetailsList?.find(
      (i) => i.tax_id?.toString() == invoiceDetails?.tax_id
    );
    return {
      ...taxData,
      label: `${HTMLEntities.decode(
        sanitizeString(taxData?.tax_name || "")
      )} (${formatTaxRate(taxData?.tax_rate)}%)`,
    };
  }, [invoiceDetails?.tax_id, JSON.stringify(taxDetailsList)]);

  const cOTotalData = useMemo(() => {
    let hasNonTmItem = false;
    let coTotal = 0;

    coTotal = invoiceItems?.change_order_sections?.reduce((sum, section) => {
      const sectionTotal = section.items.reduce((sectionSum, item) => {
        if (item.is_tm_item == 0) {
          hasNonTmItem = true;
          return sectionSum + Number(item.total);
        }
        return sectionSum;
      }, 0);
      return sum + sectionTotal;
    }, 0);

    const retainageHold =
      (-1 * (coTotal * Number(invoiceDetails?.invoice_retainage))) / 100;

    return { coTotal, retainageHold, hasNonTmItem };
  }, [
    JSON.stringify(invoiceItems?.change_order_sections),
    invoiceDetails?.invoice_retainage,
    invoiceDetails.is_new_tm_invoice,
  ]);

  // CalculateRetainage
  const estimateTotalData = useMemo(() => {
    const calculateTotal = (sections: any[]) =>
      sections?.reduce(
        (sum, section) =>
          sum +
          section.items.reduce(
            (sectionSum: number, item: any) => sectionSum + Number(item.total),
            0
          ),
        0
      );

    const estimateTotal = calculateTotal(invoiceItems?.estimate_section || []);

    let importEstimate = invoiceItems?.import_from_estimate_section?.length
      ? invoiceItems?.import_from_estimate_section
      : [];
    if (filter.length > 0) {
      importEstimate = importEstimate.map((section) => ({
        ...section,
        items: section.items.filter(
          (item) =>
            item?.item_type_key !== undefined &&
            filter.includes(item?.item_type_key)
        ),
      }));
    }

    const importTotal = calculateTotal(importEstimate);

    const itemsSOVTotal =
      invoiceDetails.is_new_tm_invoice == 1
        ? calculateTotal(
            invoiceItemsList?.itemsSOV?.length
              ? [{ items: invoiceItemsList?.itemsSOV }]
              : []
          )
        : 0;
    const sOVBudgetItemsTotal =
      invoiceDetails.is_new_tm_invoice == 0
        ? calculateTotal(
            invoiceItemsBySections?.SOVBudgetItems?.length
              ? [{ items: invoiceItemsBySections?.SOVBudgetItems }]
              : []
          )
        : 0;

    const combinedTotal =
      importTotal + estimateTotal + itemsSOVTotal + sOVBudgetItemsTotal;
    const retainageHold =
      (-1 * combinedTotal * Number(invoiceDetails?.invoice_retainage)) / 100;

    return { total: combinedTotal, retainageHold };
  }, [
    JSON.stringify(invoiceItems?.import_from_estimate_section),
    invoiceItems?.estimate_section,
    invoiceItemsList?.itemsSOV,
    invoiceItemsBySections?.SOVBudgetItems,
    invoiceDetails?.invoice_retainage,
    invoiceDetails.is_new_tm_invoice,
    filter,
  ]);

  const retainageData = useMemo(() => {
    return {
      retainage: Number(estimateTotalData?.retainageHold || "0"),
      holdRetainage: Number(
        invoiceDetails?.held_co_retainage == 1
          ? cOTotalData?.retainageHold
          : "0"
      ),
    };
  }, [estimateTotalData, cOTotalData, invoiceDetails?.held_co_retainage]);

  // Calculate total and total tax
  const finalTotal = useMemo(() => {
    const taxData = taxDetailsList?.find(
      (i) => i.tax_id?.toString() == invoiceDetails?.tax_id
    );

    const { grandTotal, totalTax } = calculateTotal(
      {
        ...invoiceItems,
        ...invoiceItemsBySections,
        items: invoiceItemsList?.items,
        itemsSOV: invoiceItemsList?.itemsSOV,
        SOVBudgetItems: invoiceItemsBySections?.SOVBudgetItems,
      },
      filter,
      Number(taxData?.tax_rate || 0)
    );

    let subTotal = grandTotal;

    if (allowRetainagePayment) {
      subTotal += retainageData?.retainage + retainageData?.holdRetainage || 0;
    }

    let total = subTotal;
    if (invoiceDetails?.is_reversible_tax == "0") {
      total = subTotal + totalTax;
    }
    total = Math.round(total || 0);
    let balanceDue = total;
    if (Number(invoiceDetails?.payment_amount) > 0) {
      balanceDue = total - Number(invoiceDetails?.payment_amount);
    }
    // Update total in details redux
    dispatch(updateIVDetail({ total: total?.toString() }));
    return { subTotal, total, balanceDue, tax: totalTax };
  }, [
    JSON.stringify(invoiceItems),
    invoiceItemsBySections,
    JSON.stringify(invoiceItemsList),
    JSON.stringify(filter),
    JSON.stringify(taxDetailsList),
    selectedTax,
    retainageData,
    invoiceDetails?.tax_id,
    invoiceDetails?.is_reversible_tax,
    invoiceDetails?.payment_amount,
    invoiceDetails?.is_new_tm_invoice,
    allowRetainagePayment,
    appSettings?.module_group_id,
  ]);

  // Show Hide Total section
  const hasNonEmptyArray = useMemo(() => {
    const invoiceItemsData: any = {
      ...invoiceItems,
      ...invoiceItemsBySections,
      items: invoiceItemsList?.items,
      itemsSOV:
        invoiceDetails?.is_new_tm_invoice == 1
          ? invoiceItemsList?.itemsSOV
          : [],
    };

    const isSectionItemsEmpty = (sections: any) =>
      sections.every(
        (section: any) =>
          Array.isArray(section.items) && section.items.length === 0
      );

    const isSOVEmpty =
      isSectionItemsEmpty(invoiceItemsData?.change_order_sections) &&
      isSectionItemsEmpty(invoiceItemsData?.estimate_section) &&
      isSectionItemsEmpty(invoiceItemsData?.work_order_sections) &&
      Array.isArray(invoiceItemsData?.SOVBudgetItems) &&
      invoiceItemsData?.SOVBudgetItems?.length === 0;

    // Disabled project and Customer fields if SOV is not empty
    if (isSOVEmpty) {
      dispatch(updateIVDetail({ is_disabled_project: "0" }));
    } else {
      dispatch(updateIVDetail({ is_disabled_project: "1" }));
    }

    return (
      isSectionItemsEmpty(invoiceItemsData?.bill_sections) &&
      isSectionItemsEmpty(invoiceItemsData?.change_order_sections) &&
      isSectionItemsEmpty(invoiceItemsData?.estimate_section) &&
      isSectionItemsEmpty(invoiceItemsData?.import_from_estimate_section) &&
      isSectionItemsEmpty(invoiceItemsData?.purchase_order_sections) &&
      isSectionItemsEmpty(invoiceItemsData?.work_order_sections) &&
      Array.isArray(invoiceItemsData?.itemsSOV) &&
      invoiceItemsData?.itemsSOV?.length === 0 &&
      Array.isArray(invoiceItemsData?.items) &&
      invoiceItemsData.items?.length === 0 &&
      Array.isArray(invoiceItemsData?.equip_log_section) &&
      invoiceItemsData?.equip_log_section?.length === 0 &&
      Array.isArray(invoiceItemsData?.expense_section) &&
      invoiceItemsData?.expense_section?.length === 0 &&
      Array.isArray(invoiceItemsData?.timecard_section) &&
      invoiceItemsData?.timecard_section?.length === 0 &&
      Array.isArray(invoiceItemsData?.billSection) &&
      invoiceItemsData?.billSection?.length === 0 &&
      Array.isArray(invoiceItemsData?.changeOrderSection) &&
      invoiceItemsData?.changeOrderSection?.length === 0 &&
      Array.isArray(invoiceItemsData?.equipmentLogSection) &&
      invoiceItemsData?.equipmentLogSection?.length === 0 &&
      Array.isArray(invoiceItemsData?.expenseSection) &&
      invoiceItemsData?.expenseSection?.length === 0 &&
      Array.isArray(invoiceItemsData?.purchaseOrderSection) &&
      invoiceItemsData?.purchaseOrderSection?.length === 0 &&
      Array.isArray(invoiceItemsData?.timeCardSection) &&
      invoiceItemsData?.timeCardSection?.length === 0 &&
      Array.isArray(invoiceItemsData?.SOVBudgetItems) &&
      invoiceItemsData?.SOVBudgetItems?.length === 0 &&
      Array.isArray(invoiceItemsData?.workOrderSection) &&
      invoiceItemsData?.workOrderSection?.length === 0 &&
      Array.isArray(invoiceItemsData?.SOVChangeOrderSection) &&
      invoiceItemsData?.SOVChangeOrderSection?.length === 0 &&
      Array.isArray(invoiceItemsData?.lumpSumItems) &&
      invoiceItemsData?.lumpSumItems?.length === 0 &&
      Array.isArray(invoiceItemsData?.retainageItem) &&
      invoiceItemsData?.retainageItem?.length === 0
    );
  }, [
    JSON.stringify(invoiceItemsList),
    JSON?.stringify(invoiceItems),
    invoiceItemsBySections,
    invoiceDetails?.is_new_tm_invoice,
  ]);

  return (
    <>
      <div className="grid gap-2.5">
        <ItemsFilter
          invoiceItem={invoiceItem}
          setInvoiceItem={setInvoiceItem}
          setIsAddInvoiceItem={setIsAddInvoiceItem}
          isAddInvoiceItem={isAddInvoiceItem}
          finalTotal={finalTotal}
          setItemToBeUpdate={setItemToBeUpdate}
        />

        {invoiceItemsLoading ? (
          <Spin
            className={`w-full flex items-center justify-center ${
              window.ENV.PAGE_IS_IFRAME
                ? "2xl:h-[calc(100dvh-290px)] xl:h-[calc(100dvh-310px)] md:h-[calc(100dvh-400px)] h-[calc(100dvh-450px)]"
                : "2xl:h-[calc(100dvh-445px)] xl:h-[calc(100dvh-463px)] md:h-[calc(100dvh-550px)] h-[200px]"
            }`}
          />
        ) : (
          <>
            <LumpSumItems
              isReadOnly={isReadOnly}
              setIsAddInvoiceItem={setIsAddInvoiceItem}
              setIsItemTabsOpen={setIsItemTabsOpen}
              setItemToBeUpdate={setItemToBeUpdate}
              setInvoiceRecordList={setSumItemsRecordList}
              setItemTabsType={setItemTabsType}
            />

            {invoiceDetails.is_new_tm_invoice === 1 && (
              <ItemsToBill
                isReadOnly={
                  isReadOnly ||
                  invoiceDetails?.approval_type_key === "invoice_submitted" ||
                  invoiceDetails?.approval_type_key === "invoice_paid"
                }
              />
            )}

            {invoiceDetails.is_new_tm_invoice === 1 && (
              <ApprovedEstimatesItems
                isReadOnly={
                  isReadOnly ||
                  invoiceDetails?.approval_type_key === "invoice_submitted" ||
                  invoiceDetails?.approval_type_key === "invoice_paid"
                }
              />
            )}

            {invoiceDetails.is_new_tm_invoice === 1 && (
              <WorkOrderItems
                isReadOnly={
                  isReadOnly ||
                  invoiceDetails?.approval_type_key === "invoice_submitted" ||
                  invoiceDetails?.approval_type_key === "invoice_paid"
                }
              />
            )}

            {invoiceDetails.is_new_tm_invoice === 1 && (
              <EstimateItems
                isReadOnly={isReadOnly}
                setIsAddInvoiceItem={setIsAddInvoiceItem}
                setIsItemTabsOpen={setIsItemTabsOpen}
                setItemToBeUpdate={setItemToBeUpdate}
                setInvoiceRecordList={setInvoiceRecordList}
                selectedId={itemToBeUpdate?.item_id}
                setItemTabsType={setItemTabsType}
              />
            )}

            {invoiceDetails.is_new_tm_invoice === 0 && (
              <OldUIItemsList
                isReadOnly={isReadOnly}
                invoiceItem={invoiceItem}
                setInvoiceItem={setInvoiceItem}
                setItemToBeUpdate={setItemToBeUpdate}
                setEquipmentLogItem={setEquipmentLogItem}
                equipmentLogItem={equipmentLogItem}
                setEquipmentItemToBeUpdate={setEquipmentItemToBeUpdate}
                expenseItem={expenseItem}
                setExpenseItem={setExpenseItem}
                setExpenseItemToBeUpdate={setExpenseItemToBeUpdate}
                setIsAddInvoiceItem={setIsAddInvoiceItem}
                setIsItemTabsOpen={setIsItemTabsOpen}
                setInvoiceRecordList={setInvoiceRecordList}
                selectedId={itemToBeUpdate?.item_id}
                setItemTabsType={setItemTabsType}
              />
            )}

            {invoiceDetails.is_new_tm_invoice === 1 && (
              <BillItem
                isReadOnly={isReadOnly}
                invoiceItem={invoiceItem}
                setInvoiceItem={setInvoiceItem}
                setItemToBeUpdate={setItemToBeUpdate}
                setEquipmentLogItem={setEquipmentLogItem}
              />
            )}

            {invoiceDetails.is_new_tm_invoice === 1 && (
              <ChangeOrderSOVItems
                isReadOnly={
                  isReadOnly ||
                  invoiceDetails?.approval_type_key === "invoice_submitted" ||
                  invoiceDetails?.approval_type_key === "invoice_paid"
                }
              />
            )}

            {invoiceDetails.is_new_tm_invoice === 1 && (
              <ChangeOrderBillItems
                isReadOnly={isReadOnly}
                invoiceItem={invoiceItem}
                setInvoiceItem={setInvoiceItem}
                setItemToBeUpdate={setItemToBeUpdate}
                setEquipmentLogItem={setEquipmentLogItem}
              />
            )}

            {invoiceDetails.is_new_tm_invoice === 1 && (
              <EquipmentLogsItems
                isReadOnly={
                  isReadOnly ||
                  invoiceDetails?.approval_type_key === "invoice_submitted" ||
                  invoiceDetails?.approval_type_key === "invoice_paid"
                }
                equipmentLogItem={equipmentLogItem}
                setEquipmentLogItem={setEquipmentLogItem}
                setEquipmentItemToBeUpdate={setEquipmentItemToBeUpdate}
                setInvoiceItem={setInvoiceItem}
              />
            )}

            {invoiceDetails.is_new_tm_invoice === 1 && (
              <ExpensesItems
                isReadOnly={isReadOnly}
                expenseItem={expenseItem}
                setExpenseItem={setExpenseItem}
                setInvoiceItem={setInvoiceItem}
                setExpenseItemToBeUpdate={setExpenseItemToBeUpdate}
                setEquipmentLogItem={setEquipmentLogItem}
              />
            )}

            {invoiceDetails.is_new_tm_invoice === 1 && (
              <PurchaseOrderItems
                isReadOnly={isReadOnly}
                invoiceItem={invoiceItem}
                setInvoiceItem={setInvoiceItem}
                setItemToBeUpdate={setItemToBeUpdate}
                setEquipmentLogItem={setEquipmentLogItem}
              />
            )}

            {invoiceDetails.is_new_tm_invoice === 1 && (
              <SubContractItems
                isReadOnly={isReadOnly}
                invoiceItem={invoiceItem}
                setInvoiceItem={setInvoiceItem}
                setItemToBeUpdate={setItemToBeUpdate}
                setEquipmentLogItem={setEquipmentLogItem}
              />
            )}

            {invoiceDetails.is_new_tm_invoice === 1 && (
              <TimeCardData isReadOnly={isReadOnly} />
            )}

            <div className="grid lg:grid-cols-2 gap-2.5">
              <div></div>
              <div className="grid gap-2">
                {(invoiceItems?.estimate_section?.length > 0 ||
                  invoiceItemsList?.itemsSOV?.length > 0 ||
                  invoiceItems?.import_from_estimate_section?.length > 0) &&
                  allowRetainagePayment &&
                  !!invoiceDetails?.project_id &&
                  invoiceDetails?.project_id !== 0 &&
                  Number(invoiceDetails?.invoice_retainage || "") > 0 && (
                    <div className="bg-[#F9FACE] rounded py-1 px-2.5">
                      <div className="flex justify-between items-center">
                        <Tooltip
                          rootClassName="max-w-[350px]"
                          title={_t(
                            "Retainage can only be held on items imported into the project's Schedule of Values, from an Approved Estimate, SOV Other Items table or a Change Order (only when 'Hold Retainage on CO Items?' is selected). Line Items added to the invoice directly from a PO, Bill, or other source cannot have retainage held"
                          )}
                        >
                          <Typography className="inline-block text-13 font-medium">
                            {_t("Retainage withheld")}
                          </Typography>
                        </Tooltip>
                        <Typography className="text-primary-900 text-13 font-medium whitespace-nowrap">
                          {
                            formatter(
                              formatAmount(
                                (
                                  Number(
                                    estimateTotalData?.retainageHold || "0"
                                  ) / 100
                                ).toFixed(2)
                              )
                            ).value_with_symbol
                          }
                        </Typography>
                      </div>
                    </div>
                  )}

                {cOTotalData?.hasNonTmItem && (
                  <div className="bg-[#F9FACE] rounded py-1 px-2.5">
                    <div className="flex justify-between items-center">
                      <CustomCheckBox
                        className="gap-1.5 text-13 w-fit checkbox-padding-remove"
                        name="held_co_retainage"
                        onChange={(e: CheckboxChangeEvent) => {
                          handleUpdateField({
                            held_co_retainage: e.target?.checked ? "1" : "0",
                          });
                        }}
                        checked={invoiceDetails?.held_co_retainage == 1}
                        disabled={
                          getStatusForField(
                            loadingStatus,
                            "held_co_retainage"
                          ) === "loading" || isReadOnly
                        }
                        loadingProps={{
                          isLoading:
                            getStatusForField(
                              loadingStatus,
                              "held_co_retainage"
                            ) === "loading",
                          className: "bg-[#f9face] !top-0",
                        }}
                      >
                        {_t("Hold Retainage on CO items?")}
                      </CustomCheckBox>

                      <Typography className="text-primary-900 text-13 font-medium whitespace-nowrap">
                        {`${
                          formatter(
                            formatAmount(
                              (
                                Number(
                                  invoiceDetails?.held_co_retainage == 1
                                    ? cOTotalData?.retainageHold
                                    : "0"
                                ) / 100
                              ).toFixed(2)
                            )
                          ).value_with_symbol
                        }`}
                      </Typography>
                    </div>
                  </div>
                )}

                {!hasNonEmptyArray && (
                  <div className="py-3 px-[15px] common-card">
                    <InvoiceCalcCard
                      finalTotal={finalTotal}
                      selectedTax={selectedTax}
                      isReadOnly={isReadOnly}
                    />
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>

      {invoiceItem && (
        <InvoiceItem
          invoiceItem={invoiceItem}
          setInvoiceItem={setInvoiceItem}
          isViewOnly={
            isReadOnly ||
            invoiceDetails?.approval_type_key === "invoice_submitted" ||
            invoiceDetails?.approval_type_key === "invoice_paid"
          }
          formData={itemToBeUpdate}
          isAddInvoiceItem={isAddInvoiceItem}
          setIsAddInvoiceItem={setIsAddInvoiceItem}
        />
      )}

      {isItemTabsOpen && (
        <InvoiceTabsItem
          isOpen={isItemTabsOpen}
          onClose={setIsItemTabsOpen}
          isViewOnly={
            isReadOnly ||
            invoiceDetails?.approval_type_key === "invoice_submitted" ||
            invoiceDetails?.approval_type_key === "invoice_paid"
          }
          formData={itemToBeUpdate}
          isAddInvoiceItem={isAddInvoiceItem}
          setIsAddInvoiceItem={setIsAddInvoiceItem}
          setItemToBeUpdate={setItemToBeUpdate}
          allRecords={
            itemTabsType == "lumpSum" ? sumItemsRecordList : invoiceRecordList
          }
          itemTabsType={itemTabsType}
          moduleName={module_singular_name}
        />
      )}

      <EquipmentLogItem
        equipmentLogItem={equipmentLogItem}
        isViewOnly={
          isReadOnly ||
          invoiceDetails?.approval_type_key === "invoice_submitted" ||
          invoiceDetails?.approval_type_key === "invoice_paid"
        }
        setEquipmentLogItem={setEquipmentLogItem}
        formData={equipmentItemToBeUpdate}
      />

      <ExpenseItem
        expenseItem={expenseItem}
        setExpenseItem={setExpenseItem}
        isViewOnly={
          isReadOnly ||
          invoiceDetails?.approval_type_key === "invoice_submitted" ||
          invoiceDetails?.approval_type_key === "invoice_paid"
        }
        formData={expenseItemToBeUpdate}
      />
    </>
  );
};

export default DetailsTab;
