// atoms
import { <PERSON>lt<PERSON> } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Hook
import { useTranslation } from "~/hook";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Number, sanitizeString } from "~/helpers/helper";
import { useGModules } from "~/zustand";
import { useParams } from "@remix-run/react";
import { defaultConfig } from "~/data";
import { ValueSetterParams } from "ag-grid-community";
import {
  useAppIVDispatch,
  useAppIVSelector,
} from "~/modules/financials/pages/invoice/redux/store";
import {
  deleteInvoiceItems,
  updateInvoiceItem,
} from "~/modules/financials/pages/invoice/redux/action/InvoiceItemsActions";
import {
  deleteInvoiceItem,
  updateInvoiceItems,
} from "~/modules/financials/pages/invoice/redux/slices/InvoiceItemsSlice";
import { ColDef } from "ag-grid-community";

interface IIvOldEquipmentLogsItemsProps {
  equipmentLogItem: boolean;
  setEquipmentLogItem: React.Dispatch<React.SetStateAction<boolean>>;
  setInvoiceItem: React.Dispatch<React.SetStateAction<boolean>>;
  setEquipmentItemToBeUpdate: React.Dispatch<
    React.SetStateAction<IInvoiceItemData>
  >;
  isReadOnly: boolean;
  equipmentLogsItems: IInvoiceItemData[];
}

const OldEquipmentLogsItems = ({
  equipmentLogItem,
  setEquipmentLogItem,
  setInvoiceItem,
  setEquipmentItemToBeUpdate,
  isReadOnly,
  equipmentLogsItems,
}: IIvOldEquipmentLogsItemsProps) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const dispatch = useAppIVDispatch();
  const invoiceItems = useAppIVSelector(
    (state) => state.proInvoiceItemList.invoiceItems
  );
  const { details } = useAppIVSelector((state) => state.invoiceDetails);
  const params = useParams();
  const { getGModuleByKey } = useGModules();
  const [sectionTotal, setSectionTotal] = useState<number>(0);

  const [selectedItemToDelete, setSelectedItemToDelete] = useState<number>();
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [tableData, setTableData] = useState<IInvoiceItemData[]>([]);

  const iconByItemTypeName: { [key: string]: IFontAwesomeIconProps["icon"] } =
    useMemo(() => {
      return {
        item_equipment: "fa-regular fa-screwdriver-wrench",
        item_material: "fa-regular fa-block-brick",
        item_labour: "fa-regular fa-user-helmet-safety",
        item_sub_contractor: "fa-regular fa-file-signature",
        item_other: "fa-regular fa-boxes-stacked",
      };
    }, []);

  useEffect(() => {
    let total = 0;
    equipmentLogsItems.map((item) => {
      total += Number(item.total) || 0;
    });
    setSectionTotal(total);
  }, [equipmentLogsItems]);

  useEffect(() => {
    const data = equipmentLogsItems;
    const sortedData = [...data].sort(
      (a, b) => Number(a?.invoice_item_no) - Number(b.invoice_item_no)
    );
    setTableData(sortedData);
  }, [equipmentLogsItems]);

  const getTotalAmount = useCallback(
    (data: IInvoiceItemData, qty: string, unit_cost: string | number) => {
      const markup_amount = Number(data.markup || 0);
      const is_markup_percentage = Number(data.is_markup_percentage);

      const total: number = Number(qty) * Number(unit_cost);
      let mainTotal: number = 0;

      if (is_markup_percentage === 1) {
        const markup = (total * markup_amount) / 100;
        mainTotal = Number(markup) + Number(total);
      } else {
        mainTotal = markup_amount / 100;
      }
      return mainTotal;
    },
    []
  );

  const handleInLineEditing = async (newValue: IUpdateInvoiceItem) => {
    const itemToBeUpdate: IUpdateInvoiceItemsParams = {
      id: Number(params.id),
      items: [
        {
          ...newValue,
        },
      ],
    };

    const response = (await updateInvoiceItem(
      itemToBeUpdate as IUpdateInvoiceItemsParams
    )) as IUpdateInvoiceItemsApiRes;

    if (response.success) {
      const updatedItems = {
        ...invoiceItems,
        items: invoiceItems.items.map((item) => {
          if (item.item_id === newValue.item_id) {
            return {
              ...item,
              ...newValue,
            };
          } else {
            return item;
          }
        }),
      };

      dispatch(updateInvoiceItems({ items: updatedItems }));
    }
  };

  const handleInvoiceItemDelete = async (itemId: number) => {
    setIsDeleting(true);
    const response = (await deleteInvoiceItems({
      id: Number(params.id),
      itemId: itemId,
    })) as IUpdateInvoiceItemsApiRes;

    if (response.success) {
      const newItems: IInvoiceItemsListData = {
        ...invoiceItems,
        items: invoiceItems.items.filter((item) => item.item_id !== itemId),
      };
      dispatch(deleteInvoiceItem({ items: newItems }));
      setIsDeleting(false);
      setIsDeleteConfirmOpen(false);
    } else {
      setIsDeleting(false);
      setIsDeleteConfirmOpen(false);
      notification.error({
        description: response.message || "Something went wrong!",
      });
    }
  };

  const handleApplyGlobalTaxUpdate = async (
    invoiceItem: IInvoiceItemData,
    applyGlobalTax: number
  ) => {
    const itemToBeUpdate: IUpdateInvoiceItemsParams = {
      id: Number(params.id),
      items: [
        {
          item_id: invoiceItem.item_id,
          subject: invoiceItem.subject,
          item_type: invoiceItem.item_type,
          apply_global_tax: applyGlobalTax,
        },
      ],
    };

    const response = (await updateInvoiceItem(
      itemToBeUpdate as IUpdateInvoiceItemsParams
    )) as IUpdateInvoiceItemsApiRes;

    if (response.success) {
      const newItems: IInvoiceItemsListData = {
        ...invoiceItems,
        items: invoiceItems.items.map((item) => {
          if (item.item_id === invoiceItem.item_id) {
            return {
              ...item,
              apply_global_tax: applyGlobalTax,
            };
          } else {
            return { ...item };
          }
        }),
      };
      dispatch(updateInvoiceItems({ items: newItems }));
    }

    if (!response.success) {
      notification.error({
        description: response.message || "Something went wrong",
      });
    }
  };

  const tabToNextCell = (params: any) => {
    const { previousCellPosition } = params;

    if (previousCellPosition) {
      return {
        rowIndex: previousCellPosition.rowIndex,
        column: previousCellPosition.column,
        floating: previousCellPosition.floating,
      };
    }

    return params.nextCellPosition;
  };

  const suppressKeyboardEvent: ColDef<
    CostItemTableCellRenderer["data"]
  >["suppressKeyboardEvent"] = (params) => {
    const { event, api } = params;

    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
      event.preventDefault();
      event.stopPropagation();
      return true;
    }

    if (event.key === "Enter") {
      event.preventDefault();
      event.stopPropagation();

      // Call valueSetter or custom logic for saving the value
      api.stopEditing(); // Stop editing to trigger valueSetter
      return true;
    }

    return true;
  };
  return (
    <>
      {tableData.length !== 0 && (
        <CollapseSingleTable
          title={_t(
            getGModuleByKey(defaultConfig.equipment_log_module)?.plural_name ||
              "Equipment Logs"
          )}
          totalRecord={`${
            formatter(((Number(sectionTotal) || 0) / 100).toFixed(2))
              .value_with_symbol
          }`}
          defaultActiveKey={tableData.length !== 0 ? 1 : 0}
          totalRecordIcon={true}
          children={
            <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[33px] before:top-6 before:bg-gradient-to-r from-primary-500">
              <div className="ag-theme-alpine">
                <StaticTable
                  className="static-table"
                  stopEditingWhenCellsLoseFocus={true}
                  columnDefs={[
                    {
                      headerName: "",
                      field: "move",
                      minWidth: 30,
                      maxWidth: 30,
                      suppressMenu: true,
                    },
                    {
                      headerName: _t("Type"),
                      field: "type",
                      maxWidth: 50,
                      minWidth: 50,
                      suppressMovable: false,
                      suppressMenu: true,
                      headerClass: "ag-header-center",
                      cellClass: "ag-cell-center",
                      cellRenderer: (params: IIvItemsCellRenderer) => {
                        const { data } = params;
                        return data.item_type_name ? (
                          <Tooltip title={_t(data.item_type_name)}>
                            <FontAwesomeIcon
                              className="w-4 h-4 text-primary-900 mx-auto"
                              icon={iconByItemTypeName[data.item_type_key]}
                            />
                          </Tooltip>
                        ) : (
                          "-"
                        );
                      },
                    },
                    {
                      headerName: _t("Item Name"),
                      field: "item",
                      minWidth: 220,
                      maxWidth: 220,
                      suppressMovable: false,
                      suppressMenu: true,
                      headerClass: "ag-header-left",
                      cellClass: "ag-cell-left",
                      cellRenderer: (params: IIvItemsCellRenderer) => {
                        const { data } = params;
                        return data.subject ? (
                          <Tooltip
                            title={HTMLEntities.decode(
                              sanitizeString(data.subject)
                            )}
                          >
                            <Typography className="table-tooltip-text">
                              {HTMLEntities.decode(
                                sanitizeString(data.subject)
                              )}
                            </Typography>
                          </Tooltip>
                        ) : (
                          "-"
                        );
                      },
                    },
                    {
                      headerName: _t("Cost Code"),
                      field: "cost_code",
                      minWidth: 425,
                      flex: 2,
                      resizable: true,
                      suppressMovable: false,
                      suppressMenu: true,
                      headerClass: "ag-header-left",
                      cellClass: "ag-cell-left",
                      cellRenderer: (params: IIvItemsCellRenderer) => {
                        const { data } = params;

                        const costCode = `${data?.cost_code_name ?? ""}${
                          data?.csi_code ? ` (${data?.csi_code})` : ""
                        }${data?.code_is_deleted === 1 ? ` (Archived)` : ""}`;

                        return (
                          <>
                            {costCode ? (
                              <Tooltip
                                title={HTMLEntities.decode(
                                  sanitizeString(costCode)
                                )}
                              >
                                <Typography className="table-tooltip-text">
                                  {HTMLEntities.decode(
                                    sanitizeString(costCode || "-")
                                  )}
                                </Typography>
                              </Tooltip>
                            ) : (
                              "-"
                            )}
                          </>
                        );
                      },
                    },
                    {
                      headerName: _t("Date"),
                      field: "payment_date",
                      maxWidth: 135,
                      minWidth: 135,
                      suppressMovable: false,
                      suppressMenu: true,
                      cellRenderer: (params: IIvItemsCellRenderer) => {
                        const { data } = params;

                        return data.module_reference_date &&
                          data.module_reference_date !== "" ? (
                          <DateTimeCard
                            format="date"
                            date={data.module_reference_date}
                          />
                        ) : (
                          <Typography className="table-tooltip-text">
                            -
                          </Typography>
                        );
                      },
                    },
                    {
                      headerName: _t("Hours"),
                      field: "hours",
                      minWidth: 80,
                      maxWidth: 80,
                      suppressMovable: false,
                      suppressMenu: true,

                      headerClass: "ag-header-right",
                      cellClass: "ag-cell-right",
                      cellEditorParams: {
                        maxLength: 8, // Limit to 10 characters
                      },
                      cellRenderer: (params: IIvItemsCellRenderer) => {
                        const { data } = params;
                        const hours = Math.floor(
                          Number(data.e_hours_used_total_minutes) / 60
                        );
                        const remainingMinutes =
                          Number(data.e_hours_used_total_minutes) % 60;

                        const formatedHours = `${hours
                          .toString()
                          .padStart(2, "0")}:${remainingMinutes
                          .toString()
                          .padStart(2, "0")}`;
                        return (
                          <Tooltip title={formatedHours}>
                            <Typography className="table-tooltip-text">
                              {formatedHours}
                            </Typography>
                          </Tooltip>
                        );
                      },
                      editable: () => (isReadOnly ? false : true),
                      valueGetter: (params: IIvItemsCellRenderer) => {
                        const hours = Math.floor(
                          Number(params.data.e_hours_used_total_minutes) / 60
                        );
                        const remainingMinutes =
                          Number(params.data.e_hours_used_total_minutes) % 60;

                        const formatedHours = `${hours
                          .toString()
                          .padStart(2, "0")}:${remainingMinutes
                          .toString()
                          .padStart(2, "0")}`;
                        return formatedHours;
                      },
                      valueSetter: (params: ValueSetterParams) => {
                        let newHours = params.newValue
                          ? params.newValue.toString()
                          : "";

                        if (newHours !== "") {
                          if (
                            Number(newHours) ===
                            Number(params.data.e_hours_used_total_minutes) / 60
                          ) {
                            return false;
                          }

                          if (newHours.length > 8) {
                            notification.error({
                              description: _t(
                                "Hours should be less than 8 digits"
                              ),
                            });
                            return false;
                          }

                          let [hours, minutes] = newHours
                            ? newHours.split(":")
                            : [];
                          const totalMins =
                            Number(hours) * 60 +
                            (minutes ? Number(minutes) : 0);

                          const updatedTotal: number = getTotalAmount(
                            params.data,
                            (Number(totalMins) / 60).toString() as string,
                            (Number(params.data.unit_cost) / 100) as
                              | string
                              | number
                          );

                          const valuesToBeUpdate: IUpdateInvoiceItem = {
                            item_id: params?.data?.item_id,
                            log_id: Number(params?.data.reference_primary_id),
                            subject: params.data.subject,
                            cost_code_id: params.data.cost_code_id,
                            quantity: totalMins / 60,
                            price: Number(params.data.unit_cost),
                            unit_cost: Number(params.data.unit_cost),
                            unit: params.data.unit,
                            is_markup_percentage:
                              params.data.is_markup_percentage,
                            markup: params.data.markup,
                            apply_global_tax: params.data.apply_global_tax,
                            e_hours_used_total_minutes: totalMins,
                            total: Number(updatedTotal * 100),
                          };
                          let updatedData = {
                            ...params.data,
                            ...valuesToBeUpdate,
                          };

                          params.node?.setData(updatedData);
                          handleInLineEditing(valuesToBeUpdate);
                          return true;
                        }

                        return false;
                      },
                    },
                    {
                      headerName: _t("Unit Cost"),
                      field: "cost",
                      minWidth: 130,
                      maxWidth: 130,
                      suppressMovable: false,

                      suppressMenu: true,
                      headerClass: "ag-header-right",
                      cellClass: "ag-cell-right",
                      cellEditor: "agNumberCellEditor",
                      suppressKeyboardEvent,
                      cellRenderer: (params: IIvItemsCellRenderer) => {
                        const { data } = params;
                        const cost = "$20.00";
                        return data.unit_cost ? (
                          <Tooltip
                            title={
                              formatter(
                                ((Number(data.unit_cost) || 0) / 100).toFixed(2)
                              ).value_with_symbol
                            }
                          >
                            <Typography className="table-tooltip-text">
                              {
                                formatter(
                                  ((Number(data.unit_cost) || 0) / 100).toFixed(
                                    2
                                  )
                                ).value_with_symbol
                              }
                            </Typography>
                          </Tooltip>
                        ) : (
                          "-"
                        );
                      },
                      editable: () => (isReadOnly ? false : true),
                      valueGetter: (params: IIvItemsCellRenderer) => {
                        return Number(params?.data?.unit_cost) / 100;
                      },
                      valueSetter: (params: ValueSetterParams) => {
                        let newUnitCost = params.newValue
                          ? params.newValue.toString()
                          : "";

                        const [integerPart, decimalPart] =
                          newUnitCost.split(".");

                        if (
                          integerPart.length > 6 ||
                          (decimalPart && decimalPart.length > 2) ||
                          integerPart[0] === "-"
                        ) {
                          notification.error({
                            description: _t(
                              "Unit cost should be less than or equal 6 digits"
                            ),
                          });
                          return false;
                        }

                        if (!isNaN(Number(newUnitCost))) {
                          if (
                            Number(newUnitCost) ===
                            Number(params.data.unit_cost) / 100
                          ) {
                            return false;
                          }

                          const updatedTotal: number = getTotalAmount(
                            params.data,
                            params.data.quantity as string,
                            newUnitCost as string | number
                          );

                          const valuesToBeUpdate: IUpdateInvoiceItem = {
                            item_id: params?.data?.item_id,
                            log_id: Number(params?.data.reference_primary_id),
                            subject: params.data.subject,
                            cost_code_id: params.data.cost_code_id,
                            quantity: params.data.quantity,
                            price: Number(newUnitCost) * 100,
                            unit_cost: Number(newUnitCost) * 100,
                            unit: params.data.unit,
                            is_markup_percentage:
                              params.data.is_markup_percentage,
                            markup: params.data.markup,
                            apply_global_tax: params.data.apply_global_tax,
                            e_hours_used_total_minutes:
                              params.data.e_hours_used_total_minutes,
                            total: updatedTotal * 100,
                          };
                          let updatedData = {
                            ...params.data,
                            ...valuesToBeUpdate,
                          };

                          params.node?.setData(updatedData);
                          handleInLineEditing(valuesToBeUpdate);
                          return true;
                        }

                        return false;
                      },
                    },
                    {
                      headerName: _t("Unit"),
                      field: "unit",
                      maxWidth: 100,
                      minWidth: 100,
                      headerClass: "ag-header-left",
                      cellClass: "ag-cell-left",
                      suppressMovable: false,
                      suppressMenu: true,
                      cellRenderer: (params: IIvItemsCellRenderer) => {
                        const { data } = params;
                        return (
                          <Tooltip title={data.unit}>
                            <Typography className="table-tooltip-text">
                              {data.unit || "-"}
                            </Typography>
                          </Tooltip>
                        );
                      },
                    },
                    {
                      headerName: _t("MU") + "%",
                      field: "mu",
                      maxWidth: 80,
                      minWidth: 80,
                      suppressMovable: false,
                      suppressMenu: true,
                      headerClass: "ag-header-right",
                      cellClass: "ag-cell-right",
                      cellRenderer: (params: IIvItemsCellRenderer) => {
                        const { data } = params;
                        const is_markup_percentage =
                          data?.is_markup_percentage ?? "";
                        const total =
                          Number(data?.quantity ?? 0) *
                          (Number(data?.unit_cost ?? 0) / 100);
                        const markup = Number(data?.markup);

                        let markupToShow = formatter("0.00").value;

                        if (is_markup_percentage?.toString() === "1") {
                          markupToShow = formatter(
                            markup?.toString() ?? "0.00"
                          ).value;
                        } else {
                          if (total != 0) {
                            const markupPercentage = markup / total - 100;
                            markupToShow = markupPercentage.toFixed(2);
                            markupToShow = formatter(
                              Number(markupToShow || 0).toFixed(2)
                            )?.value;
                          }
                        }
                        return data.markup ? (
                          <Tooltip title={markupToShow}>
                            <Typography className="table-tooltip-text">
                              {markupToShow}
                            </Typography>
                          </Tooltip>
                        ) : (
                          <>-</>
                        );
                      },
                    },
                    {
                      headerName: _t("Total"),
                      field: "total",
                      maxWidth: 130,
                      minWidth: 130,
                      suppressMovable: false,
                      suppressMenu: true,
                      headerClass: "ag-header-right",
                      cellClass: "ag-cell-right",
                      cellRenderer: (params: IIvItemsCellRenderer) => {
                        const { data } = params;
                        return data.total ? (
                          <Tooltip
                            title={
                              formatter(
                                ((Number(data.total) || 0) / 100).toFixed(2)
                              ).value_with_symbol
                            }
                          >
                            <Typography className="table-tooltip-text">
                              {
                                formatter(
                                  ((Number(data.total) || 0) / 100).toFixed(2)
                                ).value_with_symbol
                              }
                            </Typography>
                          </Tooltip>
                        ) : (
                          "-"
                        );
                      },
                    },
                    {
                      headerName: _t("Tax"),
                      field: "apply_global_tax",
                      minWidth: 50,
                      maxWidth: 50,
                      suppressMovable: false,
                      suppressMenu: true,

                      headerClass: "ag-header-center",
                      cellClass: "ag-cell-center flex justify-center",
                      editable: !isReadOnly,
                      cellEditor: "agCheckboxCellEditor",
                      valueGetter: ({ data }: IIvItemsCellRenderer) => {
                        return data?.apply_global_tax == 1;
                      },
                      valueSetter: (params: ValueSetterParams) => {
                        if (params && params.node) {
                          let nVal = params.newValue;
                          const updatedData = {
                            ...params.data,
                            apply_global_tax: nVal,
                          };
                          params.node.setData(updatedData);
                          handleApplyGlobalTaxUpdate(
                            params.data,
                            nVal === true ? 1 : 0
                          );
                        }
                        return true;
                      },
                    },
                    {
                      headerName: "",
                      field: "",
                      maxWidth: 80,
                      minWidth: 80,
                      suppressMenu: true,
                      cellRenderer: (params: IIvItemsCellRenderer) => {
                        const { data } = params;
                        return (
                          <div className="flex items-center gap-1.5 justify-end">
                            <ButtonWithTooltip
                              tooltipTitle={_t("View")}
                              tooltipPlacement="top"
                              icon="fa-solid fa-eye"
                              onClick={() => {
                                setEquipmentItemToBeUpdate(data);
                                setInvoiceItem(false);
                                setEquipmentLogItem(true);
                              }}
                            />
                            {!isReadOnly &&
                              (details?.approval_type_key === "invoice_open" ||
                                details?.approval_type_key ===
                                  "invoice_on_hold") && (
                                <ButtonWithTooltip
                                  tooltipTitle={_t("Delete")}
                                  tooltipPlacement="top"
                                  icon="fa-regular fa-trash-can"
                                  onClick={() => {
                                    setSelectedItemToDelete(
                                      Number(data.item_id)
                                    );
                                    setIsDeleteConfirmOpen(true);
                                  }}
                                />
                              )}
                          </div>
                        );
                      },
                    },
                  ]}
                  rowData={tableData}
                  noRowsOverlayComponent={() => (
                    <NoRecords
                      image={`${window.ENV.CDN_URL}assets/images/no-records-contact.svg`}
                    />
                  )}
                  tabToNextCell={tabToNextCell}
                />
              </div>
            </div>
          }
        />
      )}

      {isDeleteConfirmOpen && (
        <ConfirmModal
          isOpen={isDeleteConfirmOpen}
          modaltitle={_t("Delete")}
          description={_t("Are you sure you want to delete this Item?")}
          modalIcon="fa-regular fa-trash-can"
          isLoading={isDeleting}
          onAccept={() => {
            if (selectedItemToDelete) {
              handleInvoiceItemDelete(selectedItemToDelete);
            }
          }}
          onDecline={() => {
            setIsDeleteConfirmOpen(false);
          }}
          onCloseModal={() => {
            setIsDeleteConfirmOpen(false);
          }}
        />
      )}
    </>
  );
};

export default OldEquipmentLogsItems;
