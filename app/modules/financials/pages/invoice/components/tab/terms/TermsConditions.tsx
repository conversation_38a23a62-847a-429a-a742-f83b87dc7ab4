import { useEffect, useMemo, useRef, useState } from "react";
import parse from "html-react-parser";
// Froala editor
import type <PERSON><PERSON><PERSON> from "react-froala-wysiwyg";
// Helper, hooks and redux
import { getGConfig } from "~/zustand";
import { useTranslation } from "~/hook";
import { IVTermsField } from "~/modules/financials/pages/invoice/utils/constants";
import {
  useAppIVDispatch,
  useAppIVSelector,
} from "~/modules/financials/pages/invoice/redux/store";
import { updateIVTermsApi } from "~/modules/financials/pages/invoice/redux/action";
import { updateIVTermsDetail } from "~/modules/financials/pages/invoice/redux/slices/iVTermsSlice";
// Molecules
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { FroalaEditor } from "~/shared/components/molecules/froalaEditor";
import { NoRecords } from "~/shared/components/molecules/noRecords";

const TermsConditions = () => {
  const { _t } = useTranslation();
  const { module_access }: GConfig = getGConfig();

  const defaultTermsRef = useRef<Froala | null>(null);

  const defaultEditorRef = useRef<string | undefined>("");
  const customEditorRef = useRef<string | undefined>("");
  const dispatch = useAppIVDispatch();
  const { termsData }: IIVTermsInitialState = useAppIVSelector(
    (state) => state.invoiceTermTab
  );
  const [inputValues, setInputValues] =
    useState<Partial<IIVTermsData>>(IVTermsField);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [valOf, setValOf] = useState<number>(new Date().valueOf());

  const isReadOnly = useMemo(
    () => module_access === "read_only",
    [module_access]
  );

  useEffect(() => {
    setInputValues(termsData);
  }, [termsData?.invoice_id]);

  const handleUpdateField = async (data: IDirDetailFields) => {
    if (isLoading) {
      return false;
    }
    setIsLoading(true);
    const updateRes = (await updateIVTermsApi({
      id: termsData?.invoice_id || "",
      ...data,
    })) as IIVDetailsUpdateApiRes;

    setIsLoading(false);
    if (updateRes?.success) {
      dispatch(updateIVTermsDetail({ invoice_default_terms: data?.terms }));
    } else {
      setInputValues(termsData);
      setValOf(new Date().valueOf());
      notification.error({
        description: updateRes?.message,
      });
    }
  };

  useEffect(() => {
    defaultEditorRef.current = termsData?.invoice_default_terms || "";
    customEditorRef.current = termsData?.invoice_custom_terms || "";
  }, [termsData?.invoice_default_terms, termsData?.invoice_custom_terms]);

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Terms and Conditions")}
        subTitle={_t(
          "The multiple Terms fields have been consolidated into one easy-to-use Terms field."
        )}
        iconProps={{
          icon: "fa-solid fa-ballot-check",
          containerClassName:
            "bg-[linear-gradient(180deg,#63759A1a_0%,#63759A1a_100%)]",
          id: "terms_condition_icon",
          colors: ["#63759A", "#63759A"],
        }}
        children={
          <div className="pt-2 froala-editor">
            <div className="rounded-md shadow-[0_0_10px] shadow-black/15 p-1.5">
              <FieldLabel
                labelClass="!px-3 py-2.5 rounded-t-md bg-[#F8F8F8] block !text-black"
                children={_t("Terms")}
              />
              <div className="p-3.5">
                {isReadOnly ? (
                  inputValues?.invoice_default_terms?.trim() ? (
                    parse(
                      inputValues?.invoice_default_terms.replace(
                        /\\\"/g,
                        '"'
                      ) || ""
                    )
                  ) : (
                    <NoRecords
                      className="!py-0"
                      image={`${window.ENV.CDN_URL}assets/images/no-records-froala-editor.svg`}
                    />
                  )
                ) : (
                  <FroalaEditor
                    ref={defaultTermsRef}
                    value={inputValues?.invoice_default_terms?.replace(
                      /\\\"/g,
                      '"'
                    )}
                    key={`iv_default_terms_${valOf}`}
                    onBlurEnt={() => {
                      if (
                        defaultTermsRef.current &&
                        defaultTermsRef.current.getEditor()
                      ) {
                        const editorContent = defaultTermsRef.current
                          .getEditor()
                          .html.get();
                        if (editorContent != defaultEditorRef?.current) {
                          handleUpdateField({
                            terms: editorContent,
                            has_default_terms: 1,
                          });
                        }
                      }
                    }}
                  />
                )}
              </div>
            </div>
          </div>
        }
      />
    </>
  );
};

export default TermsConditions;
