import React, { FormEvent, useEffect, useMemo, useRef, useState } from "react";
import isEmpty from "lodash/isEmpty";
import isEqual from "lodash/isEqual";
import * as Yup from "yup";
import { useFormik } from "formik";
import { Form, useParams } from "@remix-run/react";
// Hook + helper + redux
import { useTranslation } from "~/hook";
import { defaultConfig } from "~/data";
import {
  floatNumberRegex,
  wholeNumberRegex,
} from "~/modules/financials/pages/changeOrder/utils/helpers";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import { getGConfig, getGSettings, getGTypes } from "~/zustand";
import {
  useAppIVDispatch,
  useAppIVSelector,
} from "~/modules/financials/pages/invoice/redux/store";
import {
  addInvoiceItems,
  getInvoiceItemsList,
  updateInvoiceItem,
} from "~/modules/financials/pages/invoice/redux/action/InvoiceItemsActions";
import { updateInvoiceItems } from "~/modules/financials/pages/invoice/redux/slices/InvoiceItemsSlice";
import {
  getDirectaryIdByKey,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import {
  filterOptionBySubstring,
  generateCostCodeLabel,
  getItemTypeIcon,
  onKeyDownCurrency,
} from "~/shared/utils/helper/common";
import { type RadioChangeEvent } from "antd";
// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import InvoiceSendEmail from "~/modules/financials/pages/invoice/components/InvoiceSendEmail";
import { INVOICE_ITEM } from "~/modules/financials/pages/invoice/utils/constants";
import { addUnit, getUnitList } from "~/redux/action/unitActions";

type ModifiedCustomerEmail = Omit<CustomerEmail, "type"> & {
  type: string; // New type for `type`
};
type FormikSubmitEvent = FormEvent<HTMLFormElement> & {
  nativeEvent: { submitter?: HTMLButtonElement };
};
const InvoiceTabsItem = ({
  isOpen,
  onClose,
  isViewOnly = false,
  formData,
  isAddInvoiceItem,
  setIsAddInvoiceItem,
  setItemToBeUpdate,
  allRecords,
  moduleName,
}: IInvoiceTabsItemProps) => {
  const { _t } = useTranslation();
  const params = useParams();
  const gConfig = getGConfig();
  const gSettings: GSettings = getGSettings();
  const { currency_symbol, is_cidb_auto_save } = gSettings;
  const currentCurrency = currency_symbol ?? "$";

  const [markup, setMarkup] = useState<string>("markup_percent");
  const costUnitRef = useRef<HTMLDivElement>(null);
  const skipClose = useRef<boolean>(false);
  const unitCostContainerRef = useRef<HTMLDivElement>(null);
  const [showUnitInputs, setShowUnitInputs] = useState<boolean>(!isViewOnly);
  const [unitCost, setUnitCost] = useState<number | string | undefined>(
    formData?.unit_cost
  );
  const selectedFormData = formData;
  const [confirmSaveData, setConfirmSaveData] = useState<{
    rid: number;
    message?: string;
  }>({ rid: 0 });

  const itemTypesWithMarkup = useAppIVSelector(
    (state) => state.invoiceItemTypes.itemTypes
  );

  const { details }: IIVDetailsInitialState = useAppIVSelector(
    (state) => state.invoiceDetails
  );
  const invoiceItems = useAppIVSelector(
    (state) => state.proInvoiceItemList.invoiceItems
  );

  const dispatch = useAppIVDispatch();

  const [mainTotal, setMainTotal] = useState<string | number>("");
  const [isOpenSelectAssignedTo, setIsOpenSelectAssignedTo] =
    useState<boolean>(false);
  const { inputFormatter, formatter, unformatted } = useCurrencyFormatter();
  const unitCostRef = useRef<InputRef>(null);
  const [submittingFrm, setSubmittingFrm] = useState<string>("");
  const { codeCostData }: IGetCostCodeList = useAppIVSelector(
    (state) => state.costCode
  );
  const [unit, setUnit] = useState<number | string | undefined>(formData?.unit);
  const [isExistingLoading, setIsExistingLoading] = useState<boolean>(false);
  const [isOpenContactDetails, setIsOpenContactDetails] =
    useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<Partial<IDirectoryData>>({});
  const [formEvent, setFormEvent] = useState<FormikSubmitEvent | null>(null);
  const [isRefreshVal, setIsRefreshVal] = useState<number>(0);

  const [newTypeName, setNewTypeName] = useState<string>("");
  const [unitData, setUnitData] = useState<IUnitData[]>([]);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [isMarkupChange, setIsMarkupChange] = useState<boolean>(false);

  const getUnit = async () => {
    const unit: IUnitListResponse = await getUnitList();
    setUnitData(unit?.data?.units ?? []);
  };

  useEffect(() => {
    getUnit();
  }, []);
  const itemTypes: GType[] = getGTypes();
  const filteredItemsTypes = useMemo(
    () =>
      itemTypes?.filter(
        (item: Partial<GType>) => item?.type === "company_items"
      ),
    [itemTypes]
  );
  const reorderedItemsTypes = (() => {
    if (!Array.isArray(filteredItemsTypes)) return [];
    const items = [...filteredItemsTypes];
    const index = items.findIndex((item) => item.type_id == "163");
    if (index > -1) {
      const [labourItem] = items.splice(index, 1); // remove item
      items.splice(1, 0, labourItem); // insert at 2nd place
    }
    return items;
  })();
  const handleParagraphClick = () => {
    if (!isViewOnly) {
      setShowUnitInputs(true);
      setTimeout(() => {
        unitCostRef?.current?.focus();
      }, 10);
    }
  };

  const costCodeOptions = useMemo(() => {
    // Map the existing cost codes
    const costCodeOpts = codeCostData.map((item: ICostCode) => ({
      label: generateCostCodeLabel({
        name: item?.cost_code_name,
        code: item?.csi_code,
        isArchived: item?.is_deleted == 1,
        isAllowCodeWithoutName: true,
      }),
      value: item?.code_id,
    }));

    // Check if the selected cost code exists in the data
    const costCodeExists = codeCostData.some(
      (item) => item.code_id == formData?.cost_code_id
    );

    // If not, add it as archived
    if (
      !costCodeExists &&
      formData?.cost_code_id &&
      formData?.cost_code_id != 0
    ) {
      costCodeOpts.push({
        label: generateCostCodeLabel({
          name: formData?.cost_code_name,
          code: formData?.csi_code,
          isArchived: true,
          isAllowCodeWithoutName: true,
        }),
        value: formData?.cost_code_id as string,
      });
    }

    return costCodeOpts;
  }, [codeCostData, formData]);

  const undefinedTypeMarkup = useMemo(() => {
    return itemTypesWithMarkup?.find(
      (i: IWorkOrderType) => i.type_id?.toString() === ""
    )?.mark_up;
  }, [itemTypesWithMarkup]);

  const initialValues: IInvoiceItemData = useMemo(() => {
    if (isAddInvoiceItem) {
      return {
        ...INVOICE_ITEM,
        markup:
          undefinedTypeMarkup !== "null" && undefinedTypeMarkup
            ? undefinedTypeMarkup?.toString()
            : "",
        is_markup_percentage: 1,
      };
    }
    return {
      ...formData,
      unit_cost: (Number(formData?.unit_cost || 0) / 100)
        ?.toFixed(2)
        .toString(),
      markup: isAddInvoiceItem
        ? undefinedTypeMarkup !== "null" && undefinedTypeMarkup
          ? undefinedTypeMarkup?.toString()
          : ""
        : formData?.is_markup_percentage === 0
        ? formData?.markup != null
          ? (Number(formData?.markup) / 100)?.toString()
          : null
        : formData?.markup,
      item_on_database: formData?.item_on_database,
      internal_notes: formData?.notes,
      is_markup_percentage: isAddInvoiceItem
        ? 1
        : formData?.is_markup_percentage,
      submitAction: "",
    };
  }, [formData, isOpen, itemTypesWithMarkup, isAddInvoiceItem, isRefreshVal]);

  const validationSchema = Yup.object().shape({
    subject: Yup.string().required("This field is required."),
    item_type: Yup.string().required("This field is required."),
  });

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      const formData = { ...values, submitAction: undefined };
      const isSkipClose =
        formEvent?.nativeEvent.submitter?.getAttribute("data-skip-close") ===
          "true" || skipClose.current;
      if (isAddInvoiceItem) {
        const itemsToBeAdd: IAddInvoiceItemsParams = {
          project_id: details.project_id,
          id: Number(params.id),
          items: [
            {
              item_type: Number(formData?.item_type) || 0,
              subject: formData?.subject || "",
              unit_cost: Number(formData?.unit_cost) * 100,
              quantity: Number(formData?.quantity),
              is_markup_percentage:
                markup === "markup_percent"
                  ? 1
                  : Number(formData?.is_markup_percentage),
              markup: formData?.markup?.toString()
                ? markup !== "markup_percent"
                  ? Number(formData?.markup) * 100
                  : null
                : null,
              markup_amount: Number(formData?.markup),
              cost_code_id: Number(formData?.cost_code_id),
              total: Number(mainTotal) * 100,
              invoice_total: Number(mainTotal) * 100,
              apply_global_tax: formData?.apply_global_tax === 1 ? true : false,
              add_item_to_database: formData?.item_on_database
                ? Number(formData?.item_on_database || "")
                : undefined,
              assigned_to: Number(formData?.assigned_to),
              assigned_to_contact_id: Number(formData?.assigned_to_contact_id),
              description: formData?.description,
              assignee_type: formData?.assignee_type,
              unit: formData?.unit,
              notes: formData?.notes,
              user_image: formData.user_image,
              reference_item_id:
                confirmSaveData?.rid > 0 ? confirmSaveData.rid : undefined,
            },
          ],
        };
        if (isSkipClose) {
          setSubmittingFrm("save1");
        } else {
          setSubmittingFrm(
            values.submitAction == "saveAndAddAnother"
              ? "saveAndAddAnother"
              : "save"
          );
        }

        const response = (await addInvoiceItems(
          itemsToBeAdd
        )) as IUpdateInvoiceItemsTabApiRes;

        if (Number(response?.data?.reference_item_id || "") > 0) {
          setConfirmSaveData({
            rid: Number(response?.data?.reference_item_id || ""),
            message: response?.message,
          });
          return false;
        }

        if (response.success) {
          if (!isSkipClose) {
            if (values.submitAction == "saveAndAddAnother") {
              setFormEvent(null);
              resetForm();
              setIsAddInvoiceItem(true);
              setIsRefreshVal(isRefreshVal + 1);
              setMarkup("markup_percent");
            } else {
              onClose(false);
            }
          }
          const getInvoiceItemsResponse = (await getInvoiceItemsList({
            id: Number(params.id),
            data_for: "all",
            need_section: details.is_new_tm_invoice === 1 ? 1 : 0,
            is_separate_change_order_sections:
              details.is_new_tm_invoice === 0 ? true : undefined,
            is_separate_estimate_sections: false,
          })) as IGetInvoiceItemsApiRes;
          if (getInvoiceItemsResponse.success) {
            dispatch(
              updateInvoiceItems({ items: getInvoiceItemsResponse.data })
            );
          } else {
            notification.error({
              description:
                getInvoiceItemsResponse?.message || "Something went wrong!",
            });
            setIsAddInvoiceItem(false);
          }
        } else {
          notification.error({
            description: response?.message || "Something went wrong!",
          });
          setIsAddInvoiceItem(false);
        }
      } else {
        const itemToBeUpdate: IUpdateInvoiceItemsParams = {
          id: Number(params.id),
          items: [
            {
              item_id: formData?.item_id,
              invoice_item_no: formData?.invoice_item_no,
              unit_cost: Number(formData?.unit_cost) * 100,
              quantity: formData?.quantity || 0,
              is_markup_percentage: formData?.is_markup_percentage,
              markup: formData?.markup?.toString()
                ? formData?.is_markup_percentage === 0
                  ? (Number(formData?.markup) * 100)?.toString()
                  : formData?.markup?.toString()
                : null,
              cost_code_id: formData?.cost_code_id || "",
              total: Number(mainTotal) * 100,
              invoice_total: Number(mainTotal) * 100,
              apply_global_tax: formData?.apply_global_tax,
              item_type: formData?.item_type,
              assigned_to: formData?.assigned_to,
              assignee_type: formData?.assignee_type,
              change_order_id: formData?.change_order_id,
              add_item_to_database: formData?.item_on_database,
              user_image: formData.user_image,
              reference_item_id:
                confirmSaveData?.rid > 0
                  ? confirmSaveData.rid
                  : formData?.reference_item_id,
              paid_bill: formData?.total_paid_bill,
              subject: formData?.subject,
              description: formData?.description || "",
              notes: formData?.notes || "",
              unit: formData?.unit,
              billing_option: formData?.billing_option,
              internal_notes: formData?.internal_notes,
              assigned_to_contact_id: formData?.assigned_to_contact_id,
              tax_id: formData?.tax_id,
            },
          ],
        };
        const costCode = codeCostData?.find((item) => {
          return formData?.cost_code_id == item?.code_id;
        });

        const newValue = {
          item_id: formData?.item_id,
          invoice_item_no: formData?.invoice_item_no,
          unit_cost: Number(formData?.unit_cost) * 100,
          quantity: formData?.quantity || 0,
          is_markup_percentage: formData?.is_markup_percentage,
          markup: formData?.markup?.toString()
            ? formData?.is_markup_percentage === 0
              ? (Number(formData?.markup) * 100)?.toString()
              : formData?.markup?.toString()
            : null,
          cost_code_id: formData?.cost_code_id,
          cost_code_name:
            formData?.cost_code_id &&
            formData?.cost_code_id != "0" &&
            isEmpty(costCode)
              ? selectedFormData?.cost_code_name
              : costCode?.cost_code_name || "",
          csi_code:
            formData?.cost_code_id &&
            formData?.cost_code_id != "0" &&
            isEmpty(costCode)
              ? selectedFormData?.csi_code
              : costCode?.csi_code || "",
          total: Number(mainTotal) * 100,
          apply_global_tax: formData?.apply_global_tax,
          item_type: formData?.item_type,
          assigned_to: formData?.assigned_to,
          assignee_type: formData?.assignee_type,
          change_order_id: formData?.change_order_id,
          add_item_to_database: formData?.item_on_database,
          reference_item_id: formData?.reference_item_id,
          paid_bill: formData?.total_paid_bill,
          subject: formData?.subject,
          description: formData?.description || "",
          notes: formData?.notes || "",
          unit: formData?.unit,
          billing_option: formData?.billing_option,
          internal_notes: formData?.internal_notes,
          item_type_key: filteredItemsTypes.filter(
            (item) => item.type_id.toString() === formData?.item_type.toString()
          )[0].key,
          item_type_display_name: filteredItemsTypes.filter(
            (item) => item.type_id.toString() === formData?.item_type.toString()
          )[0].display_name,
          assignee_name: formData?.assignee_name,
          assigned_to_contact_id: formData?.assigned_to_contact_id,
          user_image: formData?.user_image || "",
        };

        if (isSkipClose) {
          setSubmittingFrm("save1");
        } else {
          setSubmittingFrm(
            values.submitAction == "saveAndAddAnother"
              ? "saveAndAddAnother"
              : "save"
          );
        }

        const response = (await updateInvoiceItem(
          itemToBeUpdate as IUpdateInvoiceItemsParams
        )) as IUpdateInvoiceItemsTabApiRes;

        if (Number(response?.data?.reference_item_id || "") > 0) {
          setConfirmSaveData({
            rid: Number(response?.data?.reference_item_id || ""),
            message: response?.message,
          });
          return false;
        }

        if (response?.success) {
          if (!isSkipClose) {
            if (values.submitAction == "saveAndAddAnother") {
              setFormEvent(null);
              resetForm();
              setMarkup("markup_percent");
              setIsRefreshVal(isRefreshVal + 1);
              setIsAddInvoiceItem(true);
            } else {
              onClose(false);
            }
          }
          const updatedItems = {
            ...invoiceItems,
            items: invoiceItems.items.map((item) => {
              if (item.item_id === newValue.item_id) {
                return {
                  ...item,
                  ...newValue,
                  notes: newValue.internal_notes,
                };
              } else {
                return {
                  ...item,
                };
              }
            }),
            import_from_estimate_section:
              invoiceItems?.import_from_estimate_section.map((section) => {
                return {
                  ...section,
                  items: section.items.map((item) => {
                    if (item.item_id === newValue.item_id) {
                      return {
                        ...item,
                        ...newValue,
                        notes: newValue.internal_notes,
                      };
                    } else {
                      return item;
                    }
                  }),
                };
              }),
          };
          dispatch(updateInvoiceItems({ items: updatedItems }));
        } else {
          notification.error({
            description: response?.message || "Something went wrong!",
          });
        }
      }

      setIsExistingLoading(false);
      setConfirmSaveData({ rid: 0 });
      setSubmittingFrm("");
    },
  });

  const { handleSubmit, setFieldValue, values, touched, errors, isSubmitting } =
    formik;

  useEffect(() => {
    formik.setValues(initialValues);
  }, [formData, isOpen, isAddInvoiceItem, isRefreshVal]);

  useEffect(() => {
    if (isAddInvoiceItem === false) {
      if (formData?.is_markup_percentage === 1) {
        setMarkup("markup_percent");
      } else {
        setMarkup("markup_dolar");
      }
      setIsMarkupChange(false);
    }
  }, [formData?.is_markup_percentage, isMarkupChange]);

  const assignedTo = useMemo(() => {
    if (
      values?.assigned_to !== 0 &&
      values?.assigned_to?.toString() !== "" &&
      values?.assigned_to &&
      !!values?.assignee_name
    ) {
      const assigned_to: Partial<ModifiedCustomerEmail>[] = [
        {
          display_name: values?.assignee_name,
          user_id: Number(values?.assigned_to),
          type: values?.assignee_type,
          type_key: getDirectaryKeyById(Number(values?.assignee_type), gConfig),
          image:
            values?.assigned_to_contact_id === 0 ||
            values?.assigned_to_contact_id === null ||
            !values?.assigned_to_contact_id
              ? values?.user_image
              : "",
          contact_id: values?.assigned_to_contact_id,
        },
      ];

      return assigned_to as TselectedContactSendMail[];
    } else {
      return [];
    }
  }, [values?.assigned_to, values.assignee_name, values.assignee_type]);

  const handleFocusOut = (e: React.FocusEvent<HTMLInputElement>) => {
    if (!unitCostContainerRef.current?.contains(e.relatedTarget as Node)) {
      if (
        values?.unit_cost &&
        !isEmpty(values?.unit_cost) &&
        values.unit &&
        !isEmpty(values.unit)
      ) {
        setShowUnitInputs(false);
      }
    }
  };

  useMemo(() => {
    if (
      values?.unit_cost !== "" &&
      !isEmpty(values?.unit_cost) &&
      values?.unit_cost !== undefined &&
      values?.unit !== "" &&
      !isEmpty(values?.unit) &&
      values?.unit !== undefined &&
      !unitCostContainerRef.current?.contains(document.activeElement as Node)
    ) {
      setShowUnitInputs(false);
    } else {
      setShowUnitInputs(true);
    }
  }, [isOpen, formData, values]);

  useEffect(() => {
    if (
      values?.quantity?.toString() !== "" &&
      values?.unit_cost !== "" &&
      values?.quantity &&
      values?.unit_cost
    ) {
      const total = Number(values?.quantity) * Number(values?.unit_cost);
      setFieldValue("total", total.toString());
      setMainTotal(Number(values?.markup_amount) + Number(values?.total));
    } else {
      setFieldValue("total", "");
      setMainTotal("");
    }
    if (
      values?.total !== "" &&
      values.total &&
      values?.markup !== "" &&
      values?.markup
    ) {
      if (markup === "markup_percent") {
        const markup = (Number(values?.total) * Number(values?.markup)) / 100;
        setFieldValue("markup_amount", markup);
        setMainTotal(Number(markup) + Number(values?.total));
      } else {
        const markup = Number(values?.markup || "");
        if (
          markup != 0 &&
          values?.unit_cost != "0.00" &&
          values?.unit_cost != "0"
        ) {
          const markupPercentage =
            Number(markup) === 0
              ? 0
              : (Number(markup) * 100) / (Number(values?.total) || 1) - 100;
          setFieldValue("markup_amount", markupPercentage.toFixed(2));
          setMainTotal(markup);
        } else {
          setFieldValue("markup_amount", "0");
          setMainTotal(Number(values?.total));
        }
      }
    } else {
      setFieldValue("markup_amount", "");
      setMainTotal(Number(values?.total));
    }
  }, [
    values?.quantity,
    values?.unit_cost,
    values?.total,
    values?.markup,
    values?.is_markup_percentage,
    markup,
  ]);

  const parseNumber = (_value: string | string[]) => {
    const value = Array.isArray(_value) ? _value[0] : _value;
    let finalValue = 0;
    const parsedNumber = parseFloat(value);
    if (isNaN(parsedNumber) || !value) {
      finalValue = 0;
    } else {
      finalValue = parsedNumber;
    }

    return finalValue;
  };

  const INVOICE_ITEMS_LIST_TAB = [
    {
      label: (
        <FontAwesomeIcon
          className="text-base w-3.5 h-3.5"
          icon="fa-regular fa-percent"
        />
      ),
      value: "markup_percent",
    },
    {
      label: (
        <Typography className="text-sm min-w-3.5 h-3.5 flex items-center justify-center">
          {currentCurrency}
        </Typography>
      ),
      value: "markup_dolar",
    },
  ];

  const currentItemIndex = useMemo(() => {
    return allRecords?.findIndex((i) => i?.item_id == values?.item_id);
  }, [values?.item_id, allRecords]);

  const itemTypeName = useMemo(() => {
    const itemType = values?.item_type;

    const taxSettingsMap: Record<number, number> = {
      161: gSettings.is_taxable_material_items,
      162: gSettings.is_taxable_equipment_items,
      163: gSettings.is_taxable_labor_items,
      164: gSettings.is_taxable_subcontractor_items,
      165: gSettings.is_taxable_other_items,
    };

    const applyGlobalTax = taxSettingsMap[Number(values.item_type)] ?? 0;

    if (isAddInvoiceItem) {
      setFieldValue("apply_global_tax", applyGlobalTax);
      setFieldValue("item_on_database", is_cidb_auto_save);
    }

    if (!itemType) return "";
    const itemTypeObj = filteredItemsTypes?.find(
      (item) => item?.type_id == itemType
    );

    return itemTypeObj?.name;
  }, [
    gSettings,
    values.item_type,
    isAddInvoiceItem,
    JSON.stringify(filteredItemsTypes),
    is_cidb_auto_save,
  ]);

  const isItemOnDatabase = useMemo(() => {
    if (isAddInvoiceItem && !values?.item_type) return false;
    if (isAddInvoiceItem && !!values?.item_type) return true;
    // 86cz0jdm0
    if (Number(formData?.reference_item_id) == 0 && !!values?.item_type_key) {
      return true;
    } else {
      if (
        Number(formData?.reference_item_id) > 0 &&
        Number(formData?.item_on_database) === 1
      ) {
        return true;
      }
    }
    return false;
  }, [
    formData?.reference_item_id,
    formData?.item_on_database,
    isAddInvoiceItem,
    values?.item_type,
  ]);

  const isHideItemType = useMemo(() => {
    return (
      values?.is_discount_item == "1" || values?.held_retainage_item == "1"
    );
  }, [values]);

  const handleItemNav = async (increment: number) => {
    const oldVal = {
      subject: initialValues?.subject,
      item_type: initialValues?.item_type,
      assigned_to: initialValues?.assigned_to,
      cost_code_id: initialValues?.cost_code_id,
      quantity: initialValues?.quantity,
      unit_cost: initialValues?.unit_cost,
      unit: initialValues?.unit,
      markup: initialValues?.markup,
      description: initialValues?.description,
      internal_notes: initialValues?.internal_notes,
      apply_global_tax: initialValues?.apply_global_tax,
      item_on_database: initialValues?.item_on_database,
      add_item_to_database: initialValues?.item_on_database,
    };
    const newVal = {
      subject: values?.subject,
      item_type: values?.item_type,
      assigned_to: values?.assigned_to,
      cost_code_id: values?.cost_code_id,
      quantity: values?.quantity,
      unit_cost: values?.unit_cost,
      unit: values?.unit,
      markup: values?.markup,
      description: values?.description,
      internal_notes: values?.internal_notes,
      apply_global_tax: values?.apply_global_tax,
      item_on_database: values?.item_on_database,
      add_item_to_database: values?.item_on_database,
    };

    const formModified = isEqual(newVal, oldVal);
    if (!formModified || (!formik.isValid && formModified)) {
      skipClose.current = true;
      await formik?.submitForm();
      if (newVal.markup !== oldVal.markup) {
        setIsMarkupChange(true);
      }
      skipClose.current = false;
    }
    if (formik.isValid) {
      setItemToBeUpdate(allRecords[currentItemIndex + increment]);
    } else {
      document.activeElement instanceof HTMLElement &&
        document.activeElement.blur();
    }
  };
  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
  };
  return (
    <>
      <Drawer
        open={isOpen}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-file-invoice-dollar"
              />
            </div>
            <div className="flex justify-between w-full">
              <Header
                level={5}
                className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
              >
                {`${
                  !formData?.item_id ||
                  formData?.item_id == "0" ||
                  isAddInvoiceItem
                    ? _t("Add")
                    : ""
                } ${HTMLEntities.decode(sanitizeString(moduleName))} ${_t(
                  "Item"
                )}`}
              </Header>
              {!isAddInvoiceItem && (
                <div className="flex items-center sm:gap-2 gap-0 pr-2">
                  <ButtonWithTooltip
                    tooltipTitle={_t("Previous")}
                    tooltipPlacement="top"
                    icon="fa-regular fa-chevron-left"
                    className="item-pre-next-button disabled:bg-transparent"
                    onClick={() => {
                      handleItemNav(-1);
                    }}
                    disabled={currentItemIndex == 0}
                  />
                  <ButtonWithTooltip
                    tooltipTitle={_t("Next")}
                    tooltipPlacement="top"
                    icon="fa-regular fa-chevron-right"
                    className="item-pre-next-button disabled:bg-transparent"
                    disabled={currentItemIndex == allRecords.length - 1}
                    onClick={() => {
                      handleItemNav(1);
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        }
        closeIcon={<CloseButton onClick={() => onClose(false)} />}
      >
        <Form
          method="post"
          className="py-4"
          onSubmit={handleSubmit}
          noValidate
          onKeyDown={(e) => {
            if (
              e.key === "Enter" &&
              (e.target as HTMLElement).tagName !== "TEXTAREA"
            ) {
              e.preventDefault();
            }
          }}
        >
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <InputField
                    label={_t("Item Name")}
                    labelPlacement="top"
                    name="subject"
                    value={HTMLEntities.decode(sanitizeString(values?.subject))}
                    errorMessage={touched?.subject ? errors?.subject : ""}
                    disabled={isViewOnly}
                    isRequired={true}
                    onChange={(e) => {
                      setFieldValue("subject", e.target.value);
                    }}
                    onBlur={(e) => {
                      setFieldValue("subject", e.target.value.trim());
                      formik.handleBlur(e);
                    }}
                    autoComplete="off"
                  />
                </div>
                <div
                  className={`${
                    isHideItemType
                      ? "w-full"
                      : "grid md:grid-cols-2 md:gap-5 gap-5"
                  }`}
                >
                  <div className={`w-full ${isHideItemType && "hidden"}`}>
                    <SelectField
                      label={_t("Item Type")}
                      labelPlacement="top"
                      isRequired={true}
                      disabled={
                        isAddInvoiceItem
                          ? false
                          : Number(formData?.reference_item_id) > 0 ||
                            formData?.item_on_database == 1 ||
                            formData?.add_item_to_database == 1 ||
                            isViewOnly
                      }
                      value={
                        values.item_type?.toString() !== "0"
                          ? values.item_type?.toString()
                          : ""
                      }
                      options={reorderedItemsTypes.map((item: GType) => ({
                        label: (
                          <div className="flex items-center gap-1.5">
                            <FontAwesomeIcon
                              icon={getItemTypeIcon({
                                type: item?.type_id?.toString(),
                              })}
                            />
                            {item?.name}
                          </div>
                        ),
                        value: item.type_id,
                        ...item,
                      }))}
                      errorMessage={touched?.item_type ? errors?.item_type : ""}
                      onChange={(value, option) => {
                        setFieldValue("item_type", parseNumber(value));
                        setFieldValue("item_type_name", option?.name);
                        if (option?.name?.trim()) {
                          formik.setFieldError("item_type", "");
                        } else {
                          formik.setFieldError(
                            "item_type",
                            _t("This field is required.")
                          );
                        }
                        if (values?.is_markup_percentage == "1") {
                          const itemType = itemTypesWithMarkup?.find(
                            (i: IWorkOrderType) =>
                              i.type_id?.toString() === value?.toString()
                          );

                          setFieldValue("markup", itemType?.mark_up || "");
                        }
                      }}
                    />
                  </div>
                  <div className="w-full">
                    <ButtonField
                      label={_t("Assigned To")}
                      name="assigned_to"
                      isDisabled={isViewOnly}
                      value={HTMLEntities.decode(
                        sanitizeString(
                          values?.assigned_to !== 0 ? values?.assignee_name : ""
                        )
                      )}
                      onClick={() => {
                        setIsOpenSelectAssignedTo(true);
                      }}
                      avatarProps={{
                        user: {
                          name: HTMLEntities.decode(
                            sanitizeString(values?.assignee_name)
                          ),
                          image:
                            values?.assigned_to_contact_id === 0 ||
                            values?.assigned_to_contact_id === null ||
                            !values?.assigned_to_contact_id
                              ? values?.user_image
                              : "",
                        },
                      }}
                      addonBefore={
                        <div className="flex items-center gap-1">
                          {values?.assigned_to && values?.assignee_name ? (
                            <ContactDetailsButton
                              onClick={(e: React.MouseEvent) => {
                                e.stopPropagation();
                                setIsOpenContactDetails(true);
                              }}
                            />
                          ) : (
                            <></>
                          )}
                        </div>
                      }
                    />
                  </div>
                </div>

                <div className="w-full">
                  <SelectField
                    label={_t("Cost Code")}
                    labelPlacement="top"
                    key={values?.cost_code_id}
                    value={
                      values?.cost_code_id
                        ? costCodeOptions.filter((item) => {
                            return (
                              values?.cost_code_id?.toString() ===
                              item?.value?.toString()
                            );
                          })
                        : []
                    }
                    onChange={(value) => {
                      setFieldValue("cost_code_id", value);
                    }}
                    showSearch
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                    options={costCodeOptions}
                    disabled={isViewOnly}
                    allowClear={true}
                    onClear={() => {
                      setFieldValue("cost_code_id", "");
                    }}
                  />
                </div>
              </SidebarCardBorder>
              <SidebarCardBorder cardTitle={_t("Pricing")}>
                <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex justify-between items-center">
                    <Typography className="text-13 block text-primary-900 dark:text-white/90">
                      {_t("Qty")}
                    </Typography>
                    <div className="sm:w-40 w-28">
                      <InputNumberField
                        name="quantity"
                        id="quantity"
                        rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input"
                        placeholder={_t("Item Quantity")}
                        disabled={isViewOnly}
                        errorMessage={errors.quantity}
                        onPaste={handlePaste}
                        labelPlacement="left"
                        formInputClassName={
                          isViewOnly ? "flex items-center justify-end" : ""
                        }
                        defaultValue={
                          Number(values.quantity) !== 0 ? values.quantity : ""
                        }
                        value={
                          Number(values.quantity) !== 0
                            ? values.quantity?.toString()
                            : ""
                        }
                        formatter={(value) => {
                          return inputFormatter(value?.toString()).value;
                        }}
                        onChange={(value) => {
                          setFieldValue("quantity", value?.toString());
                        }}
                        parser={(value) => {
                          const inputValue = value
                            ? unformatted(value.toString())
                            : "";
                          return inputValue;
                        }}
                        onKeyDown={(event) => {
                          onKeyDownCurrency(event, {
                            integerDigits: 8,
                            decimalDigits: 2,
                            unformatted,
                            allowNegative: true,
                            decimalSeparator:
                              inputFormatter().decimal_separator,
                          });
                          if (
                            event.key === "Tab" &&
                            !event.shiftKey &&
                            !event.altKey &&
                            !event.ctrlKey &&
                            !event.metaKey
                          ) {
                            event.preventDefault();
                            setShowUnitInputs(true);

                            setTimeout(() => {
                              const unitCostInput =
                                document.getElementById("unit_cost");
                              if (unitCostInput) {
                                unitCostInput.focus();
                              }
                            }, 0);
                          }
                        }}
                      />
                    </div>
                  </li>
                  <li>
                    <ul className="py-0.5 relative">
                      <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                        <FontAwesomeIcon
                          className="w-3 h-3 text-primary-900 dark:text-white"
                          icon="fa-regular fa-xmark"
                        />
                      </li>
                    </ul>
                  </li>
                  <li className="flex justify-between items-center">
                    <Typography className="text-13 block text-primary-900 dark:text-white/90">
                      {_t("Unit Cost/Unit")}
                    </Typography>
                    <div
                      className="sm:w-[260px] w-28 h-[22px]"
                      ref={costUnitRef}
                    >
                      <div
                        ref={unitCostContainerRef}
                        className="text-right !text-[#008000] leading-[22px] !font-semibold text-sm"
                      >
                        {!isViewOnly && (
                          <>
                            {showUnitInputs ? (
                              <div className="flex gap-2">
                                <div className="w-[calc(100%-52px)]">
                                  <InputNumberField
                                    name="unit_cost"
                                    id="unit_cost"
                                    rootClassName={`!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input placeholder:text-13 ${
                                      !showUnitInputs && "!hidden"
                                    }`}
                                    placeholder={_t("Item Unit Cost")}
                                    disabled={
                                      isViewOnly ||
                                      details?.approval_type_key ===
                                        "invoice_submitted"
                                    }
                                    autoFocus={Boolean(
                                      values?.unit_cost &&
                                        !isEmpty(values?.unit_cost) &&
                                        values.unit &&
                                        !isEmpty(values.unit)
                                    )}
                                    onPaste={handlePaste}
                                    labelPlacement="left"
                                    errorMessage={errors.unit_cost}
                                    defaultValue={
                                      Number(values.unit_cost) !== 0
                                        ? values.unit_cost
                                        : ""
                                    }
                                    value={
                                      Number(values.unit_cost) !== 0
                                        ? values.unit_cost
                                        : ""
                                    }
                                    onChange={(value) => {
                                      setFieldValue(
                                        "unit_cost",
                                        value?.toString() || "0"
                                      );
                                    }}
                                    formatter={(value, info) => {
                                      return inputFormatter(value?.toString())
                                        .value;
                                    }}
                                    parser={(value) => {
                                      const inputValue = value
                                        ? unformatted(value.toString())
                                        : "";
                                      return inputValue;
                                    }}
                                    onKeyDown={(event) =>
                                      onKeyDownCurrency(event, {
                                        integerDigits: 10,
                                        decimalDigits: 2,
                                        unformatted,
                                        allowNegative:
                                          formData?.is_discount_item == "1",
                                        decimalSeparator:
                                          inputFormatter().decimal_separator,
                                      })
                                    }
                                    onBlur={handleFocusOut}
                                  />
                                </div>
                                <div className="w-[65px]">
                                  {window.ENV.ENABLE_UNIT_DROPDOWN ? (
                                    <SelectField
                                      className="!p-0 text-success select-field-text-13 text-left select-unit-filed !h-[22px] !font-semibold placeholder:font-normal"
                                      placeholder="Unit"
                                      name="unit"
                                      disabled={
                                        isViewOnly ||
                                        details?.approval_type_key ===
                                          "invoice_submitted"
                                      }
                                      labelPlacement="left"
                                      maxLength={15}
                                      value={values?.unit || null}
                                      iconView={true}
                                      popupClassName="!w-[260px]"
                                      showSearch
                                      options={
                                        unitData.map((type) => ({
                                          label: type.name.toString(),
                                          value: type.name.toString(),
                                        })) ?? []
                                      }
                                      allowClear
                                      filterOption={(input, option) =>
                                        filterOptionBySubstring(
                                          input,
                                          option?.label as string
                                        )
                                      }
                                      onChange={(value) => {
                                        setFieldValue(
                                          "unit",
                                          value ? value.toString() : ""
                                        );
                                      }}
                                      addItem={{
                                        text: "Add Unit: Type Unit & Press Enter",
                                        icon: "fa-regular fa-plus",
                                      }}
                                      onInputKeyDown={(e) => {
                                        if (e.key === "Enter") {
                                          const value =
                                            e?.currentTarget?.value?.trim();
                                          const newType =
                                            onEnterSelectSearchValue(
                                              e,
                                              unitData?.map((unit) => ({
                                                label: unit?.name,
                                                value: "",
                                              })) || []
                                            );
                                          if (newType) {
                                            setNewTypeName(newType);
                                          } else if (value) {
                                            notification.error({
                                              description:
                                                "Records already exist, no new records were added.",
                                            });
                                          }
                                        }
                                      }}
                                      onClear={() => {
                                        setFieldValue("unit", "");
                                      }}
                                      onBlur={handleFocusOut}
                                      // errorMessage={errors.unit}
                                    />
                                  ) : (
                                    <InputField
                                      className={`!p-0 !pl-1.5 text-sucess field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right ${
                                        !showUnitInputs && "!hidden"
                                      }`}
                                      placeholder={_t("Unit")}
                                      labelPlacement="left"
                                      errorMessage={errors.unit}
                                      maxLength={15}
                                      onBlur={handleFocusOut}
                                      value={values?.unit}
                                      disabled={
                                        isViewOnly ||
                                        details?.approval_type_key ===
                                          "invoice_submitted"
                                      }
                                      onPaste={handlePaste}
                                      type="text"
                                      onChange={(e) => {
                                        setFieldValue("unit", e.target.value);
                                      }}
                                    />
                                  )}
                                </div>
                              </div>
                            ) : (
                              <Typography
                                className={`text-[#008000] text-13 !font-medium ${
                                  isViewOnly
                                    ? "cursor-no-drop"
                                    : "cursor-pointer"
                                }`}
                                onClick={handleParagraphClick}
                              >
                                {
                                  formatter(
                                    formatAmount(
                                      Number(values?.unit_cost).toFixed(2)
                                    )
                                  ).value_with_symbol
                                }
                                /{values?.unit}
                              </Typography>
                            )}
                          </>
                        )}

                        {isViewOnly &&
                          (!isEmpty(unitCost) &&
                          unitCost !== 0.0 &&
                          unitCost !== "0.00" &&
                          !isEmpty(unit) &&
                          unit !== 0.0 ? (
                            <Typography
                              className={`text-[#008000] font-medium text-13 ${
                                isViewOnly ||
                                details?.approval_type_key ===
                                  "invoice_submitted"
                                  ? "cursor-no-drop"
                                  : ""
                              }`}
                            >
                              {values?.unit_cost}/{values?.unit}
                            </Typography>
                          ) : (
                            <div className="flex gap-2">
                              <div className="w-[calc(100%-52px)]">
                                <InputField
                                  className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                  placeholder={_t("Item Unit Cost")}
                                  type="number"
                                  name="unit_cost"
                                  id="unit_cost"
                                  maxLength={10}
                                  disabled={isViewOnly}
                                  onPaste={handlePaste}
                                  value={
                                    Number(values.unit_cost) !== 0
                                      ? values.unit_cost
                                      : ""
                                  }
                                  onChange={() => {}}
                                />
                              </div>
                              <div className="w-11">
                                <InputField
                                  className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                  placeholder={_t("Unit")}
                                  maxLength={15}
                                  name="unit"
                                  id="unit"
                                  disabled={isViewOnly}
                                  onPaste={handlePaste}
                                  value={values?.unit}
                                  type="text"
                                  onChange={() => {}}
                                />
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  </li>
                  <li className="flex justify-between border-t border-dashed border-gray-300 pt-2 mt-2">
                    <Typography className="text-13 block text-primary-900 font-semibold">
                      {_t("Total Cost")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-red-600 text-13 font-semibold"
                        disabled={isViewOnly}
                      >
                        {values?.total === ""
                          ? `${
                              formatter(formatAmount("0.00")).value_with_symbol
                            }`
                          : `${
                              formatter(
                                formatAmount(
                                  Number(values?.total || 0).toFixed(2)
                                )
                              ).value_with_symbol
                            }`}
                      </Typography>
                    </div>
                  </li>
                </ul>
                <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                  <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900 dark:text-white"
                      icon="fa-regular fa-plus"
                    />
                  </li>
                </ul>
                <ul className="grid gap-2 border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex items-center justify-between">
                    <div className="flex items-center gap-1.5">
                      <div className="w-fit p-0.5 bg-[#EEEFF0] rounded dark:bg-dark-800 ml-auto md:ml-0 sidemenu-listbutton">
                        <ListTabButton
                          value={markup ? markup : ""}
                          options={INVOICE_ITEMS_LIST_TAB}
                          disabled={isViewOnly}
                          className="min-w-[26px] !border-transparent bg-[#EEEFF0] py-0 px-1 h-5"
                          activeclassName="active:bg-[#ffffff]"
                          onChange={(e: RadioChangeEvent) => {
                            setMarkup(e.target.value);
                            setFieldValue("markup", "");
                            if (e.target.value === "markup_percent") {
                              setFieldValue("is_markup_percentage", 1);
                            } else {
                              setFieldValue("is_markup_percentage", 0);
                            }
                          }}
                        />
                      </div>
                      <div className="w-5 h-5 flex items-center justify-center group/buttonHover hover:bg-[#f0f0f0]">
                        <Tooltip
                          title={_t(
                            `% -- Add the % amount that the item should be marked up. ${currentCurrency} -- Add the ${currentCurrency} amount that should be charged for the item.`
                          )}
                          rootClassName="!max-w-[265px]"
                        >
                          <FontAwesomeIcon
                            className="w-3.5 h-3.5 !text-primary-900/80 group-hover/buttonHover:!text-primary-900"
                            icon="fa-regular fa-circle-info"
                          />
                        </Tooltip>
                      </div>
                    </div>
                    <div className="sm:w-40 w-28">
                      <InputField
                        className="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                        placeholder={
                          markup === "markup_percent"
                            ? _t("Item Markup") + " %"
                            : _t("Total Sales Price")
                        }
                        value={values?.markup ?? ""}
                        labelPlacement="left"
                        type="text"
                        disabled={isViewOnly}
                        onPaste={handlePaste}
                        onChange={(e) => {
                          const inputVal = e.target.value;
                          if (inputVal === "") {
                            setFieldValue("markup", "");
                            return;
                          }

                          if (!floatNumberRegex.test(inputVal)) {
                            return;
                          }

                          if (values?.is_markup_percentage) {
                            if (inputVal.length > 3) {
                              return;
                            }
                            if (!wholeNumberRegex.test(inputVal)) {
                              return;
                            }
                          }

                          const cleanedInput = inputVal
                            .split(".")[0]
                            .replace("-", "");
                          if (cleanedInput.length > 10) {
                            return;
                          }

                          setFieldValue(
                            "markup",
                            markup === "markup_percent"
                              ? Number(inputVal)
                              : inputVal
                          );
                        }}
                      />
                    </div>
                  </li>
                  <li className="flex items-center justify-between">
                    <Typography className="text-13 block text-primary-900">
                      {_t("Markup")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-[#008000] text-13 font-medium"
                        disabled={isViewOnly}
                      >
                        {markup === "markup_percent"
                          ? values?.markup_amount === ""
                            ? `${formatter("0.00").value_with_symbol}`
                            : `${
                                formatter(
                                  formatAmount(
                                    Number(values?.markup_amount || 0)
                                  )
                                ).value_with_symbol
                              }`
                          : values?.markup_amount === ""
                          ? "0.00%"
                          : `${Number(values?.markup_amount || 0)?.toFixed(
                              2
                            )}%`}
                      </Typography>
                    </div>
                  </li>
                </ul>
                <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                  <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900 dark:text-white"
                      icon="fa-regular fa-equals"
                    />
                  </li>
                </ul>
                <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex items-center justify-between">
                    <Typography className="text-13 block text-primary-900 font-semibold">
                      {_t("Total Revenue")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-red-600 text-13 font-semibold"
                        disabled={isViewOnly}
                      >
                        {mainTotal === ""
                          ? `${
                              formatter(formatAmount("0.00")).value_with_symbol
                            }`
                          : `${
                              formatter(
                                formatAmount(Number(mainTotal || 0).toFixed(2))
                              ).value_with_symbol
                            }`}
                      </Typography>
                    </div>
                  </li>
                </ul>
              </SidebarCardBorder>
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <TextAreaField
                    required={false}
                    label={_t("Description")}
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and can be optionally made visible to the recipient when submitted."
                    )}
                    disabled={isViewOnly}
                    value={HTMLEntities.decode(
                      sanitizeString(values?.description)
                    )}
                    onChange={(e) => {
                      setFieldValue("description", e.target.value);
                    }}
                  />
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Internal Notes")}
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and are not visible to the recipient."
                    )}
                    disabled={isViewOnly}
                    value={HTMLEntities.decode(
                      sanitizeString(values?.internal_notes)
                    )}
                    onChange={(e) => {
                      setFieldValue("internal_notes", e.target.value);
                    }}
                  />
                </div>
                <CheckBox
                  className="gap-1.5 text-primary-900 w-fit"
                  checked={!!values?.apply_global_tax}
                  onChange={(event) => {
                    const valueToSet: number = event.target.checked ? 1 : 0;
                    setFieldValue("apply_global_tax", valueToSet);
                  }}
                  disabled={isViewOnly}
                >
                  {_t("Collect Tax on this Item?")}
                </CheckBox>

                {isItemOnDatabase && (
                  <CheckBox
                    className="gap-1.5 text-primary-900 w-fit"
                    checked={
                      !!values.item_on_database || !!values.add_item_to_database
                    }
                    onChange={(event) => {
                      const valueToSet: number = event.target.checked ? 1 : 0;
                      setFieldValue("item_on_database", valueToSet);
                    }}
                    disabled={
                      formData?.add_item_to_database == 1 ||
                      formData?.item_on_database == 1
                        ? true
                        : isViewOnly
                    }
                  >
                    {`${_t("Save this item into my")} ${itemTypeName} ${_t(
                      "Items list?"
                    )}`}
                  </CheckBox>
                )}
              </SidebarCardBorder>
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full gap-4 px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              onClick={() => setFieldValue("submitAction", "saveAndClose")}
              buttonText={_t("Save & Close")}
              disabled={(isSubmitting && submittingFrm == "save") || isViewOnly}
              isLoading={isSubmitting && submittingFrm == "save"}
            />
            <PrimaryButton
              htmlType="submit"
              onClick={() => setFieldValue("submitAction", "saveAndAddAnother")}
              isLoading={isSubmitting && submittingFrm == "saveAndAddAnother"}
              disabled={
                (isSubmitting && submittingFrm == "saveAndAddAnother") ||
                isViewOnly
              }
              buttonText={_t("Save & Add Another Item")}
            />
          </div>
        </Form>
      </Drawer>

      {isOpenSelectAssignedTo && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectAssignedTo}
          closeDrawer={() => {
            setIsOpenSelectAssignedTo(false);
          }}
          singleSelecte={true}
          setCustomer={(data) => {
            if (data.length) {
              if (data[0].contact_id == "0") {
                setFieldValue("assigned_to_contact_id", data[0].contact_id);
              } else {
                setFieldValue("assigned_to_contact_id", data[0].contact_id);
              }
              setFieldValue("assigned_to", data[0].user_id);
              setFieldValue("assignee_name", data[0].display_name);
              setFieldValue("user_image", data[0]?.image);
              setFieldValue("assigned_to_contact_id", data[0].contact_id);
              setFieldValue(
                "assignee_type",
                data[0].type ||
                  getDirectaryIdByKey(data[0].type_key as CustomerTabs, gConfig)
              );
              setFieldValue("user_image", data[0]?.image);
              setFieldValue("type_key", data[0].type_key);
              setFieldValue("type_name", data[0].type_name);
            } else {
              setFieldValue("assigned_to", 0);
              setFieldValue("assignee_name", "");
            }
          }}
          options={[
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.customer_key,
            defaultConfig.misc_contact_key,
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={assignedTo as TselectedContactSendMail[]}
          groupCheckBox={true}
          projectId={details?.project_id as number}
          activeTab={defaultConfig.contractor_key}
        />
      )}

      {confirmSaveData.rid > 0 && (
        <ConfirmModal
          isOpen={confirmSaveData?.rid > 0}
          modaltitle={_t("This Item Already Exists")}
          description={confirmSaveData?.message || ""}
          modalIcon="fa-regular fa-triangle-exclamation"
          yesButtonLabel={_t("Use Existing")}
          noButtonLabel={_t("Rename")}
          onAccept={() => {
            setIsExistingLoading(true);
            handleSubmit();
          }}
          isLoading={!!submittingFrm && isExistingLoading}
          onDecline={() => {
            setSubmittingFrm("");
            setConfirmSaveData({ rid: 0 });
          }}
          onCloseModal={() => {
            setSubmittingFrm("");
            setConfirmSaveData({ rid: 0 });
          }}
        />
      )}
      {isOpenContactDetails && (
        <ContactDetails
          isOpenContact={isOpenContactDetails}
          contactId={values?.assigned_to || ""}
          onCloseModal={() => {
            setIsOpenContactDetails(false);
          }}
          onEmailClick={(data) => {
            setSelectedData(data);
            setIsSendEmailSidebarOpen(true);
          }}
          readOnly={isViewOnly}
          additional_contact_id={values?.assigned_to_contact_id}
        />
      )}
      {newTypeName.trim() && (
        <ConfirmModal
          isOpen={Boolean(newTypeName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newTypeName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => setNewTypeName("")}
          onAccept={async () => {
            if (!isAddingCustomData && newTypeName) {
              setIsAddingCustomData(true);
              const response: IUnitAddResponse = await addUnit({
                name: newTypeName,
              });
              if (response.success && response.data) {
                setUnitData([{ name: newTypeName }, ...unitData]);
                setFieldValue("unit", newTypeName);

                setNewTypeName("");
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => setNewTypeName("")}
        />
      )}
      <InvoiceSendEmail
        isOpen={isSendEmailSidebarOpen}
        options={[
          defaultConfig.employee_key,
          "my_crew",
          defaultConfig.customer_key,
          defaultConfig.lead_key,
          defaultConfig.contractor_key,
          defaultConfig.vendor_key,
          defaultConfig.misc_contact_key,
          "by_service",
        ]}
        onSendResponse={() => {
          setSelectedData({});
        }}
        onClose={() => {
          setSelectedData({});
          setIsSendEmailSidebarOpen(false);
        }}
        groupCheckBox={true}
        projectId={Number(details?.project_id)}
        selectedCustomer={
          selectedData?.user_id
            ? ([selectedData] as TselectedContactSendMail[])
            : []
        }
        app_access={false}
      />
    </>
  );
};
export default InvoiceTabsItem;
