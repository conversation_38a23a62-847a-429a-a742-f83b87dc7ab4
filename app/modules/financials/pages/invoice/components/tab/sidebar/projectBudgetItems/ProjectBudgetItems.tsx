// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useAppIVSelector } from "../../../../redux/store";
import { Number, sanitizeString } from "~/helpers/helper";
import { useEffect, useMemo, useRef, useState } from "react";
import {
  GridApi,
  GridReadyEvent,
  IRowNode,
  SelectionChangedEvent,
} from "ag-grid-community";
import LazyLoadAgGridTable from "./LazyLoadAgGridTable";
import { useGModules } from "~/zustand";
import { defaultConfig } from "~/data";

const ProjectBudgetItems = ({
  projectBudgetItems,
  setProjectBudgetItems,
  onSubmit,
  selectedItems,
  isLoading,
}: IProjectBudgetItemsProps) => {
  const { _t } = useTranslation();
  const { getGModuleByKey } = useGModules();

  const invoiceItems = useAppIVSelector(
    (state) => state.proInvoiceItemList.invoiceItems
  );
  const SOVItems = useAppIVSelector(
    (state) => state.proInvoiceItemList.SOVItems
  );
  const invoiceDetails = useAppIVSelector(
    (state) => state.invoiceDetails.details
  );
  const gridApiRef = useRef<{ [key: string]: GridApi | null }>({});
  const gridChaneORderApiRef = useRef<{ [key: string]: GridApi | null }>({});
  const gridWorkOrderApiRef = useRef<{ [key: string]: GridApi | null }>({});
  const gridApprovedEstimateApiRef = useRef<{ [key: string]: GridApi | null }>(
    {}
  );

  const [isProgrammaticSelection, setIsProgrammaticSelection] =
    useState<boolean>(true);

  const [selectedSOVItems, setSelectedSOVItems] = useState<ISelectedSOVItems>({
    change_order_items: [],
    work_order_items: [],
    budget_items: [],
    estimate_items: [],
    billing_option: "",
  });

  const { formatter } = useCurrencyFormatter();

  useEffect(() => {
    setSelectedSOVItems((prev) => {
      return {
        ...prev,
        change_order_items: selectedItems.change_order_items,
        work_order_items: selectedItems.work_order_items,
        budget_items: selectedItems.budget_items,
        estimate_items: selectedItems.estimate_items,
      };
    });
  }, [selectedItems]);

  useEffect(() => {
    setSelectedSOVItems((prev) => {
      return {
        ...prev,
        billing_option: SOVItems.billing_option,
      };
    });
  }, [SOVItems.billing_option]);

  const showPercentageColumn = useMemo(() => {
    return !(SOVItems.billing_option === "percentage");
  }, [SOVItems.billing_option]);

  const showUnitColumn = useMemo(() => {
    return !(SOVItems.billing_option === "unit");
  }, [SOVItems.billing_option]);

  const handleBudgetItemsSelectionChanged = (
    event: SelectionChangedEvent
  ): void => {
    setIsProgrammaticSelection(false);
    const selectedNodes = event.api.getSelectedNodes();
    const selected = selectedNodes.map((node: IRowNode) => {
      return node.data;
    });
    const unSelectedNodes: Array<ISOVBudgetItem> = event.api
      .getRenderedNodes()
      .filter((node: IRowNode) => node.isSelected() === false)
      .map((node) => {
        return node.data;
      });

    setSelectedSOVItems((prev) => {
      const updatedSelection = [
        ...prev.budget_items,
        ...selected.filter(
          (newItem) =>
            !prev.budget_items.some((item) => item.item_id === newItem.item_id)
        ),
      ];
      const selectedItem = updatedSelection.filter(
        (node) => !unSelectedNodes.some((item) => item.item_id === node.item_id)
      );
      return { ...prev, budget_items: [...selectedItem] };
    });
  };

  const handleEstimentItemsSelectionChanged = (
    event: SelectionChangedEvent
  ): void => {
    setIsProgrammaticSelection(false);
    const selectedNodes = event.api.getSelectedNodes();
    const selected = selectedNodes.map((node: IRowNode) => {
      return node.data;
    });
    const unSelectedNodes: Array<ISOVBudgetItem> = event.api
      .getRenderedNodes()
      .filter((node: IRowNode) => node.isSelected() === false)
      .map((node) => {
        return node.data;
      });

    setSelectedSOVItems((prev) => {
      const updatedSelection = [
        ...prev.estimate_items,
        ...selected.filter(
          (newItem) =>
            !prev.estimate_items.some(
              (item) => item.item_id === newItem.item_id
            )
        ),
      ];
      const selectedItem = updatedSelection.filter(
        (node) => !unSelectedNodes.some((item) => item.item_id === node.item_id)
      );
      return { ...prev, estimate_items: [...selectedItem] };
    });
  };

  const handleChangeOrderItemsSelectionChanged = (
    event: SelectionChangedEvent
  ): void => {
    setIsProgrammaticSelection(false);
    const selectedNodes = event.api.getSelectedNodes();
    const selected = selectedNodes.map((node: IRowNode) => {
      return node.data;
    });
    const unSelectedNodes: Array<ISOVBudgetItem> = event.api
      .getRenderedNodes()
      .filter((node: IRowNode) => node.isSelected() === false)
      .map((node) => {
        return node.data;
      });

    setSelectedSOVItems((prev) => {
      const updatedSelection = [
        ...prev.change_order_items,
        ...selected.filter(
          (newItem) =>
            !prev.change_order_items.some(
              (item) => item.item_id === newItem.item_id
            )
        ),
      ];
      const selectedItem = updatedSelection.filter(
        (node) => !unSelectedNodes.some((item) => item.item_id === node.item_id)
      );
      return { ...prev, change_order_items: [...selectedItem] };
    });
  };

  const handleWorkOrderItemsSelectionChanged = (
    event: SelectionChangedEvent
  ): void => {
    setIsProgrammaticSelection(false);
    const selectedNodes = event.api.getSelectedNodes();
    const selected = selectedNodes.map((node: IRowNode) => {
      return node.data;
    });
    const unSelectedNodes: Array<ISOVBudgetItem> = event.api
      .getRenderedNodes()
      .filter((node: IRowNode) => node.isSelected() === false)
      .map((node) => {
        return node.data;
      });

    setSelectedSOVItems((prev) => {
      const updatedSelection = [
        ...prev.work_order_items,
        ...selected.filter(
          (newItem) =>
            !prev.work_order_items.some(
              (item) => item.item_id === newItem.item_id
            )
        ),
      ];
      const selectedItem = updatedSelection.filter(
        (node) => !unSelectedNodes.some((item) => item.item_id === node.item_id)
      );
      return { ...prev, work_order_items: [...selectedItem] };
    });
  };

  const onChangeOrderGridReady = (
    params: GridReadyEvent,
    sectionId: string,
    index: number
  ) => {
    if (!gridChaneORderApiRef.current) {
      gridChaneORderApiRef.current = {};
    }
    gridChaneORderApiRef.current[sectionId] = params.api as GridApi;
    SOVItems.change_order_sections.forEach((section) => {
      const groupedItems = section.items.reduce(
        (
          acc: { [key: string]: ISOVChangeOrderItem[] },
          item: ISOVChangeOrderItem
        ) => {
          const key = item.co_section_id;
          if (!acc[key]) {
            acc[key] = [];
          }
          acc[key].push(item);
          return acc;
        },
        {}
      );
      Object.keys(groupedItems).map((section_id) => {
        const selectedNodes: IRowNode[] = [];
        if (Number(section_id) === Number(sectionId)) {
          const gridToConsider = gridChaneORderApiRef.current[section_id];
          selectedItems.change_order_items.forEach(
            (row: ISOVChangeOrderItem) => {
              const index = groupedItems[sectionId]?.indexOf(row);
              const node =
                index !== -1
                  ? gridToConsider?.getRowNode(index.toString())
                  : null;

              if (node) {
                node.setSelected(true);
                selectedNodes.push(node);
              }
            }
          );
          gridToConsider?.ensureNodeVisible(selectedNodes);
        }
      });
    });
  };

  const onWorkOrderGridReady = (
    params: GridReadyEvent,
    sectionId: string,
    index: number
  ) => {
    if (!gridWorkOrderApiRef.current) {
      gridWorkOrderApiRef.current = {};
    }
    gridWorkOrderApiRef.current[sectionId] = params.api as GridApi;
    SOVItems.work_order_sections.forEach((section) => {
      const selectedNodes: IRowNode[] = [];
      if (Number(section.section_id) === Number(sectionId)) {
        const gridToConsider = params.api;
        selectedItems.work_order_items.forEach((row: ISOVWorkOrderItem) => {
          const index = section.items?.indexOf(row);
          const node =
            index !== -1 ? gridToConsider?.getRowNode(index.toString()) : null;

          if (node) {
            node.setSelected(true);
            selectedNodes.push(node);
          }
        });
        gridToConsider?.ensureNodeVisible(selectedNodes);
      }
    });
  };

  const onApprovedEstimateGridReady = (
    params: GridReadyEvent,
    sectionId: string,
    index: number
  ) => {
    if (!gridApprovedEstimateApiRef.current) {
      gridApprovedEstimateApiRef.current = {};
    }
    gridApprovedEstimateApiRef.current[sectionId] = params.api as GridApi;
    SOVItems.estimate_section.forEach((section) => {
      const selectedNodes: IRowNode[] = [];
      if (section.section_id === sectionId) {
        const gridToConsider = params.api;
        selectedItems.estimate_items.forEach((row: ISOVEstimateItem) => {
          const index = section.items?.indexOf(row);
          const node =
            index !== -1 ? gridToConsider?.getRowNode(index.toString()) : null;

          if (node) {
            node.setSelected(true);
            selectedNodes.push(node);
          }
        });
        gridToConsider?.ensureNodeVisible(selectedNodes);
      }
    });
  };

  const onGridReadyForNoneGroupBy = (params: GridReadyEvent) => {
    if (!gridApiRef.current) {
      gridApiRef.current = {};
    }
    gridApiRef.current["bugdet_items"] = params.api as GridApi;
    const selectedNodes: IRowNode[] = [];

    const gridToConsider = params.api;
    selectedItems.budget_items.forEach((row: ISOVBudgetItem) => {
      const index = SOVItems.budget_data?.indexOf(row);
      const node =
        index !== -1 ? gridToConsider?.getRowNode(index.toString()) : null;

      if (node) {
        node.setSelected(true);
        selectedNodes.push(node);
      }
    });

    gridToConsider?.ensureNodeVisible(selectedNodes);
  };

  const closeDrawer = () => {
    setProjectBudgetItems(false);
    setSelectedSOVItems({
      change_order_items: [],
      work_order_items: [],
      budget_items: [],
      estimate_items: [],
      billing_option: "",
    });
    setIsProgrammaticSelection(true);
  };

  const budgetItemsComponent = useMemo(() => {
    return (
      <LazyLoadAgGridTable
        index={0}
        title={_t("Items (Directly Added to SOV)")}
        tableProps={{
          rowMultiSelectWithClick: true,
          suppressRowClickSelection: true,
          onSelectionChanged: handleBudgetItemsSelectionChanged,
          onGridReady: (e) => onGridReadyForNoneGroupBy(e),
          rowSelection: "multiple",
          columnDefs: [
            {
              headerName: "",
              field: "checkbox",
              minWidth: 36,
              maxWidth: 36,
              checkboxSelection: true,
              headerCheckboxSelection: (params) => {
                let hasSelectable = false;
                params.api.forEachNodeAfterFilterAndSort((node) => {
                  if (
                    params.api.getRowNode((node.rowIndex || 0).toString())
                      ?.selectable
                  ) {
                    hasSelectable = true;
                  }
                });
                return hasSelectable;
              },
              suppressMenu: true,
              showDisabledCheckboxes: true,
            },
            {
              headerName: _t("Item Name"),
              field: "item",
              minWidth: 150,
              flex: 2,
              cellClass: "ag-cell-left",
              headerClass: "ag-header-left",
              suppressMenu: true,
              cellRenderer: (params: { data: ISOVBudgetItem }) => {
                const { data } = params;
                const item = `${data.subject}${
                  data.variation_name ? " " + data.variation_name : ""
                }`;
                return (
                  <Tooltip title={HTMLEntities.decode(sanitizeString(item))}>
                    <Typography className="table-tooltip-text">
                      {HTMLEntities.decode(sanitizeString(item))}
                    </Typography>
                  </Tooltip>
                );
              },
            },
            {
              headerName: _t("MU"),
              field: "mu",
              maxWidth: 70,
              minWidth: 70,
              suppressMenu: true,
              cellClass: "ag-cell-right",
              headerClass: "ag-header-right",
              cellRenderer: (params: { data: ISOVBudgetItem }) => {
                const { data } = params;
                const is_markup_percentage = data?.is_markup_percentage ?? "";
                const total =
                  Number(data?.quantity ?? 0) *
                  (Number(data?.unit_cost ?? 0) / 100);
                const markup = Number(data?.markup);

                let markupToShow = "0.00";

                if (is_markup_percentage?.toString() === "1") {
                  markupToShow = markup?.toFixed(2) ?? "0.00";
                } else {
                  if (total != 0) {
                    const markupPercentage = markup / total - 100;
                    markupToShow = markupPercentage.toFixed(2);
                  }
                }

                return (
                  <Tooltip
                    title={markupToShow ? Number(markupToShow) + "%" : ""}
                  >
                    <Typography className="table-tooltip-text">
                      {Number(markupToShow) + "%"}
                    </Typography>
                  </Tooltip>
                );
              },
            },
            {
              headerName: _t("Billed") + " (#)",
              field: "billed",
              minWidth: 100,
              maxWidth: 100,
              hide: showUnitColumn,
              suppressMenu: true,
              cellClass: "ag-cell-right",
              headerClass: "ag-header-right",
              cellRenderer: (params: { data: ISOVBudgetItem }) => {
                const { data } = params;
                const billed = `${data.total_billed}/${data.quantity}`;
                return (
                  <Tooltip title={billed}>
                    <Typography className="table-tooltip-text">
                      {billed}
                    </Typography>
                  </Tooltip>
                );
              },
            },
            {
              headerName: _t("Billed") + " (%)",
              field: "billed",
              minWidth: 100,
              maxWidth: 100,
              hide: showPercentageColumn,
              suppressMenu: true,
              cellClass: "ag-cell-right",
              headerClass: "ag-header-right",
              cellRenderer: (params: { data: ISOVBudgetItem }) => {
                const { data } = params;
                const billed = data.total_billed;
                return (
                  <Tooltip title={billed}>
                    <Typography className="table-tooltip-text">
                      {billed}
                    </Typography>
                  </Tooltip>
                );
              },
            },
            {
              headerName: _t("Total"),
              field: "total",
              minWidth: 120,
              maxWidth: 120,
              suppressMenu: true,
              cellClass: "ag-cell-right",
              headerClass: "ag-header-right",
              cellRenderer: (params: { data: ISOVBudgetItem }) => {
                const { data } = params;
                return (
                  <Tooltip
                    title={
                      formatter(((Number(data.total) || 0) / 100).toFixed(2))
                        .value_with_symbol
                    }
                  >
                    <Typography className="table-tooltip-text">
                      {
                        formatter(((Number(data.total) || 0) / 100).toFixed(2))
                          .value_with_symbol
                      }
                    </Typography>
                  </Tooltip>
                );
              },
            },
          ],
          isRowSelectable: (node) => {
            if (
              invoiceDetails.billing_option === "unit" &&
              Number(invoiceDetails.allow_overbilling) === 0
            ) {
              return !(
                Number(node.data?.total_billed) ===
                  Number(node.data?.quantity) &&
                !invoiceItems.items
                  .filter((item) => item.is_tm_item === 0)
                  .some((item) => {
                    return item.project_budget_item_id === node.data?.item_id;
                  })
              );
            } else if (
              invoiceDetails.billing_option === "percentage" &&
              Number(invoiceDetails.allow_overbilling) === 0
            ) {
              return !(
                Number(node.data?.total_billed) >= 100 &&
                !invoiceItems.items
                  .filter((item) => item.is_tm_item === 0)
                  .some((item) => {
                    return item.project_budget_item_id === node.data?.item_id;
                  })
              );
            } else {
              return true;
            }
          },
          noRowsOverlayComponent: () => (
            <NoRecords
              image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
            />
          ),
          rowData: SOVItems.budget_data.filter(
            (item) => item.work_order_id === 0
          ),
        }}
      />
    );
  }, [SOVItems.budget_data]);

  const estimateSectionComponent = useMemo(() => {
    const components = SOVItems.estimate_section.map((section, index) => {
      if (section.items.length !== 0) {
        return (
          <LazyLoadAgGridTable
            className="sov_change_order_table"
            index={index}
            title={_t(
              index === 0
                ? _t("Approved ") +
                    `${
                      getGModuleByKey(defaultConfig.estimate_module)
                        ?.plural_name || "Estimate"
                    }` +
                    _t(" Items")
                : ""
            )}
            subTitle={_t(
              HTMLEntities.decode(sanitizeString(section.section_name))
            )}
            tableProps={{
              rowMultiSelectWithClick: true,
              suppressRowClickSelection: true,
              onSelectionChanged: handleEstimentItemsSelectionChanged,
              onGridReady: (e) =>
                onApprovedEstimateGridReady(e, `${section.section_id}`, index),
              rowSelection: "multiple",
              columnDefs: [
                {
                  headerName: "",
                  field: "checkbox",
                  minWidth: 36,
                  maxWidth: 36,
                  checkboxSelection: (params: any) => {
                    return params.data?.is_optional_item?.toString() !== "1";
                  },
                  headerCheckboxSelection: (params) => {
                    let hasSelectable = false;
                    params.api.forEachNodeAfterFilterAndSort((node) => {
                      const data = node.data;
                      if (
                        data?.is_optional_item?.toString() !== "1" &&
                        params.api.getRowNode((node.rowIndex || 0).toString())
                          ?.selectable
                      ) {
                        hasSelectable = true;
                      }
                    });
                    return hasSelectable;
                  },
                  suppressMenu: true,
                  showDisabledCheckboxes: false,
                  cellRenderer: (params: any) => {
                    const node = params.node;
                    const data = node?.data;
                    const isDisabled =
                      data?.is_optional_item?.toString() === "1";

                    let getCurrentSection = invoiceItems?.estimate_section
                      ?.find(
                        (idata) =>
                          idata.section_id === data?.est_item_section_id
                      )
                      ?.items?.find(
                        (item) =>
                          item.reference_item_id ===
                          data?.reference_item_type_id
                      )?.reference_module_id;

                    if (!getCurrentSection) {
                      getCurrentSection =
                        invoiceItems?.import_from_estimate_section
                          ?.find(
                            (idata) =>
                              idata.section_id === data?.est_item_section_id
                          )
                          ?.items?.find(
                            (item) =>
                              item.reference_item_id ===
                              data?.reference_item_type_id
                          )?.reference_module_id;
                    }

                    const isChecked = getCurrentSection && !isDisabled;

                    return (
                      <div
                        className={`ag-wrapper ag-input-wrapper ag-checkbox-input-wrapper ${
                          isChecked ? "ag-checked" : ""
                        } ${isDisabled ? "ag-disabled" : ""}`}
                      >
                        <input
                          type="checkbox"
                          className="ag-input-field-input ag-checkbox-input"
                          disabled={isDisabled}
                          checked={!!isChecked}
                          readOnly
                        />
                      </div>
                    );
                  },
                },

                {
                  headerName: _t("Item Name"),
                  field: "item",
                  minWidth: 150,
                  flex: 2,
                  cellClass: "ag-cell-left",
                  headerClass: "ag-header-left",
                  suppressMenu: true,
                  cellRenderer: (params: { data: ISOVEstimateItem }) => {
                    const { data } = params;
                    console.log("data", data);
                    const item = `${data.subject}${
                      data.variation_name ? " " + data.variation_name : ""
                    }`;
                    return (
                      <Tooltip
                        title={HTMLEntities.decode(sanitizeString(item))}
                      >
                        <Typography className="table-tooltip-text">
                          {HTMLEntities.decode(sanitizeString(item))}
                          {data?.is_optional_item?.toString() === "1" ? (
                            <span className="text-gray-500">
                              {" "}
                              ({_t("Optional")})
                            </span>
                          ) : (
                            ""
                          )}
                        </Typography>
                      </Tooltip>
                    );
                  },
                },
                {
                  headerName: _t("MU"),
                  field: "mu",
                  maxWidth: 70,
                  minWidth: 70,
                  suppressMenu: true,
                  cellClass: "ag-cell-right",
                  headerClass: "ag-header-right",
                  cellRenderer: (params: { data: ISOVEstimateItem }) => {
                    const { data } = params;
                    const is_markup_percentage =
                      data?.is_markup_percentage ?? "";
                    const total =
                      Number(data?.quantity ?? 0) *
                      (Number(data?.unit_cost ?? 0) / 100);
                    const markup = Number(data?.markup);

                    let markupToShow = "0.00";

                    if (is_markup_percentage?.toString() === "1") {
                      markupToShow = markup?.toFixed(2) ?? "0.00";
                    } else {
                      if (total != 0) {
                        const markupPercentage = markup / total - 100;
                        markupToShow = markupPercentage.toFixed(2);
                      }
                    }

                    return (
                      <Tooltip
                        title={markupToShow ? Number(markupToShow) + "%" : ""}
                      >
                        <Typography className="table-tooltip-text">
                          {Number(markupToShow) + "%"}
                        </Typography>
                      </Tooltip>
                    );
                  },
                },
                {
                  headerName: _t("Billed") + " (#)",
                  field: "billed",
                  minWidth: 100,
                  maxWidth: 100,
                  hide: showUnitColumn,
                  suppressMenu: true,
                  cellClass: "ag-cell-right",
                  headerClass: "ag-header-right",
                  cellRenderer: (params: { data: ISOVEstimateItem }) => {
                    const { data } = params;
                    const billed = `${data.total_billed}/${data.quantity}`;
                    return (
                      <Tooltip title={billed}>
                        <Typography className="table-tooltip-text">
                          {billed}
                        </Typography>
                      </Tooltip>
                    );
                  },
                },
                {
                  headerName: _t("Billed") + " (%)",
                  field: "billed",
                  minWidth: 100,
                  maxWidth: 100,
                  hide: showPercentageColumn,
                  suppressMenu: true,
                  cellClass: "ag-cell-right",
                  headerClass: "ag-header-right",
                  cellRenderer: (params: { data: ISOVEstimateItem }) => {
                    const { data } = params;
                    const billed = data.total_billed;
                    return (
                      <Tooltip title={billed}>
                        <Typography className="table-tooltip-text">
                          {billed}
                        </Typography>
                      </Tooltip>
                    );
                  },
                },
                {
                  headerName: _t("Total"),
                  field: "total",
                  minWidth: 120,
                  maxWidth: 120,
                  suppressMenu: true,
                  cellClass: "ag-cell-right",
                  headerClass: "ag-header-right",
                  cellRenderer: (params: { data: ISOVEstimateItem }) => {
                    const { data } = params;
                    return (
                      <Tooltip
                        title={
                          formatter(
                            ((Number(data.total) || 0) / 100).toFixed(2)
                          ).value_with_symbol
                        }
                      >
                        <Typography className="table-tooltip-text">
                          {
                            formatter(
                              ((Number(data.total) || 0) / 100).toFixed(2)
                            ).value_with_symbol
                          }
                        </Typography>
                      </Tooltip>
                    );
                  },
                },
              ],
              isRowSelectable: (node) => {
                let isRowSelectable = true;
                if (
                  invoiceDetails.billing_option === "unit" &&
                  Number(invoiceDetails.allow_overbilling) === 0
                )
                  if (invoiceDetails.is_new_tm_invoice === 1) {
                    isRowSelectable = !(
                      Number(node.data?.total_billed) ===
                        Number(node.data?.quantity) &&
                      !invoiceItems.estimate_section.some((section) =>
                        section.items.some(
                          (item) =>
                            item.project_budget_item_id === node.data?.item_id
                        )
                      )
                    );
                  } else {
                    isRowSelectable = !(
                      Number(node.data?.total_billed) ===
                        Number(node.data?.quantity) &&
                      !invoiceItems.items
                        .filter((item) => item.is_tm_item === 0)
                        .some((item) => {
                          return (
                            item.project_budget_item_id === node.data?.item_id
                          );
                        })
                    );
                  }
                else {
                  isRowSelectable = true;
                }

                let getCurrentSection =
                  invoiceItems?.import_from_estimate_section
                    ?.find(
                      (idata) =>
                        idata.section_id === node?.data?.est_item_section_id
                    )
                    ?.items?.find(
                      (item) =>
                        item.reference_item_id ===
                        node?.data?.reference_item_type_id
                    )?.reference_module_id;

                return isRowSelectable && !getCurrentSection;
              },
              noRowsOverlayComponent: () => (
                <NoRecords
                  image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                />
              ),
              rowData: section.items,
            }}
          />
        );
      } else {
        return <></>;
      }
    });

    const noDataTable = (
      <LazyLoadAgGridTable
        className="sov_change_order_table"
        index={0}
        title={_t(
          _t("Approved ") +
            `${
              getGModuleByKey(defaultConfig.estimate_module)?.plural_name ||
              "Estimate"
            }` +
            _t(" Items")
        )}
        tableProps={{
          rowMultiSelectWithClick: true,
          suppressRowClickSelection: true,
          onSelectionChanged: handleEstimentItemsSelectionChanged,

          rowSelection: "multiple",
          columnDefs: [
            {
              headerName: "",
              field: "checkbox",
              minWidth: 36,
              maxWidth: 36,
              checkboxSelection: true,
              headerCheckboxSelection: (params) => {
                let hasSelectable = false;
                params.api.forEachNodeAfterFilterAndSort((node) => {
                  if (
                    params.api.getRowNode((node.rowIndex || 0).toString())
                      ?.selectable
                  ) {
                    hasSelectable = true;
                  }
                });
                return hasSelectable;
              },
              suppressMenu: true,
              showDisabledCheckboxes: false,
              cellRenderer: (params: any) => {
                const node = params.node;
                let getCurrentSection = invoiceItems?.estimate_section
                  ?.find(
                    (idata) =>
                      idata.section_id === node?.data?.est_item_section_id
                  )
                  ?.items?.find(
                    (item) =>
                      item.reference_item_id ===
                      node?.data?.reference_item_type_id
                  )?.reference_module_id;

                if (!getCurrentSection) {
                  getCurrentSection = invoiceItems?.import_from_estimate_section
                    ?.find(
                      (idata) =>
                        idata.section_id === node?.data?.est_item_section_id
                    )
                    ?.items?.find(
                      (item) =>
                        item.reference_item_id ===
                        node?.data?.reference_item_type_id
                    )?.reference_module_id;
                }
                return (
                  <>
                    <div
                      className={`ag-wrapper ag-input-wrapper ag-checkbox-input-wrapper ag-disabled ${
                        getCurrentSection ? "ag-checked" : ""
                      }`}
                    >
                      <input className="ag-input-field-input ag-checkbox-input" />
                    </div>
                  </>
                );
              },
            },
            {
              headerName: _t("Item Name"),
              field: "item",
              minWidth: 150,
              flex: 2,
              cellClass: "ag-cell-left",
              headerClass: "ag-header-left",
              suppressMenu: true,
              cellRenderer: (params: { data: ISOVEstimateItem }) => {
                const { data } = params;
                const item = `${data.subject}${
                  data.variation_name ? " " + data.variation_name : ""
                }`;
                return (
                  <Tooltip title={HTMLEntities.decode(sanitizeString(item))}>
                    <Typography className="table-tooltip-text">
                      {HTMLEntities.decode(sanitizeString(item))}
                    </Typography>
                  </Tooltip>
                );
              },
            },
            {
              headerName: _t("MU"),
              field: "mu",
              maxWidth: 70,
              minWidth: 70,
              suppressMenu: true,
              cellClass: "ag-cell-right",
              headerClass: "ag-header-right",
              cellRenderer: (params: { data: ISOVEstimateItem }) => {
                const { data } = params;
                const is_markup_percentage = data?.is_markup_percentage ?? "";
                const total =
                  Number(data?.quantity ?? 0) *
                  (Number(data?.unit_cost ?? 0) / 100);
                const markup = Number(data?.markup);

                let markupToShow = "0.00";

                if (is_markup_percentage?.toString() === "1") {
                  markupToShow = markup?.toFixed(2) ?? "0.00";
                } else {
                  if (total != 0) {
                    const markupPercentage = markup / total - 100;
                    markupToShow = markupPercentage.toFixed(2);
                  }
                }

                return (
                  <Tooltip
                    title={markupToShow ? Number(markupToShow) + "%" : ""}
                  >
                    <Typography className="table-tooltip-text">
                      {Number(markupToShow) + "%"}
                    </Typography>
                  </Tooltip>
                );
              },
            },
            {
              headerName: _t("Billed") + " (#)",
              field: "billed",
              minWidth: 100,
              maxWidth: 100,
              hide: showUnitColumn,
              suppressMenu: true,
              cellClass: "ag-cell-right",
              headerClass: "ag-header-right",
              cellRenderer: (params: { data: ISOVEstimateItem }) => {
                const { data } = params;
                const billed = `${data.total_billed}/${data.quantity}`;
                return (
                  <Tooltip title={billed}>
                    <Typography className="table-tooltip-text">
                      {billed}
                    </Typography>
                  </Tooltip>
                );
              },
            },
            {
              headerName: _t("Billed") + " (%)",
              field: "billed",
              minWidth: 100,
              maxWidth: 100,
              hide: showPercentageColumn,
              suppressMenu: true,
              cellClass: "ag-cell-right",
              headerClass: "ag-header-right",
              cellRenderer: (params: { data: ISOVEstimateItem }) => {
                const { data } = params;
                const billed = data.total_billed;
                return (
                  <Tooltip title={billed}>
                    <Typography className="table-tooltip-text">
                      {billed}
                    </Typography>
                  </Tooltip>
                );
              },
            },
            {
              headerName: _t("Total"),
              field: "total",
              minWidth: 120,
              maxWidth: 120,
              suppressMenu: true,
              cellClass: "ag-cell-right",
              headerClass: "ag-header-right",
              cellRenderer: (params: { data: ISOVEstimateItem }) => {
                const { data } = params;
                return (
                  <Tooltip
                    title={
                      formatter(((Number(data.total) || 0) / 100).toFixed(2))
                        .value_with_symbol
                    }
                  >
                    <Typography className="table-tooltip-text">
                      {
                        formatter(((Number(data.total) || 0) / 100).toFixed(2))
                          .value_with_symbol
                      }
                    </Typography>
                  </Tooltip>
                );
              },
            },
          ],
          isRowSelectable: (node) => {
            let isRowSelectable = true;
            if (
              invoiceDetails.billing_option === "unit" &&
              Number(invoiceDetails.allow_overbilling) === 0
            )
              if (invoiceDetails.is_new_tm_invoice === 1) {
                isRowSelectable = !(
                  Number(node.data?.total_billed) ===
                    Number(node.data?.quantity) &&
                  !invoiceItems.estimate_section.some((section) =>
                    section.items.some(
                      (item) =>
                        item.project_budget_item_id === node.data?.item_id
                    )
                  )
                );
              } else {
                isRowSelectable = !(
                  Number(node.data?.total_billed) ===
                    Number(node.data?.quantity) &&
                  !invoiceItems.items
                    .filter((item) => item.is_tm_item === 0)
                    .some((item) => {
                      return item.project_budget_item_id === node.data?.item_id;
                    })
                );
              }
            else {
              isRowSelectable = true;
            }

            let getCurrentSection = invoiceItems?.import_from_estimate_section
              ?.find(
                (idata) => idata.section_id === node?.data?.est_item_section_id
              )
              ?.items?.find(
                (item) =>
                  item.reference_item_id === node?.data?.reference_item_type_id
              )?.reference_module_id;

            return isRowSelectable && !getCurrentSection;
          },
          noRowsOverlayComponent: () => (
            <NoRecords
              image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
            />
          ),
          rowData: [],
        }}
      />
    );
    return SOVItems.estimate_section.length ? components : noDataTable;
  }, [SOVItems.estimate_section]);

  const changeOrderComponent = useMemo(() => {
    const components = SOVItems.change_order_sections.map((section, index) => {
      const groupedItems = section.items.reduce(
        (
          acc: { [key: string]: ISOVChangeOrderItem[] },
          item: ISOVChangeOrderItem
        ) => {
          const key = item.co_section_id;
          if (!acc[key]) {
            acc[key] = [];
          }
          acc[key].push(item);
          return acc;
        },
        {}
      );

      return Object.keys(groupedItems).map((sectionID, indexOne) => {
        if (groupedItems[sectionID].length !== 0) {
          const isHeaderSelectable = groupedItems[sectionID].some((coItem) => {
            if (
              invoiceDetails.billing_option === "unit" &&
              Number(invoiceDetails.allow_overbilling) === 0
            ) {
              if (invoiceDetails.is_new_tm_invoice === 1) {
                return (
                  coItem?.is_optional_item !== 1 &&
                  !(
                    (Number(coItem.total_billed) === Number(coItem.quantity) &&
                      !invoiceItems.change_order_sections
                        .filter((section) => section.items[0]?.is_tm_item === 0)
                        .some((section) =>
                          section.items.some(
                            (item) =>
                              item.project_budget_item_id === coItem.item_id
                          )
                        )) ||
                    invoiceItems.change_order_sections
                      .filter((section) => section.items[0]?.is_tm_item === 1)
                      .some((section) =>
                        section.items.some(
                          (item) =>
                            item.reference_item_id ===
                            coItem?.reference_item_type_id
                        )
                      ) ||
                    invoiceItems.purchase_order_sections.some((section) =>
                      section.items.some(
                        (item) =>
                          item.reference_item_id ===
                          coItem.reference_item_type_id
                      )
                    )
                  )
                );
              } else {
                return (
                  coItem?.is_optional_item !== 1 &&
                  !(
                    (Number(coItem.total_billed) === Number(coItem.quantity) &&
                      !invoiceItems.items
                        .filter((item) => item.is_tm_item === 0)
                        .some((item) => {
                          return item.project_budget_item_id === coItem.item_id;
                        })) ||
                    invoiceItems.items.some(
                      (item) =>
                        item.reference_item_id === coItem.reference_item_type_id
                    )
                  )
                );
              }
            } else {
              if (invoiceDetails.is_new_tm_invoice === 1) {
                return (
                  coItem?.is_optional_item !== 1 &&
                  !(
                    (Number(coItem.total_billed) >= 100 &&
                      !invoiceItems.change_order_sections
                        .filter((section) => section.items[0]?.is_tm_item === 0)
                        .some((section) =>
                          section.items.some(
                            (item) =>
                              item.project_budget_item_id === coItem.item_id
                          )
                        )) ||
                    invoiceItems.purchase_order_sections.some((section) =>
                      section.items.some(
                        (item) =>
                          item.reference_item_id ===
                          coItem.reference_item_type_id
                      )
                    )
                  )
                );
              } else {
                return (
                  coItem?.is_optional_item !== 1 &&
                  !(
                    (Number(coItem.total_billed) >= 100 &&
                      !invoiceItems.items
                        .filter((item) => item.is_tm_item === 0)
                        .some((item) => {
                          return item.project_budget_item_id === coItem.item_id;
                        })) ||
                    invoiceItems.items.some(
                      (item) =>
                        item.reference_item_id === coItem.reference_item_type_id
                    )
                  )
                );
              }
            }
          });

          return (
            <>
              {index === 0 && indexOne === 0 ? (
                <Header level={4} className="!text-sm !mb-0 !text-[#4B4B4B] ">
                  {`${
                    getGModuleByKey(defaultConfig.change_order_module)
                      ?.plural_name || "Change Order"
                  }
                  ` + _t(" Items")}
                </Header>
              ) : (
                <></>
              )}
              <LazyLoadAgGridTable
                className="sov_change_order_table"
                index={index}
                title={
                  indexOne === 0
                    ? _t(
                        HTMLEntities.decode(
                          sanitizeString(
                            `Change Order #${section.company_order_id}`
                          )
                        )
                      )
                    : ""
                }
                subTitle={_t(
                  HTMLEntities.decode(
                    sanitizeString(groupedItems[sectionID][0].co_section_name)
                  )
                )}
                tableProps={{
                  rowMultiSelectWithClick: true,
                  suppressRowClickSelection: true,
                  onSelectionChanged: handleChangeOrderItemsSelectionChanged,
                  onGridReady: (e) =>
                    onChangeOrderGridReady(e, `${sectionID}`, index),
                  rowSelection: "multiple",
                  columnDefs: [
                    {
                      headerName: "",
                      field: "checkbox",
                      minWidth: 36,
                      maxWidth: 36,
                      checkboxSelection: true,
                      headerCheckboxSelection: isHeaderSelectable,
                      showDisabledCheckboxes: false,
                      flex: 2,
                      cellRenderer: (params: any) => {
                        const { displayName, column, data } = params;
                        const isCOexistInPO =
                          invoiceDetails.is_new_tm_invoice === 1
                            ? invoiceItems.purchase_order_sections.some(
                                (section) =>
                                  section.items.some(
                                    (item) =>
                                      item.reference_item_id ===
                                      data?.reference_item_type_id
                                  )
                              )
                            : invoiceItems.items.some(
                                (item) =>
                                  item.reference_item_id ===
                                  data?.reference_item_type_id
                              );

                        const disabledTooltip =
                          data.is_optional_item === 1
                            ? "This is Optional item and will not be included into the Invoice"
                            : "This item/record has already been included in an invoice through another linked item and cannot be added or imported again.";

                        const isSelectable =
                          data.is_optional_item !== 1 &&
                          !(
                            (Number(data?.total_billed) >= 100 &&
                              !invoiceItems.change_order_sections.some(
                                (section) =>
                                  section.items.some(
                                    (item) =>
                                      item.project_budget_item_id ===
                                      data?.item_id
                                  )
                              )) ||
                            isCOexistInPO
                          );

                        return (
                          <>
                            <Tooltip
                              title={_t(isSelectable ? "" : disabledTooltip)}
                            >
                              <div className="ag-wrapper ag-input-wrapper ag-checkbox-input-wrapper ag-disabled">
                                <input className="ag-input-field-input ag-checkbox-input" />
                              </div>
                            </Tooltip>
                          </>
                        );
                      },
                    },
                    {
                      headerName: _t("Item Name"),
                      field: "item",
                      minWidth: 150,
                      flex: 2,
                      cellClass: "ag-cell-left",
                      headerClass: "ag-header-left",
                      suppressMenu: true,
                      cellRenderer: (params: { data: ISOVChangeOrderItem }) => {
                        const { data } = params;
                        const item = `${data.subject}${
                          data.variation_name ? " " + data.variation_name : ""
                        }`;
                        return (
                          <Tooltip
                            title={HTMLEntities.decode(sanitizeString(item))}
                          >
                            <Typography className="table-tooltip-text">
                              {HTMLEntities.decode(sanitizeString(item))}
                              {data.is_optional_item === 1 ? (
                                <span className="text-gray-500 ml-1">
                                  (Optional){" "}
                                </span>
                              ) : (
                                <></>
                              )}
                            </Typography>
                          </Tooltip>
                        );
                      },
                    },
                    {
                      headerName: _t("MU"),
                      field: "mu",
                      maxWidth: 70,
                      minWidth: 70,
                      suppressMenu: true,
                      cellClass: "ag-cell-right",
                      headerClass: "ag-header-right",
                      cellRenderer: (params: { data: ISOVChangeOrderItem }) => {
                        const { data } = params;
                        const is_markup_percentage =
                          data?.is_markup_percentage ?? "";
                        const total =
                          Number(data?.quantity ?? 0) *
                          (Number(data?.unit_cost ?? 0) / 100);
                        const markup = Number(data?.markup);

                        let markupToShow = "0.00";

                        if (is_markup_percentage?.toString() === "1") {
                          markupToShow = markup?.toFixed(2) ?? "0.00";
                        } else {
                          if (total != 0) {
                            const markupPercentage = markup / total - 100;
                            markupToShow = markupPercentage.toFixed(2);
                          }
                        }

                        return (
                          <Tooltip
                            title={
                              markupToShow ? Number(markupToShow) + "%" : ""
                            }
                          >
                            <Typography className="table-tooltip-text">
                              {Number(markupToShow) + "%"}
                            </Typography>
                          </Tooltip>
                        );
                      },
                    },
                    {
                      headerName: _t("Billed") + " (#)",
                      field: "billed",
                      minWidth: 100,
                      maxWidth: 100,
                      hide: showUnitColumn,
                      suppressMenu: true,
                      cellClass: "ag-cell-right",
                      headerClass: "ag-header-right",
                      cellRenderer: (params: { data: ISOVChangeOrderItem }) => {
                        const { data } = params;
                        const billed = `${data.total_billed}/${data.quantity}`;
                        return (
                          <Tooltip title={billed}>
                            <Typography className="table-tooltip-text">
                              {billed}
                            </Typography>
                          </Tooltip>
                        );
                      },
                    },
                    {
                      headerName: _t("Billed") + " (%)",
                      field: "billed",
                      minWidth: 100,
                      maxWidth: 100,
                      hide: showPercentageColumn,
                      suppressMenu: true,
                      cellClass: "ag-cell-right",
                      headerClass: "ag-header-right",
                      cellRenderer: (params: { data: ISOVChangeOrderItem }) => {
                        const { data } = params;
                        const billed = data.total_billed;
                        return (
                          <Tooltip title={billed}>
                            <Typography className="table-tooltip-text">
                              {billed}
                            </Typography>
                          </Tooltip>
                        );
                      },
                    },
                    {
                      headerName: _t("Total"),
                      field: "total",
                      minWidth: 120,
                      maxWidth: 120,
                      suppressMenu: true,
                      cellClass: "ag-cell-right",
                      headerClass: "ag-header-right",
                      cellRenderer: (params: { data: ISOVChangeOrderItem }) => {
                        const { data } = params;
                        return (
                          <Tooltip
                            title={
                              formatter(
                                ((Number(data.total) || 0) / 100).toFixed(2)
                              ).value_with_symbol
                            }
                          >
                            <Typography className="table-tooltip-text">
                              {
                                formatter(
                                  ((Number(data.total) || 0) / 100).toFixed(2)
                                ).value_with_symbol
                              }
                            </Typography>
                          </Tooltip>
                        );
                      },
                    },
                  ],
                  isRowSelectable: (node) => {
                    if (
                      invoiceDetails.billing_option === "unit" &&
                      Number(invoiceDetails.allow_overbilling) === 0
                    ) {
                      if (invoiceDetails.is_new_tm_invoice === 1) {
                        return (
                          node.data.is_optional_item !== 1 &&
                          !(
                            (Number(node.data?.total_billed) ===
                              Number(node.data?.quantity) &&
                              !invoiceItems.change_order_sections
                                .filter(
                                  (section) =>
                                    section.items[0]?.is_tm_item === 0
                                )
                                .some((section) =>
                                  section.items.some(
                                    (item) =>
                                      item.project_budget_item_id ===
                                      node.data?.item_id
                                  )
                                )) ||
                            invoiceItems.change_order_sections
                              .filter(
                                (section) => section.items[0]?.is_tm_item === 1
                              )
                              .some((section) =>
                                section.items.some(
                                  (item) =>
                                    item.reference_item_id ===
                                    node.data?.reference_item_type_id
                                )
                              ) ||
                            invoiceItems.purchase_order_sections.some(
                              (section) =>
                                section.items.some(
                                  (item) =>
                                    item.reference_item_id ===
                                    node.data?.reference_item_type_id
                                )
                            )
                          )
                        );
                      } else {
                        return (
                          node.data.is_optional_item !== 1 &&
                          !(
                            (Number(node.data?.total_billed) ===
                              Number(node.data?.quantity) &&
                              !invoiceItems.items
                                .filter((item) => item.is_tm_item === 0)
                                .some((item) => {
                                  return (
                                    item.project_budget_item_id ===
                                    node.data?.item_id
                                  );
                                })) ||
                            invoiceItems.items.some(
                              (item) =>
                                item.reference_item_id ===
                                node.data?.reference_item_type_id
                            )
                          )
                        );
                      }
                    } else {
                      if (invoiceDetails.is_new_tm_invoice === 1) {
                        return (
                          node.data.is_optional_item !== 1 &&
                          !(
                            (Number(node.data?.total_billed) >= 100 &&
                              !invoiceItems.change_order_sections
                                .filter(
                                  (section) =>
                                    section.items[0]?.is_tm_item === 0
                                )
                                .some((section) =>
                                  section.items.some(
                                    (item) =>
                                      item.project_budget_item_id ===
                                      node.data?.item_id
                                  )
                                )) ||
                            invoiceItems.purchase_order_sections.some(
                              (section) =>
                                section.items.some(
                                  (item) =>
                                    item.reference_item_id ===
                                    node.data?.reference_item_type_id
                                )
                            )
                          )
                        );
                      } else {
                        return (
                          node.data.is_optional_item !== 1 &&
                          !(
                            (Number(node.data?.total_billed) >= 100 &&
                              !invoiceItems.items
                                .filter((item) => item.is_tm_item === 0)
                                .some((item) => {
                                  return (
                                    item.project_budget_item_id ===
                                    node.data?.item_id
                                  );
                                })) ||
                            invoiceItems.items.some(
                              (item) =>
                                item.reference_item_id ===
                                node.data?.reference_item_type_id
                            )
                          )
                        );
                      }
                    }
                  },
                  noRowsOverlayComponent: () => (
                    <NoRecords
                      image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                    />
                  ),
                  rowData: groupedItems[sectionID],
                }}
              />
            </>
          );
        }
      });
    });

    const noDataTable = (
      <LazyLoadAgGridTable
        className="sov_change_order_table"
        index={0}
        title={_t(
          `${
            getGModuleByKey(defaultConfig.change_order_module)?.plural_name ||
            "Change Order"
          }` + _t(" Items")
        )}
        tableProps={{
          rowMultiSelectWithClick: true,
          suppressRowClickSelection: true,
          onSelectionChanged: handleChangeOrderItemsSelectionChanged,

          rowSelection: "multiple",
          columnDefs: [
            {
              headerName: "",
              field: "checkbox",
              minWidth: 36,
              maxWidth: 36,
              checkboxSelection: true,
              headerCheckboxSelection: false,
              showDisabledCheckboxes: false,
              flex: 2,
              cellRenderer: (params: any) => {
                const { displayName, column, data } = params;
                const isCOexistInPO =
                  invoiceDetails.is_new_tm_invoice === 1
                    ? invoiceItems.purchase_order_sections.some((section) =>
                        section.items.some(
                          (item) =>
                            item.reference_item_id ===
                            data?.reference_item_type_id
                        )
                      )
                    : invoiceItems.items.some(
                        (item) =>
                          item.reference_item_id ===
                          data?.reference_item_type_id
                      );

                const disabledTooltip =
                  "This item/record has already been included in an invoice through another linked item and cannot be added or imported again.";

                const isSelectable = !(
                  (Number(data?.total_billed) >= 100 &&
                    !invoiceItems.change_order_sections.some((section) =>
                      section.items.some(
                        (item) => item.project_budget_item_id === data?.item_id
                      )
                    )) ||
                  isCOexistInPO
                );

                return (
                  <>
                    <Tooltip title={_t(isSelectable ? "" : disabledTooltip)}>
                      <div className="ag-wrapper ag-input-wrapper ag-checkbox-input-wrapper ag-disabled">
                        <input className="ag-input-field-input ag-checkbox-input" />
                      </div>
                    </Tooltip>
                  </>
                );
              },
            },
            {
              headerName: _t("Item Name"),
              field: "item",
              minWidth: 150,
              flex: 2,
              cellClass: "ag-cell-left",
              headerClass: "ag-header-left",
              suppressMenu: true,
              cellRenderer: (params: { data: ISOVChangeOrderItem }) => {
                const { data } = params;
                const item = `${data.subject}${
                  data.variation_name ? " " + data.variation_name : ""
                }`;
                return (
                  <Tooltip title={HTMLEntities.decode(sanitizeString(item))}>
                    <Typography className="table-tooltip-text">
                      {HTMLEntities.decode(sanitizeString(item))}
                    </Typography>
                  </Tooltip>
                );
              },
            },
            {
              headerName: _t("MU"),
              field: "mu",
              maxWidth: 70,
              minWidth: 70,
              suppressMenu: true,
              cellClass: "ag-cell-right",
              headerClass: "ag-header-right",
              cellRenderer: (params: { data: ISOVChangeOrderItem }) => {
                const { data } = params;
                const is_markup_percentage = data?.is_markup_percentage ?? "";
                const total =
                  Number(data?.quantity ?? 0) *
                  (Number(data?.unit_cost ?? 0) / 100);
                const markup = Number(data?.markup);

                let markupToShow = "0.00";

                if (is_markup_percentage?.toString() === "1") {
                  markupToShow = markup?.toFixed(2) ?? "0.00";
                } else {
                  if (total != 0) {
                    const markupPercentage = markup / total - 100;
                    markupToShow = markupPercentage.toFixed(2);
                  }
                }

                return (
                  <Tooltip
                    title={markupToShow ? Number(markupToShow) + "%" : ""}
                  >
                    <Typography className="table-tooltip-text">
                      {Number(markupToShow) + "%"}
                    </Typography>
                  </Tooltip>
                );
              },
            },
            {
              headerName: _t("Billed") + " (#)",
              field: "billed",
              minWidth: 100,
              maxWidth: 100,
              hide: showUnitColumn,
              suppressMenu: true,
              cellClass: "ag-cell-right",
              headerClass: "ag-header-right",
              cellRenderer: (params: { data: ISOVChangeOrderItem }) => {
                const { data } = params;
                const billed = `${data.total_billed}/${data.quantity}`;
                return (
                  <Tooltip title={billed}>
                    <Typography className="table-tooltip-text">
                      {billed}
                    </Typography>
                  </Tooltip>
                );
              },
            },
            {
              headerName: _t("Billed") + " (%)",
              field: "billed",
              minWidth: 100,
              maxWidth: 100,
              hide: showPercentageColumn,
              suppressMenu: true,
              cellClass: "ag-cell-right",
              headerClass: "ag-header-right",
              cellRenderer: (params: { data: ISOVChangeOrderItem }) => {
                const { data } = params;
                const billed = data.total_billed;
                return (
                  <Tooltip title={billed}>
                    <Typography className="table-tooltip-text">
                      {billed}
                    </Typography>
                  </Tooltip>
                );
              },
            },
            {
              headerName: _t("Total"),
              field: "total",
              minWidth: 120,
              maxWidth: 120,
              suppressMenu: true,
              cellClass: "ag-cell-right",
              headerClass: "ag-header-right",
              cellRenderer: (params: { data: ISOVChangeOrderItem }) => {
                const { data } = params;
                return (
                  <Tooltip
                    title={
                      formatter(((Number(data.total) || 0) / 100).toFixed(2))
                        .value_with_symbol
                    }
                  >
                    <Typography className="table-tooltip-text">
                      {
                        formatter(((Number(data.total) || 0) / 100).toFixed(2))
                          .value_with_symbol
                      }
                    </Typography>
                  </Tooltip>
                );
              },
            },
          ],
          isRowSelectable: (node) => {
            if (
              invoiceDetails.billing_option === "unit" &&
              Number(invoiceDetails.allow_overbilling) === 0
            ) {
              if (invoiceDetails.is_new_tm_invoice === 1) {
                return !(
                  (Number(node.data?.total_billed) ===
                    Number(node.data?.quantity) &&
                    !invoiceItems.change_order_sections
                      .filter((section) => section.items[0]?.is_tm_item === 0)
                      .some((section) =>
                        section.items.some(
                          (item) =>
                            item.project_budget_item_id === node.data?.item_id
                        )
                      )) ||
                  invoiceItems.purchase_order_sections.some((section) =>
                    section.items.some(
                      (item) =>
                        item.reference_item_id ===
                        node.data?.reference_item_type_id
                    )
                  )
                );
              } else {
                return !(
                  (Number(node.data?.total_billed) ===
                    Number(node.data?.quantity) &&
                    !invoiceItems.items
                      .filter((item) => item.is_tm_item === 0)
                      .some((item) => {
                        return (
                          item.project_budget_item_id === node.data?.item_id
                        );
                      })) ||
                  invoiceItems.items.some(
                    (item) =>
                      item.reference_item_id ===
                      node.data?.reference_item_type_id
                  )
                );
              }
            } else {
              if (invoiceDetails.is_new_tm_invoice === 1) {
                return !(
                  (Number(node.data?.total_billed) >= 100 &&
                    !invoiceItems.change_order_sections
                      .filter((section) => section.items[0]?.is_tm_item === 0)
                      .some((section) =>
                        section.items.some(
                          (item) =>
                            item.project_budget_item_id === node.data?.item_id
                        )
                      )) ||
                  invoiceItems.purchase_order_sections.some((section) =>
                    section.items.some(
                      (item) =>
                        item.reference_item_id ===
                        node.data?.reference_item_type_id
                    )
                  )
                );
              } else {
                return !(
                  (Number(node.data?.total_billed) >= 100 &&
                    !invoiceItems.items
                      .filter((item) => item.is_tm_item === 0)
                      .some((item) => {
                        return (
                          item.project_budget_item_id === node.data?.item_id
                        );
                      })) ||
                  invoiceItems.items.some(
                    (item) =>
                      item.reference_item_id ===
                      node.data?.reference_item_type_id
                  )
                );
              }
            }
          },
          noRowsOverlayComponent: () => (
            <NoRecords
              image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
            />
          ),
          rowData: [],
        }}
      />
    );

    return SOVItems.change_order_sections.length ? components : noDataTable;
  }, [SOVItems.change_order_sections]);

  const workOrderComponent = useMemo(() => {
    const components = SOVItems.work_order_sections.map((section, index) => {
      if (section.items.length !== 0) {
        return (
          <LazyLoadAgGridTable
            index={index}
            title={_t(
              index === 0
                ? `${
                    getGModuleByKey(defaultConfig.work_order_module)
                      ?.plural_name || "Work Order"
                  }` + _t(" Items")
                : ""
            )}
            subTitle={_t(`Work Order #${section.company_order_id}`)}
            tableProps={{
              rowMultiSelectWithClick: true,
              suppressRowClickSelection: true,
              onSelectionChanged: handleWorkOrderItemsSelectionChanged,
              onGridReady: (e) =>
                onWorkOrderGridReady(e, `${section.section_id}`, index),
              rowSelection: "multiple",
              columnDefs: [
                {
                  headerName: "",
                  field: "checkbox",
                  minWidth: 36,
                  maxWidth: 36,
                  checkboxSelection: true,
                  headerCheckboxSelection: (params) => {
                    let hasSelectable = false;
                    params.api.forEachNodeAfterFilterAndSort((node) => {
                      if (
                        params.api.getRowNode((node.rowIndex || 0).toString())
                          ?.selectable
                      ) {
                        hasSelectable = true;
                      }
                    });
                    return hasSelectable;
                  },
                  suppressMenu: true,
                  showDisabledCheckboxes: true,
                },
                {
                  headerName: _t("Item Name"),
                  field: "item",
                  minWidth: 150,
                  flex: 2,
                  cellClass: "ag-cell-left",
                  headerClass: "ag-header-left",
                  suppressMenu: true,
                  cellRenderer: (params: { data: ISOVWorkOrderItem }) => {
                    const { data } = params;
                    const item = `${data.subject}${
                      data.variation_name ? " " + data.variation_name : ""
                    }`;
                    return (
                      <Tooltip
                        title={HTMLEntities.decode(sanitizeString(item))}
                      >
                        <Typography className="table-tooltip-text">
                          {HTMLEntities.decode(sanitizeString(item))}
                        </Typography>
                      </Tooltip>
                    );
                  },
                },
                {
                  headerName: _t("MU"),
                  field: "mu",
                  maxWidth: 70,
                  minWidth: 70,
                  suppressMenu: true,
                  cellClass: "ag-cell-right",
                  headerClass: "ag-header-right",
                  cellRenderer: (params: { data: ISOVWorkOrderItem }) => {
                    const { data } = params;
                    const is_markup_percentage =
                      data?.is_markup_percentage ?? "";
                    const total =
                      Number(data?.quantity ?? 0) *
                      (Number(data?.unit_cost ?? 0) / 100);
                    const markup = Number(data?.markup);

                    let markupToShow = "0.00";

                    if (is_markup_percentage?.toString() === "1") {
                      markupToShow = markup?.toFixed(2) ?? "0.00";
                    } else {
                      if (total != 0) {
                        const markupPercentage = markup / total - 100;
                        markupToShow = markupPercentage.toFixed(2);
                      }
                    }

                    return (
                      <Tooltip
                        title={markupToShow ? Number(markupToShow) + "%" : ""}
                      >
                        <Typography className="table-tooltip-text">
                          {Number(markupToShow) + "%"}
                        </Typography>
                      </Tooltip>
                    );
                  },
                },
                {
                  headerName: _t("Billed") + " (#)",
                  field: "billed",
                  minWidth: 100,
                  maxWidth: 100,
                  hide: showUnitColumn,
                  suppressMenu: true,
                  cellClass: "ag-cell-right",
                  headerClass: "ag-header-right",
                  cellRenderer: (params: { data: ISOVWorkOrderItem }) => {
                    const { data } = params;
                    const billed = `${data.total_billed}/${data.quantity}`;
                    return (
                      <Tooltip title={billed}>
                        <Typography className="table-tooltip-text">
                          {billed}
                        </Typography>
                      </Tooltip>
                    );
                  },
                },
                {
                  headerName: _t("Billed") + " (%)",
                  field: "billed",
                  minWidth: 100,
                  maxWidth: 100,
                  hide: showPercentageColumn,
                  suppressMenu: true,
                  cellClass: "ag-cell-right",
                  headerClass: "ag-header-right",
                  cellRenderer: (params: { data: ISOVWorkOrderItem }) => {
                    const { data } = params;
                    const billed = data.total_billed;
                    return (
                      <Tooltip title={billed}>
                        <Typography className="table-tooltip-text">
                          {billed}
                        </Typography>
                      </Tooltip>
                    );
                  },
                },
                {
                  headerName: _t("Total"),
                  field: "total",
                  minWidth: 120,
                  maxWidth: 120,
                  suppressMenu: true,
                  cellClass: "ag-cell-right",
                  headerClass: "ag-header-right",
                  cellRenderer: (params: { data: ISOVWorkOrderItem }) => {
                    const { data } = params;
                    return (
                      <Tooltip
                        title={
                          formatter(
                            ((Number(data.total) || 0) / 100).toFixed(2)
                          ).value_with_symbol
                        }
                      >
                        <Typography className="table-tooltip-text">
                          {
                            formatter(
                              ((Number(data.total) || 0) / 100).toFixed(2)
                            ).value_with_symbol
                          }
                        </Typography>
                      </Tooltip>
                    );
                  },
                },
              ],
              isRowSelectable: (node) => {
                if (
                  invoiceDetails.billing_option === "unit" &&
                  Number(invoiceDetails.allow_overbilling) === 0
                )
                  if (invoiceDetails.is_new_tm_invoice === 1) {
                    return !(
                      Number(node.data?.total_billed) ===
                        Number(node.data?.quantity) &&
                      !invoiceItems.work_order_sections.some((section) =>
                        section.items.some(
                          (item) =>
                            item.project_budget_item_id === node.data?.item_id
                        )
                      )
                    );
                  } else {
                    return !(
                      Number(node.data?.total_billed) ===
                        Number(node.data?.quantity) &&
                      !invoiceItems.items
                        .filter((item) => item.is_tm_item === 0)
                        .some((item) => {
                          return (
                            item.project_budget_item_id === node.data?.item_id
                          );
                        })
                    );
                  }
                else {
                  return true;
                }
              },
              noRowsOverlayComponent: () => (
                <NoRecords
                  image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                />
              ),
              rowData: section.items,
            }}
          />
        );
      } else {
        return <></>;
      }
    });

    const noDataTable = (
      <LazyLoadAgGridTable
        index={0}
        title={_t(
          `${
            getGModuleByKey(defaultConfig.work_order_module)?.plural_name ||
            "Work Order"
          }` + _t(" Items")
        )}
        tableProps={{
          rowMultiSelectWithClick: true,
          suppressRowClickSelection: true,
          onSelectionChanged: handleWorkOrderItemsSelectionChanged,
          rowSelection: "multiple",
          columnDefs: [
            {
              headerName: "",
              field: "checkbox",
              minWidth: 36,
              maxWidth: 36,
              checkboxSelection: true,
              headerCheckboxSelection: (params) => {
                let hasSelectable = false;
                params.api.forEachNodeAfterFilterAndSort((node) => {
                  if (
                    params.api.getRowNode((node.rowIndex || 0).toString())
                      ?.selectable
                  ) {
                    hasSelectable = true;
                  }
                });
                return hasSelectable;
              },
              suppressMenu: true,
              showDisabledCheckboxes: true,
            },
            {
              headerName: _t("Item Name"),
              field: "item",
              minWidth: 150,
              flex: 2,
              cellClass: "ag-cell-left",
              headerClass: "ag-header-left",
              suppressMenu: true,
              cellRenderer: (params: { data: ISOVWorkOrderItem }) => {
                const { data } = params;
                const item = `${data.subject}${
                  data.variation_name ? " " + data.variation_name : ""
                }`;
                return (
                  <Tooltip title={HTMLEntities.decode(sanitizeString(item))}>
                    <Typography className="table-tooltip-text">
                      {HTMLEntities.decode(sanitizeString(item))}
                    </Typography>
                  </Tooltip>
                );
              },
            },
            {
              headerName: _t("MU"),
              field: "mu",
              maxWidth: 70,
              minWidth: 70,
              suppressMenu: true,
              cellClass: "ag-cell-right",
              headerClass: "ag-header-right",
              cellRenderer: (params: { data: ISOVWorkOrderItem }) => {
                const { data } = params;
                const is_markup_percentage = data?.is_markup_percentage ?? "";
                const total =
                  Number(data?.quantity ?? 0) *
                  (Number(data?.unit_cost ?? 0) / 100);
                const markup = Number(data?.markup);

                let markupToShow = "0.00";

                if (is_markup_percentage?.toString() === "1") {
                  markupToShow = markup?.toFixed(2) ?? "0.00";
                } else {
                  if (total != 0) {
                    const markupPercentage = markup / total - 100;
                    markupToShow = markupPercentage.toFixed(2);
                  }
                }

                return (
                  <Tooltip
                    title={markupToShow ? Number(markupToShow) + "%" : ""}
                  >
                    <Typography className="table-tooltip-text">
                      {Number(markupToShow) + "%"}
                    </Typography>
                  </Tooltip>
                );
              },
            },
            {
              headerName: _t("Billed") + " (#)",
              field: "billed",
              minWidth: 100,
              maxWidth: 100,
              hide: showUnitColumn,
              suppressMenu: true,
              cellClass: "ag-cell-right",
              headerClass: "ag-header-right",
              cellRenderer: (params: { data: ISOVWorkOrderItem }) => {
                const { data } = params;
                const billed = `${data.total_billed}/${data.quantity}`;
                return (
                  <Tooltip title={billed}>
                    <Typography className="table-tooltip-text">
                      {billed}
                    </Typography>
                  </Tooltip>
                );
              },
            },
            {
              headerName: _t("Billed") + " (%)",
              field: "billed",
              minWidth: 100,
              maxWidth: 100,
              hide: showPercentageColumn,
              suppressMenu: true,
              cellClass: "ag-cell-right",
              headerClass: "ag-header-right",
              cellRenderer: (params: { data: ISOVWorkOrderItem }) => {
                const { data } = params;
                const billed = data.total_billed;
                return (
                  <Tooltip title={billed}>
                    <Typography className="table-tooltip-text">
                      {billed}
                    </Typography>
                  </Tooltip>
                );
              },
            },
            {
              headerName: _t("Total"),
              field: "total",
              minWidth: 120,
              maxWidth: 120,
              suppressMenu: true,
              cellClass: "ag-cell-right",
              headerClass: "ag-header-right",
              cellRenderer: (params: { data: ISOVWorkOrderItem }) => {
                const { data } = params;
                return (
                  <Tooltip
                    title={
                      formatter(((Number(data.total) || 0) / 100).toFixed(2))
                        .value_with_symbol
                    }
                  >
                    <Typography className="table-tooltip-text">
                      {
                        formatter(((Number(data.total) || 0) / 100).toFixed(2))
                          .value_with_symbol
                      }
                    </Typography>
                  </Tooltip>
                );
              },
            },
          ],
          isRowSelectable: (node) => {
            if (
              invoiceDetails.billing_option === "unit" &&
              Number(invoiceDetails.allow_overbilling) === 0
            )
              if (invoiceDetails.is_new_tm_invoice === 1) {
                return !(
                  Number(node.data?.total_billed) ===
                    Number(node.data?.quantity) &&
                  !invoiceItems.work_order_sections.some((section) =>
                    section.items.some(
                      (item) =>
                        item.project_budget_item_id === node.data?.item_id
                    )
                  )
                );
              } else {
                return !(
                  Number(node.data?.total_billed) ===
                    Number(node.data?.quantity) &&
                  !invoiceItems.items
                    .filter((item) => item.is_tm_item === 0)
                    .some((item) => {
                      return item.project_budget_item_id === node.data?.item_id;
                    })
                );
              }
            else {
              return true;
            }
          },
          noRowsOverlayComponent: () => (
            <NoRecords
              image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
            />
          ),
          rowData: [],
        }}
      />
    );
    return SOVItems.work_order_sections.length ? components : noDataTable;
  }, [SOVItems.work_order_sections]);

  return (
    <Drawer
      open={projectBudgetItems}
      rootClassName="drawer-open"
      width={750}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-file-invoice-dollar"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(`Project Budget Items`)}
            </Header>
          </div>
          <div className="flex items-center justify-end px-4">
            <Tooltip
              title={_t(
                "You can only add items that have been imported into your Schedule of Values Items. To add additional items to this list, go to your Project > Schedule of Values tab > Import or Manually Add Items."
              )}
              placement="bottom"
            >
              <Typography className="cursor-pointer text-[#B94A48] text-sm">
                {_t("Missing Something?")}
              </Typography>
            </Tooltip>
          </div>
        </div>
      }
      closeIcon={<CloseButton onClick={() => closeDrawer()} />}
    >
      <div className="py-4">
        <div className="sidebar-body h-[calc(100vh-132px)] px-4 overflow-y-auto">
          <div className="grid gap-4">
            {SOVItems.budget_data.length > 0 && (
              <SidebarCardBorder addGap={true}>
                {budgetItemsComponent}
              </SidebarCardBorder>
            )}

            <SidebarCardBorder addGap={true}>
              {estimateSectionComponent}
            </SidebarCardBorder>

            <SidebarCardBorder addGap={true}>
              {changeOrderComponent}
            </SidebarCardBorder>

            <SidebarCardBorder addGap={true}>
              {workOrderComponent}
            </SidebarCardBorder>
          </div>
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
          <PrimaryButton
            type="primary"
            htmlType="submit"
            isLoading={isLoading}
            buttonText={_t("Save & Close")}
            disabled={isLoading}
            onClick={() => {
              onSubmit(selectedSOVItems);
            }}
          >
            {_t("Save & Close")}
          </PrimaryButton>
        </div>
      </div>
    </Drawer>
  );
};

export default ProjectBudgetItems;
