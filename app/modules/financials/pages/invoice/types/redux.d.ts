// Dashboard
interface IInvoiceDashState {
  submittedInvoicesData: IDashInvoicesData;
  receivedPaymentsData: IDashInvoicesData;
  invoiceByStatusData: IInvoiceByStatusData[];
  recentPaymentsData: IRecentPaymentsData[];
  invoicesComingDueData: IInvoicesComingDueData[];
  accountsReceivableData: TAccountsReceivableData;
  salesByMonth: IInvoiceSalesByMonth;
  balanceByProjectData: IBalanceByProjectData;
  unpaidInvoiceData: IDashInvoicesData;
  isDashLoading?: boolean;
  isDataFetched?: boolean;
  submittedInvoicesDataLastRefreshTime: string;
  receivedPaymentsDataLastRefreshTime: string;
  invoiceByStatusDataLastRefreshTime: string;
  recentPaymentsDataLastRefreshTime: string;
  invoicesComingDueDataLastRefreshTime: string;
  accountsReceivableDataLastRefreshTime: string;
  salesByMonthLastRefreshTime: string;
  balanceByProjectDataRefreshTime: sting;
  unpaidInvoiceDataRefreshTime: string;
  dateFormat: string;
  payment_alert?: number;
  searchValue: string;
}

interface IDashInvoicesData {
  this_month?: string;
  previous_month?: string;
  this_year?: string;
  previous_year?: string;
}

interface IInvoiceByStatusData {
  approval_type?: number;
  total?: string;
  display_name?: string;
  count_record?: string;
  default_color?: string;
}

interface IRecentPaymentsData {
  amount?: string;
  pay_date?: string;
  company_invoice_id?: string;
  invoice_id?: number;
}

interface IInvoicesComingDueData {
  due?: string;
  invoice_id?: number;
  total?: string;
  balance?: string;
  due_date: string;
  company_invoice_id: string;
}

interface IAccountsReceivableEntryData {
  no_of_invoice: string;
  per: string;
  total: string;
}

declare type TInvoiceDueTypes =
  | "invoice_due_today"
  | "invoice_due_1_30"
  | "invoice_due_31_60"
  | "invoice_due_61_90"
  | "invoice_due_91";

type TAccountsReceivableData = {
  [key in TInvoiceDueTypes]?: IAccountsReceivableEntryData;
};

interface IBalanceByProjectData {
  data: IBalanceData[];
  x_data: string[];
  y_amount_data: string[];
  y_balance_data: string[];
}

interface IBalanceData {
  project_name?: string;
  total?: string;
  balance?: string;
  invoice_id: number;
  project_id: number;
  due_date: string;
  due: string;
}

interface IInvoiceSalesByMonth {
  x_data?: string[];
  this_year_payment?: string[];
  this_year_invoice?: string[];
  previous_year_payment?: string[];
  previous_year_invoice?: string[];
}

interface IUnpaidInvoicesData {
  this_month?: string;
  previous_month?: string;
  this_year?: string;
  previous_year?: string;
}
interface IInvoiceDashApiRes extends Omit<IApiCallResponse, "data"> {
  data: IInvoiceDashState;
  __service_time__: string;
  __offset_seconds__: number;
  global_project: GProject;
  assigned_projects_list: string;
  is_expire: string;
  subscription_flag: string;
  subscription_status: string;
  paddle_account_status: string;
  app_access_: string;
  company_date_format: string;
  first_week_day: string;
}

interface IIVDashBalanceByProjectTableCellRenderer {
  data: IBalanceData;
}

interface IIVDashInvoicesByStatusTableCellRenderer {
  data: Partial<IInvoiceByStatusData>;
}

interface IIVDashRecentPaymnetsTableCellRenderer {
  data: Partial<IRecentPaymentsData>;
}

interface IIVDashInvoicesComingDueTableCellRenderer {
  data: Partial<IInvoicesComingDueData>;
}

interface IIVDashBalanceByProjectTableCellRenderer {
  data: Partial<IBalanceByProjectData>;
}

// Invoice List

interface IInvoiceListProps {
  setIsAddInvoiceOpen: (value: boolean) => void;
  search: string;
}

interface IInvoiceListParmas {
  length: number;
  page: number;
  directory?: number[] | string[];
  order_by_dir?: string;
  order_by_name?: string;
  search?: string;
  filter?: IDirTempFil;
  ignore_filter: number;
}

interface IInvoiceData {
  invoice_id: number;
  prefix_company_invoice_id: string;
  project_name: string;
  customer_name_only: string;
  customer_company: string;
  invoice_date: string;
  due_date: string;
  total: string | number; // Chnage this type by developer
  due_balance: string | number; // Chnage this type by developer
  approval_type_name: string;
  approval_type_key: string;
  status_color: string;
  is_deleted: number;
  email_subject: string;
  project_id: number;
  billed_to: number;
  customer_id: number;
  contact_id?: number;
  billed_to_contact: number;
  billed_to_display_name: string;
  billed_to_type_key: string;
  billed_to_type_name: string;
  customer_name: string;
  show_payment_link: string | number;
  customer_contact_id: number;
}

interface IInvoiceApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IInvoiceData[];
}

interface IInvoiceTableCellRenderer {
  data: Partial<IInvoiceData>;
}

interface IInvoiceListDashParams {
  refresh_type?: string;
}

interface IInvoiceDashApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IInvoiceDashState;
  refresh_type?: string;
}

// Details page
interface IIVDetails {
  invoice_id: number;
  is_deleted: number;
  invoice_date: string;
  due_date: string;
  term_name: string;
  term_id: string;
  billed_to_name: string;
  billed_to_name_w_o_company: string;
  billed_to_email: string;
  billed_to_company_name: string;
  billed_to_dir_type: number;
  billed_to_display_name: string;
  billed_to: number;
  original_billed_to?: number;
  billed_to_contact: number;
  billed_to_type_key: string;
  billed_to_type_name: string;
  prefix_company_invoice_id: string;
  allow_online_payment: string;
  project_allow_online_payment: string;
  invoice_retainage: string;
  authorized_by_name: string;
  authorized_by: number;
  customer_name: string;
  customer_contact_id: number;
  customer_id: number;
  customer_name_only: string;
  customer_email: string;
  customer_phone: string;
  customer_company: string;
  app_number: string;
  term_key: string;
  start_date: string;
  end_date: string;
  approval_type: number;
  approval_type_name: string;
  approval_type_key: string;
  description: string;
  total: string;
  project_id: number;
  date_added: string;
  time_added: string;
  retention: string;
  employee: string;
  qb_date: string;
  qb_date_added: string;
  qb_time_added: string;
  quickbook_invoice_id: string;
  project_name: string;
  projectPrefix: string;
  company_invoice_id: string;
  normal_subject: string;
  email_subject: string;
  parent_invoice_id: string;
  user_id: number;
  ref_estimate_id: number;
  reference_bill_id: number;
  is_advance_invoice: number;
  tax_id: string;
  tax_name: string;
  total_tax_rate: string;
  discount: string;
  is_reversible_tax: string;
  billing_option: string;
  allow_overbilling: string;
  user_company_name: string;
  current_invoice_retainage: string;
  current_invoice_items_total: string;
  tm_items_group_by: string;
  status_color: string;
  need_adjust_invoice: number;
  signature?: string;
  is_term_deleted?: string;
  customer_type?: string;
  customer_image?: string;
  customer_contact_id?: number;
  custom_invoice_id?: string;
  due_balance: string;
  show_payment_link?: string | number;
  is_disabled_project?: string;
  payment_amount?: string;
  held_co_retainage?: number;
  is_new_tm_invoice?: number;
  tm_tc_group_by?: string;
  invoiced_to?: any; // TODO: Need to change this type
  customer?: any; // TODO: Need to change this type
  taxable_retainage?: string;
}
interface IIVDetailsInitialState {
  details: Partial<IIVDetails>;
  authorizedList: IAdditionalContact[];
  authorizedContactId: number;
  isDetailLoading: boolean;
}
interface IIVDetailsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IIVDetails;
}

// Add Invoice
interface IAddInvoiceRes extends Omit<IDefaultAPIRes, "data"> {
  data: { invoice_id: string };
}

// update details response
interface IIVDetailsUpdateApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ObjType;
}

interface IInvoiceStatusList {
  label: string;
  value: IStatus;
  key?: string;
  default_color?: string;
  icon?: IFontAwesomeIconProps["icon"];
  index?: number;
  show_in_progress_bar?: number;
}

interface IAuthorizedBy {
  contact_id: number;
  user_id: number;
  display_name: string;
  company_name: string;
  type: string;
}

// Notrs tab
interface IIVNotesInitialState {
  notesData: ICommonNote[];
  order: string;
  isNoteTabLoading: boolean;
  isNotesDataFetched: boolean;
}

interface IIVNotesApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ICommonNote[];
  order: string;
}

interface IIVFetchNotesParams {
  module_key?: string;
  module_id?: number;
  record_id?: number;
  order?: string;
}

// Files and photo
interface IIVFilePhotoParamsData {
  module_key?: string;
  record_id: number;
}
interface IIVFilePhotoInitialState {
  filePhotos: IFile[];
  isFilePhotoLoading: boolean;
  isFileDataFetched: boolean;
}
interface IAddIVFilePhotoFrm {
  primary_id: number;
  module_id: number;
  module_key: string;
  attach_image?: string;
  files?: IFile[];
  project_id?: number | string;
}

interface IAddIVFilePhotoRes extends Omit<IDefaultAPIRes, "data"> {
  data: { aws_files: IFile[] };
}

// Terms
interface IIVFetchTermsParams {
  id: string;
}
interface IIVTermsData {
  invoice_id: number;
  terms: string;
  invoice_default_terms: string;
  invoice_custom_terms: string;
}
interface IIVTermsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IIVTermsData;
}

interface IIVTermsInitialState {
  termsData: Partial<IIVTermsData>;
  isTermsLoading: boolean;
  isTermsDataFetched: boolean;
}

interface IArchiveInvoiceFrm {
  formData: {
    status: number;
    moduleKey: string;
    moduleId: number;
  };
  paramsData: {
    invoiceId: string;
  };
}

interface IDeleteInvoiceRes extends Omit<IDefaultAPIRes, "data"> {
  data: ObjType;
}

interface IDeleteInvoiceFrm {
  formData: {
    module_key: string;
  };
  paramsData: {
    invoiceId: string;
  };
}

interface ITimeAndMaterialItemsListParmas {
  filter: {
    project: string;
    start_date: string;
    end_date: string;
  };
}

interface ITimeAndMaterialItemsListAwsFilesData {
  image_id: string;
  project_id: string;
  type: string;
  type_id: string;
  file_ext: string;
  file_path: string;
  title: string;
  notes: string;
  is_deleted: string;
  date_added: string;
  date_modified: string;
  module_id: string;
  primary_id: string;
  file_tags: string;
  child_item_id: string;
  user_id: string;
  company_id: string;
  file_type: string;
  is_image: string;
  parent_image_id: string;
  demo_data: string;
  height: string;
  width: string;
  size: string;
  mode: string;
  child_item_backup: string;
  child_item_backup12: string;
  upload_from: string;
  image_res: string;
  camera_res: string;
  file_name: string;
  thumb_flag: string;
  large_flag: string;
  static_folder: string;
  folder_id: string;
  refrence_img_id: string;
  quickbook_attachable_id: string;
  is_common_notes: string;
  is_bidding_file: string;
  child_item_name: string;
  file_save_when_send_email: string;
  is_file_shared: string;
  annotation_data: string;
  original_file_path: string;
  is_google_drive_file: string;
  companycam_photo_id: string;
  companycam_creator_name: string;
  original_image_id: string;
  is_project_template: string;
  project_template_id: string;
}

interface ITimeAndMaterialItemsListBillData {
  bill_id: number;
  project_id: string;
  subject: string;
  order_date: string;
  due_date: string;
  term_id: string;
  term_key: string;
  notes: string;
  tax_id: string;
  signature: string;
  total: number;
  cost: string;
  user_id: string;
  company_id: string;
  date_added: string;
  date_modified: string;
  demo_data: string;
  parent_bill_id: string;
  total_tax_rate: string;
  company_bill_id: string;
  supplier_id: string;
  is_deleted: string;
  supplier_contact_id: string;
  qb_date: string;
  quickbook_bill_id: string;
  qbc_id: string;
  is_updated: string;
  custom_bill_id: string;
  amount: number;
  reference_purchase_order_id: string;
  reference_invoice_id: string;
  need_prefix_project: string;
  is_notes_convert: string;
  ref_po: string;
  reference_module_id: string;
  reference_primary_id: string;
  reference_sub_contract_id: string;
  is_shared: string;
  reversible_tax_amount: string;
  sc_multi_bill: string;
  origin: string;
  is_billable: number;
  type: string;
  first_name: string;
  last_name: string;
  company_name: string;
  email: string;
  url: string;
  username: string;
  password: string;
  app_access: string;
  role_id: string;
  title: string;
  phone: string;
  fax: string;
  cell: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zip: string;
  misc_service: string;
  access_code: string;
  refered_by: string;
  access_users: string;
  added_by: string;
  image: string;
  paid_amount: string;
  default_vehicle: string;
  last_logged_in: string;
  signup_date: string;
  renewal_date: string;
  signup_ip: string;
  chargebee_customer_id: string;
  subscription_id: string;
  paddle_subscription_id: string;
  paddle_customer_id: string;
  old_subscription_id: string;
  subscription_status: string;
  subscription_site: string;
  subscription_referrer: string;
  subscription_response: string;
  weather_zip: string;
  parent_user_id: string;
  email_signature: string;
  internal_notes: string;
  global_project: string;
  import_id: string;
  notify_sms: string;
  notify_push: string;
  notify_email: string;
  emp_wage: string;
  last_login_offset: string;
  show_on_calendar: string;
  dir_color: string;
  sort_order: string;
  hire_date: string;
  release_date: string;
  user_dob: string;
  employee_id: string;
  name_on_check: string;
  website: string;
  opening_balance: string;
  account: string;
  business_id: string;
  ssn_id: string;
  latitude: string;
  longitude: string;
  project_sortorder: string;
  widget_updated: string;
  update_release: string;
  temperature_scale: string;
  opportunity_name: string;
  lead_project_type: string;
  quality: string;
  street1: string;
  street2: string;
  sales_city: string;
  sales_state: string;
  sales_zip: string;
  lead_value: string;
  estimate_sales_date: string;
  stage: string;
  sales_status: string;
  referral_source: string;
  assigned_projects: string;
  assigned_to: string;
  display_name: string;
  services: string;
  popup_status: string;
  unique_name: string;
  quickbook_user_id: string;
  emergency_name: string;
  emergency_relationship: string;
  emergency_phone: string;
  phone_ext: string;
  stage_key: string;
  referral_source_key: string;
  lead_project_type_key: string;
  plan_id: string;
  show_dashboard_summary: string;
  billing_rate: number;
  has_rated_android: string;
  has_rated_ios: string;
  redirect_to_timecard: string;
  company_display_name: string;
  calendar_mode: string;
  burden_rate: string;
  dashboard_shortcuts: string;
  phone_list_option: string;
  tags: string;
  file_sortorder: string;
  last_contacted: string;
  emergency_relationship_backup: string;
  emergency_relationship_key: string;
  emergency_relationship_id: string;
  notify_project_message_push: string;
  notify_company_chat_push: string;
  notify_message_period: string;
  rating: string;
  is_favorite: string;
  groups: string;
  has_updated_dashboard_widgets: string;
  driving_license: string;
  timecard_view: string;
  show_in_crew_schedule: string;
  refer_app_to_others: string;
  pdf_template_color: string;
  dailylog_view: string;
  toolbar_popup: string;
  customer_owner: string;
  default_terms: string;
  zoho_customer_id: string;
  billed_to: string;
  billed_to_contact: string;
  login_info: string;
  tpar: string;
  language: string;
  lead_type: string;
  lead_type_id: string;
  project_selection: string;
  notify_individual_message_push: string;
  notify_client_message_push: string;
  ssn_id_backup: string;
  individual_chat: string;
  team_chat: string;
  project_chat: string;
  client_chat: string;
  first_login_from: string;
  dashboard_widget_order: string;
  enable_default_cost_code: string;
  cost_code_id: string;
  enable_disable_popup: string;
  default_expense_account: string;
  is_taxable_exempt: string;
  enable_associated_projects: string;
  pdf_template_text_color: string;
  timezone_id: string;
  customer_tax_id: string;
  is_vendor_1099: string;
  lead_best_time_to_contact: string;
  lead_preferred_contact: string;
  tmp_password_encrypt: string;
  verification_code: string;
  code_expired: string;
  last_change_password: string;
  login_2fa: string;
  login_attempt: string;
  is_email_sent: string;
  is_admin_email_sent: string;
  add_thumbtack: string;
  thumbtack_business_id: string;
  is_converted: string;
  zoho_lead_ref_id: string;
  paddle_account_status: string;
  delete_all: string;
  phone2: string;
  phone_ext2: string;
  is_viewable_projects: string;
  vendor_name: string;
  vendor_company_name: string;
  total_billed: string;
  aws_files: ITimeAndMaterialItemsListAwsFilesData[];
}

interface ITimeAndMaterialItemsListChangeOrderData {
  change_order_id: number;
  company_order_id: string;
  subject: string;
  billing_status: string;
  total: number;
  total_billed: string;
  bill_paid: string;
  order_date: string;
  billing_status_name: string;
  billing_status_key: string;
  co_item_imported_in_sov: string;
  total_billed_percent: string;
  aws_files: ITimeAndMaterialItemsListAwsFilesData[];
  is_already_imported: string;
}

interface ITimeAndMaterialItemsListEquipmentLogsData {
  log_id: string;
  is_deleted: string;
  equipment_id: string;
  subject: string;
  description: string;
  operator_name: string;
  operator_Only_name: string;
  begin_hours: string;
  end_hours: string;
  hours_used: string;
  e_begin_hours: string;
  e_end_hours: string;
  e_hours_used: string;
  project_name: string;
  used_date: string;
  oil_changed_date: string;
  oil_changed_time: string;
  email_subject: string;
  unit: string;
  unit_cost: string;
  total_billed: string;
  reference_primary_id: string;
  reference_module_id: string;
  reference_item_id: string;
  item_type_display_name: string;
  item_type_name: string;
  item_type_key: string;
  cost_code_name: string;
  csi_code: string;
  code_id: string;
  cost_code_id: string;
  aws_files: ITimeAndMaterialItemsListAwsFilesData[];
  total: string;
  quantity: string;
}

interface ITimeAndMaterialItemsListExpenseData {
  expense_id: string;
  company_expense_id: string;
  project_id: string;
  reason: string;
  cost_code_id: string;
  expense_name: string;
  amount: number;
  paid_by: string;
  is_deleted: string;
  date_added: string;
  date_modified: string;
  project_location: string;
  expense_date: string;
  company_id: string;
  user_id: string;
  image: string;
  parent_expense_id: string;
  demo_data: string;
  directory_id: string;
  category: string;
  paid_through: string;
  qb_date: string;
  quickbook_expence_id: string;
  qbc_id: string;
  is_updated: string;
  tax_id: string;
  ref_number: string;
  sync_from: string;
  is_notes_convert: string;
  expense_by: string;
  item_type_key: string;
  is_shared: string;
  item_type: string;
  item_type_new: string;
  origin: string;
  is_billable: number;
  total_billed: string;
  vendor_name: string;
  dir_type: string;
  quickbook_user_id: string;
  vendor_company_name: string;
  time_added: string;
  markup: string | null;
  item_type_display_name: string;
  item_type_name: string;
  cost_code_name: string;
  csi_code: string;
  code_id: string;
  aws_files: ITimeAndMaterialItemsListAwsFilesData[];
}

interface ITimeAndMaterialItemsListPurchaseOrdersData {
  purchase_order_id: number;
  project_id: string;
  subject: string;
  order_date: string;
  order_from: string;
  ship_to: string;
  approved_by: string;
  ship_via: string;
  fob_point: string;
  term_id: string;
  term_key: string;
  notes: string;
  tax_id: string;
  signature: string;
  total: number;
  cost: string;
  user_id: string;
  company_id: string;
  date_added: string;
  date_modified: string;
  demo_data: string;
  parent_purchase_order_id: string;
  total_tax_rate: string;
  company_purchase_order_id: string;
  supplier_id: string;
  billing_status: string;
  is_deleted: string;
  supplier_contact_id: string;
  qb_date: string;
  quickbook_purchaseorder_id: string;
  qbc_id: string;
  is_updated: string;
  custom_purchase_order_id: string;
  ref_bill_id: string;
  supplier_status: string;
  date_supplier_status: string;
  ip_address: string;
  pricing_vendor: string;
  need_prefix_project: string;
  is_notes_convert: string;
  po_order_date: string;
  delivery_date: string;
  reference_id: string;
  ref_co_id: string;
  ship_to_contact: string;
  po_suppliers: string;
  is_multiple_suppliers: string;
  multiple_ref_bill_ids: string;
  ship_to_contact_additional_contact_id: string;
  is_project_template: string;
  project_template_id: string;
  origin: string;
  po_address1: string;
  po_address2: string;
  po_city: string;
  po_state: string;
  po_zip: string;
  project_name: string;
  supplier_company_name: 'Mendapara NF"s';
  time_added: string;
  bill_status: string;
  total_billed: string;
  aws_files: ITimeAndMaterialItemsListAwsFilesData[];
  is_already_imported: string;
}

interface ITimeAndMaterialItemsListTimeCardsData {
  detail_id: string;
  user_id: string;
  project_name: string;
  total_billed: number;
  cost_code_name: string;
  csi_code: string;
  code_id: string;
  billing_rate: number;
  emp_wage: string;
  burden_rate: string;
  timecard_user_rate: string;
  employee_name: string;
  employee_first_name: string;
  employee_last_name: string;
  work_seconds: string;
  ot_hours: string;
  hours: string;
  work_mins: string;
  clock_in_date: string;
  clock_in_date_key: string;
  project_location: string;
  project_id: string;
  cost_code_id: string;
  timecard_id: string;
  item_type_display_name: string;
  item_type_name: string;
  item_type_key: string;
  item_type: string;
  csi_code: string;
  employee_image: string;
  user_emp_wage_rate: string;
  user_billing_rate: string;
  user_burden_rate: string;
}

interface ITimeAndMaterialItemsListSubContractData {
  sub_contract_id: number;
  project_id: number;
  subject: string;
  order_date: string;
  agreement: string;
  issued_by: number;
  response: string;
  contractor_id: number;
  contractor_contact_id: number;
  work_retain: string;
  scope_or_work_note: string;
  inclusion_note: string;
  exclusion_note: string;
  clarification_note: string;
  document_note: string;
  total: string;
  user_id: number;
  company_id: number;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  demo_data: number;
  parent_sub_contract_id: number;
  company_sub_contract_id: number;
  custom_sub_contract_id: string;
  sc_default_terms: string;
  sc_terms: string;
  term_type: string;
  sc_status: string;
  need_prefix_project: number;
  billed_to_status: string;
  date_billed_to_status: string;
  ip_address: string;
  signature: string;
  ref_bill_id: number;
  ref_co_id: number;
  project_name: string;
  time_added: string;
  bill_status: string;
  total_billed: string;
  is_already_imported: string;
  contractor_name: string;
  contractor_company_name: string;
}

interface ITimeAndMaterialItemsListTimeCardsGroupByData {
  group_by_none: ITimeAndMaterialItemsListTimeCardsData[];
  group_by_day: { [key: string]: ITimeAndMaterialItemsListTimeCardsData[] };
  group_by_user_id: { [key: string]: ITimeAndMaterialItemsListTimeCardsData[] };
  group_by_cost_code: {
    [key: string]: ITimeAndMaterialItemsListTimeCardsData[];
  };
}

interface ITimeAndMaterialItemsAwsFilesData {
  image_id: number;
  project_id: number;
  type: string;
  type_id: string | number;
  file_ext: string;
  file_path: string;
  title: string;
  notes: string;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  module_id: number;
  primary_id: number;
  file_tags: string;
  child_item_id: number;
  user_id: number;
  company_id: number;
  file_type: number;
  is_image: number;
  parent_image_id: string | number;
  demo_data: number;
  height: number;
  width: number;
  size: string;
  mode: string;
  child_item_backup: string | number;
  child_item_backup12: string | number;
  upload_from: string;
  image_res: string;
  camera_res: string;
  file_name: string;
  thumb_flag: number;
  large_flag: number;
  static_folder: number;
  folder_id: number;
  refrence_img_id: number;
  quickbook_attachable_id: string;
  is_common_notes: number;
  is_bidding_file: number;
  child_item_name: string;
  file_save_when_send_email: number;
  is_file_shared: number;
  annotation_data: string;
  original_file_path: string;
  is_google_drive_file: number;
  companycam_photo_id: number;
  companycam_creator_name: string;
  original_image_id: number;
  is_project_template: number;
  project_template_id: string | number;
}

interface ITimeAndMaterialItemsAwsFiles {
  bills: ITimeAndMaterialItemsAwsFilesData[];
  change_orders: ITimeAndMaterialItemsAwsFilesData[];
  equipmentlogs: ITimeAndMaterialItemsAwsFilesData[];
  expense: ITimeAndMaterialItemsAwsFilesData[];
  purchase_orders: ITimeAndMaterialItemsAwsFilesData[];
  sub_contract: ITimeAndMaterialItemsAwsFilesData[];
}

interface ITimeAndMaterialItemsListData {
  bills: ITimeAndMaterialItemsListBillData[];
  change_orders: ITimeAndMaterialItemsListChangeOrderData[];
  equipmentlogs: ITimeAndMaterialItemsListEquipmentLogsData[];
  expense: ITimeAndMaterialItemsListExpenseData[];
  purchase_orders: ITimeAndMaterialItemsListPurchaseOrdersData[];
  timecards: ITimeAndMaterialItemsListTimeCardsData[];
  timecards_group_by: ITimeAndMaterialItemsListTimeCardsGroupByData;
  awsFiles: ITimeAndMaterialItemsAwsFiles;
  sub_contract: ITimeAndMaterialItemsListSubContractData[];
}

interface ISelectedTimeAndMaterialItemsListTimeCardsGroupByData {
  group_by_none: ITimeAndMaterialItemsListTimeCardsData[];
  group_by_day: ITimeAndMaterialItemsListTimeCardsData[];
  group_by_user_id: ITimeAndMaterialItemsListTimeCardsData[];
  group_by_cost_code: ITimeAndMaterialItemsListTimeCardsData[];
}

interface ISelectedTimeAndMaterialItemsListData {
  bills: ITimeAndMaterialItemsListBillData[];
  change_orders: ITimeAndMaterialItemsListChangeOrderData[];
  equipmentlogs: ITimeAndMaterialItemsListEquipmentLogsData[];
  expense: ITimeAndMaterialItemsListExpenseData[];
  purchase_orders: ITimeAndMaterialItemsListPurchaseOrdersData[];
  timecards: ITimeAndMaterialItemsListTimeCardsData[];
  timecards_group_by?: ISelectedTimeAndMaterialItemsListTimeCardsGroupByData;
  awsFiles?: ITimeAndMaterialItemsAwsFiles;
  sub_contract: ITimeAndMaterialItemsListSubContractData[];
}

interface ITimeAndMaterialItemsListApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ITimeAndMaterialItemsListData;
}

interface IInvoiceItemsInitialState {
  TimeMaterialItems: ITimeAndMaterialItemsListData;
  invoiceItems: IInvoiceItemsListData;
  invoiceItemsLoading: boolean;
  timeCardGroupingFilter: string;
  SOVItems: ISOVItemsListData;
  invoiceItemsToAdjust: IItemsToAdjustInvoiceData[];
  isSOVitemsLoading: boolean;
}

interface IPurchaseOrderTableCellRenderer {
  data: Partial<ITimeAndMaterialItemsListPurchaseOrdersData>;
}

interface ISubContractTableCellRenderer {
  data: Partial<ITimeAndMaterialItemsListSubContractData>;
}

interface IExpenseTableCellRenderer {
  data: Partial<ITimeAndMaterialItemsListExpenseData>;
}

interface IChangeOrderCellRenderer {
  data: Partial<ITimeAndMaterialItemsListChangeOrderData>;
}

interface ITimeCardCellRenderer {
  data: Partial<ITimeAndMaterialItemsListTimeCardsData>;
}

interface IBillCellRenderer {
  data: Partial<ITimeAndMaterialItemsListBillData>;
}

interface IEquipmentLogsCellRenderer {
  data: Partial<ITimeAndMaterialItemsListEquipmentLogsData>;
}

interface IAddSavedTimeMaterialItemsParams {
  id?: number;
  project_id?: number;
  selected_items?: {
    po_66: number[];
    bill_78: number[];
    co_11: number[];
    sc_67: number[];
  };
  remove_items?: {
    po_delete_66: number[];
    bill_delete_78: number[];
    co_delete_11: number[];
    sc_delete_67: number[];
  };
}

interface IGetInvoiceItemsParams {
  id: number;
  data_for: string;
  need_section: number;
  is_separate_estimate_sections: boolean;
  isHideLoading?: boolean;
  is_separate_change_order_sections?: boolean;
}

interface IInvoiceItemData {
  item_id: number | string;
  apply_global_tax: number | string;
  cost_code_id: number | string;
  company_id: number | string;
  cost_code_name: string;
  directory_id: number | string;
  invoice_id: number | string;
  invoice_item_no: number | string;
  item_type_display_name: string;
  item_type_name: string;
  item_type_key: string;
  item_type: number | string;
  description: string;
  notes: string;
  item_on_database: number | string;
  add_item_to_database?: number | string;
  is_markup_percentage: number | string;
  markup: string;
  markup_total: number | string;
  markup_amount: string;
  quantity: number | string;
  subject: string;
  tax_id: string;
  total: string;
  unit: string;
  unit_cost: string;
  variation_name: string;
  variation_id: string;
  module_reference_date: string;
  assigned_to: number | string;
  assignee_name: string;
  user_image?: string;
  assigned_to_name_only: string;
  image?: string;
  estimate_id: number | string;
  section_id: number | string;
  section_name: string;
  project_budget_item_id: number | string;
  reference_module_id: number | string;
  work_order_id: number | string;
  work_order_section_id: number | string;
  work_order_subject_name: string;
  work_order_section_name: string;
  work_order_subject: string;
  change_order_id: number | string;
  change_order_section_id: number | string;
  change_order_subject_name: string;
  change_order_section_name: string;
  change_order_subject: string;
  bill_subject_name: string;
  bill_section_name: string;
  bill_subject: string;
  bill_section_subject: string;
  purchase_order_section_id: number | string;
  purchase_order_subject_name: string;
  purchase_order_section_name: string;
  purchase_order_subject: string;
  total_mins: string | number | string;
  billing_rate: string | number | string;
  timecard_user_id: number | string;
  timecard_details: string | number;
  ot_hours: string | number | string;
  tc_employee_name: string;
  tc_employee_first_name: string;
  tc_employee_last_name: string;
  tc_employee_title: string;
  assignee_type: string;
  is_billable: number | string;
  reference_primary_id: number | string;
  billing_option: string;
  code_id: number | string;
  company_order_id: string;
  csi_code: string;
  code_is_deleted: number;
  current_amount: string;
  previous_paid_bill: number | string;
  current_paid_bill: number | string;
  total_bill: number | string;
  total_paid_bill: number | string;
  total_tax: string;
  date_added: string;
  date_modified: string;
  is_tm_item: number | string;
  item_tax: string;
  change_order_item_section_id: number | string;
  internal_notes: string;
  e_hours_used_total_minutes: number | string;
  vendor_company_name: string;
  tm_tc_group_by_cost_code_key_id: string;
  tm_tc_group_by_cost_code_key_name: string;
  tm_tc_group_by_day_key_id: string;
  tm_tc_group_by_day_key_name: string;
  tm_tc_group_by_employee_key_id: string;
  tm_tc_group_by_employee_key_name: string;
  tm_tc_group_by_key_id: string;
  tm_tc_group_by_key_name: string;
  tm_tc_group_by_none_key_id: string;
  tm_tc_group_by_none_key_name: string;
  timecard_user_rate: string;
  user_billing_rate: string;
  user_burden_rate: string;
  user_emp_wage_rate: string;
  timecard_detail_id: string | number;
  assigned_to_contact_id?: number | string;
  original_item_total: string | number;
  paid_bill: string;
  paid_unit: string | number;
  allow_overbilling: string | number;
  is_retainage_item?: string;
  code_is_deleted?: number;
  reference_item_id?: number | string;
  reference_module_item_id: number | string;
  submitAction?: string;
  is_discount_item?: string;
  held_retainage_item?: string;
  is_progressive_bill_item?: number;
  progressive_bill_percentage?: string;
  progressive_bill_total?: string;
  user_image?: string;
  sub_contract_section_subject: string;
  co_section_id: string;
  co_section_name: string;
}

interface IInvoiceItemsListBillSectionData {
  bill_subject: string;
  items: IInvoiceItemData[];
  section_id: number;
  section_name: string;
  section_subject_name: string;
}

interface IInvoiceItemsListChangeOrderSectionData {
  change_order_section_subject: string;
  change_order_subject: string;
  items: IInvoiceItemData[];
  section_id: string;
  section_name: string;
  section_subject_name: string;
}

interface IInvoiceItemsListImportFromEstimateSectionData {
  items: IInvoiceItemData[];
  section_id: string;
  section_name: string;
}

interface IInvoiceItemsListEstimateSectionData {
  items: IInvoiceItemData[];
  section_id: string;
  section_name: string;
}

interface IInvoiceItemsListPurchaseOrderSectionData {
  items: IInvoiceItemData[];
  purchase_order_subject: string;
  purchase_order_section_subject: string;
  section_id: string;
  section_name: string;
  section_subject_name: string;
}

interface IInvoiceItemsListWorkOrderSectionData {
  items: IInvoiceItemData[];
  section_id: number;
  section_name: string;
  section_subject_name: string;
  work_order_section_subject: string;
  work_order_subject: string;
}

interface IInvoiceItemsListSubContractSectionData {
  items: IInvoiceItemData[];
  section_id: number;
  section_name: string;
  section_subject_name: string;
  sub_contract_section_subject: string;
  sub_contract_subject: string;
}

interface IInvoiceItemsListData {
  bill_sections: IInvoiceItemsListBillSectionData[];
  change_order_sections: IInvoiceItemsListChangeOrderSectionData[];
  estimate_section: IInvoiceItemsListEstimateSectionData[];
  items: IInvoiceItemData[];
  import_from_estimate_section: IInvoiceItemsListImportFromEstimateSectionData[];
  purchase_order_sections: IInvoiceItemsListPurchaseOrderSectionData[];
  work_order_sections: IInvoiceItemsListWorkOrderSectionData[];
  equip_log_section: IInvoiceItemData[];
  expense_section: IInvoiceItemData[];
  timecard_section: IInvoiceItemData[];
  sub_contract_sections: IInvoiceItemsListSubContractSectionData[];
}

interface IGetInvoiceItemsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IInvoiceItemsListData;
}

interface IIvItemsCellRenderer {
  data: IInvoiceItemData;
}

interface IGetSavedTimeMaterialInvoiceItemsParams {
  id: number;
  project_id: number;
}

interface IAddInvoiceItem {
  item_type?: string;
  subject?: string;
  item_id?: number;
  invoice_item_no?: number;
  unit_cost?: string;
  quantity?: string;
  is_markup_percentage?: number;
  markup?: string;
  markup_amount?: number;
  cost_code_id?: string;
  total?: string;
  apply_global_tax: number;
  hidden_markup?: string;
  assigned_to?: number;
  assignee_type?: number | string;
  assigned_to_contact_id?: number;
  add_item_to_database?: number;
  reference_item_id?: number | string;
  paid_unit?: number;
  description?: string;
  unit?: string;
  is_discount_item?: number;
  reference_module_item_id?: string;
  variation_id?: string;
  notes?: string;
  estimate_id?: number;
  assigned_to?: number;
  user_image?: string;
}

interface IAddMannualInvoiceItem {
  item_type?: number;
  subject?: string;
  item_id?: number;
  invoice_item_no?: number;
  unit_cost?: number;
  quantity?: number;
  is_markup_percentage?: number;
  markup?: number | null;
  markup_amount?: number;
  cost_code_id?: number;
  total?: number;
  apply_global_tax?: boolean;
  hidden_markup?: number;
  assigned_to?: number;
  assignee_type?: number | string;
  assigned_to_contact_id?: number;
  add_item_to_database?: number;
  reference_item_id?: number;
  paid_unit?: number;
  description?: string;
  unit?: string;
  is_discount_item?: number;
  reference_module_item_id?: string;
  variation_id?: string;
  notes?: string;
  estimate_id?: number;
  invoice_total?: number;
  user_image?: string;
}

interface IAddInvoiceItemsParams {
  id?: number;
  project_id?: number;
  project_items?: IAddInvoiceItem[] | Array<Partial<ISOVEstimateItem>>;
  expenses?: Array<Partial<ITimeAndMaterialItemsListExpenseData>>;
  timecard_items?: Array<Partial<ITimeAndMaterialItemsListTimeCardsData>>;
  equipment_log_items?: Array<
    Partial<ITimeAndMaterialItemsListEquipmentLogsData>
  >;
  items?: IDiscountItemSave[] | IAddInvoiceItem[] | IAddMannualInvoiceItem[];
  co_items?: Array<Partial<ISOVChangeOrderItem>>;
  wo_items?: Array<Partial<ISOVWorkOrderItem>>;
  is_single_item?: string | number;
}

interface IAddInvoiceItemResData {
  reference_item_id: number;
}

interface IAddInvoiceItemRes extends Omit<IDefaultAPIRes, "data"> {
  data: IAddInvoiceItemResData;
}

interface IUpdateInvoiceItem {
  item_id?: number | string;
  invoice_item_no?: number | string;
  unit_cost?: number | string;
  quantity?: number | string;
  is_markup_percentage?: number | string;
  markup?: number | string | null;
  cost_code_id?: number | string;
  total?: number | string;
  apply_global_tax?: number | string;
  item_type?: number | string;
  hidden_markup?: number | string;
  assigned_to?: number | string;
  assignee_type?: number | string;
  assigned_to_contact_id?: number | string;
  change_order_id?: number | string;
  quickbook_invoiceitem_id?: number | string;
  quickbook_item_id?: number | string;
  qbc_id?: number | string;
  add_item_to_database?: number | string;
  reference_item_id?: number | string;
  paid_unit?: number | string;
  paid_bill?: number | string;
  subject?: string;
  description?: string;
  notes?: string;
  internal_notes?: string;
  unit?: string;
  billing_option?: string;
  timecard_user_id?: number;
  e_hours_used_total_minutes?: number | string;
  tc_employee_name?: string;
  timecard_user_rate?: string;
  billing_rate?: number;
  burden_rate?: number;
  emp_wage?: number;
  user_image?: string;
  user_id?: string;
  total_mins?: number;
  details?: number;
  detail_id?: string;
  tm_tc_group_by_key_id?: string;
  tm_tc_group_by_key_name?: string;
  ot_hours?: string;
  tm_tc_group_by_cost_code_key_id?: string;
  tm_tc_group_by_cost_code_key_name?: string;
  tm_tc_group_by_employee_key_id?: string;
  tm_tc_group_by_employee_key_name?: string;
  tm_tc_group_by_day_key_id?: string;
  tm_tc_group_by_day_key_name?: string;
  tm_tc_group_by_none_key_id?: string;
  tm_tc_group_by_none_key_name?: string;
  log_id?: number;
  price?: number;
  tax_id?: string;
  invoice_total?: number;
  billing_ratio?: string;
  billing_unit?: string;
  csi_code?: string;
  cost_code_name?: string;
}

interface IUpdateInvoiceItemsParams {
  id: number;
  items: IUpdateInvoiceItem[];
}

interface IUpdateInvoiceItemsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    invoice_id: number;
    reference_item_id: number;
  };
}

interface IIVRetainageInfoData {
  project_retainage: string;
  total_retainage: string;
  retainage_held: string;
}

interface IIVRetainageInfoApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    project_retainage: string;
    total_retainage: string;
    retainage_held: string;
  };
}

// Payment section
interface IPaymentData {
  company_invoice_id: string;
  payment_status: any;
  approval_status_key: string;
  invoice_id: number;
  payment_type_name: string;
  payment_notes: string;
  payment_id: number;
  approval_status: number;
  amount: string;
  posted_by: string;
  deposit_name?: string;
  approval_status_display_name: string;
  payment_date: string;
  default_color?: string;
  payment_type_key?: string;
}

interface IIVPaymentInitialState {
  payments: IPaymentData[];
  invoice_total?: string;
  isPaymentTabLoading: boolean;
  isPaymentDataFetched: boolean;
}

interface IIVPaymentApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    payments: IPaymentData[];
    invoice_total?: string;
    payment_url?: string;
  };
}

interface IIVFetchPaymentParams {
  id: string;
  get_payment_link?: number;
}

interface IDeleteInvoiceItemsParams {
  id: number;
  itemId: number;
}

interface IUpdateInvoiceItemsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    id_disable_project: string;
  };
}

interface IFilterInvoiceProject {
  filterByProject: (params: IBalanceData) => void;
}

interface IFilterInvoiceByIdProps {
  filterByInvoiceId: (params: string) => void;
}

interface IGenerateWepayPaymentLink {
  record_id?: number;
  module_key?: string;
  module_page?: string;
  template_id: number;
  current_user_id?: number;
  redirect_pdf_name?: string;
}

interface IGenerateWepayPaymentLinkApiresponse {
  statusCode: number;
  message: string;
  success: boolean;
  responseTime: string;
  data: {
    short_link: string;
  };
}

interface ISelectedTemplateData {
  value?: string;
  label?: string;
  master_tpl?: string;
  pdf_value?: string;
  template_id?: string;
}

interface IDeleteTimeMaterialItems {
  id?: number;
  expenses?: number[];
  timecard_items?: number[];
  equipment_log_items?: number[];
  change_order?: number[];
  purchase_orders?: number[];
  bills?: number[];
  items?: number[];
  project_items?: number[];
  sub_contract?: number[];
}

interface IIncludeTimeMaterialAttachmentsItemsData {
  current_item_id: number;
  file_path: string;
  project_id: number;
  type: string;
  type_id: number;
  file_ext: string;
  title: string;
  notes: string;
  is_deleted: number;
  file_tags: string;
  child_item_id: number;
  file_type: string;
  is_image: number;
  height: string;
  width: string;
  size: string;
  mode: string;
  image_res: string;
  camera_res: string;
  file_name: string;
  thumb_flag: string;
  large_flag: string;
  user_id: number;
}

interface IIncludeTimeMaterialAttachmentsParams {
  id?: number;
  primary_id?: number;
  items?: IIncludeTimeMaterialAttachmentsItemsData[];
  description?: string;
}

interface IIncludeTimeMaterialAttachmentsRes
  extends Omit<IDefaultAPIRes, "data"> {
  data: {};
}

interface ISOVItemsListParmas {
  project_id: number;
  module_id: number;
  need_section: number;
}

interface ISOVBudgetItem {
  item_id: number;
  project_id: number;
  directory_id: number;
  company_id: number;
  equipment_id: number;
  material_id: number;
  cost_code_id: number;
  tax_id: number;
  subject: string;
  quantity: 15;
  unit: string;
  unit_cost: string;
  hidden_markup: number;
  markup: string;
  description: string;
  estimate_id: number;
  change_order_id: number;
  work_order_id: number;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  total: string;
  total_tax: null;
  reference_item_id: number;
  budget_item_no: number;
  parent_budget_item_id: number;
  item_type: number;
  reference_item_type_id: number;
  item_assigned_to: number;
  item_assigned_to_contact_id: number;
  apply_global_tax: number;
  tax_amount: string;
  is_markup_percentage: number;
  markup_amount: string;
  section_id: number;
  item_no: number;
  internal_notes: string;
  is_project_template: number;
  project_template_id: number;
  item_on_database: number;
  modified_unit_cost: string;
  assigned_to: string;
  assigned_to_contact_id: string;
  assignee_name: null;
  assigned_to_name_only: string;
  assigned_to_company_name: string;
  company_estimate_id: null;
  estimate_approval_type: null;
  estimate_item_no: null;
  custom_section_id: null;
  est_item_section_id: string;
  section_name: null;
  order_item_no: null;
  item_total: number;
  assignee_type: null;
  item_type_display_name: string;
  item_type_name: string;
  item_type_key: string;
  wo_item_no: null;
  company_order_id: string;
  wo_company_order_id: null;
  co_company_order_id: null;
  total_billed: number;
  tm_co_total: string;
  bill_paid: string;
  tax_rate: string;
  cost_code_name: string;
  cost_code: null;
  source: string;
  source_name: string;
  previous_paid_bill: number;
  current_paid_bill: number;
  previous_amount: string;
  bill_paid_amount: number;
  current_amount: string;
  item_section_id: string;
  variation_name: string;
  variation_id: string;
  change_order_section_subject: null;
  change_order_subject_name: null;
  change_order_section_name: string;
  work_order_section_subject: null;
  work_order_subject_name: null;
  work_order_section_name: string;
  budget_item_id: number;
  no_mu_total: number;
}

interface ISOVChangeOrderData {
  change_order_id: number;
  company_order_id: string;
  subject: string;
  billing_status: number;
  total: string;
  total_billed: string;
  bill_paid: string;
  order_date: string;
  billing_status_name: string;
  billing_status_key: string;
}

interface ISOVChangeOrderItem {
  item_id: number;
  change_order_id: number;
  user_id: number;
  company_id: number;
  cost_code_id: number;
  tax_id: number;
  subject: string;
  quantity: number;
  unit: string;
  unit_cost: string;
  hidden_markup: number;
  markup: string;
  description: string;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  total: string;
  order_item_no: number;
  parent_item_id: number;
  item_type: number;
  reference_item_id: number;
  item_on_database: number;
  assigned_to: number;
  contractor_id: number;
  contractor_contact_id: number;
  is_markup_percentage: number;
  markup_amount: string;
  is_discount_item: number;
  source: string;
  cor_item_id: number;
  apply_global_tax: number;
  assigned_to_backup: number;
  assigned_to_contact_id: number;
  estimate_id: number;
  reference_module_item_id: number;
  internal_notes: string;
  company_order_id: string;
  total_billed: number;
  bill_paid: number;
  billing_status: number;
  change_order_section_subject: string;
  change_order_subject_name: string;
  change_order_section_name: string;
  variation_name: string;
  budget_item_id: number;
  item_type_display_name: string;
  item_type_name: string;
  item_type_key: string;
  cost_code_name: string;
  cost_code: string;
  tm_co_total: string;
  reference_item_type_id: number;
  co_section_id: number;
  co_section_name: string;
  is_optional_item: number;
}

interface ISOVChangeOrderSectionData {
  section_id: number;
  section_name: string;
  section_subject_name: string;
  change_order_section_subject: string;
  company_order_id: string;
  items: ISOVChangeOrderItem[];
}

interface ISOVEstimateItem {
  item_id: number;
  project_id: number;
  directory_id: number;
  company_id: number;
  equipment_id: number;
  material_id: number;
  cost_code_id: number;
  tax_id: number;
  subject: string;
  quantity: number;
  unit: string;
  unit_cost: string;
  hidden_markup: number;
  markup: string;
  description: string;
  estimate_id: number;
  change_order_id: number;
  work_order_id: number;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  total: string;
  total_tax: number;
  reference_item_id: number;
  budget_item_no: number;
  parent_budget_item_id: number;
  item_type: number;
  reference_item_type_id: number;
  item_assigned_to: number;
  item_assigned_to_contact_id: number;
  apply_global_tax: number;
  tax_amount: string;
  is_markup_percentage: number;
  markup_amount: string;
  section_id: number;
  item_no: number;
  internal_notes: string;
  is_project_template: number;
  project_template_id: number;
  item_on_database: number;
  modified_unit_cost: string;
  assigned_to: string;
  assigned_to_contact_id: string;
  assignee_name: string;
  assigned_to_name_only: string;
  assigned_to_company_name: string;
  company_estimate_id: string;
  estimate_approval_type: string;
  estimate_item_no: number;
  custom_section_id: number;
  est_item_section_id: string;
  section_name: string;
  order_item_no: number;
  item_total: number;
  assignee_type: number;
  item_type_display_name: string;
  item_type_name: string;
  item_type_key: string;
  wo_item_no: number;
  company_order_id: string;
  wo_company_order_id: number;
  co_company_order_id: number;
  total_billed: number;
  tm_co_total: string;
  bill_paid: string;
  tax_rate: string;
  cost_code_name: string;
  cost_code: string;
  source: string;
  source_name: string;
  previous_paid_bill: number;
  current_paid_bill: number;
  previous_amount: string;
  bill_paid_amount: number;
  current_amount: string;
  item_section_id: string;
  variation_name: string;
  variation_id: string;
  change_order_section_subject: string;
  change_order_subject_name: string;
  change_order_section_name: string;
  work_order_section_subject: string;
  work_order_subject_name: string;
  work_order_section_name: string;
  budget_item_id: number;
  no_mu_total: number;
  is_optional_item: number;
}

interface ISOVEstimateSectionData {
  section_id: string;
  section_name: string;
  items: ISOVEstimateItem[];
}

interface ISOVWorkOrderItem {
  item_id: number;
  project_id: numbernumber;
  directory_id: number;
  company_id: number;
  equipment_id: null;
  material_id: null;
  cost_code_id: number;
  tax_id: number;
  subject: string;
  quantity: number;
  unit: string;
  unit_cost: string;
  hidden_markup: number;
  markup: string;
  description: string;
  estimate_id: number;
  change_order_id: number;
  work_order_id: number;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  total: string;
  total_tax: null;
  reference_item_id: number;
  budget_item_no: number;
  parent_budget_item_id: number;
  item_type: number;
  reference_item_type_id: number;
  item_assigned_to: number;
  item_assigned_to_contact_id: number;
  apply_global_tax: number;
  tax_amount: string;
  is_markup_percentage: number;
  markup_amount: string;
  section_id: number;
  item_no: number;
  internal_notes: string;
  is_project_template: number;
  project_template_id: null;
  item_on_database: number;
  modified_unit_cost: string;
  assigned_to: string;
  assigned_to_contact_id: string;
  assignee_name: null;
  assigned_to_name_only: string;
  assigned_to_company_name: string;
  company_estimate_id: null;
  estimate_approval_type: null;
  estimate_item_no: null;
  custom_section_id: null;
  est_item_section_id: string;
  section_name: null;
  order_item_no: null;
  item_total: number;
  assignee_type: null;
  item_type_display_name: string;
  item_type_name: string;
  item_type_key: string;
  wo_item_no: number;
  company_order_id: string;
  wo_company_order_id: string;
  co_company_order_id: null;
  total_billed: number;
  tm_co_total: string;
  bill_paid: string;
  tax_rate: string;
  cost_code_name: string;
  cost_code: null;
  source: string;
  source_name: string;
  previous_paid_bill: number;
  current_paid_bill: number;
  previous_amount: string;
  bill_paid_amount: number;
  current_amount: string;
  item_section_id: string;
  variation_name: string;
  variation_id: string;
  change_order_section_subject: null;
  change_order_subject_name: null;
  change_order_section_name: string;
  work_order_section_subject: string;
  work_order_subject_name: string;
  work_order_section_name: string;
  budget_item_id: number;
  no_mu_total: number;
}

interface ISOVWorkOrderSectionData {
  section_id: number;
  section_name: string;
  section_subject_name: string;
  work_order_section_subject: string;
  company_order_id: string;
  items: ISOVWorkOrderItem[];
}

interface ISOVItemsListData {
  allow_over_billing: string;
  billing_option: string;
  budget_data: ISOVBudgetItem[];
  change_order_data: ISOVChangeOrderData[];
  change_order_sections: ISOVChangeOrderSectionData[];
  estimate_section: ISOVEstimateSectionData[];
  group_co_count: number;
  work_order_data: [];
  work_order_sections: ISOVWorkOrderSectionData[];
}

interface ISOVItemsListApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ISOVItemsListData;
}

interface ISelectedSOVItems {
  change_order_items: ISOVChangeOrderItem[];
  work_order_items: ISOVWorkOrderItem[];
  budget_items: ISOVBudgetItem[];
  estimate_items: ISOVEstimateItem[];
  billing_option: string;
}

// Common Slice
interface IIVCommonInitialState {
  filter: string[];
  companyItemsData: {
    items: ISCCompanyItems[];
    projectId: number;
    isLoading: boolean;
    isDataFetched: boolean;
  };
}

interface IIVCompanyItemsParamsData {
  project_id?: number;
}

interface IIVCompanyItems {
  type_id: string;
  show_in_progress_bar: number;
  name: string;
  sort_order: string;
  display_name: string;
  default_color: string;
  category: string;
  mark_up: string;
}
interface IIVCompanyItemsRes extends Omit<IDefaultAPIRes, "data"> {
  data: IIVCompanyItems[];
  projectId: number;
}

interface IUpdateInvoiceItemsTabApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    invoice_id: number;
    reference_item_id: number;
  };
}

interface IItemsToAdjustInvoiceParams {
  project_id: string;
}

interface IItemsToAdjustInvoiceData {
  cnt: string;
  item_id: number;
  invoice_total: string;
  unit_cost: string;
  quantity: number;
  subject: string;
  original_item_total: string;
  item_billed: string;
}

interface IItemsToAdjustInvoiceListApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IItemsToAdjustInvoiceData[];
}

interface IInvoiceItemsBySection {
  billSection: IInvoiceItemData[];
  changeOrderSection: IInvoiceItemData[];
  equipmentLogSection: IInvoiceItemData[];
  expenseSection: IInvoiceItemData[];
  purchaseOrderSection: IInvoiceItemData[];
  timeCardSection: IInvoiceItemData[];
  SOVBudgetItems: IInvoiceItemData[];
  workOrderSection: IInvoiceItemData[];
  SOVChangeOrderSection: IInvoiceItemData[];
  lumpSumItems: IInvoiceItemData[];
  retainageItem: IInvoiceItemData[];
  subContarctItem: IInvoiceItemData[];
}

interface IUpdateInvoiceItemsTabApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    invoice_id: number;
    reference_item_id: number;
  };
}

// Update payment alert
interface IIVPaymentAlertRes extends Omit<IDefaultAPIRes, "data"> {
  data: ObjType;
}

interface IProjectAdjustInvoiceParams {
  project_id: string;
  items: IItemToAdjustInvoice[];
}

interface IProjectAdjustInvoiceApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {};
}

interface IIVStatusListInitialState {
  payment_type: ISelectList[];
  payment_approval_status: ISelectList[];
  isStatusListDataLoading: boolean;
  isStatusListDataFetched: boolean;
}

interface IStatusList {
  label?: string;
  value?: IStatus;
  default_color?: string;
  icon?: IFontAwesomeIconProps["icon"];
  key?: string;
  show_in_progress_bar?: number;
}
