import { useRef, useState, useMemo, useEffect, FormEvent } from "react";
import isEmpty from "lodash/isEmpty";
import isEqual from "lodash/isEqual";
import * as Yup from "yup";
import { useFormik } from "formik";
import { Select, type InputRef } from "antd";
import { type RadioChangeEvent } from "antd";
// Hook Redux
import { useTranslation } from "~/hook";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import {
  filterOptionBySubstring,
  getItemTypeIcon,
  onKeyDownCurrency,
} from "~/shared/utils/helper/common";
// Atoms
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Typography } from "~/shared/components/atoms/typography";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { addItemObject } from "~/modules/people/directory/utils/constasnts";
import { addUnit, getUnitList } from "~/redux/action/unitActions";
import { useAppBillDispatch, useAppBillSelector } from "../../redux/store";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";
import { apiRoutes } from "~/route-services/routes";
import { CFRadio } from "~/components/third-party/ant-design/cf-radio";
import { CFRadioGroup } from "~/components/third-party/ant-design/cf-radio-group";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import {
  addBillItems,
  updateBillItems,
} from "../../redux/action/billDetailAction";
import { defaultConfig } from "~/data";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import {
  addItems,
  updatebillDetail,
  updateBillDetailItems,
} from "../../redux/slices/billDetailSlice";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import { getGSettings } from "~/zustand";

type FormikSubmitEvent = FormEvent<HTMLFormElement> & {
  nativeEvent: { submitter?: HTMLButtonElement };
};

const AddManualItem = ({
  isOpen,
  onClose,
  isViewOnly = false,
  recordId = 0,
  isShowNextPre = false,
  allRecords = [],
  moduleName,
}: any) => {
  const { _t } = useTranslation();
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const gSettings: GSettings = getGSettings();

  const {
    billDetail,
    amountList,
    billAccountId,
    itemTypes,
  }: IBillDetailsInitialState = useAppBillSelector(
    (state) => state.billDetails
  );
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { module_id } = currentModule || {};

  const [isItemDisable, setItemDisable] = useState<boolean>(false);
  const [isCategoryDisable, setCategoryDisable] = useState<boolean>(false);
  const dispatch = useAppBillDispatch();
  const { inputFormatter, unformatted, formatter } = useCurrencyFormatter();
  const costUnitRef = useRef<HTMLDivElement>(null);
  const unitCostContainerRef = useRef<HTMLDivElement>(null);
  const [isFocusAmountVal, setIsFocusAmountVal] = useState(false);
  const unitCostRef = useRef<InputRef>(null);
  const skipClose = useRef<boolean>(false);

  //   const dispatch = useAppSCDispatch();
  const [showUnitInputs, setShowUnitInputs] = useState<boolean>(!isViewOnly);
  const { codeCostData }: IGetCostCodeList = useAppBillSelector(
    (state) => state.costCode
  );
  const [selectedRec, setSelectedRec] = useState<any>({});
  const [formType, setFormType] = useState<string | undefined | null>(
    selectedRec && Object.keys(selectedRec).length > 0
      ? selectedRec.item_category
      : "item"
  );
  const [formEvent, setFormEvent] = useState<FormikSubmitEvent | null>(null);
  const [selRecordId, setSelRecordId] = useState<number>(recordId);
  const [isHideNextPre, setIsHideNextPre] = useState<boolean>(true);
  const [submittingFrm, setSubmittingFrm] = useState<string>("");
  const [newTypeName, setNewTypeName] = useState<string>("");
  const [unitData, setUnitData] = useState<IUnitData[]>([]);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [confirmSaveData, setConfirmSaveData] = useState<{
    rid: number;
    message?: string;
  }>({ rid: 0 });
  const [isExistingLoading, setIsExistingLoading] = useState<boolean>(false);

  const isTaxApplicable =
    gSettings?.is_non_united_state_qb_country ||
    gSettings?.quickbook_sync === "0";
  useEffect(() => {
    if (selRecordId && allRecords?.length > 0) {
      const selectedRecData = allRecords.find((r) => {
        return r.item_id == selRecordId;
      });
      setSelectedRec(selectedRecData);
      setIsHideNextPre(!isShowNextPre);
    } else {
      setSelectedRec({});
      setIsHideNextPre(true);
    }
  }, [selRecordId, JSON.stringify(allRecords)]);

  const getUnit = async () => {
    const unit: IUnitListResponse = await getUnitList();
    setUnitData(unit?.data?.units);
  };

  useEffect(() => {
    getUnit();
  }, []);

  const initialValues: any = useMemo(() => {
    const unitCost = selectedRec?.unit_cost
      ? (Number(selectedRec?.unit_cost) / 100).toString()
      : "";
    const total = selectedRec?.total
      ? (Number(selectedRec?.total) / 100).toString()
      : "";

    return {
      ...selectedRec,
      item_id: selectedRec?.item_id,
      subject: selectedRec?.subject || "",
      item_type:
        selectedRec?.item_type && selectedRec?.item_type != ""
          ? selectedRec?.item_type?.toString()
          : "",
      quantity: selectedRec?.quantity?.toString() || "",
      cost_code_id: selectedRec?.cost_code_id?.toString() || "",
      account:
        (!!selectedRec?.account_id &&
          selectedRec?.account_id !== "0" &&
          selectedRec?.account_id?.toString()) ||
        "",
      unit_cost: unitCost,
      ...(selectedRec?.unit
        ? { unit: selectedRec?.unit?.toString() || "" }
        : {}),
      total: total,
      ...(selectedRec?.cost_code_id
        ? { cost_code_id: selectedRec.cost_code_id }
        : {}),
      ...(selectedRec?.cost_code_name
        ? { cost_code_name: selectedRec.cost_code_name }
        : {}),
      ...(selectedRec?.description
        ? { description: selectedRec.description.toString() }
        : {}),
      ...(selectedRec?.internal_notes
        ? { internal_notes: selectedRec.internal_notes.toString() }
        : {}),
      submitAction: "",
      total_category: selectedRec.total_category
        ? selectedRec.total_category
        : "0.00",
      item_category: "item",
      bill_item_no:
        billDetail?.data?.items && billDetail?.data?.items?.length + 1,
      ...(selectedRec?.apply_global_tax && formType === "item"
        ? { apply_global_tax: selectedRec.apply_global_tax || false }
        : {}),
    };
  }, [selectedRec?.item_id]);
  const validationSchema = Yup.object().shape({
    subject: Yup.string().trim().required("This field is required."),
    item_type: Yup.string().trim().required("This field is required."),
  });

  const validationSchemaWithCostCode = Yup.object().shape({
    subject: Yup.string().trim().required("This field is required."),
    item_type: Yup.string().trim().required("This field is required."),
    cost_code_id: Yup.string().trim().required("This field is required."),
  });

  const formik = useFormik({
    initialValues,
    validationSchema:
      (gSettings?.quickbook_sync === "1" ||
        gSettings?.quickbook_desktop_sync === "1") &&
      (!Number(initialValues?.cost_code_id) ||
        Number(initialValues?.cost_code_id) === 0) &&
      formType === "item"
        ? validationSchemaWithCostCode
        : validationSchema,
    enableReinitialize: true,
    onSubmit: async (values, { resetForm }) => {
      if (
        (gSettings?.quickbook_sync === "1" ||
          gSettings?.quickbook_desktop_sync === "1") &&
        (!Number(values?.cost_code_id) || Number(values?.cost_code_id) === 0) &&
        formType === "item"
      ) {
        formik.setErrors({ cost_code_id: "This field is required." });
        return;
      }
      const isSkipClose =
        formEvent?.nativeEvent.submitter?.getAttribute("data-skip-close") ===
          "true" || skipClose.current;
      const formData = { ...values, submitAction: undefined };
      formData.bill_id = billDetail?.data?.bill_id;
      if (formData?.item_category !== formType) {
        formData.item_category = formType;
      }
      if (formData?.unit_cost) {
        formData.unit_cost = Number(formData?.unit_cost) * 100;
      }
      if (!formData?.cost_code_id) {
        formData.cost_code_id = "0";
      }
      if (formType !== "item") {
        formData.unit_cost = Number(formData?.total) * 100;
        formData.total_category = Number(formData?.total);
        formData.quantity = 1;
      }
      if (!!formData?.account && formData?.account !== "0") {
        formData.account_id = formData?.account;
      }
      if (confirmSaveData?.rid > 0) {
        formData.reference_item_id =
          confirmSaveData && confirmSaveData?.rid > 0
            ? confirmSaveData.rid?.toString()
            : undefined;
      }
      if (formData?.total) {
        formData.total = Number(formData?.total) * 100;
      }
      const newFormData = [formData];

      if (isSkipClose) {
        setSubmittingFrm("save1");
      } else {
        setSubmittingFrm(
          values.submitAction == "saveAndAddAnother"
            ? "saveAndAddAnother"
            : "save"
        );
      }
      try {
        let responseApi = { success: false, message: "", data: {} };
        if (selectedRec?.item_id && selectedRec?.item_id != "0") {
          responseApi = (await updateBillItems({
            id: Number(billDetail?.data?.bill_id),
            items: [formData],
            module_id: module_id ?? 0,
            is_single_item: 1,
          })) as any;
        } else {
          responseApi = (await addBillItems({
            is_single_item: 1,
            ...(formType === "item"
              ? {
                  add_item_to_database:
                    confirmSaveData?.rid > 0
                      ? 0
                      : Number(formData.add_item_to_database).toString(),
                }
              : {}),
            module_id: module_id ?? 0,
            id: Number(billDetail?.data?.bill_id),
            items: newFormData,
          })) as any;
        }
        if (Number(responseApi?.data?.reference_item_id || "") > 0) {
          setConfirmSaveData({
            rid: Number(responseApi?.data?.reference_item_id || ""),
            message: responseApi?.message,
          });
          setIsExistingLoading(false);
          return;
        }
        const fltCostCode = codeCostData.find(
          (item) => item.code_id == formData?.cost_code_id
        );

        if (responseApi?.success) {
          setCategoryDisable(false);
          if (Number(responseApi?.data?.reference_item_id || "") <= 0) {
            setConfirmSaveData({
              rid: 0,
              message: "",
            });
            setIsExistingLoading(false);

            if (!isSkipClose) {
              if (values.submitAction == "saveAndAddAnother") {
                setSelRecordId(0);
                setFormEvent(null);
                resetForm();
              } else {
                onClose(true);
              }
            }
            const isUpdate =
              selectedRec?.item_id && selectedRec?.item_id != "0";
            const extraObj = {
              cost_code_name: fltCostCode?.cost_code_name || "",
              cost_code: fltCostCode?.csi_code || "",
              item_on_database: formData?.add_item_to_database || 0,
            };
            if (selectedRec?.item_id && selectedRec?.item_id != 0) {
              const updatedItems = billDetail?.data?.items?.map((section) => ({
                ...section,
                items: section?.items?.map((item) =>
                  item.item_id == selectedRec?.item_id
                    ? {
                        ...item,
                        ...extraObj,
                        ...formData,
                        no_mu_total: formData?.total,
                      }
                    : item
                ),
              }));
              dispatch(updateBillDetailItems(responseApi?.updatedItemdata[0]));
              dispatch(updatebillDetail(responseApi?.updateBillTotalValue[0]));
              // dispatch(updateBillDetailItems(formData));
            } else {
              dispatch(addItems(responseApi.data));
            }
          }
        } else {
          setIsExistingLoading(false);
          notification.error({
            description: responseApi?.message || "",
          });
        }
        setSubmittingFrm("");
      } catch (error) {
        setIsExistingLoading(false);
        setSubmittingFrm("");
        notification.error({
          description: (error as Error).message || "",
        });
      } finally {
        setSubmittingFrm("");
      }
    },
  });

  useEffect(() => {
    formik.resetForm({ values: initialValues });
  }, [initialValues]);

  // Submit Form
  const {
    handleSubmit,
    submitForm,
    handleChange,
    setFieldValue,
    setFieldTouched,
    values,
    errors,
    touched,
  } = formik;

  // Open unit cost
  const handleParagraphClick = () => {
    if (!isViewOnly) {
      setShowUnitInputs(true);
      setTimeout(() => {
        unitCostRef?.current?.focus();
      }, 10);
    }
  };

  const handleFocusOut = (e: React.FocusEvent<HTMLInputElement>) => {
    if (!unitCostContainerRef.current?.contains(e.relatedTarget as Node)) {
      if (
        values?.unit_cost &&
        !isEmpty(values?.unit_cost) &&
        values.unit &&
        !isEmpty(values.unit)
      ) {
        setShowUnitInputs(false);
      }
    }
  };

  useEffect(() => {
    if (
      values?.unit_cost !== "" &&
      !isEmpty(values?.unit_cost) &&
      values?.unit_cost !== undefined &&
      values?.unit !== "" &&
      !isEmpty(values?.unit) &&
      values?.unit !== undefined &&
      !unitCostContainerRef.current?.contains(document.activeElement as Node)
    ) {
      setShowUnitInputs(false);
    } else {
      setShowUnitInputs(true);
    }
  }, [values]);

  useEffect(() => {
    if (
      values?.quantity !== "" &&
      values?.unit_cost !== "" &&
      values?.quantity &&
      values?.unit_cost
    ) {
      const total = Number(values?.quantity) * Number(values?.unit_cost);
      setFieldValue("total", total.toString());
    } else {
      setFieldValue("total", "");
    }
  }, [values?.quantity, values?.unit_cost]);

  const itemsTypeList = useMemo(() => {
    return itemTypes
      ?.filter((item) => item?.type_id)
      .map((item) => ({
        label: (
          <div className="flex items-center gap-1.5">
            <FontAwesomeIcon
              icon={getItemTypeIcon({ type: item?.type_id?.toString() })}
            />
            {HTMLEntities.decode(sanitizeString(item?.name))}
          </div>
        ),
        value: item?.type_id?.toString(),
        ...item,
      }));
  }, [itemTypes]);

  const itemTypeName = useMemo(() => {
    const itemType = values.item_type;
    if (!itemType) return "";
    const itemTypeObj = itemsTypeList?.find((item) => item.value == itemType);
    if (!selectedRec?.item_id || selectedRec?.item_id == "0") {
      setFieldValue("add_item_to_database", appSettings?.is_cidb_auto_save);
    }
    return itemTypeObj?.name;
  }, [
    values.item_type,
    selectedRec?.item_id,
    appSettings?.is_cidb_auto_save,
    itemsTypeList,
  ]);
  // cost code dropdown Options
  const costCodeOptions = useMemo(
    () =>
      codeCostData.length
        ? codeCostData.map((item) => {
            return {
              label: `${item.cost_code_name} ${
                item?.csi_code && item?.csi_code !== null
                  ? `(${item?.csi_code})`
                  : ""
              }`,
              value: item.code_id,
            };
          })
        : [],
    [codeCostData]
  );

  const currentItemIndex = useMemo(() => {
    return allRecords?.findIndex((i) => i.item_id == selectedRec?.item_id);
  }, [selectedRec?.item_id]);

  const handleItemNav = async (increment: number) => {
    const oldVal = {
      subject: initialValues?.subject,
      item_type: initialValues?.item_type,
      cost_code_id: initialValues?.cost_code_id,
      cost_code_name: initialValues?.cost_code_name,
      quantity: initialValues?.quantity,
      unit_cost: initialValues?.unit_cost,
      unit: initialValues?.unit,
      description: initialValues?.description,
      internal_notes: initialValues?.internal_notes,
      add_item_to_database: initialValues?.add_item_to_database,
      apply_global_tax: initialValues?.apply_global_tax,
    };
    const newVal = {
      subject: values?.subject,
      item_type: values?.item_type,
      cost_code_id: values?.cost_code_id,
      cost_code_name: values?.cost_code_name,
      quantity: values?.quantity,
      unit_cost: values?.unit_cost,
      unit: values?.unit,
      description: values?.description,
      internal_notes: values?.internal_notes,
      add_item_to_database: values?.add_item_to_database,
      apply_global_tax: values?.apply_global_tax,
    };

    const formModified = isEqual(newVal, oldVal);
    if (!formModified) {
      skipClose.current = true;
      await submitForm();
      skipClose.current = false;
    }

    const validationErrors = await formik.validateForm();

    if (!values?.cost_code_id) {
      formik.setFieldTouched("cost_code_id", true);
    }
    if (isEmpty(validationErrors)) {
      const nextRecord = allRecords[currentItemIndex + increment];
      setSelectedRec(nextRecord);
      setFormType(nextRecord?.item_category);
    } else {
      if (document.activeElement instanceof HTMLElement) {
        document.activeElement.blur();
      }
    }
  };

  const handleEnterKeyPress = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    event.preventDefault();
  };

  useEffect(() => {
    if (selectedRec?.item_category) {
      setFormType(selectedRec?.item_category);
    }
  }, []);

  const onchange = (e: RadioChangeEvent) => {
    setFormType(e?.target?.value);
  };

  useEffect(() => {
    if (billAccountId) {
      setFieldValue("account", billAccountId);
    }
  }, [billAccountId]);

  useEffect(() => {
    if (selectedRec && Object.keys(selectedRec).length > 0) {
      if (selectedRec?.item_category === "category") {
        setItemDisable(true);
        setCategoryDisable(false);
        setFormType("category");
      } else {
        setItemDisable(false);
        setCategoryDisable(true);
        setFormType("item");
      }
    }
  }, [selectedRec]);

  const itemTypeHide: string[] = [
    "retainage_payment",
    "freight_charge",
    "discount",
  ];

  const itemTypeDisable = () => {
    if (selectedRec?.reference_item_id || selectedRec?.reference_module_id) {
      let itemType = true;
      if (selectedRec?.reference_item_id?.toString() === "0") {
        itemType = false;
      }
      if (
        selectedRec?.reference_module_id ===
        defaultConfig.sub_contract_module_id
      ) {
        itemType = true;
      }

      return itemType;
    }
    return false;
  };
  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
  };
  const filteredCostCode = costCodeOptions.filter(
    (item) => String(values.cost_code_id) === String(item.value)
  );

  const costCodeValue = filteredCostCode.length
    ? filteredCostCode
    : !!values.cost_code_id && (values.cost_code_name || values.cost_code)
    ? `${values.cost_code_name || values.cost_code} (Archived)`
    : "";
  return (
    <>
      <Drawer
        open={isOpen}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-receipt"
              />
            </div>
            <div className="flex justify-between w-full">
              <Header
                level={5}
                className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
              >
                {`${
                  !selectedRec?.item_id || selectedRec?.item_id == "0"
                    ? _t("Add")
                    : ""
                } ${HTMLEntities.decode(sanitizeString(moduleName))} ${_t(
                  "Item"
                )}`}
              </Header>
              {!isHideNextPre && (
                <div className="flex items-center sm:gap-2 gap-0 pr-2">
                  <ButtonWithTooltip
                    tooltipTitle={_t("Previous")}
                    tooltipPlacement="top"
                    icon="fa-regular fa-chevron-left"
                    className="item-pre-next-button disabled:bg-transparent"
                    onClick={() => {
                      handleItemNav(-1);
                    }}
                    disabled={currentItemIndex == 0 || !!submittingFrm}
                  />
                  <ButtonWithTooltip
                    tooltipTitle={_t("Next")}
                    tooltipPlacement="top"
                    icon="fa-regular fa-chevron-right"
                    className="item-pre-next-button disabled:bg-transparent"
                    disabled={
                      currentItemIndex == allRecords.length - 1 ||
                      !!submittingFrm
                    }
                    onClick={() => {
                      handleItemNav(1);
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        }
        closeIcon={<CloseButton onClick={() => onClose()} />}
      >
        <form method="post" className="py-4" onSubmit={handleSubmit} noValidate>
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <CFRadioGroup
                  onChange={onchange}
                  value={formType || "item"}
                  className="flex gap-5"
                  view="row"
                >
                  <div>
                    <CFRadio
                      value="item"
                      id="item"
                      name="item"
                      className={`${
                        isItemDisable ? "text-black/25" : "text-primary-900"
                      }`}
                      label={"Item Detail"}
                      disabled={isItemDisable ? true : false}
                      checked={formType === "item"}
                    />
                  </div>
                  <div>
                    <CFRadio
                      value="category"
                      id="category"
                      name="category"
                      className={`${
                        isCategoryDisable ? "text-black/25" : "text-primary-900"
                      }`}
                      label={"Category Detail"}
                      disabled={isCategoryDisable ? true : false}
                      checked={formType === "category"}
                    />
                  </div>
                </CFRadioGroup>
                {!itemTypeHide?.includes(values?.feature_type ?? "") && (
                  <div className="w-full">
                    <SelectField
                      label={_t("Item Type")}
                      labelPlacement="top"
                      isRequired={true}
                      name="item_type"
                      value={
                        values.item_type && values.item_type != "0"
                          ? values.item_type?.toString()
                          : undefined
                      }
                      onChange={(value) => {
                        setFieldValue("item_type", value || "");
                        setFieldTouched("item_type", value ? false : true);
                      }}
                      options={itemsTypeList}
                      disabled={
                        (formType === "item" && itemTypeDisable()) || isViewOnly
                      }
                      errorMessage={touched?.item_type ? errors?.item_type : ""}
                    />
                  </div>
                )}
                <div className="w-full">
                  <InputField
                    label={_t("Item Name")}
                    labelPlacement="top"
                    isRequired={true}
                    name="subject"
                    value={HTMLEntities.decode(sanitizeString(values?.subject))}
                    onChange={handleChange}
                    errorMessage={touched?.subject ? errors?.subject : ""}
                    disabled={isViewOnly}
                    onPressEnter={handleEnterKeyPress}
                  />
                </div>
                {formType === "item" ? (
                  <>
                    <div>
                      <label className="ant-input-label dark:text-white/90">
                        {_t("Pricing")}
                      </label>
                      <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white mt-1.5">
                        <li className="flex justify-between items-center">
                          <Typography className="text-13 block text-primary-900 dark:text-white/90">
                            {_t("QTY")}
                          </Typography>
                          <div className="sm:w-40 w-28">
                            <InputNumberField
                              name="quantity"
                              id="quantity"
                              rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input"
                              placeholder={_t("Item Quantity")}
                              errorMessage={errors.quantity}
                              labelPlacement="left"
                              formInputClassName={
                                isViewOnly
                                  ? "flex items-center justify-end"
                                  : ""
                              }
                              disabled={isViewOnly}
                              onPaste={handlePaste}
                              defaultValue={
                                Number(values.quantity) !== 0
                                  ? values.quantity
                                  : ""
                              }
                              value={
                                values?.quantity?.toString() &&
                                values.quantity.toString() != "0"
                                  ? values.quantity.toString()
                                  : ""
                              }
                              formatter={(value) => {
                                return inputFormatter(value?.toString()).value;
                              }}
                              onChange={(value) => {
                                setFieldValue(
                                  "quantity",
                                  value?.toString() || ""
                                );
                              }}
                              parser={(value) => {
                                const inputValue = value
                                  ? unformatted(value.toString())
                                  : "";
                                return inputValue;
                              }}
                              onKeyDown={(event) => {
                                onKeyDownCurrency(event, {
                                  integerDigits: 8,
                                  decimalDigits: 2,
                                  unformatted,
                                  allowNegative: true,
                                  decimalSeparator:
                                    inputFormatter().decimal_separator,
                                });
                                if (
                                  event.key === "Tab" &&
                                  !event.shiftKey &&
                                  !event.altKey &&
                                  !event.ctrlKey &&
                                  !event.metaKey
                                ) {
                                  event.preventDefault();
                                  setShowUnitInputs(true);

                                  setTimeout(() => {
                                    const unitCostInput =
                                      document.getElementById("unit_cost");
                                    if (unitCostInput) {
                                      unitCostInput.focus();
                                    }
                                  }, 0);
                                }
                              }}
                            />
                          </div>
                        </li>
                        <li>
                          <ul className="py-0.5 relative">
                            <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                              <FontAwesomeIcon
                                className="w-3 h-3 text-primary-900 dark:text-white"
                                icon="fa-regular fa-xmark"
                              />
                            </li>
                          </ul>
                        </li>
                        <li className="flex justify-between items-center">
                          <Typography className="text-13 block text-primary-900 dark:text-white/90">
                            {_t("Unit Cost/Unit")}
                          </Typography>
                          <div
                            className="sm:w-[260px] w-28 h-[22px]"
                            ref={costUnitRef}
                          >
                            <div
                              ref={unitCostContainerRef}
                              className="text-right !text-[#008000] leading-[22px] font-semibold text-sm"
                            >
                              {!isViewOnly && (
                                <>
                                  {showUnitInputs ? (
                                    <div className="flex gap-2">
                                      <div className="w-[calc(100%-52px)]">
                                        <InputNumberField
                                          name="unit_cost"
                                          id="unit_cost"
                                          rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input placeholder:text-13 !w-[112px]"
                                          placeholder={_t("Item Unit Cost")}
                                          disabled={isViewOnly}
                                          labelPlacement="left"
                                          errorMessage={errors.unit_cost}
                                          onPaste={handlePaste}
                                          autoFocus={Boolean(
                                            values?.unit_cost &&
                                              !isEmpty(values?.unit_cost) &&
                                              values.unit &&
                                              !isEmpty(values.unit)
                                          )}
                                          defaultValue={
                                            Number(values.unit_cost) !== 0
                                              ? values.unit_cost
                                              : ""
                                          }
                                          value={
                                            Number(values.unit_cost) != 0
                                              ? values.unit_cost
                                              : ""
                                          }
                                          onChange={(value) => {
                                            setFieldValue(
                                              "unit_cost",
                                              value?.toString() || ""
                                            );
                                          }}
                                          formatter={(value, info) => {
                                            return inputFormatter(
                                              value?.toString()
                                            ).value;
                                          }}
                                          parser={(value) => {
                                            const inputValue = value
                                              ? unformatted(value.toString())
                                              : "";
                                            return inputValue;
                                          }}
                                          onKeyDown={(event) =>
                                            onKeyDownCurrency(event, {
                                              integerDigits: 10,
                                              decimalDigits: 2,
                                              unformatted,
                                              allowNegative: false,
                                              decimalSeparator:
                                                inputFormatter()
                                                  .decimal_separator,
                                            })
                                          }
                                          onBlur={handleFocusOut}
                                        />
                                      </div>
                                      <div className="w-[62px]">
                                        {window.ENV.ENABLE_UNIT_DROPDOWN ? (
                                          <SelectField
                                            className="!p-0 text-success select-field-text-13 text-left select-unit-filed !h-[22px] !font-semibold placeholder:font-normal"
                                            placeholder="Unit"
                                            name="unit"
                                            disabled={isViewOnly}
                                            labelPlacement="left"
                                            maxLength={15}
                                            value={values?.unit || null}
                                            iconView={true}
                                            popupClassName="!w-[260px]"
                                            showSearch
                                            options={
                                              unitData.map((type) => ({
                                                label: type.name.toString(),
                                                value: type.name.toString(),
                                              })) ?? []
                                            }
                                            allowClear
                                            filterOption={(input, option) =>
                                              filterOptionBySubstring(
                                                input,
                                                option?.label as string
                                              )
                                            }
                                            onChange={(value) => {
                                              setFieldValue(
                                                "unit",
                                                value ? value?.toString() : ""
                                              );
                                            }}
                                            addItem={addItemObject}
                                            onInputKeyDown={(e) => {
                                              if (e.key === "Enter") {
                                                const value =
                                                  e?.currentTarget?.value?.trim();
                                                const newUnit =
                                                  onEnterSelectSearchValue(
                                                    e,
                                                    unitData.map((type) => ({
                                                      label:
                                                        type.name.toString(),
                                                      value:
                                                        type.name.toString(),
                                                    })) || []
                                                  );
                                                if (newUnit) {
                                                  setNewTypeName(newUnit);
                                                } else if (value) {
                                                  notification.error({
                                                    description:
                                                      "Records already exist, no new records were added.",
                                                  });
                                                }
                                              }
                                            }}
                                            onClear={() => {
                                              setFieldValue("unit", "");
                                            }}
                                            errorMessage={errors.unit}
                                            onBlur={handleFocusOut}
                                          />
                                        ) : (
                                          <InputField
                                            className={`!p-0 !pl-1.5 text-sucess field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right ${
                                              !showUnitInputs && "!hidden"
                                            }`}
                                            placeholder={_t("Unit")}
                                            labelPlacement="left"
                                            errorMessage={errors.unit}
                                            maxLength={15}
                                            onBlur={handleFocusOut}
                                            value={values?.unit}
                                            disabled={isViewOnly}
                                            type="text"
                                            onChange={(e) => {
                                              setFieldValue(
                                                "unit",
                                                e.target.value
                                              );
                                            }}
                                            onPressEnter={handleEnterKeyPress}
                                          />
                                        )}
                                      </div>
                                    </div>
                                  ) : (
                                    <Typography
                                      className="text-[#008000] cursor-pointer text-13 font-medium"
                                      onClick={handleParagraphClick}
                                      disabled={isViewOnly}
                                    >
                                      {
                                        formatter(
                                          formatAmount(
                                            Number(values?.unit_cost).toFixed(2)
                                          )
                                        ).value_with_symbol
                                      }
                                      /{values?.unit}
                                    </Typography>
                                  )}
                                </>
                              )}

                              {isViewOnly &&
                                (values?.unit_cost &&
                                values?.unit_cost != "0.00" &&
                                values?.unit_cost != "0" &&
                                !isEmpty(values?.unit) &&
                                !!values?.unit ? (
                                  <Typography
                                    className={`text-[#008000] font-medium text-13 ${
                                      isViewOnly ? "cursor-no-drop" : ""
                                    }`}
                                  >
                                    {values?.unit_cost}/{values?.unit}
                                  </Typography>
                                ) : (
                                  <div className="flex gap-2">
                                    <div className="w-[calc(100%-52px)]">
                                      <InputField
                                        ref={unitCostRef}
                                        className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                        placeholder={_t("Item Unit Cost")}
                                        type="number"
                                        name="unit_cost"
                                        id="unit_cost"
                                        maxLength={10}
                                        onPaste={handlePaste}
                                        disabled={isViewOnly}
                                        value={
                                          Number(values.unit_cost) != 0
                                            ? values.unit_cost
                                            : ""
                                        }
                                        onChange={() => {}}
                                      />
                                    </div>
                                    <div className="w-11">
                                      <InputField
                                        className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                        placeholder={_t("Unit")}
                                        maxLength={15}
                                        name="unit"
                                        id="unit"
                                        disabled={isViewOnly}
                                        onPaste={handlePaste}
                                        value={values?.unit}
                                        type="text"
                                        onChange={() => {}}
                                      />
                                    </div>
                                  </div>
                                ))}
                            </div>
                          </div>
                        </li>
                        <li className="flex justify-between border-t border-dashed border-gray-300 pt-2 mt-2">
                          <Typography className="text-13 block text-primary-900 font-semibold">
                            {_t("Total Cost")}
                          </Typography>
                          <div className="sm:w-[260px] w-32 flex justify-end items-center">
                            <Typography
                              className="!text-red-600 text-13 font-semibold"
                              disabled={true}
                            >
                              {values?.total === ""
                                ? `${
                                    formatter(formatAmount("0.00"))
                                      .value_with_symbol
                                  }`
                                : `${
                                    formatter(
                                      formatAmount(
                                        Number(values?.total || 0).toFixed(2)
                                      )
                                    ).value_with_symbol
                                  }`}
                            </Typography>
                          </div>
                        </li>
                      </ul>
                    </div>
                    <div className="w-full">
                      <SelectField
                        showSearch={true}
                        label={_t("Cost Code")}
                        labelPlacement="top"
                        isRequired={
                          gSettings?.quickbook_sync === "1" ||
                          gSettings?.quickbook_desktop_sync === "1"
                        }
                        allowClear={true}
                        errorMessage={
                          touched?.cost_code_id ? errors?.cost_code_id : ""
                        }
                        options={costCodeOptions}
                        value={costCodeValue}
                        onChange={(value) => {
                          const selectedOption = costCodeOptions.find(
                            (item) => String(item.value) === String(value)
                          );
                          setFieldValue("cost_code_id", value || "");
                          setFieldTouched("cost_code_id", value ? false : true);
                          setFieldValue(
                            "cost_code_name",
                            selectedOption ? selectedOption.label : ""
                          );
                        }}
                        filterOption={(input, option) =>
                          filterOptionBySubstring(
                            input,
                            option?.label as string
                          )
                        }
                        disabled={isViewOnly}
                      />
                    </div>
                  </>
                ) : (
                  // </div>
                  <div
                    className={`grid md:gap-5 gap-5 ${
                      !Boolean(Number(appSettings?.quickbook_sync)) ||
                      (!Boolean(Number(appSettings?.quickbook_desktop_sync)) &&
                        "md:grid-cols-2")
                    }`}
                  >
                    {(Boolean(Number(appSettings?.quickbook_sync)) ||
                      Boolean(Number(appSettings?.quickbook_desktop_sync))) && (
                      <div className="w-full">
                        <SelectField
                          isRequired
                          name="account"
                          label="Account"
                          value={values?.account}
                          options={
                            amountList && amountList.length > 0
                              ? amountList.map(
                                  (type: {
                                    name?: string;
                                    item_id?: string;
                                  }) => ({
                                    label: HTMLEntities.decode(
                                      sanitizeString(type?.name || "")
                                    ),
                                    value: type?.item_id?.toString() || "", // Ensure value is always a string
                                  })
                                )
                              : []
                          }
                          onChange={(value) =>
                            formik.setFieldValue("account", value)
                          }
                          errorMessage={errors?.account}
                        />
                      </div>
                    )}
                    <div className="w-full">
                      <InputNumberField
                        label="Total"
                        value={
                          Number(values.total) != 0
                            ? values.total?.toString()
                            : ""
                        }
                        size="middle"
                        name="total"
                        placeholder="0.00"
                        onChange={(value) => {
                          setFieldValue("total", value);
                        }}
                        formatter={(value, info) => {
                          const inputValue = info.input.trim();
                          const valueToFormat =
                            inputValue !== "0" && inputValue.length > 0
                              ? unformatted(inputValue)
                              : String(value);

                          return isFocusAmountVal
                            ? inputFormatter(valueToFormat).value
                            : !!value
                            ? inputFormatter(Number(value)?.toFixed(2)).value
                            : "";
                        }}
                        onFocus={() => {
                          setIsFocusAmountVal(true);
                        }}
                        parser={(value) => {
                          if (!value) return "";
                          const inputValue = unformatted(value.toString());
                          return inputValue;
                        }}
                        onKeyDown={(
                          event: React.KeyboardEvent<HTMLInputElement>
                        ) => {
                          onKeyDownCurrency(event, {
                            integerDigits: 10,
                            decimalDigits: 2,
                            unformatted,
                            decimalSeparator:
                              inputFormatter().decimal_separator,
                            allowNegative: false,
                          });
                        }}
                        prefix={inputFormatter().currency_symbol}
                        onBlur={() => setIsFocusAmountVal(true)}
                      />
                    </div>
                  </div>
                )}
                <div className="w-full">
                  <TextAreaField
                    label={_t("Description")}
                    name="description"
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and can be optionally made visible to the recipient when submitted."
                    )}
                    value={HTMLEntities.decode(
                      sanitizeString(values?.description)
                    )}
                    disabled={isViewOnly}
                    onChange={handleChange}
                  />
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Internal Notes")}
                    name="internal_notes"
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and are not visible to the recipient."
                    )}
                    value={HTMLEntities.decode(
                      sanitizeString(values?.internal_notes)
                    )}
                    disabled={isViewOnly}
                    onChange={handleChange}
                  />
                </div>
                {isTaxApplicable ? (
                  <div className="flex items-center justify-between">
                    <CheckBox
                      className="gap-1.5 w-fit"
                      disabled={isViewOnly}
                      checked={Number(values.apply_global_tax) ? true : false}
                      onChange={(e) => {
                        setFieldValue(
                          "apply_global_tax",
                          e.target.checked ? 1 : 0
                        );
                      }}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                        }
                      }}
                    >
                      {_t("Collect Tax on this Item?")}
                    </CheckBox>
                  </div>
                ) : (
                  ""
                )}
                {!!(
                  !selectedRec?.item_id &&
                  values?.item_type &&
                  formType === "item"
                ) && (
                  <CheckBox
                    className="gap-1.5 text-primary-900 w-fit"
                    checked={!!values.add_item_to_database}
                    disabled={isViewOnly}
                    onChange={(event) => {
                      const valueToSet: number = event.target.checked ? 1 : 0;
                      setFieldValue("add_item_to_database", valueToSet);
                    }}
                  >
                    {`${_t("Save this item into my")} ${itemTypeName} ${_t(
                      "Items list?"
                    )}`}
                  </CheckBox>
                )}
              </SidebarCardBorder>
            </div>
          </div>
          <>
            {!isViewOnly ? (
              <div className="sidebar-footer flex items-center gap-2 justify-center w-full px-4 pt-4">
                <PrimaryButton
                  htmlType="submit"
                  onClick={() => {
                    setFieldValue("submitAction", "saveAndClose");
                  }}
                  isLoading={submittingFrm == "save"}
                  disabled={!!submittingFrm || isViewOnly}
                  buttonText={_t("Save & Close")}
                />
                <PrimaryButton
                  htmlType="submit"
                  onClick={() =>
                    setFieldValue("submitAction", "saveAndAddAnother")
                  }
                  isLoading={submittingFrm == "saveAndAddAnother"}
                  disabled={!!submittingFrm || isViewOnly}
                  buttonText={_t("Save & Add Another Item")}
                />
              </div>
            ) : (
              <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
                <PrimaryButton
                  htmlType="button"
                  className="justify-center primary-btn w-full"
                  onClick={() => {
                    onClose();
                  }}
                  buttonText={_t("Close")}
                />
              </div>
            )}
          </>
        </form>
      </Drawer>
      {confirmSaveData.rid > 0 && (
        <ConfirmModal
          isOpen={confirmSaveData?.rid > 0}
          modaltitle={_t("This Item Already Exists")}
          description={confirmSaveData?.message || ""}
          modalIcon="fa-regular fa-triangle-exclamation"
          yesButtonLabel={_t("Use Existing")}
          noButtonLabel={_t("Rename")}
          onAccept={() => {
            setIsExistingLoading(true);
            handleSubmit();
          }}
          isLoading={isExistingLoading && isExistingLoading}
          onDecline={() => {
            setConfirmSaveData({ rid: 0 });
          }}
          onCloseModal={() => {
            setConfirmSaveData({ rid: 0 });
          }}
        />
      )}
      {newTypeName.trim() && (
        <ConfirmModal
          isOpen={Boolean(newTypeName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newTypeName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => setNewTypeName("")}
          onAccept={async () => {
            if (!isAddingCustomData && newTypeName) {
              setIsAddingCustomData(true);
              const response: IUnitAddResponse = await addUnit({
                name: newTypeName,
              });
              if (response.success && response.data) {
                setUnitData([{ name: newTypeName }, ...unitData]);
                setFieldValue("unit", newTypeName);
                setNewTypeName("");
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => setNewTypeName("")}
        />
      )}
    </>
  );
};

export default AddManualItem;
