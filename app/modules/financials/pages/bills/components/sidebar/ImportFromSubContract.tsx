import { defaultConfig } from "~/data";
import { useTranslation } from "~/hook";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { SelectField } from "~/shared/components/molecules/selectField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import ToolTipCell from "~/shared/components/molecules/table/ToolTipCell";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { getGModule, getGModuleByKey } from "~/zustand";
import { useEffect, useRef, useState } from "react";
import {
  GridApi,
  GridReadyEvent,
  IRowNode,
  SelectionChangedEvent,
} from "ag-grid-community";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { apiRoutes } from "~/route-services/routes";
import { subContractsRoutes } from "~/route-services/sub-contracts.routes";
import { useParams } from "@remix-run/react";
import { Float } from "~/helpers/helper";
import { useAppBillDispatch, useAppBillSelector } from "../../redux/store";
import {
  addBillItems,
  deleteBillItem,
} from "../../redux/action/billDetailAction";
import { addItems, removeBillItem } from "../../redux/slices/billDetailSlice";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";

const ImportFromSubContract = ({
  importFromSubContract,
  setImportFromSubContract,
  handleBillDetails,
}: IImportSubContractProps) => {
  const { _t } = useTranslation();
  const [selectedItems, setSelectedItems] = useState<
    Array<Partial<ItemSideData>>
  >([]);
  const limit = 20;
  const params = useParams();
  const { billDetail }: IBillDetailsInitialState = useAppBillSelector(
    (state) => state.billDetails
  );
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { module_id } = currentModule || {};

  const [selectedSubContract, setselectedSubContract] = useState<number | null>(
    null
  );
  const [selectedSubContractObj, setSelectedSubContractObj] =
    useState<ISubContractorKanbanRowDataList | null>(null);

  const gridApiRef = useRef<GridApi | null>(null);
  const [subContractItem, setsubContractItem] = useState<ISCCompanyItems[]>([]);
  const [saveButtonLoader, setSaveButtonLoader] = useState<boolean>(false);
  const [itemsLoading, setItemsLoading] = useState<boolean>(false);
  const [subContract, setsubContract] = useState<
    ISubContractorKanbanRowDataList[]
  >([]);
  const dispatch = useAppBillDispatch();
  const { module_name = "Sub-Contract" }: Partial<GModule> =
    getGModule(defaultConfig.sub_contract_module_id) || {};
  const [loading, setLoading] = useState<boolean>(false);
  const handleCreate = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (selectedItems.length === 0) {
      return;
    }
  };
  const page = 0;
  const { formatter } = useCurrencyFormatter();

  useEffect(() => {
    if (subContractItem) {
      setSelectedItems(
        subContractItem?.map((value: Partial<ISCCompanyItems>) => ({
          ...value,
          isChecked: Boolean(
            billDetail?.data?.items?.find(
              (itemData: Partial<IBillDetailsItem>) =>
                itemData?.reference_module_item_id?.toString() ===
                  value?.item_id?.toString() ||
                itemData?.reference_item_id?.toString() ===
                  value?.item_id?.toString()
            )
          ),
        }))
      );
    }
  }, [subContractItem, billDetail.data.items]);
  const handleSelectionChanged = (event: SelectionChangedEvent): void => {
    const selectedNodes: ISCCompanyItems[] = [];
    event.api.forEachNode((node: IRowNode<ISCCompanyItems>, index) => {
      if (node.isSelected() && node.data) {
        selectedNodes.push(node.data);
      }
    });
    const selectedIds = selectedNodes.map((value) => value?.item_id);

    setSelectedItems((prevSelectedData: Partial<ISubcontract>[]) => {
      return prevSelectedData?.map((data) => ({
        ...data,
        isChecked: selectedIds.includes((data as { item_id: string }).item_id),
      }));
    });
  };

  const fetchData = async (page: number, limit: number) => {
    setLoading(true);
    const response = (await webWorkerApi<ISubContratsListApiRes>({
      url: apiRoutes.GET_SUB_CONTRACTS_LIST.url,
      method: "post",
      data: {
        filter: {
          project: [billDetail?.data?.project_id],
          status: "0",
          tab: "open",
        },
        limitedFields: 1,
        ignore_filter: 1,
      },
    })) as ISubContratsListApiRes;

    if (response.success) {
      const newSubContracts = response?.data
        ?.subContracts as unknown as ISubContractorKanbanRowDataList[];
      setsubContract((prev) => [
        ...prev,
        ...newSubContracts.map((subContract) => ({
          ...subContract,
        })),
      ]);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (importFromSubContract) fetchData(page, limit);
  }, [importFromSubContract]);

  const onScroll = async (event: React.UIEvent<HTMLElement>) => {
    const target = event.target as HTMLElement;

    if (
      !loading &&
      target.scrollTop + target.offsetHeight === target.scrollHeight
    ) {
      await fetchData(page, limit);
    }
  };

  const fetchItems = async (sub_contract_id: number) => {
    setItemsLoading(true);
    const response = (await webWorkerApi<ISubContractItem>({
      url: subContractsRoutes.subcontractor_items(sub_contract_id?.toString()),
      method: "post",
      data: {
        global_project: "",
        subContractId: sub_contract_id,
      },
    })) as ISCCompanyItemsRes;

    if (response.success) {
      setsubContractItem(response.data);
    } else {
      setsubContractItem([]);

      notification.error({
        description: response.message,
      });
    }
    setItemsLoading(false);
  };

  const addSubContractItem = async () => {
    try {
      if (!selectedItems || selectedItems?.length === 0) {
        notification.error({
          description: "Please select at least one item.",
        });
      } else {
        setSaveButtonLoader(true);
        const billId = params && params.id ? Number(params.id) : "";
        const items: Partial<IBillDetailsItem>[] = [];
        await Promise.all(
          selectedItems?.map(async (item: Partial<ItemSideData>) => {
            if (item.isChecked === true) {
              const tempObj: Partial<IBillDetailsItem> = {
                item_id: Number(item.item_id) || 0,
                item_type_key: item.item_type_key,
                unit_cost: (Float(item?.unit_cost) * 1).toString(),
                cost_code_id:
                  item && item.cost_code_id
                    ? Number(item.cost_code_id)
                    : undefined,
                description: item?.description,
                item_type: Number(item?.item_type),
                bill_id: billId || 0,
                subject: item?.subject,
                quantity: Number(item?.quantity),
                isChecked: true,
                unit: item?.unit,
                account_id: item?.account_id ? Number(item?.account_id) : 0,
                apply_global_tax: item?.apply_global_tax || "",
                item_category: "item",
                total: (Float(item?.total) * 1).toString(),
                ...(item?.internal_notes
                  ? { internal_notes: item?.internal_notes }
                  : {}),
                reference_module_name: "sub-contract",
                reference_module_id: 67,
                reference_module_item_id: Number(item?.item_id) ?? "",
                reference_item_id: Number(item?.item_id) ?? "",
                reference_primary_id: Number(item?.sub_contract_id) ?? "",
                ...(selectedSubContractObj?.work_retain
                  ? {
                      sc_retainage: selectedSubContractObj?.work_retain,
                    }
                  : {}),
              }; // change this in future ( temporary resolve type issue )
              items?.push(tempObj);
            }
          })
        );
        let deleteItems = selectedItems?.filter(
          (expense: Partial<ItemSideData>) =>
            expense.hasOwnProperty("isChecked") && expense.isChecked === false
        );
        const currentData = billDetail?.data?.items?.map(
          (value: Partial<IBillDetailsItem>) =>
            value.reference_module_item_id || value.reference_item_id
        );
        const uniqueNewData = items.reduce(
          (
            acc: Array<Partial<IBillDetailsItem>>,
            item: Partial<IBillDetailsItem>
          ) => {
            const existingItem = acc?.find(
              (i: Partial<IBillDetailsItem>) =>
                i.reference_module_item_id === item.reference_module_item_id ||
                i.reference_item_id === item.reference_item_id
            );
            if (!existingItem) {
              acc.push(item);
            }
            return acc;
          },
          []
        );

        const filteredData = uniqueNewData?.filter(
          (item: Partial<IBillDetailsItem>) =>
            !currentData?.includes(item.reference_module_item_id) ||
            !currentData?.includes(item.reference_item_id)
        );
        const response = await addBillItems({
          id: Number(billDetail?.data?.bill_id),
          items: filteredData as IBillDetailsItem[],
          is_single_item: 0,
          module_id: module_id || 0,
        });
        if (response.success) {
          setSaveButtonLoader(false);
          setImportFromSubContract(false);
          dispatch(addItems(filteredData));
          if (handleBillDetails) {
            handleBillDetails(response);
          }
        }
        if (deleteItems.length) {
          for (let index = 0; index < deleteItems.length; index++) {
            const selectItems = deleteItems[index];
            const selectItem = billDetail?.data?.items?.find(
              (itemData: Partial<IBillDetailsItem>) => {
                const itemId = selectItems?.item_id?.toString() ?? "0";
                const referenceModuleItemId =
                  itemData?.reference_module_item_id?.toString() ?? "0";
                const referenceItemId =
                  itemData?.reference_item_id?.toString() ?? "0";

                return [referenceModuleItemId, referenceItemId].includes(
                  itemId
                );
              }
            );
            if (!!selectItem?.item_id && !!selectItem?.bill_id) {
              const response = await deleteBillItem({
                id: selectItem?.item_id?.toString(),
                bill_id: Number(billDetail?.data?.bill_id),
              });
              if (response) {
                setSaveButtonLoader(false);
                setImportFromSubContract(false);
                dispatch(removeBillItem(selectItem));
              }
            }
          }
        }

        // closeModalHandler();
      }
    } catch (error) {
      notification.error({
        description: "Something went wrong!",
      });
    }
  };
  useEffect(() => {
    if (
      billDetail.data &&
      billDetail?.data?.items &&
      billDetail?.data?.items?.length > 0 &&
      subContractItem?.length > 0
    ) {
      const ids = billDetail.data?.items?.map((item: IBillDetailsItem) => {
        return item.reference_module_item_id || item.reference_item_id;
      });
      const selectedNodes: IRowNode[] = [];
      subContractItem.forEach((row: ISCCompanyItems) => {
        if (ids.includes(Number(row.item_id))) {
          const index = subContractItem?.indexOf(row);
          const node =
            index !== -1
              ? gridApiRef?.current?.getRowNode(index.toString())
              : null;
          if (node) {
            node.setSelected(true);

            selectedNodes.push(node);
          }
        }
      });
      gridApiRef?.current?.ensureNodeVisible(selectedNodes[0]);
    }
  }, [subContractItem, billDetail?.data.items]);

  useEffect(() => {
    if (selectedSubContract) {
      fetchItems(selectedSubContract);
    }
  }, [selectedSubContract]);

  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params.api;
  };

  useEffect(() => {
    if (selectedSubContract && subContract.length > 0) {
      const matched = subContract.find(
        (item) => item.sub_contract_id === selectedSubContract
      );
      setSelectedSubContractObj(matched || null);
    }
  }, [selectedSubContract, subContract]);

  return (
    <>
      <Drawer
        open={importFromSubContract}
        rootClassName="drawer-open"
        width={750}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-file-contract"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(
                `Import ${
                  getGModuleByKey(defaultConfig.sub_contracts_module)
                    ?.module_name
                } Items`
              )}
            </Header>
          </div>
        }
        closeIcon={
          <CloseButton onClick={() => setImportFromSubContract(false)} />
        }
      >
        <form className="py-4" onSubmit={handleCreate}>
          <div className="sidebar-body h-[calc(100dvh-132px)]">
            <div className="grid gap-4">
              <div className="px-4 h-[calc(100dvh-172px)] overflow-y-auto">
                <SidebarCardBorder addGap={true}>
                  <div className="grid gap-3.5">
                    <div className="w-full">
                      <SelectField
                        isRequired
                        label={module_name}
                        labelPlacement="top"
                        onPopupScroll={onScroll}
                        allowClear
                        options={
                          subContract
                            ? subContract?.map(
                                (item: {
                                  company_sub_contract_id: string;
                                  subject: string;
                                  sub_contract_id: number;
                                }) => ({
                                  label: `SC #${item?.company_sub_contract_id}: ${item?.subject}`,
                                  value: item?.sub_contract_id,
                                })
                              )
                            : []
                        }
                        onChange={(value) => {
                          setselectedSubContract(
                            subContract.find(
                              (item) => item.sub_contract_id == Number(value)
                            )?.sub_contract_id || null
                          );
                        }}
                      />
                    </div>

                    <div className="p-2 common-card">
                      <div className={`ag-theme-alpine`}>
                        {itemsLoading && (
                          <Spin className="w-full h-[208px] flex items-center justify-center" />
                        )}
                        <StaticTable
                          className={
                            itemsLoading
                              ? "static-table checkbox-ml-none hidden"
                              : "static-table"
                          }
                          onSelectionChanged={handleSelectionChanged}
                          rowMultiSelectWithClick={true}
                          suppressRowClickSelection={true}
                          rowData={
                            selectedSubContract && selectedSubContract
                              ? subContractItem
                              : []
                          }
                          rowSelection="multiple"
                          onGridReady={onGridReady}
                          columnDefs={[
                            {
                              headerName: "",
                              minWidth: 36,
                              maxWidth: 36,
                              field: "",
                              suppressMenu: true,
                              checkboxSelection: true,
                              headerCheckboxSelection: true,
                              headerClass: "ag-header-center",
                              cellClass: "ag-cell-center",
                            },

                            {
                              headerName: _t("Item Name"),
                              resizable: false,
                              field: "subject",
                              minWidth: 100,
                              flex: 2,
                              suppressMenu: true,
                              headerClass: "ag-header-left",
                              cellClass: "ag-cell-left",
                              cellRenderer: ToolTipCell,
                            },

                            {
                              headerName: _t("QTY"),
                              resizable: false,
                              field: "quantity",
                              maxWidth: 70,
                              minWidth: 70,
                              suppressMenu: true,
                              cellClass: "ag-cell-right",
                              headerClass: "ag-header-right",
                              valueGetter: ({ data }) =>
                                Number(data.quantity).toString() || "0.00",
                              cellRenderer: ToolTipCell,
                            },

                            {
                              headerName: _t("Cost/Unit"),
                              resizable: false,
                              field: "unit_cost",
                              minWidth: 120,
                              maxWidth: 120,
                              suppressMenu: true,
                              cellClass: "ag-cell-right",
                              headerClass: "ag-header-right",
                              valueGetter: ({ data }) =>
                                `${
                                  formatter(
                                    (Number(data.unit_cost) / 100)
                                      .toFixed(2)
                                      .toString() || "0.00"
                                  ).value_with_symbol
                                }${data?.unit ? `/${data.unit}` : ""}`,
                              cellRenderer: ToolTipCell,
                            },

                            {
                              headerName: _t("Total"),
                              resizable: false,
                              field: "total",
                              minWidth: 120,
                              maxWidth: 120,
                              suppressMenu: true,
                              cellClass: "ag-cell-right",
                              headerClass: "ag-header-right",
                              valueGetter: ({ data }) =>
                                formatter(
                                  (Number(data.total) / 100)
                                    .toFixed(2)
                                    .toString() || "0.00"
                                ).value_with_symbol,
                              cellRenderer: ToolTipCell,
                            },
                          ]}
                          noRowsOverlayComponent={() => (
                            <NoRecords
                              image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                            />
                          )}
                        />
                      </div>
                    </div>
                  </div>
                </SidebarCardBorder>
              </div>
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <PrimaryButton
              htmlType="button"
              buttonText={_t("Save & Close")}
              onClick={addSubContractItem}
              loading={saveButtonLoader}
              disabled={
                !selectedSubContract ||
                selectedItems?.every((element) => {
                  return !(element as { isChecked: boolean }).isChecked;
                })
              }
            />
          </div>
        </form>
      </Drawer>
    </>
  );
};

export default ImportFromSubContract;
