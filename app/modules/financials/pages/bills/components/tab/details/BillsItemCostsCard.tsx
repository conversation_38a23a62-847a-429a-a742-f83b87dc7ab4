import { useTranslation } from "~/hook";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import ToolTipCell from "~/shared/components/molecules/table/ToolTipCell";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ModuleItemsFilter } from "~/shared/components/molecules/moduleItemsFilter";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { useEffect, useMemo, useRef, useState } from "react";
import { useAppBillDispatch, useAppBillSelector } from "../../../redux/store";
import {
  ColDef,
  GridOptions,
  IRowNode,
  ValueFormatterParams,
  ValueGetterParams,
  ValueParserParams,
  ValueSetterParams,
} from "ag-grid-community";
import {
  addItems,
  reloadItems,
  setItemFilterData,
  updatebillDetail,
  updateBillDetailItems,
  updateBillDragDropItems,
} from "../../../redux/slices/billDetailSlice";
import { useParams } from "@remix-run/react";
import debounce from "lodash/debounce";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import {
  addBillItems,
  getBillDetailAPI,
  updateBillItems,
} from "../../../redux/action/billDetailAction";
import { floatWithNegativeRegex } from "~/modules/financials/pages/changeOrder/utils/helpers";
import BillDeleteItemAction from "./item/BillDeleteItemAction";
import { resetBillListCache } from "../../../redux/slices/billListSlice";
import { getCostCode } from "~/redux/action/getCostCodeAction";
import BillItemFromCostCodeDatabase from "../../sidebar/BillItemFromCostCodeDatabase ";
import ImportItemsPurchase from "../../sidebar/ImportItemsPurchase";
import ImportFromSubContract from "../../sidebar/ImportFromSubContract";
import BillDiscontItem from "../../sidebar/BillDiscontItem";
import ImportCreditItem from "../../sidebar/ImportCreditItem";
import { getGConfig, getGModuleByKey, getGSettings } from "~/zustand";
import ImportExpense from "../../sidebar/ImportExpense";
import BillFrieghtChargeItem from "../../sidebar/BillFrieghtChargeItem";
import BillRetainagePayment from "../../sidebar/BillRetainagePayment";
import AddLumSum from "../../sidebar/AddLumSum";
import AddManualItem from "../../sidebar/AddManualItem";
import { defaultConfig } from "~/data";
import { addUnit, getUnitList } from "~/redux/action/unitActions";
import UnitCellEditor from "~/modules/settings/costItemsDatabase/components/unitCellEditor/UnitCellEditor";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { CheckboxChangeEvent } from "antd/es/checkbox";

const BillsItemCostsCard = ({
  handleUpdateField,
  setTotalAmountForHeader,
  totalAmountWithTaxForHeader,
}: BillItemCostCardProps) => {
  const { _t } = useTranslation();
  const [filter, setFilter] = useState<string[]>([]);
  const { billDetail, itemFilter }: IBillDetailsInitialState =
    useAppBillSelector((state) => state.billDetails);

  const { codeCostData }: IGetCostCodeList = useAppBillSelector(
    (state) => state.costCode
  );
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const {
    module_access = "no_access",
    module_id,
    singular_name,
  } = currentModule || {};
  const is_module_read_only = module_access === "read_only";
  const { formatter } = useCurrencyFormatter();
  const gSettings: GSettings = getGSettings();
  const [open, setOpen] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const params = useParams();
  const dispatch = useAppBillDispatch();
  const [filteredItems, setFilteredItems] = useState<IBillDetailsItem[]>(
    billDetail?.data?.items || []
  );
  const [units, setUnits] = useState<
    IUnitListResponseDataAndStatusCode["units"]
  >([]);
  const [selectedBillItem, setSelectedBillItem] =
    useState<IBillDetailsItem | null>(null);
  const [selectedItemId, setSelectedItemId] = useState<number>(0);
  const [showBILLItemDatbase, setShowBILLItemDatbase] =
    useState<boolean>(false);
  const [showNewBILLItem, setShowNewBILLItem] = useState<boolean>(false);
  const [importItemsPurchase, setImportItemsPurchase] =
    useState<boolean>(false);
  const [importFromSubContract, setImportFromSubContract] =
    useState<boolean>(false);
  const [expenseSingularItem, setExpenseSingularItem] =
    useState<boolean>(false);
  const [lumpSumTotalItem, setLumpSumTotalItem] = useState<boolean>(false);
  const [manualBillSingularItem, setManualBillSingularItem] =
    useState<boolean>(false);
  const [addDiscountItem, setAddDiscountItem] = useState<boolean>(false);
  const [freightchargeItem, setfreightchargeItem] = useState<boolean>(false);
  const [creditItem, setCreditItem] = useState<boolean>(false);
  const [addRetainagePayment, setaddRetainagePayment] =
    useState<boolean>(false);
  const gridRef = useRef<ExtendedAgGridReact<IBillDetailsItem> | null>(null);
  const [selectedData, setSelectedData] = useState<IBillDetailsItem>();
  const [newUnitName, setNewUnitName] = useState<string>("");
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const totalAmountForHeader = useMemo(() => {
    return filteredItems?.reduce((acc: number, item: IBillDetailsItem) => {
      const singleItemTotal = Number(item.total) ?? 0;
      return acc + singleItemTotal / 100;
    }, 0);
  }, [filteredItems]);
  const isProjectExist =
    billDetail?.data &&
    billDetail?.data?.project_id &&
    billDetail?.data?.project_id.toString() !== "0" &&
    billDetail?.data?.project_id !== "";
  useEffect(() => {
    setTotalAmountForHeader(totalAmountForHeader);
  }, [totalAmountForHeader]);

  const BILL_ITEMS_LIST_OPTIONS = [
    {
      label: `Add Item to ${singular_name}`,
      value: "title",
      disabled: true,
    },
    {
      label: "Import from Cost Items Database",
      value: "database",
    },
    {
      label: `Import from ${
        getGModuleByKey(CFConfig.purchase_order_module)?.module_name
      }`,
      value: "purchase_order",
      module_id: CFConfig.purchase_order_module_id,
    },
    {
      label: `Import from ${
        getGModuleByKey(CFConfig.sub_contracts_module)?.module_name
      }`,
      value: "sub_contract_singular",
      module_id: CFConfig.sub_contract_module_id,
    },
    {
      label: `Import from ${
        getGModuleByKey(CFConfig.expense_module)?.module_name
      }`,
      value: "expense_singular",
      module_id: CFConfig.expense_module_id,
    },
    {
      label: "Lump Sum Total",
      value: "lump_sum_total",
    },
    {
      label: `Add Manual ${
        getGModuleByKey(CFConfig.bill_module)?.module_name
      } Item`,
      value: "add_manual_bill_singular",
    },
    {
      label: "Add Discount",
      value: "add_discount",
    },
    {
      label: "Freight Charge",
      value: "freight_charge",
    },
    {
      label: "Credit",
      value: "credit",
    },
    {
      label: "Add Retainage (Partial or Full) for Payment",
      value: "add_retainage_payment",
    },
  ];

  const ADD_DROPDOWN_MAPPING = {
    database: () => setShowBILLItemDatbase(true),
    add_item_to_Bill: () => setShowNewBILLItem(true),
    purchase_order: () =>
      isProjectExist
        ? setImportItemsPurchase(true)
        : notification.error({
            description: "Please select project.",
          }),
    sub_contract_singular: () =>
      isProjectExist
        ? setImportFromSubContract(true)
        : notification.error({
            description: "Please select project.",
          }),
    expense_singular: () =>
      isProjectExist
        ? setExpenseSingularItem(true)
        : notification.error({
            description: "Please select project.",
          }),
    lump_sum_total: () => setLumpSumTotalItem(true),
    add_manual_bill_singular: () => setManualBillSingularItem(true),
    add_discount: () => setAddDiscountItem(true),
    freight_charge: () => setfreightchargeItem(true),
    credit: () => setCreditItem(true),
    add_retainage_payment: () => setaddRetainagePayment(true),
  };

  const dropdownListOptions = useMemo(() => {
    return BILL_ITEMS_LIST_OPTIONS.map((item) => {
      const onClickHandler =
        ADD_DROPDOWN_MAPPING[item.value as keyof typeof ADD_DROPDOWN_MAPPING];

      return {
        ...item,
        onClick: onClickHandler,
      };
    });
  }, [billDetail?.data]);

  useEffect(() => {
    const items = billDetail?.data?.items || [];
    const filtered = items.filter(
      (item) => item.is_retainage_item?.toString() !== "1"
    );
    dispatch(setItemFilterData(itemFilter?.length ? itemFilter : filtered));
    setFilteredItems(itemFilter?.length ? itemFilter : filtered);
  }, [billDetail?.data?.items]);

  const selectFilter = (filterKey: string) => {
    let updatedList = [...filter];

    if (!filter?.includes(filterKey)) {
      updatedList = [...filter, filterKey];
    } else {
      updatedList?.splice(filter?.indexOf(filterKey), 1);
    }
    setFilter(updatedList);
  };

  useEffect(() => {
    if (window.ENV.ENABLE_UNIT_DROPDOWN) {
      (async function () {
        try {
          const response = await getUnitList();
          if (!response.success) {
            return;
          }
          const units = response.data?.units || [];
          setUnits(units);
          const api = gridRef.current?.api;
          if (!api) return;

          const existingColDefs = api.getColumnDefs();
          if (!existingColDefs) return;

          const updatedColDefs = existingColDefs.map((col) =>
            "field" in col && col.field === "unit"
              ? {
                  ...col,
                  filterParams: {
                    values:
                      units.map((unit) => ({
                        label: unit.name?.toString(),
                        value: unit.name?.toString(),
                      })) ?? [],
                  },
                }
              : col
          );

          api.setColumnDefs(updatedColDefs);

          // Ensure the grid header re-renders
          api.refreshHeader();
        } catch (error) {}
      })();
    }
  }, []);

  const costCodeOptions = useMemo(() => {
    const defaultOptions: Partial<ICostCode>[] = [
      {
        code_id: "0",
        csi_code: "",
        cost_code_name: "Select Cost Code",
      },
    ];

    return [...defaultOptions, ...codeCostData]?.map(
      (item: Partial<ICostCode>) =>
        `${item?.cost_code_name}` +
        `${item?.csi_code ? ` (${item?.csi_code})` : ""}`
    );
  }, [codeCostData]);
  useEffect(() => {
    dispatch(
      getCostCode({
        project_id: billDetail?.data?.project_id
          ? billDetail?.data?.project_id
          : undefined,
      })
    );
  }, [billDetail?.data?.project_id]);
  const applyFilter = () => {
    const items = billDetail?.data?.items || [];
    const filtered = items.filter(
      (item) => item.is_retainage_item?.toString() !== "1"
    );
    if (filter.length === 0) {
      setFilteredItems(filtered || []);
      dispatch(setItemFilterData(filtered));
    } else {
      const filteredData = filtered?.filter(
        (item) =>
          item?.item_type_key && filter.includes(item.item_type_key as string)
      );
      setFilteredItems(filteredData as IBillDetailsItem[]);
      dispatch(setItemFilterData(filteredData));
    }
    setIsLoading(false);
  };

  const uniCostDropdownHight =
    filteredItems?.length === 1
      ? 60
      : filteredItems?.length === 2
      ? 90
      : filteredItems?.length === 3
      ? 120
      : filteredItems?.length === 4
      ? 150
      : 180;

  const handleFilterChange = debounce(() => {
    applyFilter();
  }, 300);

  useEffect(() => {
    handleFilterChange();
    return handleFilterChange.cancel;
  }, [billDetail?.data?.items]);

  useEffect(() => {
    setIsLoading(true);
    setFilteredItems([]);
    handleFilterChange();
    return handleFilterChange.cancel;
  }, [filter]);

  const handleDragAndDrop = async (
    items: IBillDetailsInitialState["items"]
  ) => {
    const uniqueItems = items.filter(
      (value, index, self) =>
        index === self.findIndex((t) => t.item_id === value.item_id)
    );
    const oldItems = items;
    const response = await updateBillItems({
      id: Number(billDetail?.data?.bill_id),
      items: uniqueItems.map((item, index) => ({
        ...item,
        bill_item_no: Number((index + 1).toString()),
        total: itemTotalCalculator(item).toString(),
      })),
      module_id: module_id ?? 0,
      is_single_item: 0,
    });
    if (response?.success) {
      dispatch(updateBillDragDropItems([...uniqueItems]));
      setFilteredItems(
        uniqueItems.filter((data) => data.is_retainage_item?.toString() !== "1")
      );
    } else {
      dispatch(updateBillDragDropItems([...oldItems]));
      setFilteredItems(
        oldItems.filter((data) => data.is_retainage_item?.toString() !== "1")
      );
    }
  };

  const NoDataOverlay = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
    />
  );

  const LoadingOverlay = () => (
    <div className="my-10">
      <Spin className="w-full h-full flex items-center justify-center" />
    </div>
  );

  const gridOptions: GridOptions = {
    stopEditingWhenCellsLoseFocus: true,
    onRowDragEnd: function (event) {
      const { node, overIndex } = event as {
        node: IRowNode;
        overIndex: number;
      };
      if (!gridOptions.api || !node) return;

      const rowData: IBillDetailsInitialState["items"] = [];
      gridOptions.api.forEachNode((node) => rowData.push(node.data));
      const newItems = rowData?.map((r, idx) => ({
        ...r,
        order_item_no: idx + 1,
      }));
      handleDragAndDrop(newItems);
    },
  };

  const iconByItemTypeName: { [key: string]: IFontAwesomeIconProps["icon"] } =
    useMemo(() => {
      return {
        item_material: "fa-regular fa-block-brick",
        item_labour: "fa-regular fa-user-helmet-safety",
        item_sub_contractor: "fa-regular fa-file-signature",
        item_equipment: "fa-regular fa-screwdriver-wrench",
        item_other: "fa-regular fa-boxes-stacked",
      };
    }, []);

  const shouldAddItemsDropDownHide = useMemo(() => {
    const ddHideStatuses: string[] = ["182", "180", "324"];
  }, [billDetail]);

  const readOnlyMode = useMemo(() => {
    return is_module_read_only || shouldAddItemsDropDownHide;
  }, [is_module_read_only, shouldAddItemsDropDownHide]);

  const itemCleaner = (item: IBillDetailsItem) => {
    return {
      ...item,
      unit_cost:
        item.subject === "Discount" &&
        item.unit_cost &&
        Number(item.unit_cost) > 0
          ? -(Number(item.unit_cost) || 0)
          : Number(item.unit_cost) || 0,
      total:
        item.subject === "Discount" &&
        item.unit_cost &&
        Number(item.unit_cost) > 0
          ? -(Number(item.unit_cost) || 0)
          : Number(item.total) || 0,
    };
  };

  const handleCreate: IBillItemCreateHandler = async (
    items,
    { skipClose, addFromResponse, is_single_item }
  ) => {
    if (!billDetail?.data?.bill_id) return Promise.resolve();
    return addBillItems({
      id: Number(billDetail?.data?.bill_id),
      items,
      is_single_item: is_single_item,
    }).then(({ success, data, message }) => {
      if (success) {
        let cleanedItems = [];

        if (addFromResponse && data) {
          cleanedItems = data.map(itemCleaner);
        } else {
          cleanedItems = items.map(itemCleaner).map((item) => ({
            ...item,
            bill_id: billDetail?.data?.bill_id,
          }));
        }
        if (is_single_item) {
          dispatch(reloadItems(cleanedItems));
        } else {
          dispatch(addItems(cleanedItems));
        }

        if (!skipClose) {
          handleBillDetails({ data });
          setManualBillSingularItem(false);
          setShowNewBILLItem(false);
          setSelectedBillItem(null);
          dispatch(resetBillListCache());
          // dispatch(setShouldWidgetsRefresh(true));
        }
      }
    });
  };

  const itemTotalCalculator = (item: IBillDetailsItem) => {
    const { unit_cost, markup, quantity, is_markup_percentage } = item;
    if (is_markup_percentage !== 1) {
      if (!!Number(quantity) && !!unit_cost) {
        if (Number(markup) === 0) {
          return Math.round(Number(unit_cost) * Number(quantity));
        }
        return Math.round(Number(markup));
      } else {
        if (Number(markup) === 0) {
          return 0;
        }
        return 0;
      }
    }
    const markupAmount =
      Number(unit_cost) * (Number(markup) / 100) * Number(quantity);
    let totalFloat = 0;
    if (quantity !== 0) {
      totalFloat = Number(unit_cost) * Number(quantity) + Number(markupAmount);
    } else {
      totalFloat = Number(unit_cost) * Number(quantity);
    }
    return Math.round(totalFloat);
  };

  const handleUpdate: IBillItemCreateHandler = (
    newItems,
    { skipClose } = {}
  ) => {
    if (!billDetail?.data?.bill_id) return Promise.resolve();
    const oldItems = billDetail?.data?.items
      ? [...billDetail?.data?.items]
      : [];
    return updateBillItems({
      id: Number(billDetail?.data?.bill_id),
      items: newItems.map((item) => ({
        ...item,
        total: itemTotalCalculator(item).toString(),
      })),
      module_id: module_id ?? 0,
      is_single_item: 1,
    }).then(
      async ({ success, message, updatedItemdata, updateBillTotalValue }) => {
        if (success) {
          if (!skipClose) {
            setManualBillSingularItem(false);
            setSelectedBillItem(null);
            dispatch(resetBillListCache());

            const updatedBillDetails = await getBillDetailAPI({
              bill_id: params?.id || "",
            });

            dispatch(updatebillDetail(updatedBillDetails?.data));
          }
        } else {
          oldItems.map((data) => {
            dispatch(updateBillDetailItems(data));
          });
          notification.error({
            message: message,
            description: _t("Please try again later"),
          });
        }
      }
    );
  };

  const handleManualClose = (isSubmitForm = false) => {
    setManualBillSingularItem(false);
    setSelectedItemId(0);
    if (isSubmitForm) {
      handleBillDetails(billDetail);
    }
  };
  const handleBillDetails = async (response) => {
    if (response?.data) {
      const updatedBillDetails = await getBillDetailAPI({
        bill_id: response?.data?.bill_id || Number(billDetail?.data?.bill_id),
      });

      dispatch(updatebillDetail(updatedBillDetails?.data));
    }
  };
  const costCodeWithOption = (params: {
    data: {
      cost_code?: string;
      cost_code_id: string;
      cost_code_name?: string;
    };
  }) => {
    const costCodesData = codeCostData.find(
      (ele) => ele.code_id?.toString() === params.data?.cost_code_id?.toString()
    );

    // Use cost_code if cost_code_name is null or empty
    const costCodeValue =
      params.data.cost_code_name || params.data.cost_code || "";

    // Add CSI code or Archived information if available
    const additionalInfo = costCodesData
      ? costCodesData.csi_code
        ? ` (${costCodesData.csi_code})`
        : costCodesData.archive
        ? " (Archived)"
        : ""
      : " (Archived)";

    return costCodeValue && costCodesData
      ? (!costCodeValue.includes(costCodesData.csi_code) &&
          params.data.cost_code_name &&
          additionalInfo) ||
        (!!costCodeValue && additionalInfo?.includes("Archived"))
        ? `${costCodeValue}${additionalInfo}`
        : !params.data.cost_code_name && additionalInfo
        ? `${additionalInfo}`
        : costCodeValue
      : !!costCodeValue && additionalInfo?.includes("Archived")
      ? `${costCodeValue}${additionalInfo}`
      : "";
  };

  const suppressKeyboardEvent: ColDef<
    CostItemTableCellRenderer["data"]
  >["suppressKeyboardEvent"] = (params) => {
    const { event, api } = params;

    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
      event.preventDefault();
      event.stopPropagation();
      return true;
    }

    if (event.key === "Enter") {
      event.preventDefault();
      event.stopPropagation();

      // Call valueSetter or custom logic for saving the value
      api.stopEditing(); // Stop editing to trigger valueSetter
      return true;
    }

    return true;
  };

  const columnDefs: ColDef[] = useMemo(
    () => [
      {
        headerName: "",
        field: "",
        minWidth: is_module_read_only ? 0 : 30,
        maxWidth: is_module_read_only ? 0 : 30,
        rowDrag: !is_module_read_only,
        suppressMenu: true,
        cellClass: "ag-cell-center ag-move-cell custom-move-icon-set",
        cellRenderer: () => {
          return (
            <div className="w-6 h-6 flex items-center justify-center absolute top-1 left-1 !z-0 opacity-100">
              <FontAwesomeIcon
                className="w-4 h-4 text-[#4b5a76]"
                icon="fa-solid fa-grip-dots"
              />
            </div>
          );
        },
      },
      {
        headerName: _t("Type"),
        minWidth: 50,
        maxWidth: 50,
        field: "type",
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-center",
        cellClass: "ag-cell-center",
        cellRenderer: (params: IBillItemTableCellRenderer) => {
          const { data } = params;
          return (
            <>
              {Object.keys(iconByItemTypeName).map((key) => {
                if (data.item_type_key === key) {
                  return (
                    <Tooltip title={data.item_type_name}>
                      <FontAwesomeIcon
                        className="w-4 h-4 text-primary-900 mx-auto"
                        icon={iconByItemTypeName[key]}
                      />
                    </Tooltip>
                  );
                } else {
                  <>-</>;
                }
              })}
            </>
          );
        },
      },
      {
        headerName: _t("Item Name"),
        field: "subject",
        minWidth: 150,
        flex: 2,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        cellRenderer: (params: IBillItemTableCellRenderer) => {
          const { data } = params;

          return (
            <Tooltip title={sanitizeString(data.subject)}>
              <Typography className="table-tooltip-text">
                {HTMLEntities.decode(sanitizeString(data.subject))}
              </Typography>
            </Tooltip>
          );
        },
        editable: () => (readOnlyMode ? false : true),
        valueGetter: (params: IBillItemTableCellRenderer) => {
          return params?.data?.subject;
        },
        valueSetter: (params: { data: IBillDetailsItem; newValue: string }) => {
          if (!params.newValue?.trim()) {
            notification.error({
              description: "Item Name is required.",
            });
            return;
          }
          if (params.newValue) {
            const updatedData = {
              ...params.data,
              item_id: params?.data?.item_id,
              subject: HTMLEntities.decode(sanitizeString(params.newValue)),
            };
            params.node.setData(updatedData);
            handleUpdate([updatedData], {});
            return true;
          }
          return false;
        },
      },
      {
        headerName: _t("Cost Code"),
        field: "cost_code_name",
        minWidth: 180,
        flex: 2,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-left",
        cellEditor: "agRichSelectCellEditor",
        cellClass: "ag-cell-left",
        cellRenderer: ToolTipCell,
        editable: () => (readOnlyMode ? false : true),
        keyCreator: (params) => params.value?.cost_code_name?.trim(),
        valueFormatter: (params: ValueFormatterParams) =>
          params.value?.cost_code_name,
        valueParser: (params: ValueParserParams) =>
          costCodeOptions.find(
            (costCode) => costCode.cost_code_name === params.newValue
          ),
        cellEditorParams: {
          values: costCodeOptions,
          searchType: "matchAny",
          allowTyping: true,
          filterList: true,
          valueListMaxHeight: uniCostDropdownHight,
        },
        valueGetter: (params: ValueGetterParams) => {
          if (
            !is_module_read_only ||
            Boolean(Number(billDetail?.data?.due_balance ?? 0))
          ) {
            const costCodeValue = costCodeWithOption(params);

            return costCodeValue;
          }
        },
        valueSetter: (params: ValueSetterParams) => {
          if (params && params.node) {
            const costCode = HTMLEntities.decode(
              sanitizeString(params.newValue)
            ).trim();
            if (costCode) {
              const updatedData = {
                ...params.data,
                item_id: params?.data?.item_id,
                cost_code_name: costCode,
                cost_code_id:
                  codeCostData.find(
                    (item) =>
                      (
                        `${item?.cost_code_name.trim()}` +
                        `${item?.csi_code ? ` (${item?.csi_code.trim()})` : ""}`
                      )?.trim() === costCode
                  )?.code_id || "",
                cost_code: codeCostData.find(
                  (item) =>
                    (
                      `${item?.cost_code_name.trim()}` +
                      `${item?.csi_code ? ` (${item?.csi_code.trim()})` : ""}`
                    )?.trim() === costCode
                )?.csi_code,
              };
              params.node.setData(updatedData);
              handleUpdate([updatedData], {});
            }
          }
          return true;
        },
      },
      {
        headerName: _t("QTY"),
        field: "quantity",
        minWidth: 80,
        maxWidth: 80,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-right",
        cellClass: "ag-cell-right",
        cellEditor: "agNumberCellEditor",
        cellRenderer: (params: IBillItemTableCellRenderer) => {
          const { data } = params;
          const quantity =
            formatter(formatAmount(Number(data.quantity), { isQuantity: true }))
              .value || 0;

          return (
            <>
              <Tooltip title={quantity}>
                <Typography className="table-tooltip-text">
                  {quantity}
                </Typography>
              </Tooltip>
            </>
          );
        },
        editable: () => (readOnlyMode ? false : true),

        valueGetter: (params: ValueGetterParams) => {
          return params.data.quantity && params.data.quantity !== "0"
            ? Number(params.data.quantity)
            : 0;
        },

        valueSetter: (params: ValueSetterParams) => {
          if (params && params.node) {
            const cleanedValue =
              params.newValue === null
                ? ""
                : params.newValue.toString().split(".")[0].replace("-", "");

            if (cleanedValue.length > 6) {
              notification.error({
                description: _t("Quantity should be less than 6 digits"),
              });
              return false;
            }
            if (
              params.newValue !== null &&
              !floatWithNegativeRegex.test(params.newValue)
            ) {
              notification.error({
                description: _t(
                  "Decimal part should be less than or equal to 2 digits"
                ),
              });
              return false;
            }

            const updatedData = {
              ...params.data,
              quantity: !!params.newValue ? Number(params.newValue) : 0,
              total: Number(params.newValue) * params.data.unit_cost,
            };

            params.node.setData(updatedData);
            handleUpdate([updatedData], {});
          }
          return true;
        },
      },

      {
        headerName: _t("Unit Cost"),
        field: "unit_cost",
        minWidth: 130,
        maxWidth: 130,
        headerClass: "ag-header-right",
        cellClass: "ag-cell-right",
        suppressMovable: false,
        suppressMenu: true,
        cellEditor: "agNumberCellEditor",
        cellRenderer: (params: IBillItemTableCellRenderer) => {
          const { data } = params;
          return (
            <>
              {data.unit_cost ? (
                <Tooltip
                  title={`${
                    formatter((Number(data?.unit_cost) / 100).toFixed(2))
                      .value_with_symbol
                  }`}
                >
                  <Typography className="table-tooltip-text">
                    {
                      formatter((Number(data?.unit_cost) / 100).toFixed(2))
                        .value_with_symbol
                    }
                  </Typography>
                </Tooltip>
              ) : (
                <div>{formatter("0.00").value_with_symbol}</div>
              )}
            </>
          );
        },
        editable: () => (readOnlyMode ? false : true),
        cellEditorParams: {
          maxLength: 20,
        },
        valueGetter: (params: ValueGetterParams) => params.data.unit_cost / 100,

        valueSetter: (params: ValueSetterParams) => {
          if (params && params.node) {
            if (Number(params.newValue) < 0) {
              notification.error({
                description: _t(
                  "Negative values are not allowed for Unit Cost."
                ),
              });
              return false;
            }

            const cleanedValue = BigInt(
              Math.floor(Number(params.newValue))
            ).toString();

            if (cleanedValue.length > 10) {
              notification.error({
                description: _t(
                  "Unit cost should be less than or equal to 10 digits"
                ),
              });
              return false;
            }
            if (!floatWithNegativeRegex.test(params.newValue)) {
              notification.error({
                description: _t(
                  "Decimal part should be less than or equal to 2 digits"
                ),
              });
              return false;
            }
            const updatedData = {
              ...params.data,
              unit_cost: Number(params.newValue) * 100,
              total: Number(params.newValue) * 100 * params.data.quantity,
            };
            params.node.setData(updatedData);
            handleUpdate([updatedData], {});
          }
          return true;
        },
      },

      {
        headerName: _t("Unit"),
        field: "unit",
        minWidth: 80,
        maxWidth: 80,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        cellRenderer: (params: IBillItemTableCellRenderer) => {
          const { data } = params;
          return (
            <>
              {data.unit !== "" ? (
                <Tooltip title={HTMLEntities.decode(sanitizeString(data.unit))}>
                  <Typography className="table-tooltip-text">
                    {HTMLEntities.decode(sanitizeString(data.unit))}
                  </Typography>
                </Tooltip>
              ) : (
                <div>-</div>
              )}
            </>
          );
        },
        ...(window.ENV.ENABLE_UNIT_DROPDOWN && {
          suppressKeyboardEvent: (params) => {
            if (params.event.key === "Enter") {
              params.event.preventDefault();
              return true; // Block Ag-Grid's default behavior
            }
            return false;
          },
          cellEditorParams: {
            values: units,
            onKeyDown: (
              e: React.KeyboardEvent<HTMLInputElement>,
              data: IWorkorderDetailsItem
            ) => {
              if (e.key === "Enter") {
                const value = e?.currentTarget?.value?.trim();
                const newType = onEnterSelectSearchValue(
                  e,
                  units?.map((unit) => ({
                    label: unit?.name,
                    value: "",
                  })) || []
                );
                if (newType) {
                  setNewUnitName(newType);
                  setSelectedData(data);
                } else if (value) {
                  notification.error({
                    description:
                      "Records already exist, no new records were added.",
                  });
                }
              }
            },
          },
          cellEditor: UnitCellEditor<ISTItemDetails>,
        }),
        editable: () => (readOnlyMode ? false : true),
        valueGetter: (params: IWorkOrderItemTableCellRenderer) => {
          return params?.data?.unit;
        },
        valueSetter: (params: ValueSetterParams<IWorkorderDetailsItem>) => {
          const newUnit = window.ENV.ENABLE_UNIT_DROPDOWN
            ? (params?.newValue?.name?.trim() || "")?.toString()
            : (params?.newValue || "")?.toString();

          const [integerPart] = newUnit.split(".");

          if (integerPart.length > 15) {
            notification.error({
              description: _t("Unit should be less than 15 characters"),
            });
            return false;
          }

          const valuesToBeUpdate: IWorkorderDetailsItem = {
            ...params.data,
            unit: newUnit,
          };
          params?.node?.setData(valuesToBeUpdate);
          handleUpdate([valuesToBeUpdate], {});
          return true;
        },
      },
      {
        headerName: _t(""),
        minWidth: 130,
        maxWidth: 130,
        field: "sc_paid_bill_amt",
        suppressMovable: false,
        suppressMenu: true,
        cellClass: "ag-cell-right",
        hide: billDetail.data?.sc_multi_bill?.toString() === "0",
        editable: (params: CostItemTableCellRenderer) =>
          is_module_read_only
            ? // will remove after testing
              // || (Number(due_balance ?? 0) <= 0 && Number(total ?? 0) > 0)
              false
            : params.data.reference_module_id?.toString() ===
              defaultConfig.sub_contract_module_id.toString(),
        headerClass: "ag-header-right",
        valueGetter: (params: CostItemTableCellRenderer) => {
          const amount = params?.data?.sc_paid_bill_amt;
          return !isNaN(Number(amount)) ? +(amount / 100).toFixed(2) : "";
        },
        valueSetter: (params: {
          data: {
            unit_cost: number;
            sc_paid_bill_amt: string;
            tax_rate: string;
            sc_paid_bill_percentage: number;
            total: number;
            quantity: number;
            total_sc_paid_bill_amt: number;
          };
          newValue: string;
        }) => {
          if (
            params?.newValue !== undefined &&
            !isNaN(Number(params?.newValue))
          ) {
            let newPaidAmt = params?.newValue;
            const data = params?.data;

            const originalAmt = data?.quantity * data?.unit_cost;
            const billAmtRemain = (
              originalAmt -
              Number(data?.total_sc_paid_bill_amt) / 100
            ).toFixed(2);

            const amt =
              Number(data?.sc_paid_bill_amt) / 100 + Number(billAmtRemain);

            if (
              Number(newPaidAmt) > amt &&
              billDetail?.data?.allow_overbilling === "0"
            ) {
              if (amt > 0) {
                newPaidAmt = amt.toString();
                notification.error({
                  message: "Notification Title",
                  description: `Select a amount between 0 and ${amt}`,
                });
                return false;
              } else {
                notification.error({
                  message: "Notification Title",
                  description: _t(
                    "This amount is higher than 100% of the Sub-Contract."
                  ),
                });
              }
              return false;
            }

            if (data?.unit_cost * data.quantity < parseFloat(newPaidAmt)) {
              notification.error({
                message: "Notification Title",
                description: `Select a amount between 0 and ${
                  data?.unit_cost * data.quantity
                }`,
              });
              return false;
            }

            const items: any = {
              ...data,
              total:
                !newPaidAmt || isNaN(parseFloat(newPaidAmt))
                  ? 0
                  : parseFloat(newPaidAmt),
              sc_paid_bill_amt:
                !newPaidAmt || isNaN(parseFloat(newPaidAmt))
                  ? ""
                  : parseFloat(newPaidAmt).toString(),
              unit_cost: Number(data?.unit_cost / 100).toFixed(2),
              reference_module_id: defaultConfig.sub_contract_module_id,
            };

            const updatedData = {
              ...items,
              sc_paid_bill_percentage: Number(
                (
                  (parseFloat(newPaidAmt) * 100) /
                  (items?.unit_cost * data.quantity)
                )?.toFixed(2) ?? "0"
              ),
              unit_cost: (Number(items?.unit_cost) * 100).toFixed(2),
            };

            params?.node?.setData({
              // updatedData,
              ...data,
              sc_paid_bill_amt: isNaN(Number(items?.sc_paid_bill_amt * 100))
                ? ""
                : Number(items?.sc_paid_bill_amt * 100),
            });
            handleUpdate(
              [
                {
                  ...updatedData,
                  sc_paid_bill_amt: isNaN(Number(items?.sc_paid_bill_amt))
                    ? ""
                    : Number(items?.sc_paid_bill_amt),
                  total: isNaN(Number(items?.total)) ? 0 : Number(items?.total),
                  unit_cost: (Number(items?.unit_cost) * 100).toFixed(2),
                },
              ],
              {}
            );
            return true;
          }
          return false;
        },
        cellRenderer: (params: IBillItemTableCellRenderer) => {
          const data = params;

          return (
            <>
              {data.data.reference_module_id?.toString() ===
                CFConfig.sub_contract_module_id.toString() && (
                <div className="text-right">
                  {isNaN(Number(data?.data?.sc_paid_bill_amt))
                    ? ""
                    : formatter(
                        Number(params?.data?.sc_paid_bill_amt) / 100 === 0
                          ? Number(
                              (data?.data?.sc_paid_bill_amt || 0) / 100
                            ).toFixed(0)
                          : Number(
                              (data?.data?.sc_paid_bill_amt || 0) / 100
                            ).toFixed(2)
                      ).value_with_symbol}
                </div>
              )}
            </>
          );
        },
      },
      {
        headerName: _t("This Bill"),
        minWidth: 80,
        maxWidth: 80,
        field: "sc_paid_bill_percentage",
        suppressMovable: false,

        suppressMenu: true,
        // suppressKeyboardEvent,
        editable: (params: CostItemTableCellRenderer) =>
          is_module_read_only
            ? false
            : params.data.reference_module_id?.toString() ===
              defaultConfig.sub_contract_module_id?.toString(),
        hide: billDetail?.data?.sc_multi_bill?.toString() === "0",
        headerClass: "ag-header-right",
        cellClass: "ag-right-aligned-cell",
        valueSetter: (params: {
          data: {
            unit_cost: number;
            sc_paid_bill_amt: number;
            sc_paid_bill_percentage: number;
            total: number;
            quantity: number;
            total_sc_paid_bill_amt: string;
            total_sc_paid_bill_percentage: string;
            tax_rate: string;
          };
          newValue: string;
        }) => {
          if (params?.newValue !== undefined) {
            const newPaidPercentage = Number(params.newValue);
            const originalData = { ...params.data }; // Shallow clone first

            const totalScBilled = Number(
              originalData.total_sc_paid_bill_percentage ?? 0
            );
            const billedAmtPercentage = Number(
              originalData.sc_paid_bill_percentage ?? 0
            );
            const remainingPercentage = 100 - totalScBilled;

            const maxAllowed = billedAmtPercentage + remainingPercentage;

            if (
              newPaidPercentage > maxAllowed &&
              billDetail.data.allow_overbilling === "0"
            ) {
              notification.error({
                message: "Notification Title",
                description: `Select a percentage between 0 and ${maxAllowed}%.`,
              });
              return false;
            }

            if (newPaidPercentage > 100) {
              notification.error({
                message: "Notification Title",
                description: `Select a percentage between 0 and 100%.`,
              });
              return false;
            }

            const rowTotal =
              (originalData.unit_cost / 100) * originalData.quantity;
            const total = ((rowTotal * newPaidPercentage) / 100).toFixed(2);

            const updatedData = {
              ...originalData,
              sc_paid_bill_percentage: newPaidPercentage,
              sc_paid_bill_amt: Number(total),
              total: Number(total),
            };

            params?.node?.setData(updatedData);
            handleUpdate([updatedData], {});
            return true;
          }
          return false;
        },
        cellRenderer: (params: CostItemTableCellRenderer) => {
          return (
            <>
              {params.data.reference_module_id?.toString() ===
                defaultConfig.sub_contract_module_id.toString() && (
                <div className="text-right">
                  {params?.data?.sc_paid_bill_percentage !== undefined &&
                  !isNaN(params?.data?.sc_paid_bill_percentage)
                    ? `${Number(
                        params?.data?.sc_paid_bill_percentage?.toString()
                      ).toFixed(2)}%`
                    : "0%"}
                </div>
              )}
            </>
          );
        },
      },
      {
        headerName: _t("Billed"),
        minWidth: 180,
        maxWidth: 180,
        field: "total_sc_paid_bill_amt",
        headerClass: "ag-header-right",
        cellClass: "ag-cell-right",
        suppressMovable: false,

        suppressMenu: true,
        hide: billDetail?.data?.sc_multi_bill?.toString() === "0",

        cellRenderer: (params: CostItemTableCellRenderer) => {
          return (
            <>
              {params.data.reference_module_id?.toString() ===
                defaultConfig.sub_contract_module_id?.toString() && (
                <div className="flex gap-1 overflow-hidden w-full justify-end">
                  <Tooltip
                    title={
                      formatter(
                        params?.data?.total_sc_paid_bill_amt / 100 == 0
                          ? (
                              params?.data?.total_sc_paid_bill_amt / 100
                            ).toFixed(0)
                          : (
                              params?.data?.total_sc_paid_bill_amt / 100
                            ).toFixed(2)
                      ).value_with_symbol
                    }
                  >
                    <Typography className="table-tooltip-text !max-w-[calc(100%-48px)] block truncate">
                      {
                        formatter(
                          params?.data?.total_sc_paid_bill_amt / 100 == 0
                            ? (
                                params?.data?.total_sc_paid_bill_amt / 100
                              ).toFixed(0)
                            : (
                                params?.data?.total_sc_paid_bill_amt / 100
                              ).toFixed(2)
                        ).value_with_symbol
                      }
                    </Typography>
                  </Tooltip>
                  <Typography className="table-tooltip-text">
                    (
                    {params?.data?.total_sc_paid_bill_percentage !==
                      undefined &&
                    !isNaN(params?.data?.total_sc_paid_bill_percentage)
                      ? `${Number(
                          params?.data?.total_sc_paid_bill_percentage?.toString()
                        ).toFixed(2)}%`
                      : "0%"}
                    )
                  </Typography>
                </div>
              )}
            </>
          );
        },
      },
      {
        headerName: _t(""),
        minWidth: 130,
        maxWidth: 130,
        field: "sc_paid_bill_amt",
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-right",
        cellClass: "ag-cell-right",
        cellRenderer: (params: IBillItemTableCellRenderer) => {
          const data = params;

          return (
            <>
              {data.data.reference_module_id?.toString() ===
                CFConfig.subcontractor_module_id.toString() && (
                <div className="text-right">
                  {isNaN(Number(data?.data?.sc_paid_bill_amt))
                    ? ""
                    : formatter(
                        Number(params?.data?.sc_paid_bill_amt) / 100 === 0
                          ? Number(
                              (data?.data?.sc_paid_bill_amt || 0) / 100
                            ).toFixed(0)
                          : Number(
                              (data?.data?.sc_paid_bill_amt || 0) / 100
                            ).toFixed(2)
                      ).value_with_symbol}
                </div>
              )}
            </>
          );
        },
      },

      {
        headerName: _t("Total"),
        field: "total",
        minWidth: 130,
        maxWidth: 130,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-right",
        cellClass: "ag-cell-right",
        cellRenderer: (params: IBillItemTableCellRenderer) => {
          const { data } = params;

          return (
            <>
              <Tooltip
                title={
                  data.total === "0"
                    ? formatter("0.00").value_with_symbol
                    : `${
                        formatter((Number(data?.total) / 100).toFixed(2))
                          .value_with_symbol
                      }`
                }
              >
                <Typography className="table-tooltip-text">
                  {data.total === "0"
                    ? formatter("0.00").value_with_symbol
                    : `${
                        formatter((Number(data?.total) / 100).toFixed(2))
                          .value_with_symbol
                      }`}
                </Typography>
              </Tooltip>
            </>
          );
        },
      },
      {
        headerName: _t("Tax"),
        field: "apply_global_tax",
        minWidth: 50,
        maxWidth: 50,
        suppressMovable: false,
        suppressMenu: true,

        headerClass: "ag-header-center",
        cellClass: "ag-cell-center flex justify-center",
        editable: () =>
          !is_module_read_only &&
          (gSettings?.is_non_united_state_qb_country ||
            gSettings?.quickbook_sync === "0"),
        hide: !(
          gSettings?.is_non_united_state_qb_country ||
          gSettings?.quickbook_sync === "0"
        ),
        cellEditorParams: {
          maxLength: 20,
        },
        cellRenderer: (params: ValueSetterParams) =>
          CheckCellRenderer(params, handleUpdate),
      },
      {
        headerName: "",
        field: "",
        minWidth: 80,
        maxWidth: 80,
        cellRenderer: ({ data }: IBillItemTableCellRenderer) => {
          return (
            <div className="flex items-center gap-1.5 justify-center">
              <ButtonWithTooltip
                tooltipTitle={_t("View")}
                tooltipPlacement="top"
                icon="fa-solid fa-eye"
                onClick={() => {
                  setSelectedBillItem(data);
                  setSelectedItemId(data.item_id || 0);
                  setManualBillSingularItem(true);
                }}
              />
              {!is_module_read_only && (
                <BillDeleteItemAction
                  itemIds={data?.item_id}
                  billId={billDetail?.data?.bill_id}
                />
              )}
            </div>
          );
        },
      },
    ],
    [
      billDetail?.data?.items?.[0]?.total_sc_paid_bill_amt,
      codeCostData,
      billDetail?.data?.items,
    ]
  );

  return (
    <>
      <div className="py-3 lg:col-span-1 md:col-span-2 px-[15px] common-card h-fit">
        <CrudCommonCard
          headerTitle={_t(`Lump Sum/Item Costs`)}
          iconProps={{
            icon: "fa-solid fa-sack-dollar",
            containerClassName:
              "bg-[linear-gradient(180deg,#42DD9B1a_0%,#3CB9B31a_100%)]",
            id: "bills_item_costs_card_icon",
            colors: ["#42DD9B", "#3CB9B3"],
          }}
          headerRightButton={
            <>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1.5">
                  <div className="flex items-center gap-1.5 bg-blue-100 text-primary-900 py-[3px] px-[9px] rounded-sm dark:bg-dark-800 dark:text-white/90 w-fit whitespace-nowrap">
                    <FontAwesomeIcon
                      className="w-[17px] h-[17px]"
                      icon="fa-solid fa-money-check-dollar"
                    />
                    <Typography className="!mb-0 !text-sm !text-primary-900 leading-4 !font-semibold dark:!text-white/90">
                      {totalAmountWithTaxForHeader || 0.0}
                    </Typography>
                  </div>
                </div>
                <div className="w-full">
                  {!is_module_read_only ? (
                    <DropdownMenu
                      options={dropdownListOptions}
                      buttonClass="w-fit h-auto"
                      contentClassName="w-[310px] max-h-[46vh] !overflow-y-auto add-items-drop-down"
                      placement="bottomRight"
                    >
                      <div className="py-1 px-2.5 bg-[#EBF1F9] rounded flex items-center gap-[5px]">
                        <Typography className="text-primary-900 text-sm">
                          {_t(
                            `Add Item to ${
                              HTMLEntities.decode(
                                sanitizeString(singular_name)
                              ) ?? "Bill"
                            }`
                          )}
                        </Typography>

                        <FontAwesomeIcon
                          className="w-3 h-3 text-primary-900"
                          icon="fa-regular fa-chevron-down"
                        />
                      </div>
                    </DropdownMenu>
                  ) : (
                    ""
                  )}
                </div>
                <div className="md:relative absolute top-0 right-0">
                  <ModuleItemsFilter
                    onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
                    filter={filter}
                    onChangeFilter={setFilter}
                    openFilter={open}
                    disabled={billDetail?.data?.items?.length === 0}
                  />
                </div>
              </div>
            </>
          }
          children={
            <div className="pt-2">
              <div className="ag-theme-alpine">
                <StaticTable
                  className="static-table"
                  rowDragManaged={true}
                  // rowHeight={45}
                  ref={gridRef}
                  key={isLoading ? "loading" : "loaded"}
                  animateRows={true}
                  suppressPaginationPanel={false}
                  stopEditingWhenCellsLoseFocus={true}
                  suppressDragLeaveHidesColumns={true}
                  columnDefs={columnDefs}
                  gridOptions={gridOptions}
                  rowData={filteredItems}
                  noRowsOverlayComponent={
                    isLoading ? LoadingOverlay : NoDataOverlay
                  }
                />
              </div>
            </div>
          }
        />

        {!!(showBILLItemDatbase && !!billDetail?.data?.bill_id) && (
          <BillItemFromCostCodeDatabase
            showBillItemFromCostCodeDatabase={showBILLItemDatbase}
            setShowBillItemFromCostCodeDatabase={setShowBILLItemDatbase}
            onSubmit={handleCreate}
            items={billDetail?.data?.items || []}
          />
        )}

        {lumpSumTotalItem && (
          <AddLumSum
            isOpen={lumpSumTotalItem}
            onClose={() => setLumpSumTotalItem(false)}
            handleBillDetails={handleBillDetails}
          />
        )}

        {importItemsPurchase && (
          <ImportItemsPurchase
            importItemsPurchase={importItemsPurchase}
            setImportItemsPurchase={setImportItemsPurchase}
            projectId={billDetail?.data?.project_id}
            onSubmit={handleCreate}
            handleBillDetails={handleBillDetails}
          />
        )}

        {importFromSubContract && (
          <ImportFromSubContract
            importFromSubContract={importFromSubContract}
            setImportFromSubContract={setImportFromSubContract}
            projectId={billDetail?.data?.project_id}
            onSubmit={handleCreate}
            handleBillDetails={handleBillDetails}
          />
        )}

        {addDiscountItem && (
          <BillDiscontItem
            addDiscountItem={addDiscountItem}
            setAddDiscountItem={setAddDiscountItem}
            onSubmit={handleCreate}
          />
        )}
        {creditItem && (
          <ImportCreditItem
            importItemsCredit={creditItem}
            setImportItemsCredit={setCreditItem}
            handleBillDetails={handleBillDetails}
          />
        )}
        {expenseSingularItem && (
          <ImportExpense
            expenseSingularItem={expenseSingularItem}
            setExpenseSingularItem={setExpenseSingularItem}
            handleBillDetails={handleBillDetails}
          />
        )}

        {freightchargeItem && (
          <BillFrieghtChargeItem
            freightchargeItem={freightchargeItem}
            setfreightchargeItem={setfreightchargeItem}
            onSubmit={handleCreate}
          />
        )}

        {addRetainagePayment && (
          <BillRetainagePayment
            addRetainagePayment={addRetainagePayment}
            setaddRetainagePayment={setaddRetainagePayment}
            onSubmit={handleCreate}
          />
        )}

        {manualBillSingularItem && (
          <AddManualItem
            isOpen={manualBillSingularItem}
            moduleName={singular_name}
            recordId={selectedItemId}
            allRecords={billDetail.data.items}
            isShowNextPre={true}
            isViewOnly={is_module_read_only}
            onClose={handleManualClose}
          />
        )}

        {newUnitName.trim() && window.ENV.ENABLE_UNIT_DROPDOWN && (
          <ConfirmModal
            isOpen={Boolean(newUnitName.trim())}
            modalIcon="fa-regular fa-clipboard-list-check"
            modaltitle={_t("Add Option To List")}
            description={_t(
              `This will add "${newUnitName}" to the list. Do you want to add it?`
            )}
            isLoading={isAddingCustomData}
            onCloseModal={() => {
              setNewUnitName("");
              setSelectedData(undefined);
            }}
            onAccept={async () => {
              if (!isAddingCustomData && newUnitName) {
                setIsAddingCustomData(true);
                const response = (await addUnit({
                  name: newUnitName,
                })) as IUnitAddResponse & {
                  data: {
                    data: IUnitListResponseDataAndStatusCode["units"][0];
                  };
                };
                if (response.success) {
                  const newUnits = [response.data.data, ...units];
                  setUnits(newUnits);
                  setNewUnitName("");
                  setSelectedData(undefined);
                  const api = gridRef.current?.api;
                  if (!api) return;

                  const renderedNodes = api.getRenderedNodes();
                  if (renderedNodes) {
                    const currentRowNode = renderedNodes.find(
                      (node) => node.data?.item_id === selectedData?.item_id
                    );
                    if (currentRowNode && currentRowNode.data) {
                      const oldData = { ...currentRowNode.data };
                      currentRowNode?.setData({
                        ...currentRowNode.data,
                        unit: newUnitName,
                      });
                      const data = {
                        ...currentRowNode.data,
                        unit: newUnitName,
                      };
                      const response = await handleUpdate([data], {});
                      if (!response.success) {
                        currentRowNode?.setData(oldData);
                        notification.error({ description: response.message });
                      }
                    }
                  }
                  const existingColDefs = api.getColumnDefs();
                  if (!existingColDefs) return;

                  const updatedColDefs = existingColDefs.map((col) =>
                    "field" in col && col.field === "unit"
                      ? {
                          ...col,
                          filterParams: {
                            values:
                              newUnits.map((unit) => ({
                                label: unit.name?.toString(),
                                value: unit.name?.toString(),
                              })) ?? [],
                          },
                          cellEditorParams: {
                            ...col.cellEditorParams,
                            values: newUnits,
                          },
                        }
                      : col
                  );

                  api.setColumnDefs(updatedColDefs);

                  // Ensure the grid header re-renders
                  api.refreshHeader();
                } else {
                  notification.error({
                    description: response.message,
                  });
                }
                setIsAddingCustomData(false);
              }
            }}
            onDecline={() => {
              setNewUnitName("");
              setSelectedData(undefined);
            }}
          />
        )}
      </div>
    </>
  );
};

export default BillsItemCostsCard;

const CheckCellRenderer = (
  params: CostItemTableCellRenderer,
  handleUpdate: any
) => {
  const [checkBoxData, setCheckBoxData] = useState(
    (params?.data as any)?.apply_global_tax === "1" // change this in future ( temporary resolve type issue )
  );
  const [TaxLoadingStatus, setTaxLoadingStatus] = useState<{
    [key: string]: boolean;
  }>({});
  const gConfig: GConfig = getGConfig();
  return (
    <CustomCheckBox
      className="gap-0 mx-auto w-4"
      checked={checkBoxData}
      onChange={(e: CheckboxChangeEvent) => {
        const value = e?.target?.checked;
        setCheckBoxData(!checkBoxData);
        setTaxLoadingStatus({
          [params?.data?.item_id]: true,
        });
        if (params && params.node) {
          const updatedData = {
            ...params.data,
            apply_global_tax: value === true ? "1" : "0",
          };
          params.node.setData(updatedData);
          handleUpdate([updatedData], {}, setTaxLoadingStatus);
        }
      }}
      disabled={gConfig?.module_read_only}
      loadingProps={{
        isLoading:
          params && params?.data?.item_id
            ? TaxLoadingStatus[params?.data?.item_id]
            : false,
        className: "bg-[#ffffff]",
      }}
    />
  );
};
