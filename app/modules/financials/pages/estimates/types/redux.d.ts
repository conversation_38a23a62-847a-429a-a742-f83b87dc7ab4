// Estimates Dashboard
interface IEstimatesDashState {
  clientResentResponse: Array<IClientRecentResponse>;
  estByStatus: Array<IEstByStatus>;
  winLostGraphData: Partial<IWinLostGraphData>;
  estOutForBid: Array<IEstOutForBid>;
  bidResponse: Array<IBidResponse>;
  estPendingApprovalResult: Array<IEstimatePendingApproval>;

  isDashLoading?: boolean;
  isDataFetched?: boolean;

  clientResentResponseLastRefreshTime: string;
  estByStatusLastRefreshTime: string;
  estPendingApprovalLastRefreshTime: string;
  winLostLastRefreshTime: string;
  estOutForBidLastRefreshTime: string;
  bidResponseLastRefreshTime: string;
  searchValue: string;
}

interface IClientRecentResponse {
  estimate_id: number;
  subject: string;
  company_estimate_id: string;
  client_icon: string;
  full_name: string;
  status: string;
  status_key: string;
  status_id: number;
}
interface IEstByStatus {
  estimate_id: string;
  display_name: string;
  default_color: string;
  total: string;
  amount: string;
  percentage: string;
}
interface IWinLostGraphData {
  current_win_count: number;
  current_lost_count: number;
  previous_win_count: number;
  previous_lost_count: number;
  current_win_amount: string;
  current_lost_amount: number;
  previous_win_amount: string;
  previous_lost_amount: number;
  current_total_amount: number;
  previous_total_amount: number;
  current_total_count: number;
  previous_total_count: number;
}
interface IEstOutForBid {
  est_title: string;
  bid_package_title: string;
  due_date: string;
}

interface IBidResponse {
  estimate_id: number;
  est_title: string;
  contractor_name: string;
  status_key: string;
  bid_package_title: string;
  user_image: string;
  status_name: string;
  submit_status_name: string;
}

interface IEstimatePendingApproval {
  company_estimate_id: string;
  client_icon: string;
  full_name: string;
  submitted: string;
  expires: string;
  amount: string;
}
interface IEstimateItemVariantPayload {
  item_type?: string;
  item_id?: string;
}
interface IEstimateItemVariantApiRes extends Omit<IApiCallResponse, "data"> {
  data: VariaonsofItem[];
  // variants :VariaonsofItem[];
}
interface VariaonsofItem {
  variation_id?: string;
  name?: string;
  company_id?: string;
  is_deleted?: string;
  date_added?: string;
  date_modified?: string;
  status_name?: string;
  label?: string;
  value?: string;
}
interface IEstimatesDashApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IEstimatesDashState;
  refreshType?: string;
}

// Estimate list
interface IEstTempFil {
  project?: string | undefined;
  status?: string;
}

interface IEstListParmas {
  limit: number;
  page: number;
  order?: {
    column: string;
    dir: string;
  };
  search?: string;
  filter?: ISCTempFil;
  ignore_filter?: number;
}

interface IEstimateList {
  title: string;
  customer_name: string;
  estimate_id: number;
  custom_estimate_id: string;
  company_estimate_id: string;
  total: string;
  cost: string;
  total_w_tax: number;
  total_markup_amount: number;
  estimate_item_total: number;
  total_markup_item_amount: number;
  project_manager_image: string;
  project_manager_name: string;
  estimate_project_type_name: string;
  approval_type_name: string;
  default_color: string;
  estimate_date: string;
  expire_date: string;
}

interface IEstimateListApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    estimates: IEstimateList[];
  };
}

// Add Estimate
interface IAddESInitialValues {
  project_id?: number;
  customer_id: number;
  title: string;
  custom_estimate_id?: number;
  billing_option?: string;
  estimate_date: string;
  estimate_time: string;
  approval_type?: string;
  access_to_custom_fields?: number;
  estimator_id?: number;
  estimator_contact_id?: number;
  custom_fields?: ICustomFieldInitValue;
}

interface IAddEstimateRes extends Omit<IDefaultAPIRes, "data"> {
  data: number;
}
interface IEstimateDetailApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IEstimateDetailData;
}

interface IEstimateCoverSheetDetailApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ESCoverSheetDetailData;
}

interface IEstimateScopeWorkDetailApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ESScopeWorkDetailData;
}
interface IEstimatesUpdateApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IEstimatesDashState;
  refresh_type?: string;
}
interface IEstimateDetailParams {
  estimate_id: string | undefined | number;
  module_id?: number;
  add_event?: boolean;
}

interface IEstimateDetailScopeListParams {
  estimate_id?: string | undefined;
  module_id?: number;
}
// Details

interface IEstimatesDetailState {
  isEstimateDetailLoading?: boolean;
  estimateDetail?: IEstimateDetailData;
  isDetailLoading?: boolean;
  estimateStatusList?: EStatusList[];
  scopeWorkDetail?: valueESScopeWorkDetailData;
  previous_Task?: valueESScopeWorkDetailData;
  isScopeWorkLoading?: boolean;
  coverSheetDetail?: ESCoverSheetDetailData;
  isCoverSheetLoading?: boolean;
  coverSheetListData?: ESCoverSheetListData;
  isCoverSheetListLoading?: boolean;
  service_ticket_job_status?: IGetStatusData[];
  service_ticket_priority?: IGetStatusData[];
  service_ticket_appointment_status?: IGetStatusData[];
  todoPriorityList?: ITodoPriority[];
  todoStatusList?: ITodoStatus[];
  companyItemsList?: ICompanyItems[];
  statusListLoading?: boolean;
  customerSeletedData?: IECustomer;
  isDashLoading?: boolean;
  // taxData: IEstimateTaxData;
}

interface EStatusList {
  label?: string;
  value?: IStatus | undefined;
  item_id?: number;
  default_color?: string;
  bgcolor?: string;
  icon?: FontAwesomeIconProps["icon"] | string;
  key?: string | undefined;
  bgcolor?: string;
  sort_order?: number;
  status_color?: string;
  show_in_progress_bar?: number;
  does_sync_qb?: boolean | number;
  // type_id: number;
  // name: string;
  // display_name?: string;
  // category?: string;
}

interface IEStatusProcess {
  [key: string]: string | number | boolean | undefined;
  estimate_id?: string | number;
  generate_invoice?: number | boolean;
  generate_project?: number | boolean;
  copy_to_project_sov?: number | boolean;
  create_schedule_tasks?: number | boolean;
  enable_procurement_tab?: number | boolean;
  complete_project?: number | boolean;
  project_color?: "FFFFFF";
}

interface ESCoverSheetListData {
  custom: Array<ICoverSheet>;
  default: Array<ICoverSheet>;
}

interface ICoverSheet {
  template_html: string;
  is_coversheet_default: number;
  pdf_value: string;
  template_name: string;
  template_id: number | string;
}

interface ESCoverSheetDetailData {
  cover_sheet_id: number;
  module_id: number;
  user_id: number;
  record_id: number;
  cover_sheet_html: string;
  is_delete?: number;
  show_in_pdf?: 0 | 1;
  added_on: string;
  company_id: number;
}

interface ESScopeWorkDetailData {
  description?: string;
  checklist?: ESChecklistItem[];
  estimate_id?: number;
}

interface ESChecklistItem {
  item_id: number;
  estimate_id?: number;
  task_name?: string;
  reference_item_id?: number;
  company_id?: number;
  user_id?: number;
  show_on_pdf?: number; // Optional
  item_sort_order?: number | undefined;
  date_added?: string; // Optional
  date_modified?: string; // Optional
  task?: string;
  esw_item_id?: number; // Optional
  status?: number;
}

interface IESParamsData {
  estimate_id: string | undefined | number;
  forImport?: boolean;
  get_html_only?: boolean | number;
  module_id?: number;
  replace_fields?: number;
  t_id?: string;
  batch_array?: string;
  record_id?: number | string;
}
interface IESCopyParamsData {
  sectionIDs?: number[];
  import_quantity?: number;
  import_scope?: number;
  import_inclusions?: number;
  import_terms?: number;
  import_exclusions?: number;
  import_coversheet?: number;
  import_attachment?: number;
  import_estimate_fields?: number;
  estimate_id: string | undefined | number;
}
interface IESItemRemoveParamsData {
  estimate_id: string | undefined;
  item_id?: number | string;
  delete_multiple_item?: number;
  multiple_item_id?: string[] | number[];
  forImport?: boolean;
}
interface IEOneBuildImportParamas {
  estimate_id?: string;
  section_id?: number;
  estimate_item_no?: number;
  add_item_to_database: number;
  importFrom?: string;
  outside_us: number;
  items: SelectedItemsToAdd;
  project_id?: number | string;
}

interface IEAddRecordBatchDocumentParams {
  record_id: number | string;
  company_id: number;
  user_id: number;
  module_id: number;
  module_name: string;
  document_name: string;
  print_doc_arr: { document_id?: string; document_html?: string }[];
  batch_url: string;
  template_id: number | string;
  is_mst_template: number | boolean;
  project_id: number | string;
}

interface IESUpdateItemParams {
  estimate_id: string | number | undefined;
  is_single_item?: number;
  items?: Partial<ESEstimateItem>[];
  sectionItems?: Partial<ESEstimateItem>[];
}

interface IECustomer {
  contact_id?: number;
  user_id?: number;
  is_project_exists?: string;
  access_role?: string;
  orig_type?: number;
  unique_name?: string;
  quality?: string;
  app_access?: number;
  company?: string;
  type?: string;
  first_name?: string;
  last_name?: string;
  display_name?: string;
  company_admin?: string;
  module_display_name?: string;
  customer_tax_id?: number;
  company_name?: string;
  email?: string;
  title?: string | null;
  phone?: string;
  fax?: string;
  cell?: string;
  access_code?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  zip?: string;
  street1?: string | null;
  street2?: string | null;
  sales_city?: string | null;
  sales_state?: string | null;
  sales_zip?: string | null;
  stage_name?: string | null;
  referral_source_name?: string | null;
  referral_source?: string | null;
  lead_value?: string | null;
  estimate_sales_date?: string | null;
  refered_by?: string | null;
  notes?: string | null;
  type_name?: string;
  type_key?: string;
  parent_type?: string;
  parent_type_key?: string;
  status_name?: string;
  is_favorite?: string;
  term_id?: string;
  term_key?: string;
  billed_to?: string;
  billed_to_contact?: string;
  billed_to_type_name?: string;
  billed_to_dir_type?: string | null;
  billed_to_image?: string | null;
  billed_to_type_key?: string;
  billed_to_display_name?: string | null;
  stage?: string | null;
  enable_default_cost_code?: string;
  cost_code_id?: string;
  image?: string;
  services?: string;
}

interface IEstimateDetailData {
  estimate_id?: number;
  customer_id?: number;
  full_name?: string;
  display_name?: string;
  temperature_scale?: string;
  save_estimate_template?: boolean | number;
  project_id?: number | string | undefined;
  prj_record_type?: number | string;
  contact_id?: number | string;
  title?: string;
  description?: string;
  authorized_by?: number;
  billed_to_type_key?: string | number;
  billed_to_type?: string | number;
  billed_to_type_name?: string;
  notes?: string;
  approval_type?: string | number;
  estimate_date?: string;
  estimate_time?: any;
  is_locked?: number | string;
  expire_date?: string | null;
  user_id?: number | string;
  company_id?: number;
  is_deleted?: string;
  date_added?: string;
  date_modified?: string;
  image?: any;
  company_estimate_id?: number | string;
  company_estimate_id_backup?: number;
  total?: string;
  revenue?: string;
  cost?: string;
  authorized_type?: string;
  signature?: string;
  parent_estimate_id?: any;
  demo_data?: number;
  is_lock?: number;
  inclusion?: string;
  exclusion?: string;
  estimate_terms?: string;
  estimate_default_terms?: string;
  terms?: string;
  estimate_custom_terms?: string;
  customer_contact_id?: number | string;
  custom_estimate_id?: string;
  is_template?: number;
  billed_to?: number | string;
  qb_date?: any;
  quickbook_estimate_id?: number;
  qbc_id?: string;
  is_updated?: number;
  tax_id?: string;
  ref_invoice_id?: number;
  locked_by?: number;
  estimate_project_type?: string | number;
  estimate_project_type_key?: string;
  show_files_on_pdf?: number;
  billed_to_status?: any;
  date_billed_to_status?: any;
  ip_address?: any;
  approval_type_key?: string;
  approval_type_backup?: any;
  billing_option?: string;
  bid_manager_id?: number;
  project_manager_id?: number | string;
  billed_to_contact?: number;
  enable_procurement_tab?: number;
  address_from?: string;
  est_street1?: string;
  est_street2?: string;
  est_city?: string;
  est_state?: string;
  est_zip?: string;
  est_approval_date?: any;
  est_submit_approval_email_status?: number;
  estimate_status_update_from_cron?: number;
  is_project_template?: number;
  project_template_id?: any;
  origin?: number;
  cover_sheet_template_id?: number;
  is_residential_enable?: number;
  company_sector?: any;
  estimator_id?: any;
  estimator_contact_id?: any;
  estimate_term_message?: string;
  tax_name?: any;
  tax_rate?: string;
  is_group_tax?: string;
  group_children?: string;
  approval_type_name?: string;
  project_name?: string;
  quickbook_project_id?: number;
  total_tax_rate?: string;
  is_reversible_tax?: string;
  quickbook_classprojecttype_id?: string;
  estimate_project_type_name?: string;
  project_type_name?: string;
  project_type?: string;
  prj_type?: string;
  address_one?: string;
  address_two?: string;
  cust_location?: string;
  cust_type?: number | string | null | undefined;
  est_created_email?: string;
  employee?: string;
  default_section_id?: number;
  locked_by_username?: any;
  prj_customer_name?: string;
  project_customer_email?: string;
  authorized_by_name?: any;
  authorized_by_name_only?: any;
  authorized_by_company_name?: string;
  customer_name?: string;
  customer_company?: string;
  customer_name_only?: string;
  customer_fisrt_name?: string;
  customer_last_name?: string;
  customer_phone?: string;
  customer_fax?: string;
  customer_cell?: string;
  customer_title?: string;
  user_company_name?: string;
  bid_manager_name?: string;
  project_manager_name?: string | undefined;
  project_manager_name_only?: any;
  project_manager_company_name?: string;
  project_manager_email?: string;
  cmp_phone?: string;
  cmp_image?: string;
  cust_address?: string;
  cust_street1?: string;
  cust_street2?: string;
  email_subject?: string;
  cust_phone?: string;
  cust_city?: string;
  cust_state?: string;
  cust_zip?: string;
  quickbook_customer_id?: number;
  customer_email?: string;
  customer_billed_to_id?: number;
  prj_address1?: string;
  prj_address2?: string;
  prj_city?: string;
  prj_state?: string;
  prj_zip?: string;
  prj_id?: string;
  billed_to_name?: any;
  billed_to_email?: any;
  billed_to_id?: any;
  billed_to_display_name?: string;
  billed_to_company_name?: any;
  billed_to_name_w_o_company?: any;
  estimate_date_use_qb?: string;
  expire_date_use_qb?: any;
  billed_to_dir_type?: any;
  current_estimate_items_total?: string;
  estimate_taxable_total?: string;
  estimate_date_for_maill?: string;
  start_date?: string;
  end_date?: any;
  time_added?: string;
  time_modified?: string;
  qb_date_added?: any;
  qb_time_added?: any;
  time_billed_to_status?: any;
  origin_date_modified?: string;
  project_is_deleted?: number;
  project_manager_dir_type?: any;
  project_manager_name_only?: string;
  project_manager_name?: string;
  estimator_display_name?: string | number | undefined;
  estimator_to_type?: string | number;
  estimator_to_dir_type?: string | number;
  type?: string | number;
  type_key?: string | number;
  company?: string | number;
  sheet_html?: string;
  cover_sheet_html?: string;
  longitude?: number | string;
  latitude?: number | string;
  email?: string;
  city?: string;
  state?: string;
  first_name?: string;
  last_name?: string;
  zip?: string;
  street1?: string;
  street2?: string;
  address1?: string;
  address2?: string;
  template_html?: string;
  unique_name?: string;
  customer_address1?: string;
  customer_address2?: string;
  is_batch_exist?: 0 | 1 | 2;
  hide_batch_coversheet?: number;
  orig_type?: string | number;
  billed_to_image?: string;
  customer_image?: string;
  project_manager_image?: string;
  estimator_image?: string;
}

interface IEDetailsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IEstimateDetailData;
}
interface IEInvoiceGenrateApiRes extends Omit<IDefaultAPIRes, "data"> {
  data?: {
    invoice_id?: string;
  };
}

interface IEProjectGenrateApiRes extends Omit<IDefaultAPIRes, "data"> {
  data?: {
    project_id?: string;
  };
}

interface IEstimateAddressKey {
  project?: string[];
  customer?: string[];
  other?: string[];
  directory?: string[];
}

interface IEstimateAddrInfo {
  user_id?: string | number;
  address1?: string;
  address2?: string;
  street1?: string;
  street2?: string;
  city?: string;
  state?: string;
  zip?: string;
  type?: string | number;
  latitude?: number;
  longitude?: number;
  address_from?: string;
  est_street1?: string;
  est_street2?: string;
  est_city?: string;
  est_state?: string;
  est_zip?: string;
}
interface IEstimatesItemsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ESSection;
  refresh_type?: string;
}
interface IEstimatesItemUpdateApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ESEstimateItem[];
}

interface IEstimatesCopyApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    estimate_id: number;
  };
}
interface IEstimatesUpdateItemsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ESEstimateItem[];
  refresh_type?: string;
}

interface IEstimatorGetByProjectId extends Omit<IEResponse, "data"> {
  data: IEstimatorData;
  refresh_type?: string;
}

interface IEstimatorData {
  estimator_id?: number;
  user_type?: string;
  display_name?: string;
}
interface IEstimatesBiddingApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ESEstimateBidding[];
  refresh_type?: string;
}

interface DeleteBidderPayload {
  bidding_id: number;
  bidder_id: number;
}

interface UpdateBidderPayload {
  bidding_id: number;
  bidder_id: number;
  data: Partial<Bidder>;
}
interface IEstimatesBiddingStatusApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: { estimate_bid_status: EstimateBidStatus[] };
}
interface EstimateBidStatus {
  type_id: number;
  show_in_progress_bar: number;
  key: string;
  name: string;
  sort_order: string;
  display_name: string;
  default_color: string | null;
  category: string;
  text_color?: string;
}

interface ESEstimateBidding {
  bidding_id: number;
  estimate_id: number;
  title: string;
  bid_status: number;
  bid_manager_id: number;
  project_id: number;
  bid_manager_name: string;
  bid_manager_phone: string;
  bid_manager_company_name: string;
  bid_status_name: string;
  bid_status_key: string;
  bid_manager_email: string;
  deadline_date: string;
  deadline_time: string;
  deadline_date_global_format: string;
  deadline: string;
  bid_manage_short_name: string;
  bid_items?: BidItem[];
  bidders?: Bidder[];
  user_image?: string;
}

interface Bidder {
  bidder_id: number;
  bidding_id: number;
  estimate_id: number;
  bid_user_id: number;
  user_id: number;
  bid_amount: number;
  date_added: string;
  bid_status?: number | string;
  date_modified: string;
  bidder_name_only: string;
  bidder_company_name: string;
  submitted_date: string | null;
  ip_address?: string;
  sent_date: string | null;
  sent_time: string;
  submitted_time: string;
  bidder_name: string;
  user_image: string;
  submit_status_name: string | null;
  bid_status_name: string;
  bidder_email: string;
  est_submit_bidder_email_status?: number;
  files?: IFile[];
  notes?: string;
  subject?: string;
  bid_item_id?: number | string;
  items?: BidItem[];
  item_id?: number | string;
  type?: number;
  type_name?: string;
}
interface ESSection {
  section_id: number;
  estimate_id: number;
  custom_section_id: number;
  section_name: string;
  cost_code_id: number;
  description: any;
  user_id: any;
  company_id: any;
  modify_by: number;
  parent_section_id: number;
  demo_data: any;
  date_added: any;
  date_modified: any;
  is_optional_section: number | boolean;
  is_project_template: number;
  project_template_id: any;
  code_id: any;
  csi_code: any;
  parent_id: any;
  csi_name: any;
  is_deleted: any;
  parent_code_id: any;
  demo_parent_code_id: any;
  test_id: any;
  parent_id_back: any;
  qb_date: any;
  quickbook_costcode_id: any;
  quickbook_parent_id: any;
  qbc_id: any;
  is_updated: any;
  quickbook_level: any;
  is_managed_level: any;
  import_id: any;
  show_on_timecard: any;
  income_account_id: any;
  expense_account_id: any;
  has_no_child: any;
  qb_type: any;
  deleted_archived: any;
  is_sample_parent: any;
  cost_code_name: any;
  section_total: string;
  section_total_with_optional_item: string;
  taxable_total: string;
  markup_total: number;
  markup_items_total: number;
  total_w_o_tax_markup: number;
  taxable_total_new: number;
  items: ESEstimateItem[];
}

interface ESEstimateItem {
  selected: boolean;
  order_number: number;
  item_id: number;
  estimate_id: number;
  directory_id: number;
  company_id: number;
  equipment_id: any;
  material_id: any;
  cost_code_id: number | string;
  tax_id: any;
  subject: string;
  quantity: number;
  unit: string;
  unit_cost: string;
  hidden_markup: number;
  markup: string | number | null;
  description: string;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  total: number;
  estimate_item_no: number;
  parent_item_id: number;
  item_type: number | string;
  reference_item_id: number;
  assigned_to: number;
  assigned_to_contact_id: number;
  is_temaplate: number;
  quickbook_estimateitem_id: number;
  quickbook_item_id: number;
  qbc_id: string;
  item_on_database: number;
  apply_global_tax: number;
  section_id: number;
  is_markup_percentage: number;
  markup_amount: string;
  bidder_item_id: number;
  is_optional_item: number;
  variation_id: number | null;
  internal_notes: any;
  one_build_id: any;
  is_project_template: number;
  project_template_id: any;
  origin: number;
  modified_unit_cost: string;
  item_type_display_name: string;
  item_type_name: string;
  item_type_key: string;
  company_estimate_id: string;
  assignee_type: any;
  assignee_name: any;
  assigned_to_name_only: string;
  assigned_to_company_name: string;
  section_name: string;
  custom_section_id: number;
  source_name: string;
  item_total: number;
  cost_code_name: any;
  cost_code: any;
  code_id: any;
  updated_unit_cost: string;
  quickbook_costcode_id: any;
  tax_rate: number;
  user_image?: string;
  origin_date_modified: string;
  variation_name: string;
  no_mu_total: number;
  is_added_item: 1 | 0;
  csi_code: string;
  estimate_item_id?: number | string;
  code_is_deleted?: boolean | number;
  add_item_to_database?: number;
}

interface ESEstimateCalculation {
  quantity?: number;
  unit_cost?: number | undefined | string;
  is_optional_item?: boolean;
  is_markup_percentage?: boolean;
  markup?: number;
  apply_global_tax?: boolean | string;
}

interface SectionMetrics {
  sectionName?: string;
  sectionId?: string | number;
  estimatedCost: number;
  subtotalTaxeble: number;
  subtotal: number;
  markup: number;
  profitMargin?: string;
  tax: number;
  grandTotal: number;
  hours: number;
  [key: string]: number;
}

interface ESChartItem {
  estimated_total: number;
  actual_total: number;
  item_title: string;
  due: number;
}

interface ESChartDataType {
  material: ESChartItem;
  labor: ESChartItem;
  equipment: ESChartItem;
  sub_contractor: ESChartItem;
  other_item: ESChartItem;
  unassigned_item: ESChartItem;
  total: ESChartItem;
  [key: string]: { estimated_total: number; due: number };
}

interface IEstimateAddItem extends Partial<ESEstimateItem> {
  subject?: string;
  quantity?: number | string;
  unit?: string;
  unit_cost?: number | string;
  cost_code_id?: string | number;
  tax_id?: string;
  markup?: string | number;
  is_markup_percentage?: number;
  total?: number;
  item_type?: string | number;
  assigned_to?: number | string;
  user_image?: string;
  assigned_to_contact_id?: number;
  contractor_id?: string;
  contractor_contact_id?: number;
  add_item_to_database?: number;
  description?: string;
  hidden_markup?: number | string;
  internal_notes?: string;
  estimate_item_id?: string | number;
  estimate_id?: string | number;
  reference_item_id?: string | number;
  is_cost_item?: number;
  section_id?: number;
  type_name?: string;
  reference_module_item_id?: string; // avoided custom key
  item_on_database?: number; // avoided custom key
}

type estimateFilePhotoState = {
  estimateFilePhotos: IFile[];
  isEstimateFilePhotoLoading: boolean;
  isFileFetchLoading: boolean;
};

interface IEstimateFilePhotoFormData {
  primary_id: number;
  module_id: number;
  module_key: string;
  directory_type?: number;
  attach_image?: string;
  files?: IFile[];
  project_id?: number | string;
}
interface IEstimateFilePhotoAddRes extends Omit<IDefaultAPIRes, "data"> {
  data: { aws_files: IFile[] };
}
interface IEstimateFetchFilePhotoParams {
  module_key?: string;
  record_id: number;
}
interface ISentEmailData {
  company_document_id?: string;
  email_subject?: string;
  mail_body?: string;
  email_attachment?: string;
  document_pdf_link?: string;
  email_template_name?: string;
  date_added?: string;
  batch_data?: IEmailBatchData[];
  old_sent_document?: string;
  sent_email_data?: IEmailBatchData;
}

interface IEmailBatchData {
  record_batch_id?: string;
  company_document_id?: string;
  record_id?: string;
  module_id?: string;
  document_id?: string;
  html_without_data?: string;
  document_html?: string;
  attached_files?: string;
  display_order?: string;
  user_id?: string;
  company_id?: string;
  date_added?: Date;
}

interface IESFinaliseInitState {
  batchDocumentList: IDocumentData[];
  selectedEsTemplateData?: (IPDFFileTemplateData & { html?: string }) | null;
  isEsTemplateLoading: boolean;
  mailBody: string | null;
  pdfUrl: string | null;
  isBatchPdfLinkLoading: boolean;
  sent_email_data: ISentEmailData | null;
  isEditDocumentClicked: boolean;
  pdfTemplateList: IPDFFileTemplateData[] | null;
  selectedTemplateOption: (IDropdownMenuOption & IEExtraTemplateOption) | null;
  isBatchExist: "0" | "1" | "2";
  refreshCount: number;
  customEmailTitle: string | null;
}
interface IEstimatesOneBuildApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IOneBuildData;
  refresh_type?: string;
}
type IPartialOneBuildData = Partial<IOneBuildData>;
interface IOneBuildData {
  company_id: number;
  company_name: string;
  zip_code: string;
  description: string;
  slogan: string;
  image: string;
  license: string;
  tax_ein: string;
  street: string;
  city: string;
  state: string;
  phone: string;
  fax: string;
  website: string;
  date_added: string;
  date_modified: string;
  module_group_id: number;
  company_employee: number;
  company_note: string;
  logo_width: number;
  logo_height: number;
  copy_from_company: number;
  copy_s3_files: number;
  organization_type: number;
  phone_code: string;
  phone_type: string;
  industry_code: string;
  industry_code_type: string;
  company_type: string;
  company_primary_industry: string;
  country: string;
  header_logo: string;
  angilead_entity_id: number;
  angilead_status: number;
  zapier_token: string;
  company_size: number;
  one_build_location: number;
  one_build_item_name: number;
  one_build_price: number;
  one_build_unit: number;
  one_build_description: number;
  one_build_photo: number;
  one_build_cost_code: number;
  pdf_template_color: string;
  pdf_template_text_color: string;
  one_build_state_county: string;
  one_build_lat: string;
  one_build_lng: string;
  one_build_import_cost_code: string;
  is_active: number;
  thumbtack_business_id: string;
  other_primary_industry: string;
  add_item_to_database?: number;
  importedItems?: IOneBuildItemsData[];
  allItemsIds?: number[];
}

interface IEstimatesOneBuildItemsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IOneBuildItemsData;
}
interface IOneBuildItemsData {
  nodes: BuildItemsNode[];
  pageInfo: PageInfo;
  dataLocation: DataLocation;
  totalCount: number;
}

interface BuildItemsNode {
  state: string;
  county: string;
  csiDivision: string;
  csiDivisionNumber: string;
  csiDivisionName: string;
  csiSection: string;
  csiTitle: string;
  nahbDivision: string;
  nahbDivisionDescription: string;
  nahbCode: string;
  nahbCodeDescription: string;
  categoryPath: string[];
  properties: any[];
  description: string;
  imagesUrls: string[];
  knownUoms: KnownUom[];
  materialRateUsdCents: number;
  laborRateUsdCents: any;
  burdenedLaborRateUsdCents: any;
  laborSourceId: any;
  laborName: any;
  productionRate: any;
  calculatedUnitRateUsdCents: number;
  id: string;
  sourceType: string;
  name: string;
  inputUOM: string;
  outputUOM: string;
  activeUoms?: KnownUom;
  nodes: Node[];
  pageInfo: PageInfo;
  dataLocation: DataLocation;
  totalCount: number;
  isOneBuildIdInDB?: number | string;
}

interface Node {
  state: string;
  county: string;
  csiDivision: string;
  csiDivisionNumber: string;
  csiDivisionName: string;
  csiSection: string;
  csiTitle: string;
  nahbDivision: string;
  nahbDivisionDescription: string;
  nahbCode: string;
  nahbCodeDescription: string;
  categoryPath: string[];
  properties: any[];
  description: string;
  imagesUrls: string[];
  knownUoms: KnownUom[];
  materialRateUsdCents: number;
  laborRateUsdCents: any;
  burdenedLaborRateUsdCents: any;
  laborSourceId: any;
  laborName: any;
  productionRate: any;
  calculatedUnitRateUsdCents: number;
  id: string;
  sourceType: string;
  name: string;
  inputUOM: string;
  outputUOM: string;
}

interface KnownUom {
  uom: string;
  materialRateUsdCents: number;
  laborRateUsdCents: any;
  burdenedLaborRateUsdCents: any;
  productionRate: any;
  calculatedUnitRateUsdCents: number;
}

interface PageInfo {
  hasNextPage: boolean;
}

interface DataLocation {
  countyName: string;
  stateName: string;
}

interface IEsUpdateBatchApiParam {
  estimate_id?: string;
  is_checked?: 1 | 0;
  batches?: {
    document_id?: string;
    display_order?: number;
  }[];
  module_batch_ids?: number[];
}

interface ESBiddingParams {
  estimate_id: string | undefined | number;
  bidding_id?: string | number;
  item_ids?: number[];
  apply_bid_price?: 0 | 1;
  send_bid_notification?: 0 | 1;
  bidder_status?: {
    bidder_id?: number;
    bid_status?: number | string;
  }[];
}

interface ESBiddingDeleteParams {
  estimate_id: string | undefined;
  bidding_ids?: number[];
}

interface ESBidderDeleteParams {
  estimate_id: string | undefined;
  bidder_ids?: number[];
}

interface IEstimatesBiddingOneApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: BiddingObject;
  refresh_type?: string;
}

interface ESBiddingAddParams extends ESBiddingParams {
  title: string;
  deadline_date: string;
  deadline_time: string;
  bid_status: number | string;
  bid_status_key: string;
  bid_manager_id: string;
  reminder_days: string;
  scope_of_work: string;
  terms: string;
  inclusion: string;
  exclusion: string;
  clarification: string;
  bidders: Bidder[];
  files?: IFile[];
  aws_files?: any;
  attach_image?: string;
  removed_items?: string[];
  bid_items?: BidItem[];
  estimate_id?: string | number;
  items?: BidItem[];
  dontCloseDrawer?: Boolean;
}

interface BidItem {
  bid_item_id: number;
  estimate_id: number;
  estimate_item_id: number;
  bidding_id: number;
  user_id: number;
  company_id: number;
  cost_code_id: number;
  subject: string;
  quantity: number;
  unit: string;
  description: string;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  item_type: number;
  reference_item_id: number;
  assigned_to: number;
  internal_notes: string | null;
  is_project_template: number;
  project_template_id: number | null;
  item_type_display_name: string;
  item_type_name: string;
  item_type_key: string;
  cost_code_name: string | null;
  cost_code: string | null;
  code_id: number | null;
  bid_submitted_price?: any;
  is_added_item: 0 | 1;
}

interface BiddingObject {
  bidding_id: number;
  estimate_id: number;
  title: string;
  deadline_date: string;
  deadline_time: string;
  reminder_days: number;
  bid_status: number;
  bid_manager_id: number;
  scope_of_work: string;
  terms: string;
  inclusion: string;
  exclusion: string;
  clarification: string;
  user_id: number;
  company_id: number;
  date_added: string;
  date_modified: string;
  is_project_template: number;
  project_template_id: number | null;
  parent_bidding_id: number;
  project_id: number;
  bid_manager_name: string;
  bid_manager_phone: string;
  bid_manager_company_name: string;
  bid_status_name: string;
  bid_status_key: string;
  bid_manager_email: string;
  deadline_date_global_format: string;
  deadline: string;
  bid_manage_short_name: string;
  items: BidItem[];
}
