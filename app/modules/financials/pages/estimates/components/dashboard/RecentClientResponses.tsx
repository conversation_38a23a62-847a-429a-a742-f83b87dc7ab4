// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
// Hook
import { useNavigate, useSearchParams } from "@remix-run/react";
import { useEffect, useState } from "react";
import { useTranslation } from "~/hook";
import { routes } from "~/route-services/routes";
import { fetchDashData } from "../../redux/action/dashboardAction";
import { useAppESDispatch, useAppESSelector } from "../../redux/store";
import { sanitizeString } from "~/helpers/helper";

const RecentClientResponses = () => {
  const { _t } = useTranslation();
  const navigate = useNavigate();
  const {
    isDashLoading,
    clientResentResponse,
    clientResentResponseLastRefreshTime,
  }: IEstimatesDashState = useAppESSelector((state) => state.dashboard);

  const dispatch = useAppESDispatch();

  const [isLoading, setIsLoading] = useState(false);
  const [rowData, setRowData] = useState<IClientRecentResponse[]>([]);
  const [serachParams, setSearchParams] = useSearchParams();
  const { isComponent } = useAppESSelector((state) => state.frameComponent);
  const handleRefreshWidget = async () => {
    setIsLoading(true);
    setRowData([]);
    await dispatch(fetchDashData({ refreshType: "client_resent_response" }));
    setIsLoading(false);
  };

  useEffect(() => {
    if (!isLoading && clientResentResponse) {
      setRowData(clientResentResponse);
    }
  }, [clientResentResponse, isLoading]);

  const columnDefs = [
    {
      headerName: _t("Estimate") + " #",
      field: "estimate_id",
      minWidth: rowData?.length > 0 ? 100 : 87,
      maxWidth: rowData?.length > 0 ? 100 : 87,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: { data: IClientRecentResponse }) => {
        const id = HTMLEntities.decode(
          sanitizeString(data?.company_estimate_id || "-")
        );
        return (
          <Tooltip title={id}>
            <Typography className="table-tooltip-text">{id || "-"}</Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Title"),
      field: "subject",
      minWidth: rowData?.length > 0 ? 120 : 80,
      flex: 1,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: { data: IClientRecentResponse }) => {
        const title = HTMLEntities.decode(sanitizeString(data?.subject || "-"));

        return (
          <Tooltip title={title}>
            <Typography className="table-tooltip-text">
              {title || "-"}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Customer"),
      field: "customer",
      maxWidth: 90,
      minWidth: 90,
      suppressMenu: true,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      cellRenderer: ({ data }: { data: IClientRecentResponse }) => {
        const customerName = HTMLEntities.decode(
          sanitizeString(data?.full_name || "-")
        );

        return data?.full_name?.trim()?.length ? (
          <Tooltip title={customerName}>
            <div className="w-fit mx-auto overflow-hidden">
              <AvatarProfile
                user={{
                  name: customerName,
                  image: data.client_icon,
                }}
                className="m-auto"
                iconClassName="text-[11px] font-semibold"
              />
            </div>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Status"),
      field: "status",
      minWidth: rowData?.length > 0 ? 100 : 80,
      maxWidth: rowData?.length > 0 ? 100 : 80,
      suppressMenu: true,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      cellRenderer: ({ data }: { data: IClientRecentResponse }) => {
        const status = data.status || "-";
        const statusBgColor =
          data.status === "Approved" ? "#006A1E1d" : "#FF452A1d";
        const statusTextColor =
          data.status === "Approved" ? "!text-[#006A1E]" : "!text-[#FF452A]";
        return (
          <Tooltip title={status}>
            <div className="text-center overflow-hidden">
              <Tag
                color={statusBgColor}
                className={` mx-auto text-13 type-badge common-tag ${statusTextColor}`}
              >
                {status || "-"}
              </Tag>
            </div>
          </Tooltip>
        );
      },
    },
  ];
  const noRowsOverlay = () => <StaticTableRowLoading columnDefs={columnDefs} />;
  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-CO-recent-client.svg`}
    />
  );
  return (
    <>
      <DashboardCardHeader
        title={_t("Recent Client Responses")}
        showRefreshIcon={true}
        refreshIconTooltip={clientResentResponseLastRefreshTime}
        onClickRefresh={handleRefreshWidget}
        isRefreshing={isLoading}
      />
      <div className="py-2 px-2.5">
        <div className="ag-theme-alpine h-[209px]">
          <StaticTable
            // onCellClicked={(params: CellClickedEvent) => {
            //   if (!isComponent) {
            //     navigate(
            //       `${routes.MANAGE_ESTIMATE.url}/${params?.data?.estimate_id}`
            //     );
            //   } else {
            //     setSearchParams({})
            //   }
            // }}
            className="static-table ag-grid-cell-pointer "
            columnDefs={columnDefs}
            key={isDashLoading ? "loading" : "loaded"}
            rowData={rowData ?? []}
            noRowsOverlayComponent={
              (isDashLoading || isLoading) && rowData?.length === 0
                ? noRowsOverlay
                : rowData.length === 0
                ? noData
                : null
            }
            enableOpenInNewTab={window.ENV.ENABLE_ALL_CLICK}
            generateOpenInNewTabUrl={(data: { estimate_id?: number }) =>
              `${routes.MANAGE_ESTIMATE.url}/${data?.estimate_id}`
            }
          />
        </div>
      </div>
    </>
  );
};

export default RecentClientResponses;
