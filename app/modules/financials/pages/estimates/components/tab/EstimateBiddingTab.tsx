import {
  FirstDataRenderedEvent,
  GetDetailRowDataParams,
  RowGroupOpenedEvent,
} from "ag-grid-community";
import { Tooltip, Typography } from "antd";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { getApiData } from "~/helpers/axios-api-helper";
import { getApiDefaultParams, sanitizeString } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import { apiRoutes } from "~/route-services/routes";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
// molecules
import { AddButton } from "~/shared/components/molecules/addButton";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { AgGridReact } from "ag-grid-react";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { getModuleAccess } from "~/shared/utils/helper/module";
import { getGSettings } from "~/zustand";
import { useGlobalMenuModule } from "~/zustand/global/menuModules/slice";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { getGlobalUser } from "~/zustand/global/user/slice";
import {
  deleteEstBidder,
  deleteEstBidding,
  getEstBidding,
} from "../../redux/action/ESBiddingAction";
import {
  deleteBidder,
  deleteBidding,
  updateSendEmailBidding,
} from "../../redux/slices/ESBiddingSlice";
import { useAppESDispatch, useAppESSelector } from "../../redux/store";
import { BIDDERS_BID_STATUS } from "../../utils/constants";
import CreateBidPackage from "./sidebar/createBidPackage/CreateBidPackage";

dayjs.extend(utc);

const EstimateBidding = (props: IEReadOnlyComponent) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const { date_format }: GSettings = getGSettings();
  const [createBid, setCreateBid] = useState<TCreateBidTabsValues | "">("");
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [biddingId, setBiddingId] = useState<number>();
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [confirmBiddingDialogOpen, setConfirmBiddingDialogOpen] =
    useState<boolean>(false);
  const [confirmBidderDialogOpen, setConfirmBidderDialogOpen] =
    useState<boolean>(false);
  const [confirmSendBidInviteDialogOpen, setConfirmSendBidInviteDialogOpen] =
    useState<boolean>(false);
  const [selectedBidderForInvite, setSelectedBidderForInvite] =
    useState<Bidder | null>(null);
  const [isLoadingSendMail, setIsLoadingSendMail] = useState<boolean>(false);
  const [selectedBid, setSelectedBid] = useState<Partial<ESEstimateBidding>>(
    {}
  );
  const { estimateDetail = {} } = useAppESSelector(
    (state) => state.estimateDetail
  );
  const [selectedBidderDetails, setSelectedBidderDetails] = useState<
    Partial<Bidder>
  >({});
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const scrollPosition = useRef({ top: 0, left: 0 });
  const dispatch = useAppESDispatch();
  const { biddingList, isLoading, biddingStatus } = useAppESSelector(
    (state) => state.biddingDetails
  );
  const [isActiveTable, setIsActiveTable] = useState<string[]>([]);
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());
  const gridRef = useRef<AgGridReact>(null);
  const { getGlobalModuleByKey } = useGlobalModule();
  const { checkGlobalMenuModulePermissionByKey } = useGlobalMenuModule();

  const bidManagerModule = useMemo(
    () => getGlobalModuleByKey(CFConfig.estimate_bidding_module),
    [getGlobalModuleByKey]
  );
  const bidManagerModuleAccess = useMemo(
    () => getModuleAccess(bidManagerModule),
    [bidManagerModule]
  );
  const biddingMenuPermission = checkGlobalMenuModulePermissionByKey(
    CFConfig.estimate_bidding_module
  );

  const bidManagerModuleNoAccess = useMemo(
    () =>
      bidManagerModuleAccess === "read_only" ||
      bidManagerModuleAccess === "no_access" ||
      biddingMenuPermission === "disabled",
    []
  );

  const isReadOnly = props.isReadOnly || bidManagerModuleNoAccess;

  const statusColorMap = biddingStatus?.reduce(
    (
      acc: { [key: string]: { default_color: string; text_color: string } },
      status: EstimateBidStatus
    ) => {
      const keys: string = status?.key;
      acc[keys] = {
        default_color: status?.default_color ?? "#000000",
        text_color: status?.text_color ?? "#000000",
      };
      return acc;
    },
    {}
  );

  useEffect(() => {
    dispatch(
      getEstBidding({
        estimate_id: estimateDetail?.estimate_id?.toString(),
      })
    );
  }, []);

  // Save the current scroll position
  const saveScrollPosition = () => {
    const gridApi = gridRef?.current?.api;
    if (gridApi) {
      scrollPosition.current = {
        top: gridApi?.getVerticalPixelRange()?.top,
        left: gridApi?.getHorizontalPixelRange()?.left,
      };
    }
  };

  // Restore the scroll position
  const restoreScrollPosition = () => {
    const gridApi = gridRef?.current?.api;
    if (gridApi) {
      gridApi?.ensureIndexVisible(scrollPosition?.current?.top, "top");
      gridApi?.ensureColumnVisible(scrollPosition?.current?.left?.toString());
    }
  };

  const handleSendBidInvite = (bidder: Bidder) => {
    setSelectedBidderForInvite(bidder);
    setConfirmSendBidInviteDialogOpen(true);
  };

  const confirmSendBidInvite = async () => {
    if (selectedBidderForInvite) {
      await sendEmailToSingleBidder(selectedBidderForInvite);
    }
    setSelectedBidderForInvite(null);
  };

  async function sendEmailToSingleBidder(bidderDetail: Bidder) {
    // Save the current scroll position
    saveScrollPosition();
    setIsLoadingSendMail(true);
    getApiData({
      url: apiRoutes.SERVICE.url,
      method: "post",
      data: getApiDefaultParams({
        op: "send_estimate_bidding_notification",
        user,
        otherParams: {
          estimate_id: estimateDetail?.estimate_id,
          bidder_id: bidderDetail?.bidder_id,
          bidding_id: bidderDetail?.bidding_id,
        },
      }),
      success: (response: { success: string; message: string }) => {
        if (response?.success === "1") {
          // dispatch(updateSendEmailBidding)
          const currentDate = dayjs()?.utc()?.format(date_format);
          const curretnTime = dayjs()?.utc()?.format("hh:mm A");
          dispatch(
            updateSendEmailBidding({
              bidding_id: bidderDetail?.bidding_id,
              bidder_id: bidderDetail?.bidder_id,
              data: {
                sent_date: currentDate,
                sent_time: curretnTime,
              },
            })
          );
          // Restore the scroll position after the Redux update
          restoreScrollPosition();
          dispatch(
            getEstBidding({
              estimate_id: estimateDetail?.estimate_id?.toString(),
            })
          );
          // notification.success({
          //   description: response?.message,
          // });
          setIsLoadingSendMail(false);
          setConfirmSendBidInviteDialogOpen(false);
        } else {
          notification.error({
            description: response?.message,
          });
          setIsLoadingSendMail(false);
          setConfirmSendBidInviteDialogOpen(false);
        }
      },
      error: (description) => {
        notification.error({
          description,
        });
        setIsLoadingSendMail(false);
        setConfirmSendBidInviteDialogOpen(false);
      },
    });
  }

  const columnDefs = [
    {
      headerName: "",
      field: "",
      cellRenderer: "agGroupCellRenderer",
      suppressMenu: true,
      cellClass: "no-space-td expand-icon",
      maxWidth: 32,
      minWidth: 32,
      flex: 1,
      sortable: false,
    },
    {
      headerName: _t("Bid Title"),
      field: "bid_title",
      minWidth: biddingList?.length > 0 ? 410 : undefined,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: { data: ESEstimateBidding }) => {
        const itemName = data?.title ?? "-";
        return (
          <Tooltip title={itemName}>
            <Typography className="table-tooltip-text">
              {itemName || "-"}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      field: _t("Deadline"),
      minWidth: biddingList?.length > 0 ? 240 : undefined,
      maxWidth: 240,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ data }: { data: ESEstimateBidding }) => {
        return data?.deadline_date ? (
          <DateTimeCard
            format="datetime"
            date={data?.deadline_date}
            time={data?.deadline_time}
          />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Manager"),
      field: "unit_cost",
      minWidth: biddingList?.length > 0 ? 130 : undefined,
      flex: 2,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      suppressMovable: false,
      suppressMenu: true,
      cellEditor: "agNumberCellEditor",
      cellEditorParams: {
        maxLength: 20,
      },
      cellRenderer: ({ data }: { data: ESEstimateBidding }) => {
        const bidManagerName = HTMLEntities.decode(
          sanitizeString(data?.bid_manager_name ?? "-")
        );
        return bidManagerName ? (
          <Tooltip title={bidManagerName}>
            <div className="flex items-center gap-2 overflow-hidden max-w-full w-fit">
              <AvatarProfile
                user={{
                  name: bidManagerName,
                  image: data?.user_image || "",
                }}
                iconClassName="text-[11px] font-semibold"
              />
              <Typography className="table-tooltip-text !max-w-[calc(100%-32px)]">
                {bidManagerName}
              </Typography>
            </div>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      field: _t("Status"),
      minWidth: biddingList?.length > 0 ? 180 : undefined,
      maxWidth: 180,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ data }: { data: ESEstimateBidding }) => {
        const itemName = data?.bid_status_name ?? "-";
        const textColor =
          statusColorMap?.[data?.bid_status_key]?.default_color ?? "#000000";
        const color = textColor + "1d";

        return itemName ? (
          <Tooltip title={itemName}>
            <div className="text-center overflow-hidden">
              <Tag
                color={color}
                style={{
                  color: `${textColor || ""}`,
                }}
                className="mx-auto text-13 type-badge common-tag max-w-24"
              >
                {itemName}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: "",
      field: "",
      maxWidth: 120,
      minWidth: 120,
      suppressMenu: true,
      suppressMovable: true,
      cellClass: "ag-cell-center",
      headerClass: "ag-header-center",
      cellRenderer: ({ data }: { data: ESEstimateBidding }) => {
        return (
          <div className="flex items-center gap-1.5 justify-center w-[100px]">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={() => {
                setBiddingId(data.bidding_id);
                setCreateBid("details");
                setDrawerOpen(true);
                setIsEdit(true);
              }}
            />

            {!isReadOnly && (
              <ButtonWithTooltip
                tooltipTitle={_t("Delete")}
                tooltipPlacement="top"
                icon="fa-regular fa-trash-can"
                onClick={() => {
                  setSelectedBid(data);
                  setConfirmBiddingDialogOpen(true);
                }}
              />
            )}
          </div>
        );
      },
    },
  ];

  const detailCellRendererParams = {
    detailGridOptions: {
      suppressDragLeaveHidesColumns: true,
      columnDefs: [
        {
          headerName: _t("Company Name"),
          field: "bidder_company_name",
          suppressMovable: false,
          suppressMenu: true,
          maxWidth: 200,
          minWidth: 200,
          sortable: false,
          headerClass: "ag-header-left",
          cellClass: "ag-cell-left",
          cellRenderer: ({ data }: { data: Bidder }) => {
            const bidderCmpName = HTMLEntities.decode(
              sanitizeString(data?.bidder_company_name)
            );
            return bidderCmpName ? (
              <Tooltip title={bidderCmpName}>
                <Typography className="table-tooltip-text">
                  {bidderCmpName}
                </Typography>
              </Tooltip>
            ) : (
              <>-</>
            );
          },
        },
        {
          headerName: _t("Name"),
          field: "bidder_name",
          suppressMovable: false,
          suppressMenu: true,
          minWidth: 200,
          maxWidth: 200,
          sortable: false,
          headerClass: "ag-header-left",
          cellClass: "ag-cell-left",
          cellRenderer: ({ data }: { data: Bidder }) => {
            const bidderName = HTMLEntities.decode(
              sanitizeString(data?.bidder_name_only)
            );
            return (
              <Tooltip title={bidderName}>
                <Typography className="table-tooltip-text">
                  {bidderName || "-"}
                </Typography>
              </Tooltip>
            );
          },
        },
        {
          headerName: _t("Date Sent"),
          field: "date_added",
          suppressMovable: false,
          suppressMenu: true,
          sortable: false,
          minWidth: 240,
          maxWidth: 240,
          cellClass: "ag-cell-left",
          headerClass: "ag-header-left",
          cellRenderer: ({ data }: { data: Bidder }) => {
            return data?.sent_date ? (
              <DateTimeCard
                format="datetime"
                date={data?.sent_date}
                time={data?.sent_time ?? undefined}
              />
            ) : (
              "-"
            );
          },
        },
        {
          headerName: _t("Will Submit"),
          field: "submit_status_name",
          suppressMovable: false,
          suppressMenu: true,
          sortable: false,
          minWidth: 120,
          maxWidth: 120,
          cellClass: "ag-cell-left",
          headerClass: "ag-header-left",
          cellRenderer: ({ data }: { data: Bidder }) => {
            const willSubmit = data?.submit_status_name;
            return (
              <Tooltip title={willSubmit}>
                <Typography className="table-tooltip-text">
                  {willSubmit || "-"}
                </Typography>
              </Tooltip>
            );
          },
        },
        {
          headerName: _t("Invited"),
          field: "sent_date",
          suppressMovable: false,
          suppressMenu: true,
          sortable: false,
          minWidth: 135,
          maxWidth: 135,
          cellClass: "ag-cell-left",
          headerClass: "ag-header-left",
          cellRenderer: ({ data }: { data: Bidder }) => {
            const invitedDate = data?.sent_date ?? undefined;
            return invitedDate ? (
              <DateTimeCard format="date" date={invitedDate} />
            ) : (
              "-"
            );
          },
        },
        {
          headerName: _t("Submitted"),
          field: "submitted_date",
          suppressMovable: false,
          suppressMenu: true,
          sortable: false,
          minWidth: 150,
          maxWidth: 150,
          cellClass: "ag-cell-left",
          headerClass: "ag-header-left",
          cellRenderer: ({ data }: { data: Bidder }) => {
            const bidderName = data?.submitted_date ?? undefined;
            return bidderName ? (
              <DateTimeCard format="date" date={bidderName} />
            ) : (
              "-"
            );
          },
        },
        {
          headerName: _t("Total"),
          field: "bid_amount",
          suppressMovable: false,
          suppressMenu: true,
          sortable: false,
          minWidth: 150,
          cellClass: "ag-cell-right",
          headerClass: "ag-header-right",
          cellRenderer: ({ data }: { data: Bidder }) => {
            const bidderName = formatter(
              (+(data?.bid_amount ?? 0) / 100)?.toFixed(2)
            )?.value_with_symbol;
            return (
              <Tooltip title={bidderName}>
                <Typography className="table-tooltip-text">
                  {bidderName || "-"}
                </Typography>
              </Tooltip>
            );
          },
        },
        {
          headerName: _t("Status"),
          field: "bid_status_name",
          suppressMovable: false,
          suppressMenu: true,
          sortable: false,
          minWidth: 170,
          maxWidth: 170,
          cellClass: "ag-cell-center",
          headerClass: "ag-header-center",
          cellRenderer: ({ data }: { data: Bidder }) => {
            const status = data?.bid_status_name;
            const color = BIDDERS_BID_STATUS.find(
              (status) => status.label === data?.bid_status_name
            )?.color;
            return status ? (
              <Tooltip title={status}>
                <div className="text-center overflow-hidden">
                  <Tag
                    color={color + "1d"}
                    className={`mx-auto text-13 type-badge common-tag max-w-24 ${
                      color ? "" : "!text-primary-900"
                    }
                    `}
                    style={{ color: color }}
                  >
                    {status}
                  </Tag>
                </div>
              </Tooltip>
            ) : (
              <>-</>
            );
          },
        },
        {
          headerName: "",
          field: "",
          maxWidth: 100,
          minWidth: 100,
          suppressMenu: true,
          suppressMovable: true,
          cellClass: "ag-cell-right",
          headerClass: "ag-header-right",
          cellRenderer: ({ data }: { data: Bidder }) => {
            const findBidDetails = biddingList.find(
              (item) => item.bidding_id === data.bidding_id
            );
            const isSendEmailEnabled =
              estimateDetail?.approval_type !== "estimate_lost" &&
              (findBidDetails?.bid_status === 298 ||
                findBidDetails?.bid_status === 299);

            return (
              <div className="flex items-center gap-1.5 justify-center">
                <ButtonWithTooltip
                  tooltipTitle={_t(
                    isSendEmailEnabled
                      ? _t(
                          `${
                            data?.sent_date ? "Resend" : "Send"
                          } bid invitation`
                        )
                      : estimateDetail?.approval_type === "estimate_lost"
                      ? _t(
                          "Can't send Bid Invite email because this estimate status is Lost."
                        )
                      : _t(
                          "The Send Invite option is available once the Bid Package is in Final or Submitted status."
                        )
                  )}
                  disabled={!isSendEmailEnabled || isReadOnly}
                  tooltipPlacement="top"
                  icon="fa-regular fa-envelope"
                  onClick={() => {
                    handleSendBidInvite(data);
                  }}
                />

                {!isReadOnly && findBidDetails?.bid_status === 297 && (
                  <ButtonWithTooltip
                    tooltipTitle={_t("Delete")}
                    tooltipPlacement="top"
                    icon="fa-regular fa-trash-can"
                    onClick={() => {
                      setSelectedBidderDetails(data);
                      setConfirmBidderDialogOpen(true);
                    }}
                  />
                )}
              </div>
            );
          },
        },
      ],
      noRowsOverlayComponent: () => (
        <NoRecords
          image={`${window.ENV.CDN_URL}assets/images/no-bid-responses.svg`}
        />
      ),
      defaultColDef: { flex: 1 },
    },
    getDetailRowData: (params: GetDetailRowDataParams) => {
      params.successCallback(params.data.bidders ?? []);
    },
  };

  useEffect(() => {
    setIsActiveTable([biddingList?.length > 0 ? "1" : "0"]);
  }, [biddingList]);

  const handleDeleteBiddingData = async () => {
    setIsDeleting(true);
    if (selectedBid?.bidding_id) {
      const response = await deleteEstBidding({
        estimate_id: estimateDetail?.estimate_id?.toString(),
        bidding_ids: [selectedBid?.bidding_id],
      });
      if (response.success) {
        dispatch(deleteBidding(selectedBid?.bidding_id));
        setSelectedBid({});
        setConfirmBiddingDialogOpen(false);
      } else {
        notification.error({
          description: response?.message,
        });
      }
    }
    setConfirmBiddingDialogOpen(false);
    setIsDeleting(false);
  };
  const handleDeleteBidderData = async () => {
    if (selectedBidderDetails?.bidder_id && selectedBidderDetails?.bidding_id) {
      const response = await deleteEstBidder({
        estimate_id: estimateDetail?.estimate_id?.toString(),
        bidder_ids: [selectedBidderDetails?.bidder_id],
      });
      if (response.success) {
        dispatch(
          deleteBidder({
            bidder_id: selectedBidderDetails?.bidder_id,
            bidding_id: selectedBidderDetails?.bidding_id,
          })
        );
        setSelectedBidderDetails({});
        setConfirmBidderDialogOpen(false);
      }
    }
  };

  const onFirstDataRendered = useCallback(
    (params: FirstDataRenderedEvent) => {
      expandedRows?.forEach((rowId) => {
        params?.api?.getDisplayedRowAtIndex(rowId)?.setExpanded(true);
      });
    },
    [expandedRows]
  );

  const onRowGroupOpened = useCallback((event: RowGroupOpenedEvent) => {
    const rowId = event?.node?.rowIndex ?? 0;
    setExpandedRows((prev) => {
      const newSet = new Set(prev);
      if (event?.node?.expanded) {
        newSet?.add(rowId);
      } else {
        newSet?.delete(rowId);
      }
      return newSet;
    });
  }, []);

  useEffect(() => {
    const gridApi = gridRef?.current?.api;
    if (gridApi) {
      expandedRows?.forEach((rowId) => {
        gridApi?.getDisplayedRowAtIndex(rowId)?.setExpanded(true);
      });
      gridApi?.ensureIndexVisible(scrollPosition?.current?.top, "top");
      gridApi?.ensureColumnVisible(scrollPosition?.current?.left?.toString());
    }
  }, [biddingList, expandedRows]);

  return (
    <>
      <div className="grid  gap-2.5">
        <CollapseSingleTable
          title={_t("Bidding")}
          extraInfo
          // className="move-collapse-table"
          rightsideContant={
            !isReadOnly && (
              <div className="flex items-center gap-1">
                <AddButton
                  onClick={() => {
                    setCreateBid("details");
                    setDrawerOpen(true);
                    setIsEdit(false);
                  }}
                >
                  Bid Package
                </AddButton>
              </div>
            )
          }
          activeKey={isActiveTable}
          onChange={(key: string | string[]) => {
            setIsActiveTable(Array.isArray(key) ? key : [key]);
          }}
          children={
            <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
              <div className="ag-theme-alpine">
                <StaticTable
                  className="multi-list-table static-multi-table static-table"
                  columnDefs={columnDefs}
                  rowData={biddingList}
                  masterDetail={true}
                  defaultColDef={{
                    flex: 1,
                  }}
                  // detailRowAutoHeight={true}
                  detailCellRendererParams={detailCellRendererParams}
                  ref={gridRef}
                  onFirstDataRendered={onFirstDataRendered}
                  onRowGroupOpened={onRowGroupOpened}
                  icons={{
                    groupExpanded: `<div class="ag-icon-tree-open cell-renderer-icon relative">
                        <div class="ant-tooltip custom-tooltip tooltip-placement-right left-6">
                          <div class="ant-tooltip-arrow absolute"></div>
                          <div class="ant-tooltip-content relative">
                            <div class="ant-tooltip-inner" role="tooltip">Collapse</div>
                          </div>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 512 512"><path d="M233.4 105.4c12.5-12.5 32.8-12.5 45.3 0l192 192c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L256 173.3 86.6 342.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l192-192z"/></svg>
                      </div>
                    `,
                    groupContracted: `<div class="ag-icon-tree-closed cell-renderer-icon relative">
                        <div class="ant-tooltip custom-tooltip tooltip-placement-right left-6">
                          <div class="ant-tooltip-arrow absolute"></div>
                          <div class="ant-tooltip-content relative">
                            <div class="ant-tooltip-inner" role="tooltip">Expand</div>
                          </div>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 512 512"><path d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"/></svg>
                      </div>
                    `,
                  }}
                  noRowsOverlayComponent={() => (
                    <NoRecords
                      image={`${window.ENV.CDN_URL}assets/images/no-bid-responses.svg`}
                    />
                  )}
                  // Use `getRowHeight` for dynamic row height calculation
                  getRowHeight={(params) => {
                    if (params.node && params.node.detail) {
                      // Detail row height calculation
                      const biddingList = params.data?.bidders ?? [];
                      if (biddingList?.length) {
                        return biddingList?.length * 42.4 + 64; // 42 is row height and 64 is header height
                      } else {
                        return 210;
                      }
                    }
                    // Default height for main rows
                    return undefined; // Default height for parent rows
                  }} // Attach dynamic row height
                />
              </div>
            </div>
          }
        />
      </div>

      {/* Estimates  >  Bidding tab > Add/Edit Bid Package */}
      {drawerOpen && (
        <CreateBidPackage
          isReadOnly={isReadOnly}
          setCreateBid={setCreateBid}
          createBid={createBid}
          setDrawerOpen={setDrawerOpen}
          drawerOpen={drawerOpen}
          isEdit={isEdit}
          biddingId={biddingId}
        />
      )}
      {confirmSendBidInviteDialogOpen && (
        <ConfirmModal
          isOpen={confirmSendBidInviteDialogOpen}
          isLoading={isLoadingSendMail}
          modaltitle={`${
            selectedBidderForInvite?.sent_date ? "Resend" : "Send"
          } Bid Invitation`}
          description={`Do you want to ${
            selectedBidderForInvite?.sent_date ? "resend" : "send"
          } a bid invite to this contractor?`}
          onAccept={confirmSendBidInvite}
          onDecline={() => setConfirmSendBidInviteDialogOpen(false)}
          onCloseModal={() => setConfirmSendBidInviteDialogOpen(false)}
          descriptionclassName="flex text-center justify-center"
          modalIcon="fa-regular fa-envelope"
          zIndex={9999}
        />
      )}
      <ConfirmModal
        isOpen={confirmBiddingDialogOpen}
        modaltitle={_t("Delete")}
        isLoading={isDeleting}
        description="Are you sure you want to delete this Bidding?"
        onAccept={() => {
          handleDeleteBiddingData();
        }}
        onDecline={() => {
          setSelectedBid({});
          setConfirmBiddingDialogOpen(false);
        }}
        onCloseModal={() => {
          setSelectedBid({});
          setConfirmBiddingDialogOpen(false);
        }}
        descriptionclassName="flex text-center justify-center"
        modalIcon="fa-regular fa-trash-can"
        zIndex={999}
      />
      <ConfirmModal
        isOpen={confirmBidderDialogOpen}
        modaltitle={_t("Delete")}
        description="Are you sure you want to delete this Bidder?"
        onAccept={() => {
          handleDeleteBidderData();
        }}
        onDecline={() => {
          setSelectedBidderDetails({});
          setConfirmBidderDialogOpen(false);
        }}
        onCloseModal={() => {
          setSelectedBidderDetails({});
          setConfirmBidderDialogOpen(false);
        }}
        descriptionclassName="flex text-center justify-center"
        modalIcon="fa-regular fa-trash-can"
        zIndex={9999}
      />
    </>
  );
};

export default EstimateBidding;
