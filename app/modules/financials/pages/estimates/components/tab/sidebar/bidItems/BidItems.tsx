import {
  IRowNode,
  SelectionChangedEvent,
  FirstDataRenderedEvent,
} from "ag-grid-community";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "~/hook";
import { ICON_MAP } from "~/modules/financials/pages/changeOrder/components/sidebar/utils";
// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// molecules
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { StaticTable } from "~/shared/components/molecules/staticTable";
// Other
import { getOneEstBidding } from "../../../../redux/action/ESBiddingAction";
import { getEstimateItems } from "../../../../redux/action/ESItemAction";
import { useAppESDispatch, useAppESSelector } from "../../../../redux/store";
import { isValidId } from "../../../../utils/common";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import { getGModuleDashboard } from "~/zustand";
import { sanitizeString } from "~/helpers/helper";
import { generateCostCodeLabel } from "~/shared/utils/helper/common";
const BidItems = ({
  bidItems,
  setBidItems,
  formik,
  biddingId,
  isEdit,
}: IBidItemsProps) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const { estimateDetail = {} } = useAppESSelector(
    (state) => state.estimateDetail
  );
  const { sections } = useAppESSelector((state) => state.estimateItems);
  const dispatch = useAppESDispatch();
  const [sectionWiseSelectedItems, setSectionWiseSelectedItems] = useState<
    Record<string, Partial<ESEstimateItem>>
  >({});
  const gModuleDashboard: Partial<GModuleDashboard> = getGModuleDashboard();
  const [selectedNodesOfItems, setSelectedNodesOfItems] = useState<Set<string>>(
    new Set()
  );
  const [isButtonDisabled, setButtonDisabled] = useState<boolean>(false);
  const { codeCostData } = useAppESSelector((state) => state.costCode);

  const filteredCodeCostData = codeCostData
    ?.filter(
      (item) =>
        (item?.cost_code_name?.toString() !== "" ||
          item?.csi_code?.toString() !== "") &&
        Number(item?.parent_id) > 0
    )
    ?.map((el) => ({
      ...el,
      // label: `${el?.cost_code_name}${
      //   el?.csi_code && el?.csi_code !== null ? ` (${el?.csi_code})` : ""
      // }${el?.is_deleted === 1 ? ` (Archived)` : ""}`,
      label: generateCostCodeLabel({
        name: el?.cost_code_name,
        code: el?.csi_code,
        isArchived: false,
        isAllowCodeWithoutName: true,
      }),
    }));
  function getColumnDefs(index: number) {
    return [
      {
        headerName: _t("Type"),
        maxWidth: 90,
        minWidth: 90,
        field: "item_type_name",
        suppressMenu: true,
        headerClass: "ag-header-center",
        cellClass: "ag-cell-center",
        checkboxSelection: (params) => {
          // Check if item is already in bid_items
          const isItemAlreadyAdded = formik?.values?.bid_items?.some(
            (item) => item.estimate_item_id === params.data.item_id
          );
          return !isItemAlreadyAdded; // Disable checkbox if item already added
        },
        // check if all items in the section are exist in bid_items
        // if yes then disable header checkbox
        // @TODO: optimize this logic later
        headerCheckboxSelection: !sections[index]?.items?.every((item) =>
          formik?.values?.bid_items?.some(
            (bidItem: ESEstimateItem) =>
              bidItem.estimate_item_id === item.item_id
          )
        ),
        cellRenderer: ({ data }: { data: ESEstimateItem }) => {
          const value = data?.item_type_name;
          const isItemAlreadyAdded = formik?.values?.bid_items?.some(
            (item) => item.estimate_item_id === data.item_id
          );

          return value ? (
            <Tooltip
              title={
                isItemAlreadyAdded
                  ? _t("Already added to bid items")
                  : _t(value)
              }
            >
              <FontAwesomeIcon
                className={`w-4 h-4 text-primary-900 flex mx-auto ${
                  isItemAlreadyAdded ? "opacity-50" : ""
                }`}
                icon={
                  ICON_MAP[value as keyof typeof ICON_MAP] ||
                  ICON_MAP["default"]
                }
              />
            </Tooltip>
          ) : (
            <>-</>
          );
        },
      },
      {
        headerName: _t("Item Name"),
        field: "subject",
        minWidth: 190,
        flex: 2,
        suppressMenu: true,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        cellRenderer: ({ data }: { data: ESEstimateItem }) => {
          const itemName = HTMLEntities.decode(sanitizeString(data?.subject));

          return itemName ? (
            <Tooltip title={itemName}>
              <Typography className="table-tooltip-text">{itemName}</Typography>
            </Tooltip>
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t("Cost Code"),
        field: "cost_code_name",
        minWidth: 190,
        flex: 2,
        suppressMenu: true,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        cellRenderer: ({ data }: { data: ESEstimateItem }) => {
          const costCode = generateCostCodeLabel({
            name: data?.cost_code_name || "",
            code: data?.cost_code,
            isArchived:
              data?.code_is_deleted == 1 ||
              filteredCodeCostData?.findIndex(
                (el) =>
                  el?.code_id?.toString() === data?.cost_code_id?.toString()
              ) == -1,
            isAllowCodeWithoutName: true,
          });
          return costCode ? (
            <Tooltip title={costCode}>
              <Typography className="table-tooltip-text">{costCode}</Typography>
            </Tooltip>
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t("QTY"),
        field: "quantity",
        maxWidth: 100,
        minWidth: 100,
        suppressMenu: true,
        headerClass: "ag-header-right",
        cellClass: "ag-cell-right",
        cellRenderer: ({ data }: { data: ESEstimateItem }) => {
          const quantity = data?.quantity ?? "";
          const quantityUnit = data.quantity?.toString().includes(".")
            ? formatter(Number(data.quantity).toFixed(2)).value || 0
            : formatter(Number(data.quantity).toFixed(0)).value;

          return quantity ? (
            <>
              <Tooltip title={!!quantity ? quantityUnit : ""}>
                <Typography className="table-tooltip-text">
                  {quantity ? quantityUnit : "-"}
                </Typography>
              </Tooltip>
            </>
          ) : (
            <Typography className="table-tooltip-text">-</Typography>
          );
        },
      },
      {
        headerName: _t("Unit"),
        field: "unit",
        maxWidth: 60,
        minWidth: 60,
        suppressMenu: true,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        cellRenderer: ({ data }: { data: ESEstimateItem }) => {
          const unit = data?.unit;
          return unit ? (
            <Tooltip title={unit}>
              <Typography className="table-tooltip-text">{unit}</Typography>
            </Tooltip>
          ) : (
            "-"
          );
        },
      },
    ];
  }

  // Helper function to handle selection changes
  const handleSelectionChanged = (event: SelectionChangedEvent): void => {
    const selectedNodes = event?.api
      ?.getSelectedNodes()
      ?.map((node) => node?.data);
    const selectedNodesObj = {};
    const selectedIds = new Set(
      selectedNodes?.map((node) => {
        selectedNodesObj[node?.item_id] = node;
        return node?.item_id;
      })
    );
    const allIds = new Set(
      event?.api?.getRenderedNodes()?.map((node) => node?.data?.item_id)
    );

    const unselectedIds = [...allIds].filter((id) => !selectedIds.has(id));

    setSelectedNodesOfItems((prev) => {
      const updatedSet = new Set(prev);
      selectedIds.forEach((id) => updatedSet.add(id));
      unselectedIds.forEach((id) => updatedSet.delete(id));
      return updatedSet;
    });

    setSectionWiseSelectedItems((prev) => ({
      ...prev,
      ...selectedNodesObj,
    }));
  };
  async function fetchBiddingData() {
    if (biddingId) {
      try {
        const response = await dispatch(
          getOneEstBidding({
            estimate_id: estimateDetail?.estimate_id,
            bidding_id: biddingId,
          })
        );
        if (response?.payload?.success) {
          const data = response?.payload?.data;
          formik?.setValues({
            ...data,
            bid_items: data?.items ?? [],
            bidders: data?.bidder ?? [],
          });
        } else {
          notification.error({
            description: response?.payload?.message,
          });
        }
      } catch (e: any) {
        notification.error({
          description: e?.message,
        });
      }
    }
  }

  const submitHandler = async () => {
    if (selectedNodesOfItems.size === 0) {
      notification.error({
        description: _t("Please select item to add."),
      });
      return;
    }
    setButtonDisabled(true);
    const selectedItemIds = new Set(selectedNodesOfItems);
    const bidItemIdsSet = new Set(
      formik?.values?.bid_items?.map(
        (el: { estimate_item_id: any }) => el?.estimate_item_id
      )
    );
    const preservedItems: ESEstimateItem[] = [];
    const needToRemove: ESEstimateItem[] = [];
    const needToAdd: ESEstimateItem[] = [];
    const needToRemoveBidItemIds: number[] = [];

    formik?.values?.bid_items?.forEach((el) => {
      if (selectedItemIds.has(el?.estimate_item_id)) {
        preservedItems.push(el);
      } else {
        needToRemove.push(el?.estimate_item_id);
        if (isValidId(el?.bid_item_id)) {
          needToRemoveBidItemIds.push(el?.bid_item_id);
        }
      }
    });

    selectedItemIds.forEach((estimate_item_id) => {
      if (!bidItemIdsSet.has(estimate_item_id)) {
        needToAdd.push({
          ...sectionWiseSelectedItems[estimate_item_id],
          description:
            gModuleDashboard?.module_setting?.bid_item_description_source ===
            "description"
              ? (sectionWiseSelectedItems?.[estimate_item_id]
                  ?.description as string)
              : "",
          estimate_item_id,
          is_added_item: 1,
          bid_item_id: 0,
        });
      }
    });

    if (isEdit) {
      if (needToRemove?.length) {
        const filterRemovedItems = formik?.values?.bid_items?.filter(
          (el) => !needToRemove?.includes(el.estimate_item_id)
        );
        formik?.setFieldValue("bid_items", filterRemovedItems);
        formik?.setFieldValue("removed_items", needToRemoveBidItemIds);

        // const response = await deleteEstBiddingItem({
        //   bidding_id: biddingId,
        //   estimate_id,
        //   item_ids: needToRemove?.map((el) => el?.bid_item_id),
        // });
        // if (response?.success) {
        //   fetchBiddingData();
        //   setBidItems(false);
        // } else {
        //   notification.error({
        //     description: response?.message,
        //   });
        // }
      }

      if (needToAdd?.length) {
        formik?.setFieldValue("bid_items", [...preservedItems, ...needToAdd]);
        //   const response = await updateEstBidding({
        //     bidding_id: biddingId,
        //     estimate_id,
        //     bid_items: needToAdd.map((el) => ({
        //       ...el,
        //       bid_item_id: 0,
        //     })),
        //   });
        //   if (response?.success) {
        //     fetchBiddingData();
        //     setBidItems(false);
        //   } else {
        //     notification.error({
        //       description: response?.message,
        //     });
        //   }
      }
      setBidItems(false);
    } else {
      formik?.setFieldValue("bid_items", [...preservedItems, ...needToAdd]);
      setBidItems(false);
    }
    setButtonDisabled(false);
  };

  const onFirstDataRendered = useCallback(
    (params: FirstDataRenderedEvent) => {
      const estimate_item_ids = formik?.values?.bid_items?.map(
        (item: { estimate_item_id: string | number }) => item?.estimate_item_id
      );

      const nodesToSelect: IRowNode[] = [];
      params?.api?.forEachNode((node: IRowNode) => {
        if (estimate_item_ids?.includes(node?.data?.item_id)) {
          nodesToSelect.push(node);
        }
      });

      params?.api?.setNodesSelected({ nodes: nodesToSelect, newValue: true });
    },
    [formik?.values?.bid_items]
  );

  useEffect(() => {
    if (!sections?.length) {
      dispatch(getEstimateItems({ estimate_id: estimateDetail?.estimate_id }));
    }
  }, [sections?.length, dispatch, estimateDetail?.estimate_id]);

  useEffect(() => {
    setSelectedNodesOfItems(
      new Set(
        formik?.values?.bid_items?.map((el) => el?.estimate_item_id) ?? []
      )
    );
    return () => {
      setSelectedNodesOfItems(new Set());
      setSectionWiseSelectedItems({});
    };
  }, [formik?.values?.bid_items]);

  const Sections = sections?.map((section, index) => (
    <CollapseSingleTable
      key={section?.section_id}
      title={HTMLEntities.decode(sanitizeString(section?.section_name ?? ""))}
      defaultActiveKey={section?.items?.length > 0 ? [1] : []}
      children={
        <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[33px] before:top-5 before:bg-gradient-to-r from-primary-500">
          <div className="ag-theme-alpine">
            <StaticTable
              gridId={section?.section_id?.toString()}
              className="static-table"
              rowMultiSelectWithClick
              suppressRowClickSelection
              onSelectionChanged={handleSelectionChanged}
              rowSelection="multiple"
              columnDefs={getColumnDefs(index)}
              onFirstDataRendered={onFirstDataRendered}
              rowData={section?.items}
              noRowsOverlayComponent={() => (
                <NoRecords
                  image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                />
              )}
            />
          </div>
        </div>
      }
    />
  ));

  return (
    <Drawer
      open={bidItems}
      rootClassName="drawer-open"
      width={750}
      classNames={{ body: "!p-0 !overflow-hidden" }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-magnifying-glass-dollar"
            />
          </div>
          <Header
            level={5}
            className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
          >
            {_t(`Bid Items`)}
          </Header>
        </div>
      }
      closeIcon={<CloseButton onClick={() => setBidItems(false)} />}
    >
      <div className="py-4">
        <div className="sidebar-body h-[calc(100dvh-132px)] overflow-y-auto px-4">
          {bidItems && Sections?.length > 0 ? (
            <div className="grid grid-col gap-3">{Sections}</div>
          ) : (
            <NoRecords
              className="h-full"
              image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
            />
          )}
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
          <PrimaryButton
            htmlType="submit"
            buttonText={_t("Save")}
            onClick={submitHandler || selectedNodesOfItems.size === 0}
            disabled={isButtonDisabled}
            isLoading={isButtonDisabled}
            tooltip={
              selectedNodesOfItems.size === 0
                ? _t("Please select item to add.")
                : ""
            }
          />
        </div>
      </div>
    </Drawer>
  );
};

export default BidItems;
