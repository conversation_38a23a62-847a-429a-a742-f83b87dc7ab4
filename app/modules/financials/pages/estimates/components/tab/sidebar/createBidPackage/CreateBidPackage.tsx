import { useCallback, useEffect, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Button } from "~/shared/components/atoms/button";
import { Drawer } from "~/shared/components/atoms/drawer";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Header } from "~/shared/components/atoms/header";
// Molecules
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { DrawerVericalOptions } from "~/shared/components/molecules/drawerVericalOptions";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { ProgressBarHeader } from "~/shared/components/molecules/ProgressBarHeader";
// Other
import {
  CREATE_BID_TAB_OPTIONS,
  ESTIMATE_BID__STATUS_ICON,
} from "../../../../utils/constants";

import dayjs from "dayjs";
import { useFormik } from "formik";
import * as Yup from "yup";
import { getDirectaryKeyById } from "~/components/sidebars/multi-select/customer/zustand/action";
import { sanitizeString } from "~/helpers/helper";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { getGlobalUser } from "~/zustand/global/user/slice";
import {
  addEstBidding,
  deleteEstBiddingItem,
  getEstBidding,
  getOneEstBidding,
  updateEstBidding,
} from "../../../../redux/action/ESBiddingAction";
import { useAppESDispatch, useAppESSelector } from "../../../../redux/store";
import { getUpdatedFields, parseDeadlineTime } from "../../../../utils/common";
import Bidders from "./tab/Bidders";
import Details from "./tab/Details";
import Files from "./tab/Files";
import Items from "./tab/Items";
import Submissions from "./tab/Submissions";
import Terms from "./tab/Terms";
// dayjs.extend(timezone);
// dayjs.extend(utc);
const CreateBidPackage = ({
  createBid,
  setCreateBid,
  options,
  setDrawerOpen,
  drawerOpen,
  isEdit,
  biddingId,
  isReadOnly = false,
}: ICreateBidPackageProps) => {
  const { _t } = useTranslation();
  const dispatch = useAppESDispatch();
  const [sideMenuOpen, setSideMenuOpen] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState({});
  const { biddingStatus, isBiddingStatusLaoding } = useAppESSelector(
    (state) => state?.biddingDetails
  );
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { timezone_utc_tz_id = "" } = user || {};
  const [activeStep, setActiveStep] = useState<string | number>("");
  const [currntTooltip, setCurrntTooltip] = useState<string | null>(null);
  const [selectedBidManager, setSelectedBidManager] =
    useState<TselectedContactSendMail | null>(null);
  const [bidApiData, setBidApiData] = useState<BiddingObject>();

  const isBidClosed = bidApiData?.bid_status_key === "est_bid_closed";
  const { estimateDetail = {} } = useAppESSelector(
    (state) => state.estimateDetail
  );

  const {
    user_id = 0,
    type = 0,
    first_name = "",
    last_name = "",
    company_name = "",
    display_name = "",
    email = "",
    image = "",
  } = user || {};

  async function addBidding(values: ESBiddingAddParams) {
    let updatedFields = { ...values };
    if (isEdit) {
      updatedFields = getUpdatedFields(bidApiData, values);
    }

    // if (values.bid_items?.length > 0) {
    //   values.bid_items = values.bid_items.map((item) => item.item_id);
    // }

    if (updatedFields?.deadline_time) {
      updatedFields.deadline_time =
        parseDeadlineTime(values.deadline_time)?.format("HH:mm:ss") || "";
    }
    let deadline_date;
    if (updatedFields.deadline_date) {
      deadline_date = dayjs(
        updatedFields.deadline_date,
        CFConfig.day_js_date_format
      )?.format("YYYY-MM-DD");
    }

    if (!isEdit && updatedFields?.files && updatedFields?.files?.length > 0) {
      updatedFields.aws_files = updatedFields?.files
        ?.filter((item) => item.image_id === undefined)
        ?.map((item) => {
          const { file_path, ...rest } = item;
          return {
            ...rest,
            file_url: file_path,
          };
        });
      updatedFields.attach_image = updatedFields?.files
        ?.filter((item) => item.image_id !== undefined)
        ?.map((item) => item.image_id)
        ?.join(",");
    }

    if (isEdit && values?.removed_items && values?.removed_items?.length > 0) {
      const response = await deleteEstBiddingItem({
        bidding_id: biddingId,
        estimate_id: estimateDetail?.estimate_id,
        item_ids: values?.removed_items,
      });
      if ((response as IDefaultAPIRes)?.success) {
        // fetchBiddingData();
        // setBidItems(false);
      } else {
        notification.error({
          description: (response as IDefaultAPIRes)?.message,
        });
      }
    }

    try {
      let response: IDefaultAPIRes;
      if (isEdit && biddingId) {
        const valuableObj = getValuableObj({
          ...updatedFields,
          estimate_id: estimateDetail?.estimate_id,
          reminder_days: values?.reminder_days,
          bidding_id: biddingId,
          bidders: null,
          deadline_date,
        });
        const finalPayload = {
          ...valuableObj,
          terms: values?.terms,
          inclusion: values?.inclusion,
          exclusion: values?.exclusion,
          clarification: values?.clarification,
        };
        response = (await updateEstBidding(finalPayload)) as IDefaultAPIRes;
      } else {
        response = await addEstBidding(
          getValuableObj({
            ...updatedFields,
            estimate_id: estimateDetail?.estimate_id,
            deadline_date,
            reminder_days: values?.reminder_days,
            bidders: updatedFields?.bidders?.map((item) => ({
              item_id: 0,
              user_id: item?.user_id,
            })),
          })
        );
      }

      if (response?.success) {
        if (isEdit && biddingId) {
          fetchBiddingData();
        }
        dispatch(
          getEstBidding({
            estimate_id: estimateDetail?.estimate_id?.toString(),
          })
        );
        if (!isEdit && values?.dontCloseDrawer !== true) {
          setDrawerOpen(false);
          formik?.resetForm();
          setInitialValues({});
        }
      } else {
        // if (isEdit && biddingId) {
        //   setDrawerOpen(false);
        // }
        notification.error({
          description: response?.message,
        });
      }
    } catch (e: any) {
      // setDrawerOpen(false);
      notification.error({
        description: e?.message,
      });
    }
  }

  async function fetchBiddingData() {
    try {
      const response = await dispatch(
        getOneEstBidding({
          estimate_id: estimateDetail?.estimate_id,
          bidding_id: biddingId,
        })
      );
      if ((response.payload as IDefaultAPIRes)?.success) {
        const data = (response.payload as IEstimatesBiddingOneApiRes)?.data;
        const bidStatusIndex = biddingStatus?.findIndex(
          (el) => el?.type_id == data?.bid_status
        );
        if (bidStatusIndex !== -1) {
          setActiveStep(bidStatusIndex + 1);
        }

        const bidData = {
          ...data,
          bid_items: data?.items ?? [],
          bidders: data?.bidder ?? [],
        };
        setBidApiData(bidData);
        formik?.setValues(bidData);
        setInitialValues(bidData);
      } else {
        notification.error({
          description: (response.payload as IDefaultAPIRes)?.message,
        });
      }
    } catch (e: any) {
      notification.error({
        description: e?.message,
      });
    }
  }

  const formik = useFormik({
    initialValues: {
      title: "",
      // deadline_date: isEdit ? '' : moment(new Date()).format(date_format),
      deadline_date: isEdit
        ? ""
        : timezone_utc_tz_id
        ? dayjs()?.tz(timezone_utc_tz_id)?.format(CFConfig.day_js_date_format)
        : dayjs()?.format(CFConfig.day_js_date_format),
      deadline_time: isEdit ? "" : "17:30:00",
      // : dayjs()?.tz(timezone_utc_tz_id)?.format("HH:mm:ss"),
      bid_status: "297",
      bid_manager_id: isEdit ? "" : user_id?.toString(),
      reminder_days: "",
      scope_of_work: "",
      terms: "",
      inclusion: "",
      exclusion: "",
      clarification: "",
      removed_items: [],
      bid_items: [],
      bidders: [],
      estimate_id: 0,
    },
    validationSchema: Yup?.object({
      title: Yup?.string()?.trim()?.required("This field is required."),
      deadline_date: Yup?.string()?.required("This field is required."),
      deadline_time: Yup?.string()?.required("This field is required."),
      bid_manager_id: Yup?.string()?.required("This field is required."),
      reminder_days: Yup?.string()
        .nullable()
        .matches(/^\d+$/, "Only numbers are allowed")
        .max(4, "Maximum 4 digits are allowed"),
    }),
    onSubmit: async (values: ESBiddingAddParams, { setSubmitting }) => {
      const { deadline_date, deadline_time } = formik.values;
      let formattedTime = dayjs(
        deadline_time,
        ["hh:mm A", "HH:mm:ss"],
        true
      )?.format("HH:mm:ss");
      let date = dayjs(
        `${deadline_date} ${formattedTime}`,
        `${CFConfig.day_js_date_format} HH:mm:ss`
      );
      if (timezone_utc_tz_id) {
        date = date.tz(timezone_utc_tz_id, true);
      }
      const currentDateTime = timezone_utc_tz_id
        ? dayjs()?.tz(timezone_utc_tz_id)
        : dayjs();
      if (currentDateTime.isAfter(date)) {
        notification.error({
          description:
            "Bidding deadline date and time should be greater than the current date and time.",
        });
        return;
      }
      await addBidding(values);
      setSubmitting(false);
    },
  });
  // on create new bid set current user as bid manager
  useEffect(() => {
    if (!isEdit && !selectedBidManager && user_id) {
      // formik?.setFieldValue("bid_manager_id", user_id);
      setSelectedBidManager({
        user_id,
        type,
        type_key: getDirectaryKeyById(
          type?.toString() === "1" ? 2 : 1,
          undefined
        ),
        // type_name: `Employee`,
        first_name,
        last_name,
        email: email ?? "",
        company_name,
        display_name,
        image,
      });
    }
  }, []);
  useEffect(() => {
    if (isEdit && biddingId) {
      fetchBiddingData();
    } else {
      formik?.resetForm();
      setInitialValues({});
    }
  }, [isEdit, biddingId]);

  const getEmailIconProps = useCallback(() => {
    let isShowOpenedEmailIcon = false;
    let isShowClosedEmailIcon = false;

    let emailOpenedDate = null;
    let emailSentDate = null;

    if (formik?.values?.bidders?.length > 0) {
      formik?.values?.bidders?.forEach((bidder: Bidder) => {
        if (bidder?.est_submit_bidder_email_status === 1) {
          isShowClosedEmailIcon = true;
          emailSentDate = bidder?.sent_date + " " + bidder?.sent_time;
        } else if (bidder?.est_submit_bidder_email_status === 2) {
          isShowOpenedEmailIcon = true;
          emailOpenedDate = bidder?.date_modified
            ? dayjs(bidder?.date_modified)?.format(CFConfig.day_js_date_format)
            : null;
        }
      });
    }

    return {
      isShowOpenedEmailIcon,
      isShowClosedEmailIcon,
      emailOpenedDate,
      emailSentDate,
    };
  }, [formik?.values?.bidders]);

  const emailIconProps = getEmailIconProps();

  // const activeStep = formik?.values?.bid_status;
  let selectedComponent;
  switch (createBid) {
    case "details":
      selectedComponent = (
        <Details
          formik={formik}
          initialValues={initialValues}
          isEdit={isEdit}
          isReadOnly={isReadOnly}
          selectedBidManager={selectedBidManager}
          setSelectedBidManager={setSelectedBidManager}
        />
      );
      break;
    case "items":
      selectedComponent = (
        <Items
          formik={formik}
          biddingId={biddingId}
          isEdit={isEdit}
          isReadOnly={isReadOnly}
          isInDraftMode={activeStep === 1}
        />
      );
      break;
    case "terms":
      selectedComponent = (
        <Terms
          isBidClosed={isBidClosed}
          formik={formik}
          isReadOnly={isReadOnly}
        />
      );
      break;
    case "files":
      selectedComponent = (
        <Files
          formik={formik}
          biddingId={biddingId}
          isEdit={isEdit}
          isReadOnly={isReadOnly}
        />
      );
      break;
    case "bidders":
      selectedComponent = (
        <Bidders
          formik={formik}
          biddingId={biddingId}
          isEdit={isEdit}
          isReadOnly={isReadOnly}
          isBidClosed={isBidClosed}
        />
      );
      break;
    case "submissions":
      selectedComponent = (
        <Submissions
          formik={formik}
          biddingId={biddingId}
          isEdit={isEdit}
          isReadOnly={isReadOnly}
          isBidClosed={isBidClosed}
        />
      );
      break;
    default:
      selectedComponent = <></>;
  }
  useEffect(() => {
    const bidStatusIndex = biddingStatus?.findIndex(
      (el) => el?.type_id === formik?.values?.bid_status
    );
    if (bidStatusIndex !== -1) {
      setActiveStep(bidStatusIndex + 1);
    }
  }, [formik?.values.bid_status, biddingStatus]);

  async function handleBidStatusChange(e: IProgressBarHeaderPropOption) {
    if (Object.keys(formik?.errors)?.length > 0) {
      setCreateBid("details");
      notification.error({
        description: "Please fill all the required fields in details tab",
      });
      return;
    }

    if (e?.type_id) {
      await formik?.setFieldValue("bid_status", e?.type_id);
      await formik?.setFieldValue("dontCloseDrawer", true);
      if (isEdit) {
        await formik?.submitForm();
      }
    }
  }

  return (
    <Drawer
      open={drawerOpen}
      rootClassName="drawer-open"
      width={1410}
      push={false}
      classNames={{
        header: "!hidden",
        body: "!p-0 !overflow-hidden",
      }}
    >
      <div className="sidebar-body">
        <div className="flex">
          <DrawerVericalOptions<TCreateBidTabsValues | "">
            sideMenuOpen={sideMenuOpen}
            setSideMenuOpen={setSideMenuOpen}
            defaultOptions={CREATE_BID_TAB_OPTIONS}
            options={options}
            selectedOption={createBid}
            onClick={(value: TCreateBidTabsValues | "") => {
              if (createBid === "items") {
                formik.setFieldValue("dontCloseDrawer", true);
                if (isEdit) {
                  formik?.submitForm();
                }
              }
              setCreateBid(value);
            }}
          />

          <div className="flex w-full max-w-[1170px] flex-[1_0_0%] overflow-hidden flex-col">
            <div className="md:px-4 pl-10 pr-5 py-2.5 border-b border-gray-300 dark:border-white/10 flex items-center relative justify-start">
              <Button
                className="md:!hidden flex !w-6 h-6 !absolute left-2.5"
                type="text"
                onClick={() => setSideMenuOpen((prev) => !prev)}
                icon={
                  <FontAwesomeIcon
                    className="text-base w-[18px] h-[18px] text-primary-gray-80 dark:text-white/90"
                    icon="fa-regular fa-bars"
                  />
                }
              />
              <div className="flex items-center w-[calc(100%-30px)]">
                <div className="flex items-center">
                  <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-gray-200/50 dark:bg-dark-500">
                    <FontAwesomeIcon
                      className="w-4 h-4 !text-primary-900 dark:!text-white/90"
                      icon="fa-regular fa-file-contract"
                    />
                  </div>
                  <Header
                    level={5}
                    className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold whitespace-nowrap"
                  >
                    {_t("Bid Package")}
                  </Header>
                </div>
                {isEdit && biddingId && (
                  <ul className="sm:flex items-center justify-center w-full hidden">
                    {biddingStatus?.map((item, index) => {
                      const isActive = Number(activeStep) >= Number(index + 1);
                      const statusTooltipName =
                        "status_" + (item?.value || index);
                      const emailTooltipName =
                        "email_" + (item?.value || index);
                      return (
                        <li
                          key={index}
                          onClick={() => {}}
                          className={`relative 2xl:min-w-[125px] xl:min-w-24 lg:w-24 w-20 grid justify-end first:before:hidden before:absolute before:h-[2px] before:!w-[calc(100%-54px)]  ${
                            isActive
                              ? "before:bg-primary-900"
                              : "before:bg-[#ACAEAF]"
                          } before:top-[30%] lg:before:left-[-22px] before:left-[-13px]`}
                        >
                          <ProgressBarHeader
                            tooltipProps={{
                              open: currntTooltip === statusTooltipName,
                              onOpenChange: (visible) => {
                                setCurrntTooltip(
                                  visible ? statusTooltipName : ""
                                );
                              },
                            }}
                            isReadOnly={isReadOnly}
                            option={{
                              icon: ESTIMATE_BID__STATUS_ICON[
                                item?.key as keyof typeof ESTIMATE_BID__STATUS_ICON
                              ],
                              default_color: item?.default_color ?? "#4DAFFE",
                              label: HTMLEntities?.decode(
                                sanitizeString(item?.name)
                              ),
                              value: item?.key?.toString(),
                              index: index + 1,
                              type_id: item?.type_id,
                            }}
                            isActive={isActive}
                            onClick={(e: IProgressBarHeaderPropOption) => {
                              if (!isReadOnly) {
                                handleBidStatusChange(e);
                              }
                            }}
                            children={
                              item?.key === "est_bid_submitted" &&
                              (emailIconProps?.isShowClosedEmailIcon ||
                                emailIconProps?.isShowOpenedEmailIcon) && (
                                <Tooltip
                                  open={currntTooltip === emailTooltipName}
                                  onOpenChange={(visible) => {
                                    setCurrntTooltip(
                                      visible ? emailTooltipName : null
                                    );
                                  }}
                                  title={_t(
                                    emailIconProps?.isShowOpenedEmailIcon
                                      ? `Opened on ${emailIconProps?.emailOpenedDate}`
                                      : emailIconProps?.isShowClosedEmailIcon
                                      ? `Submitted on ${emailIconProps?.emailSentDate}`
                                      : ""
                                  )}
                                  placement="top"
                                >
                                  <FontAwesomeIcon
                                    className="w-3.5 h-3.5 text-primary-900 relative z-10"
                                    icon={
                                      "fa-regular " +
                                      (emailIconProps?.isShowOpenedEmailIcon
                                        ? "fa-envelope-open"
                                        : "fa-envelope")
                                    }
                                  />
                                </Tooltip>
                              )
                            }
                          />
                        </li>
                      );
                    })}
                  </ul>
                )}
              </div>
              <div className="md:relative md:right-[18px] !absolute right-2.5">
                <CloseButton
                  onClick={() => {
                    setDrawerOpen(false);
                    formik?.resetForm();
                    setInitialValues({});
                  }}
                />
              </div>
            </div>
            <form
              onSubmit={formik?.handleSubmit}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                }
              }}
            >
              <div className="py-4">
                <div
                  className={`sidebar-body overflow-y-auto px-4 ${
                    isEdit && biddingId
                      ? "sm:h-[calc(100vh-165px)] h-[calc(100vh-132px)]"
                      : "h-[calc(100vh-132px)]"
                  }`}
                >
                  <div className="grid gap-4">
                    <div>{selectedComponent}</div>
                  </div>
                </div>
                {!isReadOnly && (
                  <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
                    <PrimaryButton
                      disabled={formik?.isSubmitting}
                      isLoading={formik?.isSubmitting}
                      htmlType="submit"
                      buttonText={_t("Save")}
                      onClick={() => {
                        if (createBid != "details") {
                          if (Object.keys(formik?.errors)?.length > 0) {
                            setCreateBid("details");
                            notification.error({
                              description:
                                "Please fill all the required fields in details tab",
                            });
                          }
                        }
                      }}
                    />
                  </div>
                )}
              </div>
            </form>
          </div>
        </div>
      </div>
    </Drawer>
  );
};

export default CreateBidPackage;
