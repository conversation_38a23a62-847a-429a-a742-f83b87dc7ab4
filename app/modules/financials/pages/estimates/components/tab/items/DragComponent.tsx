// Atoms
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// store
import {
  ColDef,
  ICheckboxCellRendererParams,
  RowDragEvent,
  ValueGetterParams,
  ValueSetterParams,
  SuppressKeyboardEventParams,
  ColGroupDef,
  EditableCallbackParams,
} from "ag-grid-community";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useDrag, useDrop } from "react-dnd";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import { ICON_MAP } from "~/modules/financials/pages/changeOrder/components/sidebar/utils";
import { floatWithNegativeRegex } from "~/modules/financials/pages/changeOrder/utils/helpers";
import { getGSettings } from "~/zustand";
import {
  updateEstimateItems,
  updateEstimateItemsOrder,
  updateEstimateSectionOrder,
} from "../../../redux/action/ESItemAction";
import {
  moveSectionState,
  selectAll,
  selectItem,
  setSection,
  unselectAll,
  updateItem,
} from "../../../redux/slices/ESItemSlice";
import { useAppESDispatch, useAppESSelector } from "../../../redux/store";
import {
  calculateMarkupPercentage,
  itemTotalCalculator,
} from "../details/EstimatesCalc";
import { isValidId } from "../../../utils/common";
import {
  generateCostCodeLabel,
  qtyNumberCheck,
  wholeNumberRegex,
} from "~/shared/utils/helper/common";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import UnitCellEditor from "~/modules/settings/costItemsDatabase/components/unitCellEditor/UnitCellEditor";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { addUnit, getUnitList } from "~/redux/action/unitActions";
// import { useInView } from "./useInView";
import { useInView } from "react-intersection-observer";
import { Spin } from "~/shared/components/atoms/spin";

const DragComponent = ({
  index,
  isReadOnly,
  onGridReady,
  ParentIsReadOnly,
  singleSection,
  actionHandlers,
}: SectionDraggableProps) => {
  const { sections } = useAppESSelector((state) => state.estimateItems);
  const { getGlobalModuleByKey } = useGlobalModule();

  const EstimateModule = useMemo(
    () => getGlobalModuleByKey(CFConfig.estimate_module),
    [getGlobalModuleByKey] // Ensures memoization based on function reference
  );

  const { module_name: estimate_module_name = "" } = EstimateModule || {};
  const { formatter, inputFormatter } = useCurrencyFormatter();
  const gSettings: GSettings = getGSettings();
  const { default_item_view } = gSettings;
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { allow_delete_module_items = "0" } = user || {};
  const {
    setSectionOpen,
    setItemsOpen,
    setConfirmCopyDialogOpen,
    setConfirmDialogOpen,
    setCopySectionData,
    setCostItemDatabaseOpen,
    setEstimateBulkMarkup,
    setImportEstimateFrom1Build,
    setIsDeleteConfirmOpen,
    setIsItemAdd,
    setItemsData,
    setSectionData,
    setSelectDeletedItems,
    setSelectedItemId,
    setSelectedSectionId,
  } = actionHandlers;
  // const isReadOnly = useMemo(
  //   () => checkModuleAccessByKey(module_key) === "read_only",
  //   [module_key]
  // );
  // const { ref, isInView } = useInView();
  const { ref, inView: isInView } = useInView({
    threshold: 0.1, // 10% visible
    triggerOnce: false,
  });
  const dispatch = useAppESDispatch();
  const { _t } = useTranslation();
  const [isLoadingCheckBox, setIsLoadingCheckBox] = useState<
    Record<number, boolean>
  >({});
  // const [loadingStatus, setLoadingStatus] =
  //   useState<Array<IEFieldStatus>>(fieldStatus);
  const { estimateDetail, isEstimateDetailLoading } = useAppESSelector(
    (state) => state.estimateDetail
  );
  const { flags, selectedItems } = useAppESSelector(
    (state) => state.estimateItems
  );
  const { codeCostData } = useAppESSelector((state) => state.costCode);

  const filteredCodeCostData = codeCostData
    ?.filter(
      (item) =>
        (item?.cost_code_name?.toString() !== "" ||
          item?.csi_code?.toString() !== "") &&
        Number(item?.parent_id) > 0
    )
    ?.map((el) => ({
      ...el,
      // label: `${el?.cost_code_name}${
      //   el?.csi_code && el?.csi_code !== null ? ` (${el?.csi_code})` : ""
      // }${el?.is_deleted === 1 ? ` (Archived)` : ""}`,
      label: generateCostCodeLabel({
        name: el?.cost_code_name,
        code: el?.csi_code,
        isArchived: false,
        isAllowCodeWithoutName: true,
      }),
    }));
  const [costchangeConfirmOpen, setCostChangeConfirmation] =
    useState<Partial<ESEstimateItem> | null>(null);
  // const [selectedItems, setSelectedItems] = useState<
  //   Record<string, { selected: boolean; section_id: number }>
  // >({});

  // const handleChangeFieldStatus = ({
  //   field,
  //   status,
  //   action,
  // }: IEFieldStatus) => {
  //   const checkStatus = loadingStatus.find(
  //     (item: IEFieldStatus) => item?.field === field
  //   );
  //   if (
  //     !(
  //       (checkStatus?.status === "loading" ||
  //         checkStatus?.status === "success" ||
  //         checkStatus?.status === "save") &&
  //       (action === "ME" || action === "ML")
  //     )
  //   ) {
  //     setLoadingStatus((prevState: IEFieldStatus[]) =>
  //       prevState.map((item) =>
  //         item.field === field ? { ...item, status: status } : item
  //       )
  //     );
  //   }
  // };

  const gridRef = useRef<ExtendedAgGridReact<ESEstimateItem> | null>(null);
  const [selectedData, setSelectedData] = useState<ESEstimateItem>();
  const [newUnitName, setNewUnitName] = useState<string>("");
  const [units, setUnits] = useState<
    IUnitListResponseDataAndStatusCode["units"]
  >([]);
  const [isUnitsGetting, setIsUnitsGetting] = useState<boolean>(true);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);

  useEffect(() => {
    if (window.ENV.ENABLE_UNIT_DROPDOWN) {
      (async function () {
        try {
          const response = await getUnitList();
          if (!response.success) {
            setIsUnitsGetting(false);
            return;
          }
          const units = response.data?.units || [];
          setUnits(units);
          const api = gridRef.current?.api;
          if (!api) return;

          const existingColDefs = api.getColumnDefs();
          if (!existingColDefs) return;

          const updatedColDefs = existingColDefs.map((col) =>
            "field" in col && col.field === "unit"
              ? {
                  ...col,
                  filterParams: {
                    values:
                      units.map((unit) => ({
                        label: unit.name?.toString(),
                        value: unit.name?.toString(),
                      })) ?? [],
                  },
                }
              : col
          );

          api.setColumnDefs(updatedColDefs);

          // Ensure the grid header re-renders
          api.refreshHeader();
        } catch (error) {}
        setIsUnitsGetting(false);
      })();
      return () => {
        if (gridRef?.current?.api?.destroy) {
          gridRef?.current?.api?.destroy();
        }
      };
    }
  }, []);

  const { taxIsReversible, taxRate } = useMemo(() => {
    return {
      taxIsReversible:
        typeof estimateDetail?.is_reversible_tax === "boolean"
          ? estimateDetail?.is_reversible_tax
          : estimateDetail?.is_reversible_tax?.toString() === "1",
      taxRate: parseFloat(estimateDetail?.tax_rate ?? "0") || 0,
    };
  }, [estimateDetail?.is_reversible_tax, estimateDetail?.tax_rate]);

  // function generatePayload(items: ESEstimateItem[], newSectionId?: number) {
  //   return items?.map((item, index) => ({
  //     sectionId: newSectionId || item.section_id,
  //     itemId: item.item_id,
  //     updatedItem: {
  //       order_number: index + 1,
  //     },
  //   }));
  // }

  const ADD_ITEMS_MODIFY_SECTION = [
    {
      label: "Add Items / Modify Section",
      value: "add_item_to_estimate",
      disabled: true,
    },
    {
      label: "Import from Cost Items Database",
      value: "Database",
      onClick: (singleSection: Partial<ESSection>) => {
        setCostItemDatabaseOpen(singleSection);
      },
    },
    {
      label: `Add Manual ${estimate_module_name} Item`,
      value: "add_item_to_estimate",
      onClick: (singleSection: Partial<ESSection>) => {
        setItemsOpen(true);
        setItemsData({
          section_id: singleSection?.section_id,
        });
        setIsItemAdd(true);
      },
    },
    {
      label: "Import from 1build Materials Cost Database",
      value: "1build",
      onClick: (singleSection: Partial<ESSection>) => {
        setImportEstimateFrom1Build(singleSection);
      },
      itemClass: "!bg-deep-orange-500 !text-white",
    },
    {
      label: "Apply Automatic/Bulk Markup",
      value: "apply_automatic_bulk",
      onClick: (singleSection: Partial<ESSection>) => {
        setEstimateBulkMarkup(singleSection);
      },
    },
    {
      label: "Copy Section",
      value: "copy_section",
      onClick: (singleSection: Partial<ESSection>) => {
        setConfirmCopyDialogOpen(true);
        setCopySectionData({
          section_id: singleSection?.section_id,
          estimate_id: +`${estimateDetail?.estimate_id}`,
        });
      },
    },
    {
      label: "Delete this Section",
      value: "delete_section",
      onClick: (singleSection: Partial<ESSection>) => {
        if (sections?.length > 1) {
          setConfirmDialogOpen(true);
          setSelectDeletedItems(singleSection);
        } else {
          notification.error({
            description: _t("At least one section is needed"),
          });
        }
      },
    },
  ];

  // const gridOptions: GridOptions = {
  //   stopEditingWhenCellsLoseFocus: true,
  //   onRowDragEnd: function (event) {
  //     if (isReadOnly) {
  //       return;
  //     }

  //     const { node, overIndex } = event as { node: any; overIndex: number };
  //     if (!gridOptions.api || !node) return;
  //     const rowData: ESEstimateItem[] = [];
  //     gridOptions.api.forEachNode((node) => rowData.push(node.data));
  //     rowData.splice(overIndex, 0, rowData.splice(node.rowIndex, 1)[0]);
  //     handleDragAndDrop(rowData);
  //   },
  // };

  // const handleDragAndDrop = async (items: ESEstimateItem[]) => {
  //   const payload = generatePayload(items);
  //   dispatch(updateItemsBulk(payload));
  // };

  const [{ isDragging }, dragRef] = useDrag({
    type: "SECTION",
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    end: async (draggedItem, monitor) => {
      // if (draggedItem.index === index) return;
      let payloadSec: Array<Partial<ESEstimateItem>> = [];
      const updatedSections = sections?.map((section, idx) => {
        payloadSec?.push({
          section_id: section?.section_id,
          custom_section_id: idx + 1,
        });

        return {
          ...section,
          custom_section_id: idx + 1,
          // items: section?.items?.map((item) => {
          //   return {
          //     ...item,
          //     custom_section_id: idx + 1,
          //   };
          // }),
        };
      });
      try {
        const update_Section = await dispatch(
          updateEstimateSectionOrder({
            estimate_id: estimateDetail?.estimate_id,
            sectionItems: payloadSec,
          })
        );
        const response = update_Section?.payload as IEstimatesItemsApiRes;

        if (response?.success) {
          dispatch(
            moveSectionState({ fromIndex: index, toIndex: draggedItem?.index })
          );
          dispatch(setSection(updatedSections));
        } else {
          notification.error({
            description: response?.message,
          });
          // await dispatch(getEstimateItems({ estimate_id }));
          // const updatedSections = [...sections];
          // const [movedSection] = updatedSections.splice(index, 1);
          // updatedSections.splice(draggedItem.index, 0, movedSection);
          // dispatch(setSection(updatedSections));
          // dispatch(
          //   moveSectionState({ fromIndex: index, toIndex: draggedItem.index })
          // );
          dispatch(setSection(updatedSections));
        }
      } catch (error) {
        // console.error("Error updating section order:", error);
        notification.error({
          description: error?.toString(),
        });
        // await dispatch(getEstimateItems({ estimate_id }));
        dispatch(setSection(sections));
      }
    },
  });

  const [{ isOver }, dropRef] = useDrop<DragItem, void, { isOver: boolean }>({
    accept: "SECTION", // Define the type of draggable item this drop target accepts
    hover: (item) => {
      if (item.index === index) return; // Prevent unnecessary updates if already in the correct position

      // Perform the drag-and-drop action
      dispatch(moveSectionState({ fromIndex: item.index, toIndex: index }));

      // Update the dragged item's index to prevent further redundant hover actions
      item.index = index;
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(), // Tracks whether the drop target is being hovered
    }),
  });

  const rowDataLength = singleSection?.items?.filter((el) => {
    const totalCost = Number(el?.quantity) * Number(el?.unit_cost);
    return flags?.showZeroQuantityItemsOnly ? totalCost <= 0 : true;
  });

  const uniCostDropdownHight =
    rowDataLength?.length === 1
      ? 60
      : rowDataLength?.length === 2
      ? 90
      : rowDataLength?.length === 3
      ? 120
      : rowDataLength?.length === 4
      ? 150
      : 180;

  const dragAndDropRef = (node: HTMLElement) => {
    dragRef(node);
    dropRef(node);
  };
  //  Old revrsablke tax and otehr tax section total
  // const filteredItems = singleSection?.items?.filter((el) => {
  //   const totalCost = Number(el?.quantity) * Number(el?.unit_cost);
  //   return (
  //     !el?.is_optional_item &&
  //     (flags?.showZeroQuantityItemsOnly ? totalCost <= 0 : true)
  //   );
  // });

  // const subtotal =
  //   filteredItems
  //     ?.map((ite) => itemTotalCalculator(ite, !flags?.isMarkupHidden))
  //     ?.reduce((sum, value) => sum + value, 0) || 0;
  // const subtotalTaxeble =
  //   filteredItems
  //     ?.filter((ite) =>
  //       typeof ite?.apply_global_tax === "boolean"
  //         ? ite?.apply_global_tax
  //         : ite?.apply_global_tax?.toString() === "1"
  //     )
  //     ?.map((ite) => itemTotalCalculator(ite, !flags?.isMarkupHidden))
  //     ?.reduce((sum, value) => sum + value, 0) || 0;
  // const tax = taxIsReversible ? 0 : subtotalTaxeble * taxRate;
  // const totalFloat = subtotal + tax;
  // const sectionTotal = formatter(
  //   (totalFloat / 100)?.toFixed(2)
  // )?.value_with_symbol;
  //   const { subtotal, subtotalTaxable, tax, totalFloat, sectionTotal } =
  //     useMemo(() => {
  //       const isMarkupHidden = !flags?.isMarkupHidden;

  //       const subtotal =
  //         filteredItems
  //           ?.map((item) => itemTotalCalculatorForSec(item, isMarkupHidden))
  //           ?.reduce((sum, value) => sum + value, 0) || 0;

  //       const subtotalTaxable =
  //         filteredItems
  //           ?.filter((item) => {
  //             const applyGlobalTax = item?.apply_global_tax;
  //             return typeof applyGlobalTax === "boolean"
  //               ? applyGlobalTax
  //               : applyGlobalTax?.toString() === "1";
  //           })
  //           ?.map((item) => itemTotalCalculatorForSec(item, isMarkupHidden))
  //           ?.reduce((sum, value) => sum + value, 0) || 0;
  //       const tax = taxIsReversible ? 0 : subtotalTaxable * taxRate;
  //       const totalFloat = subtotal + tax;

  //       const sectionTotal = formatter(
  //         (totalFloat / 100)?.toFixed(2)
  //       )?.value_with_symbol;

  //       return { subtotal, subtotalTaxable, tax, totalFloat, sectionTotal };
  //     }, [
  //       filteredItems,
  //       flags?.isMarkupHidden,
  //       taxRate,
  //       taxIsReversible,
  //       formatter,
  //     ]);
  const SingleSectionMetrics = useMemo(() => {
    const items = singleSection?.items;

    const filteredItems =
      items
        ?.filter((el) => {
          const totalCost = Number(el?.quantity) * Number(el?.unit_cost);
          return (
            !el?.is_optional_item &&
            (flags?.showZeroQuantityItemsOnly ? totalCost == 0 : true)
          );
        })
        ?.map((el) => ({
          ...el,
          unit_cost: `${Number(el.unit_cost) / 100}`,
          markup: el?.is_markup_percentage
            ? Number(el?.markup)
            : Number(el?.markup) / 100,
        })) ?? [];

    const calculateSum = (
      array: ESEstimateItem[],
      callback: (item: ESEstimateItem) => number
    ): number =>
      array?.map(callback)?.reduce((sum, value) => sum + value, 0) || 0;

    const estimatedCost = calculateSum(filteredItems, (ite) =>
      itemTotalCalculator(ite, false)
    );
    const subtotal = calculateSum(filteredItems, (ite) =>
      itemTotalCalculator(ite, !flags?.isMarkupHidden)
    );

    const subtotalTaxeble = calculateSum(
      filteredItems.filter((ite) =>
        typeof ite?.apply_global_tax === "boolean"
          ? ite?.apply_global_tax
          : ite?.apply_global_tax?.toString() === "1"
      ),
      (ite) => itemTotalCalculator(ite, !flags?.isMarkupHidden)
    );

    const markup = calculateSum(filteredItems, (item) =>
      item?.is_markup_percentage
        ? Number(item?.unit_cost) *
          (Number(item?.markup) / 100) *
          item?.quantity
        : Number(item?.unit_cost) * item?.quantity
        ? Number(item?.markup) - Number(item?.unit_cost) * item?.quantity
        : 0
    );

    const profitMargin = estimatedCost
      ? ((markup / estimatedCost) * 100).toFixed(2) + "%"
      : "0%";

    const tax = taxIsReversible ? 0 : subtotalTaxeble * (taxRate / 100);
    const grandTotal = subtotal + tax;

    return {
      // sectionName: section?.section_name,
      estimatedCost,
      subtotalTaxeble,
      subtotal,
      markup,
      profitMargin,
      tax,
      grandTotal,
    };
  }, [sections, flags, taxRate, taxIsReversible, singleSection?.items]);

  const updateItemField = async ({
    itemId,
    updatedItem,
    itemData,
  }: {
    itemId: number;
    updatedItem: Partial<ESEstimateItem>;
    itemData?: ESEstimateItem;
  }) => {
    // if unit and quantity is updated then send total value to backend
    const updatedItemKeys = Object.keys(updatedItem);
    if (updatedItemKeys.includes("apply_global_tax")) {
      setIsLoadingCheckBox((prev) => ({ ...prev, [itemId]: true }));
    }
    if (
      itemData &&
      (updatedItemKeys.includes("unit") ||
        updatedItemKeys.includes("quantity") ||
        updatedItemKeys.includes("markup") ||
        updatedItemKeys.includes("unit_cost"))
    ) {
      updatedItem["total"] = itemTotalCalculator(
        {
          ...itemData,
          ...updatedItem,
        },
        true
        // !!itemData.hidden_markup
      );
    }
    dispatch(
      updateItem({
        sectionId: singleSection?.section_id,
        itemId,
        updatedItem,
      })
    );
    const apiRes = await dispatch(
      updateEstimateItems({
        estimate_id: estimateDetail?.estimate_id,
        items: [
          {
            ...updatedItem,
            section_id: singleSection?.section_id,
            item_id: itemId,
          },
        ],
      })
    );

    const response = apiRes.payload as IEstimatesItemsApiRes;

    if (response?.success) {
      // await dispatch(getEstimateItems({ estimate_id }));
      setIsLoadingCheckBox((prev) => ({ ...prev, [itemId]: false }));
    } else {
      dispatch(
        updateItem({
          sectionId: singleSection?.section_id,
          itemId,
          updatedItem: itemData,
        })
      );
      notification.error({
        description: response?.message,
      });
      setIsLoadingCheckBox((prev) => ({ ...prev, [itemId]: false }));
    }
    return response;
  };
  const handleSelection = useCallback(
    (selected: boolean, params: { data: ESEstimateItem }) => {
      dispatch(selectItem({ selected, data: params?.data }));
    },
    [dispatch]
  );
  // const handleSelection = (
  //   selected: boolean,
  //   params: { data: ESEstimateItem }
  // ) => {
  //   // setSelectedItems((prev) => {
  //   //   const updated = { ...prev }; // Create a copy of the current state
  //   //   if (selected) {
  //   //     updated[params?.data?.item_id] = {
  //   //       selected: true,
  //   //       section_id: params?.data?.section_id,
  //   //     }; // Add item to selected
  //   //   } else {
  //   //     delete updated?.[params?.data?.item_id]; // Remove item from selected
  //   //   }
  //   //   return updated;
  //   // });
  //   dispatch(selectItem({ selected, data: params?.data }));
  // };
  // const handleDragEnd = (event: RowDragEvent<any, any>) => {
  //   const prevSection = event.node.data.section_id;
  //   const prevRow = event.node.rowIndex;
  //   const nextSection = event.overNode.data.section_id;
  //   const nextRow = event.overIndex;
  //   if (isReadOnly) return;
  //   if (nextSection !== prevSection) {
  //     const updated_items = sections?.map((section) => {
  //       const items: ESEstimateItem[] = [...section.items];
  //       if (section.section_id === prevSection) {
  //         items.splice(prevRow, 1);
  //         return { ...section, items: items };
  //       } else if (section.section_id === nextSection) {
  //         items.splice(nextRow, 0, {
  //           ...event.node.data,
  //           section_id: nextSection,
  //         });
  //         return { ...section, items: items };
  //       } else {
  //         return { ...section };
  //       }
  //     });
  //     dispatch(setSection(updated_items));
  //     updateItemField({
  //       itemId: event.node.data.item_id,
  //       itemData: event.node.data,
  //       updatedItem: {
  //         section_id: nextSection,
  //       },
  //     });
  //   } else {
  //     const updated_items = sections?.map((section) => {
  //       if (section.section_id === prevSection) {
  //         const item_data = [...section.items];
  //         const old_index = item_data.findIndex(
  //           (ele) => ele.item_id === event.node.data.item_id
  //         );
  //         item_data.splice(old_index, 1);
  //         item_data.splice(prevRow, 0, event.node.data);
  //         return { ...section, items: item_data };
  //       } else {
  //         return { ...section };
  //       }
  //     });
  //     dispatch(setSection(updated_items));
  //   }
  // };

  const handleDragEnd = useCallback(
    async (event: RowDragEvent<any, any>, sectionDet: Partial<ESSection>) => {
      if (isReadOnly) return;
      const prevSection = event.node.data.section_id;
      const prevRow = event.node.rowIndex;
      const nextSection = sectionDet?.section_id || null;

      let nextRow = event.overIndex === -1 ? 0 : event.overIndex;

      const newItems: ESEstimateItem[] = [];

      let newPayload: {
        item_id: number;
        order_number: number;
        section_id: number;
      }[] = [];

      event?.api?.forEachNode((e, index) => {
        newItems?.push({
          ...e?.data,
          estimate_item_no: index + 1,
          order_number: index + 1,
        });
      });

      const Newsections = JSON.parse(JSON.stringify(sections)) as ESSection[];
      if (nextSection !== prevSection) {
        // Moving between sections
        const PreviousSection = Newsections?.find(
          (sec) => sec.section_id === prevSection
        );
        const NextSection = Newsections?.find(
          (sec) => sec.section_id === nextSection
        );

        if (NextSection && PreviousSection) {
          if (prevRow !== null) {
            NextSection?.items?.splice(nextRow, 0, {
              ...PreviousSection?.items?.[prevRow],
              section_id: nextSection ?? 0,
            });
            PreviousSection?.items?.splice(prevRow, 1);
          }
          newPayload = [
            ...(PreviousSection?.items?.map((el, i) => ({
              item_id: Number(el?.item_id),
              order_number: i + 1,
              section_id: Number(el?.section_id),
            })) ?? []),
            ...(NextSection?.items?.map((el, i) => ({
              item_id: Number(el?.item_id),
              order_number: i + 1,
              section_id: Number(el?.section_id),
            })) ?? []),
          ];
        }
        dispatch(setSection(Newsections));
        const update_Item = await dispatch(
          updateEstimateItemsOrder({
            estimate_id: estimateDetail?.estimate_id,
            items: newPayload,
          })
        );
        const response = update_Item?.payload as IEstimatesItemsApiRes;
        if (response?.success) {
        } else {
          notification.error({
            description: _t("Item Order Not updated in between Sections."),
          });
          dispatch(setSection(sections));
        }
      }
      if (nextSection === prevSection) {
        // Rearranging within the same section
        const UpdateSameSection = Newsections?.find(
          (sec) => sec.section_id === prevSection
        );
        if (UpdateSameSection) {
          UpdateSameSection.items = newItems;
          newPayload =
            newItems?.map((el, i) => ({
              item_id: Number(el?.item_id),
              order_number: i + 1,
              section_id: Number(el?.section_id),
            })) ?? [];
        }
        dispatch(setSection(Newsections));
        const update_Item = await dispatch(
          updateEstimateItemsOrder({
            estimate_id: estimateDetail?.estimate_id,
            items: newPayload,
          })
        );
        const response = update_Item?.payload as IEstimatesItemsApiRes;
        if (response?.success) {
        } else {
          notification.error({
            description: _t("Item Order Not updated in Section."),
          });
          dispatch(setSection(sections));
        }
      }
    },
    [dispatch, isReadOnly, sections]
  );

  const SelectedMul = useMemo(() => {
    return Object?.keys(selectedItems ?? {})?.length >= 1;
  }, [selectedItems]);

  const suppressKeyboardEvent: ColDef<
    CostItemTableCellRenderer["data"]
  >["suppressKeyboardEvent"] = (params) => {
    const { event, api } = params;

    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
      event.preventDefault();
      event.stopPropagation();
      return true;
    }

    if (event.key === "Enter") {
      event.preventDefault();
      event.stopPropagation();

      // Call valueSetter or custom logic for saving the value
      api.stopEditing(); // Stop editing to trigger valueSetter
      return true;
    }

    return true;
  };

  const singleSectionItems = singleSection?.items?.filter((el) => {
    const totalCost = Number(el?.quantity) * Number(el?.unit_cost);
    return flags?.showZeroQuantityItemsOnly ? totalCost == 0 : true;
  });

  const MuHeader = () => {
    return (
      <div className="w-full flex items-center justify-end gap-1.5">
        <span className="text-[#181d1f] font-semibold">{_t("MU%")}</span>
        {flags?.isMarkupHidden && (
          <Tooltip title="Markup is currently hidden from all totals while 'hide' is enabled. Markup is hidden for reference only and is still applied to all PDFs">
            <FontAwesomeIcon
              className="h-3.5 w-3.5 -mt-px"
              icon="fa-regular fa-info-circle"
            />
          </Tooltip>
        )}
      </div>
    );
  };
  // useEffect(()=>{
  //   if(Object.keys(selectedItems)?.length){
  //     document.querySelectorAll(
  //       `.ag-cell[col-id="checkbox"]`
  //     ).forEach((cell) => cell.classList.add("visible"));
  //   }
  //   // else{
  //   //   document.querySelectorAll(
  //   //     `.ag-cell[col-id="checkbox"]`
  //   //   ).forEach((cell) => cell.classList.remove("visible"));
  //   // }
  // },[selectedItems])
  const columnDefs: (ColDef | ColGroupDef)[] = [
    {
      headerName: "",
      minWidth: isReadOnly ? 0 : 30,
      maxWidth: isReadOnly ? 0 : 30,
      field: "move",
      suppressMenu: true,
      hide: Boolean(isReadOnly),
      rowDrag: !isReadOnly,
      cellClass: () =>
        `ag-cell-center ag-move-cell custom-move-icon-set cell-invinsible ${
          Object.keys(selectedItems)?.length ? "" : " hover-visibility"
        }`,
      cellRenderer: () => {
        return (
          <>
            {/* {getStatusForField(
            loadingStatus,
            "save_estimate_template"
          ) !== "loading" ? ( */}
            <div className="w-6 h-6 flex items-center justify-center absolute top-1 left-1 !z-0 opacity-100">
              <FontAwesomeIcon
                className="w-4 h-4 text-[#4b5a76]"
                icon="fa-solid fa-grip-dots"
              />
            </div>
            {/* ) : (
            <div className="w-6 h-6 flex items-center justify-center absolute top-1 left-1 !z-0 opacity-100">
              <FontAwesomeIcon
                className="w-4 h-4 text-[#4b5a76] fa-spin"
                icon={`fa-duotone fa-solid fa-spinner-third`}
              />
            </div>
          )} */}
          </>
        );
      },
    },
    {
      headerName: "",
      headerComponent: () => {
        const allSelected =
          singleSection?.items?.every(
            (item) => selectedItems?.[item?.item_id]?.selected
          ) && singleSection?.items?.length > 0;
        return (singleSection?.items?.length || 0) > 0 &&
          !flags.showZeroQuantityItemsOnly ? (
          <CheckBox
            checked={allSelected}
            onChange={(e) => {
              if (e.target.checked)
                dispatch(
                  selectAll({
                    sectionId: singleSection.section_id as number,
                  })
                );
              else
                dispatch(
                  unselectAll({
                    sectionId: singleSection.section_id as number,
                  })
                );
            }}
          />
        ) : null;
      },
      field: "checkbox",
      minWidth: isReadOnly ? 0 : 40,
      maxWidth: isReadOnly ? 0 : 40,
      hide: Boolean(isReadOnly || flags.showZeroQuantityItemsOnly),
      checkboxSelection: false,
      headerCheckboxSelection: false,
      suppressMenu: true,
      suppressMovable: true,
      cellClass: () =>
        `ag-cell-center ad-call-pr-0 ad-call-pl-0 hover-visibility ${
          Object.keys(selectedItems)?.length ? "visible" : "cell-invinsible"
        }`,
      cellRenderer: (params: ICheckboxCellRendererParams) => {
        if (!params.data) {
          return;
        }
        const data = params?.data as ESEstimateItem;
        const is_checked: boolean = Boolean(
          data?.selected || selectedItems?.[data?.item_id]
        );
        return (
          <CheckBox
            checked={is_checked}
            onChange={(e) => {
              if (params && params.node) {
                const selected = Boolean(e.target.checked);
                const updatedData = {
                  ...params.data,
                  selected,
                };
                handleSelection(e.target.checked, {
                  data: updatedData,
                });
                params.node.setData(updatedData);
                params.api.refreshCells({
                  rowNodes: [params.node],
                  force: true,
                });
              }
            }}
          />
        );
      },
    },
    // {
    //   headerName: "",
    //   field: "checkbox",
    //   minWidth: 27,
    //   maxWidth: 27,
    //   checkboxSelection: false, // Disable built-in checkbox since we are using custom ones
    //   headerCheckboxSelection: false, // Disable header checkbox
    //   headerClass: "ag-header-center",
    //   // cellClass: `ag-cell-center ad-call-pr-0 ad-call-pl-0 hidden-price`,
    //   suppressMenu: true,
    //   cellClass: ()=>
    //     `ag-cell-center ad-call-pr-0 ad-call-pl-0 hover-visibility ${Object.keys(selectedItems)?.length ? "visible" : "cell-invinsible"}`,
    //   cellRenderer: (event: any) => {
    //     // const isChecked = checkedItem.some(
    //     //   (item) => item.item_id.toString() === event?.data.item_id.toString()
    //     // );
    //     const isChecked: boolean = Boolean(
    //        selectedItems?.[event?.data.item_id]
    //     );

    //     // setIsCheckedItem(checkedItem.length > 0 && true)
    //     return (
    //         <CFCheckBox
    //           checked={isChecked}
    //           onChange={(e: any) => {
    //             if (e.target.checked) {
    //               const selected = Boolean(e.target.checked);
    //               const updatedData = {
    //                 ...event?.data,
    //                 selected,
    //               };
    //               handleSelection(e.target.checked, {
    //                 data: updatedData,
    //               });
    //               // setCheckedItem((prev) => [...prev, event?.data]);
    //             } else {
    //               const selected = Boolean(e.target.checked);
    //               const updatedData = {
    //                 ...event?.data,
    //                 selected,
    //               };
    //               handleSelection(e.target.checked, {
    //                 data: updatedData,
    //               });
    //               // setCheckedItem((prev) =>
    //               //   prev.filter((item) => item.item_id !== event?.data.item_id)
    //               // );
    //             }
    //           }}
    //           className="pointer-events-auto"
    //           />
    //     );
    //   },
    // },
    {
      headerName: _t("Type"),
      field: "item_type_name",
      maxWidth: 50,
      minWidth: 50,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: ({ data }: { data: ESEstimateItem }) => {
        const value = data?.item_type_name;
        return value ? (
          <Tooltip title={value}>
            <FontAwesomeIcon
              className="w-4 h-4 text-primary-900 mx-auto"
              icon={
                ICON_MAP[value as keyof typeof ICON_MAP] || ICON_MAP["default"]
              }
            />
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("Item Name"),
      field: "subject",
      minWidth: 150,
      flex: 2,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,

      editable: !isReadOnly,
      cellRenderer: ({ data }: { data: ESEstimateItem }) => {
        const itemName =
          HTMLEntities.decode(sanitizeString(data?.subject)) ?? "-";
        return (
          <Tooltip title={itemName}>
            <Typography className="table-tooltip-text">
              {itemName}
              <span className="text-gray-500 ml-1">
                {Boolean(data?.is_optional_item) ? "(Optional)" : ""}
              </span>
            </Typography>
          </Tooltip>
        );
      },
      valueGetter: (params: ValueGetterParams) => {
        return HTMLEntities.decode(sanitizeString(params?.data?.subject));
      },
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          if (!params.newValue.trim().length) {
            notification.error({
              description: _t("Item Name is required."),
            });
            return false;
          }
          const updatedData = {
            ...params.data,
            subject: params.newValue.trim(),
          };
          params.node.setData(updatedData);
          updateItemField({
            itemId: updatedData.item_id,
            itemData: params.data,
            updatedItem: {
              subject: params.newValue.trim(),
            },
          });
          // handleUpdate([updatedData], {});
        }
        return true;
      },
    },
    {
      headerName: _t("Cost Code"),
      field: "cost_code_id",
      minWidth: 180,
      flex: 2,
      editable: !ParentIsReadOnly,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellEditor: "agRichSelectCellEditor",
      // keyCreator: (params) =>
      //   `${params?.data?.cost_code_name ?? ""}${params?.data?.csi_code
      //     ? ` (${params?.data?.csi_code})`
      //     : ""
      //   }${params?.data?.is_deleted === 1 ? ` (Archived)` : ""
      //   }`,
      // old key ceation based on cost_code_name
      // keyCreator : (params) =>`${params?.data?.cost_code_name}${
      //     params?.data?.csi_code
      //       ? ` (${params?.data?.csi_code})`
      //       : ""
      //   }${
      //     params?.data?.is_deleted === 1 ? ` (Archived)` : ""
      //   }`,
      // valueFormatter: (params: ValueFormatterParams) =>
      //   params.value?.cost_code_id ?? "",
      // valueParser: (params: ValueParserParams) =>
      //   filteredCodeCostData?.find(
      //     (costCode) =>
      //       costCode.cost_code_id === params.newValue
      //   ) ?? {},
      cellEditorParams: {
        values: filteredCodeCostData?.map((item) => item?.label),
        filterList: true,
        searchType: "matchAny",
        allowTyping: true,
        // need to check
        valueListMaxHeight:
          singleSectionItems?.length == 1
            ? 60
            : singleSectionItems?.length == 2
            ? 90
            : singleSectionItems?.length == 3
            ? 120
            : singleSectionItems?.length == 4
            ? 150
            : 180,
      },
      // valueGetter: (params: ValueGetterParams) => {
      //   return `${params?.data?.cost_code_name ?? ""}${
      //     params?.data?.csi_code
      //       ? ` (${params?.data?.csi_code})`
      //       : ""
      //   }${
      //     (params?.data?.is_deleted === 1 ) ? ` (Archived) ` : ""
      //   } ${JSON.stringify({v :filteredCodeCostData?.find(
      //     (el) => el?.code_id === params?.data?.cost_code_id
      //   ),filteredCodeCostData,p:params?.data})}`;
      //   // return params?.data?.label
      //   // old key ceation based on cost_code_name
      //   // return params.data?.cost_code_name
      //   //   ? `${params.data?.cost_code_name}${
      //   //       params.data?.cost_code
      //   //         ? ` (${params.data?.cost_code})`
      //   //         : ""
      //   //     }`
      //   //   : "";
      // },
      valueGetter: (params: ValueGetterParams) => {
        return params.data?.cost_code_name
          ? generateCostCodeLabel({
              name: params.data?.cost_code_name,
              code: params.data?.cost_code,
              isArchived: false,
              isAllowCodeWithoutName: true,
            })
          : "";
      },
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          const costCodelabel = params.newValue;
          const costCode = filteredCodeCostData?.find(
            (el) => el?.label?.trim() === costCodelabel?.trim()
          );
          if (costCode) {
            const updatedData = {
              ...params.data,
              cost_code_name: costCode?.cost_code_name,
              cost_code_id: costCode?.code_id,
              cost_code: costCode?.csi_code,
              code_is_deleted: 0,
            };
            params.node.setData(updatedData);
            updateItemField({
              itemId: updatedData.item_id,
              itemData: params.data,
              updatedItem: {
                cost_code_id: Number(costCode?.code_id),
                cost_code_name: costCode?.cost_code_name,
                cost_code: costCode?.csi_code,
                code_is_deleted: 0,
              },
            });
          }
        }
        return true;
      },
      cellRenderer: ({ data }: { data: ESEstimateItem }) => {
        const costCode = generateCostCodeLabel({
          name: data?.cost_code_name || "",
          code: data?.cost_code,
          isArchived:
            data?.code_is_deleted == 1 ||
            filteredCodeCostData?.findIndex(
              (el) => el?.code_id?.toString() === data?.cost_code_id?.toString()
            ) == -1,
          isAllowCodeWithoutName: true,
        });
        return costCode ? (
          <Tooltip title={costCode}>
            <Typography className="table-tooltip-text">
              {costCode || "-"}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("QTY"),
      field: "quantity",
      minWidth: 80,
      maxWidth: 80,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellEditor: "agNumberCellEditor",
      suppressKeyboardEvent,
      cellRenderer: ({ data }: { data: ESEstimateItem }) => {
        const quantity = data?.quantity ?? "";
        const quantityUnit = formatter(
          formatAmount(Number(data.quantity || 0), { isQuantity: true })
        ).value;
        // : formatter(Number(data.quantity).toFixed(0)).value;

        return (
          <>
            <Tooltip title={!!quantity ? quantityUnit : "0"}>
              <Typography className="table-tooltip-text">
                {quantity ? quantityUnit : "0"}
              </Typography>
            </Tooltip>
          </>
        );
      },
      valueGetter: (params: ValueGetterParams) => {
        return Number(params.data?.quantity);
      },
      editable: !isReadOnly,
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          let nVal = params.newValue;
          if (!nVal || nVal == null) {
            const _updateData = {
              ...params.data,
              quantity: "",
            };
            params.node.setData(_updateData);
            updateItemField({
              itemId: _updateData.item_id,
              itemData: params.data,
              updatedItem: {
                quantity: 0,
              },
            });
            return true;
          }
          const checkNum = qtyNumberCheck(nVal);
          const integerPartLength = nVal.toString().split(".")[0].length;

          if (integerPartLength > 6 || !checkNum) {
            notification.error({
              description: "Quantity should be less than or equal to 6 digits.",
            });
            return false;
          }
          // if (Number(nVal) < 0) {
          //   notification.error({
          //     description: _t("Quantity should be more then 0"),
          //   });
          //   return false;
          // }

          if (!floatWithNegativeRegex.test(nVal)) {
            notification.error({
              description: _t(
                "Decimal part should be less than or equal to 2 digits."
              ),
            });
            return false;
          }
          if (nVal.toString().includes(".")) {
            nVal = nVal.toFixed(2);
          }
          const updatedData = {
            ...params.data,
            quantity: Number(nVal),
          };
          params.node.setData(updatedData);
          updateItemField({
            itemId: updatedData.item_id,
            itemData: params.data,
            updatedItem: {
              quantity: Number(nVal),
            },
          });
        }
        return true;
      },
    },
    {
      headerName: _t("Unit Cost"),
      field: "unit_cost",
      minWidth: 130,
      maxWidth: 130,
      cellEditor: "agNumberCellEditor",
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      suppressMovable: false,
      suppressMenu: true,
      suppressKeyboardEvent,
      cellRenderer: ({ data }: { data: ESEstimateItem }) => {
        // const unitCost = data?.unit_cost ?? "$0";
        const totalFloat = Number(data?.unit_cost);
        const unitCost = data?.unit_cost
          ? formatter(formatAmount(totalFloat / 100))?.value_with_symbol
          : formatter("0")?.value_with_symbol;

        // // Log the combined result
        // const combinedCondition =
        //   data?.unit_cost != null &&
        //   data?.updated_unit_cost != null &&
        //   !isNaN(Number(data?.unit_cost)) &&
        //   !isNaN(Number(data?.updated_unit_cost)) &&
        //   Number(data?.unit_cost) !==
        //     Number(data?.updated_unit_cost);
        const oneBuildId = data?.one_build_id; // Use correct logic to retrieve `oneBuildId`
        const unitCostRounded = Math.round(Number(data?.unit_cost));
        const updatedUnitCostRounded = Math.round(
          Number(data?.updated_unit_cost)
        );
        const shouldShowUpdateIcon =
          data?.updated_unit_cost &&
          unitCostRounded !== updatedUnitCostRounded &&
          (!oneBuildId || oneBuildId === undefined) &&
          isValidId(data?.reference_item_id);

        return (
          <div className="flex gap-1 items-center justify-end overflow-hidden">
            {shouldShowUpdateIcon && !isReadOnly && (
              <Tooltip
                title={_t(
                  "The Cost is different than the Cost defined within the Cost Items Database. Click to update here."
                )}
                placement="top"
              >
                <FontAwesomeIcon
                  className="ml-1 w-3.5 h-3.5 text-deep-orange-500 cursor-pointer"
                  icon="fa-regular fa-triangle-exclamation"
                  onClick={() => {
                    setCostChangeConfirmation(data);
                  }}
                />
              </Tooltip>
            )}
            <Tooltip title={unitCost}>
              <Typography className="table-tooltip-text">{unitCost}</Typography>
            </Tooltip>
          </div>
        );
      },
      editable: !isReadOnly,
      valueGetter: (params: ValueGetterParams) => params.data?.unit_cost / 100,
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          let nVal = params.newValue;
          if (!nVal || nVal == null) {
            const _updateData = {
              ...params.data,
              unit_cost: "0.00",
            };
            params.node.setData(_updateData);
            updateItemField({
              itemId: _updateData.item_id,
              itemData: params.data,
              updatedItem: {
                unit_cost: "0.00",
              },
            });
            return true;
          }
          if (Number(params.newValue) < 0) {
            notification.error({
              description: _t("Negative values are not allowed for Unit Cost."),
            });
            return false;
          }
          const checkNum = qtyNumberCheck(nVal);
          const cleanedValue =
            nVal != null ? nVal?.toString().split(".")[0].replace("-", "") : "";
          const fullStr = BigInt(Math.floor(Number(nVal))).toString();

          if (cleanedValue && cleanedValue != null && fullStr?.length > 10) {
            notification.error({
              description:
                "Unit cost should be less than or equal to 10 digits.",
            });
            return false;
          }
          if (!floatWithNegativeRegex.test(nVal)) {
            notification.error({
              description: _t(
                "Decimal part should be less than or equal to 2 digits"
              ),
            });
            return false;
          }
          if (nVal.toString().includes(".")) {
            nVal = nVal.toFixed(2);
          }
          const updatedData = {
            ...params.data,
            unit_cost: Number(nVal) * 100,
          };
          params.node.setData(updatedData);
          updateItemField({
            itemId: updatedData.item_id,
            itemData: params.data,
            updatedItem: {
              unit_cost: `${Number(nVal) * 100}`,
              modified_unit_cost: `${Number(nVal) * 100}`,
            },
          });
        }
        return true;
      },
    },
    {
      headerName: _t("Unit"),
      field: "unit",
      minWidth: 100,
      maxWidth: 100,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      suppressMovable: false,
      editable: !isReadOnly,
      ...(window.ENV.ENABLE_UNIT_DROPDOWN && {
        suppressKeyboardEvent: (
          params: SuppressKeyboardEventParams
        ): boolean => {
          if (params.event.key === "Enter") {
            params.event.preventDefault();
            return true; // Block Ag-Grid's default behavior
          }
          return false;
        },
        cellEditorParams: {
          values: units,
          onKeyDown: (
            e: React.KeyboardEvent<HTMLInputElement>,
            data: ESEstimateItem
          ) => {
            if (e.key === "Enter") {
              const value = e?.currentTarget?.value?.trim();
              const newType = onEnterSelectSearchValue(
                e,
                units?.map((unit) => ({
                  label: unit?.name,
                  value: "",
                })) || []
              );
              if (newType) {
                setNewUnitName(newType);
                setSelectedData(data);
              } else if (value) {
                notification.error({
                  description:
                    "Records already exist, no new records were added.",
                });
              }
            }
          },
        },
        cellEditor: UnitCellEditor<IChangeOrderSectionItem>,
      }),
      suppressMenu: true,
      cellRenderer: ({ data }: { data: ESEstimateItem }) => {
        const unit =
          !!data?.unit && data?.unit?.trim()?.length ? data?.unit : "-";
        return (
          <>
            <Tooltip title={unit}>
              <Typography className="table-tooltip-text">
                {unit || "-"}
              </Typography>
            </Tooltip>
          </>
        );
      },
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          const cleanedValue = window.ENV.ENABLE_UNIT_DROPDOWN
            ? (params?.newValue?.name?.trim() || "")?.toString()
            : (params?.newValue || "")?.toString();
          if (cleanedValue?.length > 15) {
            notification.error({
              description: _t(
                "Unit should be less than or equal to 15 characters"
              ),
            });
            return false;
          }
          const updatedData = {
            ...params.data,
            unit: cleanedValue,
          };
          params.node.setData(updatedData);
          updateItemField({
            itemId: updatedData.item_id,
            itemData: params.data,
            updatedItem: {
              unit: cleanedValue,
            },
          });
        }
        return true;
      },
    },
    {
      headerName: _t("MU%"),
      field: "markup",
      minWidth: 80,
      maxWidth: 80,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellEditor: "agNumberCellEditor",
      suppressMovable: false,
      suppressMenu: true,
      editable: (params: EditableCallbackParams) =>
        Boolean(!isReadOnly) &&
        Boolean(params?.data?.is_markup_percentage) &&
        !flags?.isMarkupHidden,
      headerComponent: MuHeader,
      valueGetter: (params: ValueGetterParams) =>
        params.data?.markup?.toString() ? params.data?.markup : "",
      cellRenderer: ({ data }: { data: ESEstimateItem }) => {
        const mu = calculateMarkupPercentage(data);
        return (
          <div className="flex gap-0.5 items-center justify-end">
            {flags?.isMarkupHidden ? (
              <FontAwesomeIcon
                className="h-3.5 w-3.5"
                icon="fa-regular fa-eye-slash"
              />
            ) : (
              // Show the markup value when markup is visible
              <div className="flex gap-1 items-center justify-end overflow-hidden">
                {!data?.is_markup_percentage && Boolean(!isReadOnly) ? (
                  <Tooltip
                    title={_t(
                      `To edit a ${
                        formatter().currency_symbol
                      } amount View and Edit the Item`
                    )}
                    placement="top"
                  >
                    <FontAwesomeIcon
                      className="w-3.5 h-3.5 text-primary-900"
                      icon="fa-regular fa-circle-info"
                    />
                  </Tooltip>
                ) : (
                  ""
                )}
                <Tooltip title={mu}>
                  <Typography
                    className={`table-tooltip-text ${
                      !data?.is_markup_percentage && Boolean(!isReadOnly)
                        ? "!max-w-[42px]"
                        : ""
                    }`}
                  >
                    {mu === "" ? "-" : mu}
                  </Typography>
                </Tooltip>
              </div>
            )}
          </div>
        );
      },
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          let nVal = params.newValue?.toString();
          if (!nVal || nVal == null) {
            const _updateData = {
              ...params.data,
              markup: null,
            };
            params.node.setData(_updateData);
            updateItemField({
              itemId: _updateData.item_id,
              itemData: params.data,
              updatedItem: {
                markup: null,
              },
            });
            return true;
          }
          if (Number(params.newValue) < 0) {
            notification.error({
              description: _t("Negative values are not allowed for Markup."),
            });
            return false;
          }
          const checkNum = qtyNumberCheck(nVal);

          if (
            nVal &&
            nVal.toString().length > 3 &&
            (!nVal.toString().includes(".") || !checkNum)
          ) {
            notification.error({
              description: "Markup should be less than or equal to 3 digits.",
            });
            return false;
          }
          if (!wholeNumberRegex.test(nVal)) {
            notification.error({
              description: _t("Decimal is not allowed in Markup"),
            });
            return false;
          }
          if (nVal.toString().includes(".")) {
            nVal = nVal.toFixed(0);
          }
          const updatedData = {
            ...params.data,
            markup: Number(nVal),
          };
          params.node.setData(updatedData);
          updateItemField({
            itemId: updatedData.item_id,
            itemData: params.data,
            updatedItem: {
              markup: `${Number(nVal)}`,
            },
          });
        }
        return true;
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 130,
      maxWidth: 130,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ data }: { data: ESEstimateItem }) => {
        const totalFloat = itemTotalCalculator(data, !flags?.isMarkupHidden);
        const value = formatter(
          formatAmount((totalFloat || 0) / 100)
        )?.value_with_symbol;
        return (
          <>
            <Tooltip title={value}>
              <Typography className="table-tooltip-text">
                {value || "-"}
              </Typography>
            </Tooltip>
          </>
        );
      },
    },
    {
      headerName: _t("Tax"),
      field: "apply_global_tax",
      minWidth: 50,
      maxWidth: 50,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: ({ data }: { data: ESEstimateItem }) => {
        return (
          <div className="flex items-center gap-2 justify-center">
            <CustomCheckBox
              checked={
                typeof data?.apply_global_tax === "boolean"
                  ? data?.apply_global_tax
                  : data?.apply_global_tax?.toString() === "1"
              }
              disabled={
                isReadOnly ||
                Object.values(isLoadingCheckBox).some((isLoading) => isLoading)
              }
              loadingProps={{
                isLoading: isLoadingCheckBox?.[data?.item_id] || false,
                className: "bg-[#ffffff]",
              }}
              onChange={() => {
                // handleChange(params, "is_billable");
                updateItemField({
                  itemId: data?.item_id,
                  itemData: data,
                  updatedItem: {
                    apply_global_tax: data?.apply_global_tax ? 0 : 1,
                  },
                });
              }}
              className="gap-0"
              name="apply_global_tax"
            />
          </div>
        );
      },
    },
    {
      headerName: _t("Assigned To"),
      field: "assigned_to",
      sortable: true,
      maxWidth: 120,
      minWidth: 120,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ data }: { data: ESEstimateItem }) => {
        return data?.assignee_name ? (
          <Tooltip
            title={HTMLEntities.decode(sanitizeString(data?.assignee_name))}
          >
            <div className="w-fit mx-auto overflow-hidden">
              <AvatarProfile
                user={{
                  name: HTMLEntities.decode(
                    sanitizeString(data?.assignee_name)
                  ),
                  image:
                    data?.assigned_to_contact_id == 0 ? data?.user_image : "",
                }}
                className="m-auto"
                iconClassName="text-[11px] font-semibold"
              />
            </div>
          </Tooltip>
        ) : (
          <div className="w-fit mx-auto overflow-hidden">-</div>
        );
      },
    },
    {
      headerName: "",
      field: "",
      minWidth: 80,
      maxWidth: 80,
      suppressMenu: true,
      cellRenderer: ({ data }: { data: ESEstimateItem }) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={() => {
                setItemsOpen(true);
                setItemsData(data);
              }}
            />

            {!isReadOnly && (
              <ButtonWithTooltip
                tooltipTitle={_t("Delete")}
                disabled={isReadOnly}
                tooltipPlacement="top"
                icon="fa-regular fa-trash-can"
                onClick={() => {
                  setSelectedItemId(Number(data?.item_id) || 0);
                  setSelectedSectionId(Number(data?.section_id) || 0);

                  setIsDeleteConfirmOpen(true);
                }}
              />
            )}
          </div>
        );
      },
    },
  ];
  // for row sckeelton we will use in future
  // const limitofRow = useMemo(() => {
  //   const lengthof = singleSectionItems?.length ?? 0;
  //   if (lengthof > 50) {
  //     return 50;
  //   } else if (lengthof < 1) {
  //     return 6;
  //   } else {
  //     return lengthof;
  //   }
  // }, [singleSectionItems]);
  const CollapseSingleTableMemo = useMemo(() => {
    return (
      <div
        key={`${singleSection?.section_id}-${singleSection?.code_id}`}
        style={{ opacity: isDragging ? 0.5 : 1 }}
      >
        <CollapseSingleTable
          key={`${singleSection?.section_id}-${singleSection?.code_id}`}
          style={{
            opacity: isDragging ? 0.5 : 1,
            transform: isDragging ? "scale(1.05)" : "scale(1)",
            transition: "transform 0.2s ease, opacity 0.2s ease", // Smooth animation
          }}
          defaultActiveKey={default_item_view ? [] : [1]}
          title={_t(
            `${HTMLEntities.decode(
              sanitizeString(singleSection?.section_name ?? "")
            )}`
          )}
          className={`${
            isReadOnly ? "" : "move-collapse-table"
          } est-item-table`}
          leftsideContant={
            !isReadOnly && (
              <div ref={dragAndDropRef as any}>
                <ButtonWithTooltip
                  icon="fa-solid fa-grip-dots"
                  tooltipTitle={_t("Move")}
                  tooltipPlacement="top"
                  iconClassName="w-3.5 h-3.5"
                  className="hover:!bg-primary-8 active:!bg-primary-8 cursor-move absolute top-3.5 left-[5px]"
                  onClick={() => {}}
                />
              </div>
            )
          }
          rightsideContant={
            <div className="flex items-center gap-1 flex-wrap justify-end">
              <div className="flex items-center gap-1.5 mr-2 bg-primary-8 text-primary-900 py-[3px] px-[9px] rounded-sm dark:bg-dark-800 dark:text-white/90 w-fit whitespace-nowrap">
                <FontAwesomeIcon
                  className="w-4 h-4"
                  icon="fa-duotone fa-solid fa-money-check-dollar"
                />
                <Typography className="!mb-0 !text-sm !text-primary-900 leading-4 !font-semibold dark:!text-white/90 whitespace-nowrap">
                  {/* {sectionTotal} */}
                  {
                    formatter(formatAmount(SingleSectionMetrics?.grandTotal))
                      ?.value_with_symbol
                  }
                  {/* {allTotals?.totalGrandTotal} */}
                </Typography>
              </div>
              {!isReadOnly && (
                <DropdownMenu
                  options={ADD_ITEMS_MODIFY_SECTION?.map((item) => {
                    return {
                      ...item,
                      onClick: () =>
                        item.onClick && item?.onClick(singleSection),
                    };
                  })}
                  buttonClass="w-fit h-auto m-0"
                  contentClassName="w-fit add-items-drop-down"
                  placement="bottomRight"
                >
                  <div className="py-1 sm:px-2.5 px-2 bg-[#DFE1E4] rounded flex items-center gap-[5px]">
                    <Typography className="text-primary-900 text-sm">
                      {_t("Add Items / Modify Section")}
                    </Typography>
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900"
                      icon="fa-regular fa-chevron-down"
                    />
                  </div>
                </DropdownMenu>
              )}
              <ButtonWithTooltip
                icon="fa-solid fa-eye"
                tooltipTitle={_t("View")}
                tooltipPlacement="top"
                iconClassName="w-3.5 h-3.5"
                className="hover:!bg-primary-8 active:!bg-primary-8 ml-1"
                onClick={() => {
                  setSectionOpen(true);
                  setSectionData(singleSection);
                }}
              />
            </div>
          }
          children={
            <div ref={ref}>
              {isInView ? (
                <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
                  <div className="ag-theme-alpine">
                    <StaticTable
                      ref={gridRef}
                      suppressContextMenu={true}
                      suppressDragLeaveHidesColumns={true}
                      suppressMoveWhenRowDragging={true}
                      className="static-table"
                      suppressRowClickSelection={true}
                      rowDragManaged={!isReadOnly}
                      animateRows={true}
                      onRowDragEnd={(e) => handleDragEnd(e, singleSection)}
                      // rowDragManaged={true}
                      // rowDragEntireRow={true}
                      // onRowDragEnd={(params) => gridOptions.onRowDragEnd(params)}
                      onGridReady={(params) =>
                        onGridReady(index, params, singleSection as ESSection)
                      }
                      stopEditingWhenCellsLoseFocus={true}
                      columnDefs={columnDefs}
                      getRowClass={(params) => {
                        const sectionId = params.node.data.section_id;
                        const rowIndex = params.node.rowIndex;
                        return `section-${sectionId} row-${rowIndex} hoverablerow`; // Add custom classes to rows
                      }}
                      rowData={
                        singleSectionItems?.sort(
                          (a: ESEstimateItem, b: ESEstimateItem) =>
                            Number(a?.order_number!) - Number(b?.order_number)
                        ) ?? []
                      }
                      noRowsOverlayComponent={() => (
                        <NoRecords
                          image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                        />
                      )}
                    />
                  </div>
                </div>
              ) : (
                <div className="w-full common-card flex justify-center items-center h-20">
                  <Spin />
                </div>
                // <div
                //   className=" animate-pulse bg-green-400"
                //   style={{
                //     height: `${
                //       (singleSection?.items?.length ?? 0) * 40 || 400
                //     }px`,
                //   }}
                // >
                //   {" "}
                // </div>
              )}
            </div>
          }
        />
      </div>
    );
  }, [
    singleSection,
    flags,
    default_item_view,
    isReadOnly,
    SelectedMul,
    isLoadingCheckBox,
    sections?.length,
    selectedItems,
    SingleSectionMetrics,
    columnDefs,
    isInView,
  ]);

  return (
    <>
      {CollapseSingleTableMemo}

      <ConfirmModal
        isOpen={Boolean(
          costchangeConfirmOpen !== null &&
            costchangeConfirmOpen?.item_id &&
            !isReadOnly
        )}
        modaltitle={_t("Confirmation")}
        isLoading={
          costchangeConfirmOpen?.item_id !== undefined &&
          isLoadingCheckBox?.[costchangeConfirmOpen?.item_id]
        }
        description={`The item price in the Estimate does not match the price in your Cost Items Database. Do you want to import the current price of ${
          formatter(
            formatAmount(
              +(costchangeConfirmOpen?.updated_unit_cost ?? "0") / 100
            )
          ).value_with_symbol
        }?`}
        onAccept={async () => {
          if (costchangeConfirmOpen && costchangeConfirmOpen?.item_id) {
            setIsLoadingCheckBox((prev) => ({
              ...prev,
              [String(costchangeConfirmOpen?.item_id)]: true,
            }));
            await updateItemField({
              itemId: costchangeConfirmOpen?.item_id,
              itemData: costchangeConfirmOpen as ESEstimateItem,
              updatedItem: {
                unit_cost: `${Number(
                  costchangeConfirmOpen?.updated_unit_cost
                )}`,
              },
            });
          }

          setCostChangeConfirmation(null);
        }}
        onDecline={() => setCostChangeConfirmation(null)}
        onCloseModal={() => setCostChangeConfirmation(null)}
        descriptionclassName="flex text-center justify-center"
        modalIcon="fa-regular fa-file-check"
      />
      {newUnitName.trim() && window.ENV.ENABLE_UNIT_DROPDOWN && (
        <ConfirmModal
          isOpen={Boolean(newUnitName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newUnitName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => {
            setNewUnitName("");
            setSelectedData(undefined);
          }}
          onAccept={async () => {
            if (!isAddingCustomData && newUnitName) {
              setIsAddingCustomData(true);
              const response = (await addUnit({
                name: newUnitName,
              })) as IUnitAddResponse & {
                data: {
                  data: IUnitListResponseDataAndStatusCode["units"][0];
                };
              };
              if (response.success) {
                const newUnits = [response.data.data, ...units];
                setUnits(newUnits);
                setNewUnitName("");
                setSelectedData(undefined);
                const api = gridRef.current?.api;
                if (!api) return;

                const renderedNodes = api.getRenderedNodes();

                if (renderedNodes && selectedData?.item_id) {
                  const currentRowNode = renderedNodes.find(
                    (node) => node.data?.item_id === selectedData?.item_id
                  );
                  if (currentRowNode && currentRowNode.data) {
                    const oldData = { ...currentRowNode.data };
                    currentRowNode?.setData({
                      ...currentRowNode.data,
                      unit: newUnitName,
                    });
                    const response = await updateItemField({
                      itemId: selectedData.item_id,
                      itemData: selectedData,
                      updatedItem: {
                        unit: newUnitName,
                      },
                    });
                    if (!response.success) {
                      currentRowNode?.setData(oldData);
                      notification.error({ description: response.message });
                    }
                  }
                }
                const existingColDefs = api.getColumnDefs();
                if (!existingColDefs) return;

                const updatedColDefs = existingColDefs.map((col) =>
                  "field" in col && col.field === "unit"
                    ? {
                        ...col,
                        filterParams: {
                          values:
                            newUnits.map((unit) => ({
                              label: unit.name?.toString(),
                              value: unit.name?.toString(),
                            })) ?? [],
                        },
                        cellEditorParams: {
                          ...col.cellEditorParams,
                          values: newUnits,
                        },
                      }
                    : col
                );

                api.setColumnDefs(updatedColDefs);

                // Ensure the grid header re-renders
                api.refreshHeader();
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => {
            setNewUnitName("");
            setSelectedData(undefined);
          }}
        />
      )}
    </>
  );
};

export default DragComponent;
