import { useEffect, useMemo } from "react";
import { getApiData } from "~/helpers/axios-api-helper";
import { getApiDefaultParams, sanitizeString } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import { pdfTemplateActions } from "~/redux/action/pdfTemplateActions";
import { estimateRoutes } from "~/route-services";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// molecules
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
// Other
import {
  setEsTemplateLoading,
  setPdfTemplateList,
  setPdfUrl,
  setSelectedTemplateOption,
  setSentEmailData,
  updateSelectedEsTemplateData,
} from "../../../redux/slices/ESFinaliseSlice";
import { useAppESDispatch, useAppESSelector } from "../../../redux/store";
import { getDynamicOp } from "../../../utils/common";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { useGlobalModule } from "~/zustand/global/modules/slice";

const TemplateDetails = ({ setIsDocUpdated }: ISelectTemplateProps) => {
  const { _t } = useTranslation();
  const { getGlobalModuleByKey } = useGlobalModule();

  const EstimateModule = useMemo(
    () => getGlobalModuleByKey(CFConfig.estimate_module),
    [getGlobalModuleByKey] // Ensures memoization based on function reference
  );

  const { module_id: estimate_module_id = 0 } = EstimateModule || {};
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { estimateDetail = {} } = useAppESSelector(
    (state) => state.estimateDetail
  );
  const { coverSheetDetail } = useAppESSelector(
    (state) => state.getEstimateCoverSheet
  );
  const {
    sent_email_data,
    isEditDocumentClicked,
    pdfTemplateList,
    selectedTemplateOption,
  } = useAppESSelector((state) => state.estimateFinalise);

  const dispatch = useAppESDispatch();

  async function getSelectedTemplateHtml() {
    dispatch(updateSelectedEsTemplateData(null));
    if (!!selectedTemplateOption && selectedTemplateOption?.pdf_value) {
      dispatch(setEsTemplateLoading(true));

      getApiData({
        url: estimateRoutes.getPdfTemplateDetails,
        method: "post",
        data: getApiDefaultParams({
          op: getDynamicOp(selectedTemplateOption?.pdf_value),
          user,
          otherParams: {
            t_id:
              selectedTemplateOption?.company_template_id?.toString() ??
              selectedTemplateOption?.template_id?.toString(),
            module_id: estimate_module_id,
            ViewPDFOnly: 0,
            action: "html",
            master_tpl: selectedTemplateOption?.master_tpl,
            estimate_id: estimateDetail?.estimate_id,
          },
        }),
        success: (
          response: IDownloadDocumentApiRes & {
            html?: string;
          }
        ) => {
          if (response?.html) {
            dispatch(
              updateSelectedEsTemplateData({
                ...selectedTemplateOption,
                html: response?.html,
              })
            );
          }
          dispatch(setEsTemplateLoading(false));
        },
        error: (error) => {
          notification.error({
            description: error,
          });
          dispatch(setEsTemplateLoading(false));
        },
      });
    }
  }

  useEffect(() => {
    if (selectedTemplateOption?.value) {
      getSelectedTemplateHtml();
    }
  }, [selectedTemplateOption?.value, estimateDetail?.estimate_id]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = (await pdfTemplateActions({
          module_id: estimate_module_id,
          status: 1,
          record_id: estimateDetail?.estimate_id || "",
        })) as IPDFFileTemplateRes & ISentEmailData;

        if (response?.success) {
          if (response?.sent_email_data) {
            dispatch(
              setSentEmailData({
                ...(response?.sent_email_data ?? {}),
                old_sent_document: response?.old_sent_document,
              })
            );
          }

          let arrCustomizeTpl = response?.customize_tpl?.length
            ? response?.customize_tpl?.map(
                (item: Partial<IPDFFileTemplateData>) => {
                  if (item?.default_tpl === "1") {
                    dispatch(
                      setSelectedTemplateOption({
                        ...item,
                        value:
                          item?.company_template_id?.toString() ??
                          item?.template_id?.toString(),
                        label: HTMLEntities.decode(
                          sanitizeString(item?.template_name)
                        ),
                      })
                    );
                  }
                  return {
                    ...item,
                    value:
                      item?.company_template_id?.toString() ??
                      item?.template_id?.toString(),
                    label: HTMLEntities.decode(
                      sanitizeString(item?.template_name)
                    ),
                  };
                }
              )
            : [];
          let arrDefaultTpl = response?.default_tpl?.length
            ? response?.default_tpl?.map(
                (item: Partial<IPDFFileTemplateData>) => {
                  if (item?.default_tpl === "1") {
                    dispatch(
                      setSelectedTemplateOption({
                        ...item,
                        value:
                          item.company_template_id?.toString() ??
                          item.template_id?.toString(),
                        label: HTMLEntities.decode(
                          sanitizeString(item?.template_name)
                        ),
                      })
                    );
                  }
                  return {
                    ...item,
                    value:
                      item.company_template_id?.toString() ??
                      item.template_id?.toString(),
                    label: HTMLEntities.decode(
                      sanitizeString(item?.template_name)
                    ),
                  };
                }
              )
            : [];

          dispatch(
            setPdfTemplateList([
              {
                label: "My Customized Templates",
                value: "",
              },
              ...arrCustomizeTpl,
              {
                label: "Original Templates",
                value: "",
              },
              ...arrDefaultTpl,
            ] as IPDFFilePreviewTemplateOptions[])
          );
        } else {
          notification.error({
            description: response.message,
          });
          // onCloseModal();
          dispatch(setSelectedTemplateOption(null));
          // setFileUrl("");
          dispatch(setPdfTemplateList([]));
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    if (estimate_module_id && estimateDetail?.estimate_id) {
      fetchData();
    }
  }, [estimateDetail?.estimate_id, estimate_module_id]);

  useEffect(() => {
    // refresh preview when show_in_pdf changes
    getSelectedTemplateHtml();
  }, [coverSheetDetail?.show_in_pdf]);

  useEffect(() => {
    if (
      isEditDocumentClicked &&
      sent_email_data?.email_template_name &&
      pdfTemplateList?.length
    ) {
      const getTemplateData = pdfTemplateList?.find(
        (item) => item?.pdf_value === sent_email_data?.email_template_name
      );
      if (getTemplateData) {
        dispatch(setSelectedTemplateOption(getTemplateData));
        dispatch(setPdfUrl(null));
      }
    }
  }, [sent_email_data, pdfTemplateList, isEditDocumentClicked]);

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Select Your Template:​")}
        hideBorder={true}
        customContent={
          <div className="flex items-center justify-center rounded-full w-[30px] h-[30px] flex-shrink-0 bg-[linear-gradient(180deg,#7FA3FF1a_0%,#3387FD1a_100%)]">
            <Typography className="text-transparent bg-clip-text bg-[linear-gradient(180deg,#7FA3FF_0%,#3387FD_100%)] font-bold text-base">
              1
            </Typography>
          </div>
        }
        leftContentClassName="!flex !flex-row items-center"
        headerLeftContent={
          <Tooltip
            title={_t(
              "Select a template from your available templates to generate a preview"
            )}
          >
            <FontAwesomeIcon
              className="text-base w-3.5 h-3.5 text-primary-900/80 dark:text-white/90"
              icon="fa-solid fa-circle-info"
            />
          </Tooltip>
        }
        headerRightButton={
          <DropdownMenu
            options={
              pdfTemplateList?.map(
                (
                  item: IPDFFileTemplateData & {
                    disabled?: boolean;
                    value?: string;
                  }
                ) => {
                  const updatedItem = { ...item };
                  if (item.value === "") {
                    updatedItem.disabled = true;
                  }
                  return updatedItem;
                }
              ) ?? []
            }
            onOptionClick={(selectedItem) => {
              dispatch(setSelectedTemplateOption(selectedItem));
              setIsDocUpdated?.(true);
            }}
            buttonClass="w-fit h-auto"
            contentClassName="w-full add-items-drop-down estimate-finalize-dropdown"
            placement="bottomRight"
          >
            <div className="py-1 px-2.5 bg-[#EBF1F9] rounded flex items-center gap-[5px]">
              <Typography className="text-primary-900 text-sm">
                {selectedTemplateOption?.label
                  ? selectedTemplateOption?.label
                  : _t("Select Your Template")}
              </Typography>
              <FontAwesomeIcon
                className="w-3 h-3 text-primary-900"
                icon="fa-regular fa-chevron-down"
              />
            </div>
          </DropdownMenu>
        }
      />
    </>
  );
};

export default TemplateDetails;
