import { useEffect, useState } from "react";
import { useTranslation } from "~/hook";
// molecules
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
// Other
import { SendEmailModal } from "~/shared/components/molecules/sendEmailModal";
import { useAppESSelector } from "../../redux/store";
import { isValidId } from "../../utils/common";
import AvailableDocuments from "./estimatefinalize/AvailableDocuments​";
import ConfirmAttachedFiles from "./estimatefinalize/ConfirmAttachedFiles​";
import PreviewEstimate from "./estimatefinalize/PreviewEstimate​";
import TemplateDetails from "./estimatefinalize/Template";
import useSubmitForApproval from "./estimatefinalize/useSubmitForApproval";
import { ReSubmitEstimatePopup } from "./modal";
import { sanitizeString } from "~/helpers/helper";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";

const EstimateFinalizeTab = ({ isReadOnly }: IEReadOnlyComponent) => {
  const { _t } = useTranslation();
  const { estimateDetail } = useAppESSelector((state) => state.estimateDetail);
  const {
    sent_email_data,
    selectedEsTemplateData,
    pdfUrl,
    isEditDocumentClicked,
    mailBody,
    customEmailTitle,
  } = useAppESSelector((state) => state.estimateFinalise);
  const { submitForApprovalApiCall, isSubmitLoading } = useSubmitForApproval();

  const [isReSubmitEst, setIsResubmit] = useState<boolean>(false);
  const [isResubmitConfirm, setIsResubmitConfirm] = useState<boolean>(false);
  const [isDocUpdated, setIsDocUpdated] = useState<boolean>(false);
  const [isConfirmOpen, setIsConfirmOpen] = useState<boolean>(false);
  const [isEmailBodyOpen, setIsEmailBodyOpen] = useState<boolean>(false);
  const [ApprovalSubject, setApprovalSubject] = useState<string>("");
  const [submitForApprovalMessage, setSubmitForApprovalMessage] =
    useState<string>("");

  async function submitApprovalhandler() {
    if (
      Number(sent_email_data?.old_sent_document) > 0 &&
      !isEditDocumentClicked &&
      !isReSubmitEst &&
      isValidId(sent_email_data?.company_document_id) &&
      sent_email_data?.date_added &&
      estimateDetail?.approval_type_key !== "estimate_approved" &&
      estimateDetail?.approval_type_key !== "estimate_completed"
    ) {
      setIsResubmit(true);
      return;
    }

    if (isReSubmitEst) {
      if (pdfUrl && selectedEsTemplateData?.pdf_value) {
        submitForApprovalApiCall({
          pdfUrl,
          pdf_name: selectedEsTemplateData?.pdf_value ?? "",
          company_document_id: sent_email_data?.company_document_id,
          customMailBody: sent_email_data?.mail_body,
          customEmailSubject: sent_email_data?.email_subject,
        });
      } else {
        notification.error({
          description: "Create Estimate Preview First to Submit",
        });
      }
      return;
    }
    setIsEmailBodyOpen(true);
  }

  useEffect(() => {
    setApprovalSubject(customEmailTitle || estimateDetail?.email_subject || "");
    setSubmitForApprovalMessage(mailBody || "");
  }, [mailBody, customEmailTitle]);

  return (
    <>
      <div className="h-full">
        <div className="grid xl:grid-cols-2 gap-2.5 h-full">
          <div className="flex flex-col gap-2.5">
            <div className="flex flex-col gap-2.5 min-[1400px]:max-h-[calc(100dvh-435px)] min-[1400px]:overflow-y-auto">
              <div className="py-3 px-[15px] common-card">
                <TemplateDetails setIsDocUpdated={setIsDocUpdated} />
              </div>
              <DndProvider backend={HTML5Backend}>
                <div className="py-3 px-[15px] common-card">
                  <AvailableDocuments setIsDocUpdated={setIsDocUpdated} />
                </div>
              </DndProvider>
              <div className="py-3 px-[15px] common-card">
                <ConfirmAttachedFiles />
              </div>
            </div>
            <div className="text-center">
              <PrimaryButton
                isLoading={isSubmitLoading}
                disabled={isSubmitLoading || !pdfUrl}
                type="primary"
                htmlType="button"
                className={`h-[38px] !shadow-none !duration-1000 ease-in-out w-fit m-auto px-7 orange-button ${
                  isSubmitLoading || !pdfUrl
                    ? "!bg-deep-orange-500/60 border-0 !hover:!bg-[#FF5400]"
                    : "!bg-[#FF5400] hover:!bg-[#FF5400]"
                }`}
                // className="h-[38px] !bg-[#FF5400] hover:!bg-[#FF5400] active:!bg-[#FF5400] !shadow-none !duration-1000 ease-in-out w-fit m-auto px-7 orange-button"
                onClick={() => {
                  submitApprovalhandler();
                  // setIsConfirmOpen(true);
                }}
                buttonText={_t("Submit to Client")}
              />
            </div>
          </div>

          <div className="py-3 px-[15px] common-card h-full">
            <PreviewEstimate
              setIsDocUpdated={setIsDocUpdated}
              isDocUpdated={isDocUpdated}
            />
          </div>
        </div>
      </div>

      <ConfirmModal
        isOpen={isConfirmOpen}
        modalIcon="fa-regular fa-file-check"
        modaltitle={_t("Confirmation")}
        description={_t("Send approval request via email now.")}
        onCloseModal={() => setIsConfirmOpen(false)}
        onAccept={() => {
          setIsConfirmOpen(false);
        }}
        onDecline={() => setIsConfirmOpen(false)}
      />
      {isEmailBodyOpen && !isReSubmitEst && (
        <SendEmailModal
          isOpen={isEmailBodyOpen}
          onCloseModal={() => setIsEmailBodyOpen(false)}
          ApprovalSubject={ApprovalSubject}
          setApprovalSubject={setApprovalSubject}
          submitForApprovalMessage={submitForApprovalMessage}
          setSubmitForApprovalMessage={setSubmitForApprovalMessage}
          customer_name={
            HTMLEntities.decode(
              sanitizeString(
                estimateDetail?.billed_to_name || estimateDetail?.customer_name
              )
            ) || "0"
          }
          onSendClick={async () => {
            if (pdfUrl) {
              await submitForApprovalApiCall({
                pdfUrl,
                pdf_name: selectedEsTemplateData?.pdf_value ?? "",
                company_document_id: sent_email_data?.company_document_id,
                customMailBody: submitForApprovalMessage,
                customEmailSubject: ApprovalSubject,
              });
            } else {
              notification.error({
                description: "Create Estimate Preview First to Submit",
              });
            }
            setIsEmailBodyOpen(false);
          }}
          isSumbitForApprovalLoading={isSubmitLoading}
        />
      )}

      {isResubmitConfirm && (
        <ConfirmModal
          isOpen={isResubmitConfirm}
          modalIcon="fa-regular fa-file-check"
          modaltitle={_t("Confirmation")}
          isLoading={isSubmitLoading}
          description={_t("Are you sure you want to resubmit?")}
          onCloseModal={() => setIsResubmitConfirm(false)}
          onAccept={() => {
            submitApprovalhandler();
          }}
          onDecline={() => setIsResubmitConfirm(false)}
        />
      )}
      {isReSubmitEst && !isResubmitConfirm && (
        <ReSubmitEstimatePopup
          resubmitClickHandler={() => {
            setIsResubmitConfirm(true);
          }}
          isOpen={isReSubmitEst}
          onClose={() => {
            setIsResubmit(false);
          }}
        />
      )}
    </>
  );
};

export default EstimateFinalizeTab;
