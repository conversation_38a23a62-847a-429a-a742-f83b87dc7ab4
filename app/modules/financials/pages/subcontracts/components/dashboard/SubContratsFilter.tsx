import { useMemo } from "react";
import {
  defaultModuleFilter,
  getGConfig,
  getGModuleDashboard,
  getGModuleFilters,
} from "~/zustand";
import { getProjectModules } from "~/zustand/global-project-complete/store";
import ModuleDashboardFilter from "~/components/page/common/page-dashboard-header/filter";
import { RECORD_STATUS_OPTION, STATUS_CODE } from "~/shared/constants";
import { defaultConfig } from "~/data";
import { useTranslation } from "~/hook";
import { sanitizeString } from "~/helpers/helper";
import dayjs from "dayjs";

const SubContratsFilter = ({
  onClearSearch,
  kanbanView = false,
}: {
  onClearSearch: () => void;
  kanbanView?: boolean;
}) => {
  const { _t } = useTranslation();
  const { module_name, module_id, module_key }: GConfig = getGConfig();
  const gProjectModules: IProjectModules = getProjectModules();
  const filter = getGModuleFilters() as
    | Partial<ISubContratsModuleFilter>
    | undefined;

  const gModuleDashboard: Partial<GModuleDashboard> = getGModuleDashboard();

  const StatusbarOption: Array<ModuleStatus> | undefined =
    gModuleDashboard?.module_setting?.module_status;

  const subContractorStatusList = StatusbarOption?.map(
    (item: ModuleStatus) => ({
      label: HTMLEntities.decode(sanitizeString(item.name)),
      value: item?.key ?? "",
    })
  );

  const filterItems: FilterItem<Partial<ISubContratsModuleFilter>>[] =
    useMemo(() => {
      return [
        {
          type: "datepicker",
          label: "Date Range",
          valueSetter: ({
            start_date = "",
            end_date = "",
          }: Partial<ISubContratsModuleFilter>) => {
            const startDate = start_date
              ? dayjs(start_date)?.format("YYYY-MM-DD")
              : "";
            const endDate = end_date
              ? dayjs(end_date)?.format("YYYY-MM-DD")
              : "";

            return [startDate, endDate];
          },
          valueGetter: (data: [string, string] | undefined) => {
            let filter: Partial<IExpenseFilter> = {};
            if (data) {
              filter = {
                start_date: data[0],
                end_date: data[1],
              };
            }
            return {
              start_date: filter.start_date || "",
              end_date: filter.end_date || "",
            };
          },
          name: "date_range",
          id: "date_range",
        },
        {
          type: "project",
          label: "Projects",
          valueSetter: ({ project }: Partial<ISubContratsModuleFilter>) => {
            return project
              ? project
                  .toString()
                  .split(",")
                  .filter((id) => id !== "0" && id !== "")
                  .map((id) => ({ id: Number(id) }))
              : [];
          },
          valueGetter: (
            project: Partial<Project> | Array<Partial<Project>> | undefined
          ) => {
            if (project) {
              return Array.isArray(project)
                ? {
                    project:
                      project
                        .map((item: Partial<Project>) => item?.id)
                        .join(",") ?? "",
                    project_names:
                      project.length > 2
                        ? `${project.length} Selected`
                        : project
                            .map((item: Partial<Project>) =>
                              item?.project_name?.replaceAll(`\\"`, "")
                            )
                            .join(","),
                  }
                : {
                    project: project.id?.toString() || "",
                    project_names: project.project_name || "",
                  };
            }
            return {
              project: "",
              project_names: "",
            };
          },
          name: "project",
          projectApiParams: {
            is_completed:
              Number(gProjectModules?.[module_key as keyof IProjectModules]) !==
              1,
          },
        },
        {
          type: "customer",
          label: "Directory",
          app_access: false,
          valueSetter: ({
            directory = "",
          }: Partial<ISubContratsModuleFilter>) => {
            return directory
              .toString()
              .split(",")
              .filter((user_id) => user_id !== "0" && user_id !== "")
              .map((user_id) => ({ user_id: Number(user_id) }));
          },
          valueGetter: (
            directory:
              | Partial<CustomerSelectedData>
              | Array<Partial<CustomerSelectedData>>
              | undefined
          ) => {
            if (directory) {
              return Array.isArray(directory)
                ? {
                    directory:
                      directory
                        .map(
                          (item: Partial<CustomerSelectedData>) => item.user_id
                        )
                        .join(",") ?? "",
                    directory_names:
                      directory.length > 2
                        ? `${directory.length} Selected`
                        : directory
                            .map(
                              (item: Partial<CustomerSelectedData>) =>
                                item.display_name
                            )
                            .join(","),
                  }
                : {
                    directory: directory.user_id?.toString() || "",
                    directory_names: directory.display_name || "",
                  };
            }
            return {
              directory: "",
              directory_names: "",
            };
          },
          name: "customer",
          options: [
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            defaultConfig.misc_contact_key,
            "by_service",
          ],
          showNewDirectoryAddButton: false,
        },
        {
          type: "select",
          label: _t("Status"),
          options: subContractorStatusList ?? [],
          valueSetter: ({
            sub_contract_status = "",
          }: Partial<ISubContratsModuleFilter>) => {
            if (sub_contract_status == "0" || sub_contract_status == "") {
              return [];
            } else {
              return subContractorStatusList
                ? sub_contract_status
                    .split(",")
                    .filter((ele: string) => ele !== "") ?? []
                : [];
            }
          },
          valueGetter: (
            _: string | string[] | undefined,
            options: DefaultOptionType | DefaultOptionType[] | undefined
          ) => {
            if (Array.isArray(options)) {
              return {
                sub_contract_status:
                  options
                    .map((option: DefaultOptionType) => option?.value)
                    .join(",") ?? "",
                sub_contract_names:
                  (options || [])
                    .map((option: DefaultOptionType) => option?.label)
                    .join(", ") ?? "",
              };
            }
            return {
              sub_contract_status: "",
              sub_contract_names: "",
            };
          },
          name: "sub_contract_status",
          multiple: true,
          showSearch: false,
        },
        {
          type: "select",
          label: _t("Record Status"),
          options: RECORD_STATUS_OPTION,
          defaultSelectedValue: defaultModuleFilter.sub_contrats_module.status,
          valueSetter: ({ status = "" }: Partial<ISubContratsModuleFilter>) =>
            status || STATUS_CODE.ACTIVE,
          valueGetter: (data: string | string[] | undefined) => {
            if (!Array.isArray(data)) {
              return {
                status: data,
              };
            }
            return {
              status: STATUS_CODE.ACTIVE,
            };
          },
          name: "status",
        },
      ];
    }, [StatusbarOption]);

  const filterComponent = useMemo(() => {
    return (
      <ModuleDashboardFilter<Partial<ISubContratsModuleFilter>>
        filterItems={filterItems}
        popoverParams={{
          itemLabelClassName: "min-w-[104px]",
        }}
        title={module_name}
        moduleId={module_id}
        filter={filter}
        defaultValue={defaultModuleFilter.sub_contrats_module}
        onResetClick={() => onClearSearch()}
        kanbanView={kanbanView}
      />
    );
  }, [
    filterItems,
    defaultModuleFilter.sub_contrats_module,
    filter,
    kanbanView,
  ]);

  return filterComponent;
};

export default SubContratsFilter;
