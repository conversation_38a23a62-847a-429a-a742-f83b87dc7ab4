import { useEffect, useMemo, useRef, useState } from "react";
import { useParams } from "@remix-run/react";
// Ag grid
import { ValueGetterParams, ValueSetterParams } from "ag-grid-community";
// Hook Redux
import { getGConfig } from "~/zustand";
import { useTranslation } from "~/hook";
import { qtyNumberCheck } from "~/shared/utils/helper/common";
import {
  useAppSCDispatch,
  useAppSCSelector,
} from "~/modules/financials/pages/subcontracts/redux/store";
import {
  deleteSCItems,
  fetchSCEstimateItem,
  updateSCOriginalItemsApi,
} from "~/modules/financials/pages/subcontracts/redux/action";
import { resetDash } from "~/modules/financials/pages/subcontracts/redux/slices";
import { updateEstimateItems } from "~/modules/financials/pages/subcontracts/redux/slices/sCItemsSlice";
import { SCIcons } from "~/modules/financials/pages/subcontracts/utils/constants";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import { floatWithNegativeRegex } from "~/modules/financials/pages/changeOrder/utils/helpers";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { AddSubContractItem } from "../sidebar/subContractItem";
import { ColDef } from "ag-grid-community";

import UnitCellEditor from "~/modules/settings/costItemsDatabase/components/unitCellEditor/UnitCellEditor";
import { addUnit, getUnitList } from "~/redux/action/unitActions";

const SubContractItems = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const { _t } = useTranslation();
  const { id: sCId }: RouteParams = useParams();
  const gConfig: GConfig = getGConfig();
  const { formatter } = useCurrencyFormatter();
  const dispatch = useAppSCDispatch();
  const { estimateItem, isEstimateItemLoading }: ISCItemsInitialState =
    useAppSCSelector((state) => state.subContractsItems);
  const { details }: ISCDetailsInitialState = useAppSCSelector(
    (state) => state.subContractsDetails
  );
  const { codeCostData }: IGetCostCodeList = useAppSCSelector(
    (state) => state.costCode
  );
  const { filter }: ISCCommonInitialState = useAppSCSelector(
    (state) => state.sCCommonData
  );

  const [selectedSCOItemId, setSelectedSCOItemId] = useState<number>();
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [selectedItemId, setSelectedItemId] = useState<number>(0);
  const [isViewOnly, setIsViewOnly] = useState<boolean>(false);

  const gridRef = useRef<ExtendedAgGridReact<ISCOriginalScope> | null>(null);
  const [selectedData, setSelectedData] = useState<ISCOriginalScope>();
  const [newUnitName, setNewUnitName] = useState<string>("");
  const [units, setUnits] = useState<
    IUnitListResponseDataAndStatusCode["units"]
  >([]);
  const [isUnitsGetting, setIsUnitsGetting] = useState<boolean>(true);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);

  useEffect(() => {
    if (window.ENV.ENABLE_UNIT_DROPDOWN) {
      (async function () {
        try {
          const response = await getUnitList();
          if (!response.success) {
            setIsUnitsGetting(false);
            return;
          }
          const units = response.data?.units || [];
          setUnits(units);
          const api = gridRef.current?.api;
          if (!api) return;

          const existingColDefs = api.getColumnDefs();
          if (!existingColDefs) return;

          const updatedColDefs = existingColDefs.map((col) =>
            "field" in col && col.field === "unit"
              ? {
                  ...col,
                  filterParams: {
                    values:
                      units.map((unit) => ({
                        label: unit.name?.toString(),
                        value: unit.name?.toString(),
                      })) ?? [],
                  },
                }
              : col
          );

          api.setColumnDefs(updatedColDefs);

          // Ensure the grid header re-renders
          api.refreshHeader();
        } catch (error) {}
        setIsUnitsGetting(false);
      })();
    }
  }, []);

  const handleDeleteSCItem = async () => {
    if (!isDeleting) {
      setIsDeleting(true);
      const deleteRes = (await deleteSCItems({
        id: Number(sCId),
        item_ids: [selectedSCOItemId || 0],
      })) as ISCItemsDeleteApiRes;

      if (deleteRes?.success) {
        dispatch(
          fetchSCEstimateItem({
            id: sCId || "",
            is_separate_estimate_sections: 1,
            isHideLoading: true,
          })
        );
      } else {
        notification.error({
          description: deleteRes?.message,
        });
      }
      setIsDeleting(false);
      setIsDeleteConfirmOpen(false);
    }
  };

  const filterCostCodeName = (val: string) => {
    if (val) {
      return val
        .toString()
        .replace(/\(.*?\)/g, "")
        .trim();
    }
    return "";
  };

  const updateCellValueApi = async (
    params: ValueSetterParams,
    fieldName: keyof ISCOriginalScope
  ) => {
    const { data, node, oldValue, newValue } = params;
    try {
      const formData: ISCOriginalItemFormData = {
        items: [
          {
            quantity: data?.quantity || "",
            item_type_key: data?.item_type_key || "",
            subject: data?.subject,
            unit: data?.unit || "",
            unit_cost: data?.unit_cost || "",
            total: data?.total,
            no_mu_total: data?.no_mu_total,
            cost_code: data?.cost_code,
            item_id: data?.item_id,
            reference_item_id: data?.reference_item_id,
            item_type: data?.item_type,
            assigned_to: data?.assigned_to,
            description: data?.description,
            internal_notes: data?.internal_notes,
            total_sc_paid_bill_amt: data?.total_sc_paid_bill_amt,
            total_sc_paid_bill_percentage: data?.total_sc_paid_bill_percentage,
            sub_contract_item_no: data?.sub_contract_item_no,
          },
        ],
      };

      let newValueFinal = newValue;
      if (fieldName === "unit_cost") {
        newValueFinal = newValue ? Number(newValue) * 100 : "";
        formData.items[0]["no_mu_total"] = (
          Number(data?.quantity) * newValueFinal
        ).toString();
        formData.items[0]["total"] = (
          Number(data?.quantity) * newValueFinal
        ).toString();
      }

      if (fieldName === "quantity") {
        formData.items[0]["no_mu_total"] = (
          Number(newValueFinal) * Number(data?.unit_cost)
        ).toString();
        formData.items[0]["total"] = (
          Number(newValueFinal) * Number(data?.unit_cost)
        ).toString();
      }

      if (
        fieldName === "quantity" &&
        newValueFinal != null &&
        newValueFinal.toString().includes(".")
      ) {
        formData.items[0][fieldName] = newValueFinal.toFixed(2);
      } else if (
        fieldName === "unit_cost" &&
        newValueFinal != null &&
        newValueFinal.toString().includes(".")
      ) {
        formData.items[0][fieldName] = newValueFinal.toFixed(2);
      } else {
        formData.items[0][fieldName] =
          newValueFinal == null ? "" : newValueFinal;
      }

      if (fieldName === "cost_code_id") {
        const newValueFlt = filterCostCodeName(newValue);
        const fltCostCode = codeCostData.find(
          (item) => item.cost_code_name?.toString()?.trim() == newValueFlt
        );
        formData.items[0][fieldName] = Number(fltCostCode?.code_id || "");
        formData.items[0]["cost_code_name"] = fltCostCode?.cost_code_name || "";
        formData.items[0]["cost_code"] = fltCostCode?.csi_code || "";
      }

      if (fieldName === "unit" && window.ENV.ENABLE_UNIT_DROPDOWN) {
        formData.items[0][fieldName] = (
          newValue?.name?.trim() || ""
        )?.toString();
      }

      //? Update redux
      const updatedItems = estimateItem?.map((section) => ({
        ...section,
        items: section?.items?.map((item) =>
          item.item_id == formData?.items[0]?.item_id
            ? { ...item, ...formData?.items[0] }
            : item
        ),
      }));

      dispatch(updateEstimateItems(updatedItems));

      const response = (await updateSCOriginalItemsApi({
        ...formData,
        id: sCId,
      })) as ISCOriginItemUpRes;

      if (response?.success) {
        if (fieldName === "unit") {
          params?.api?.stopEditing();
        }
        dispatch(resetDash());
      } else {
        // if (node) {
        //   node.setData({
        //     ...data,
        //     [fieldName]: oldValue,
        //   });
        // }
        notification.error({
          description:
            response?.message || _t("Something went wrong. Please try again."),
        });
        dispatch(
          fetchSCEstimateItem({
            id: sCId || "",
            is_separate_estimate_sections: 1,
            isHideLoading: true,
          })
        );
      }
    } catch (e) {
      notification.error({
        description: _t("Error fetching"),
      });
      dispatch(
        fetchSCEstimateItem({
          id: sCId || "",
          is_separate_estimate_sections: 1,
          isHideLoading: true,
        })
      );
    }
  };

  const handleTableAction = (
    params: ISCEstimateTableCellRenderer,
    key: string
  ) => {
    if (key === "view") {
      setIsViewOnly(true);
      setSelectedItemId(params?.data?.item_id || 0);
    } else if (key === "edit") {
      setIsViewOnly(false);
      setSelectedItemId(params?.data?.item_id || 0);
    }
  };

  const itemlistFilter = useMemo(() => {
    if (filter.length === 0) {
      return estimateItem;
    }

    return estimateItem.map((section) => ({
      ...section,
      items: section.items.filter(
        (item) =>
          item?.item_type_key !== undefined &&
          filter.includes(item?.item_type_key)
      ),
    }));
  }, [JSON.stringify(estimateItem), JSON.stringify(filter)]);

  const suppressKeyboardEvent: ColDef<
    CostItemTableCellRenderer["data"]
  >["suppressKeyboardEvent"] = (params) => {
    const { event, api } = params;

    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
      event.preventDefault();
      event.stopPropagation();
      return true;
    }

    if (event.key === "Enter") {
      event.preventDefault();
      event.stopPropagation();

      // Call valueSetter or custom logic for saving the value
      api.stopEditing(); // Stop editing to trigger valueSetter
      return true;
    }

    return true;
  };

  const columnDefs: ColDef[] = [
    {
      headerName: "",
      field: "",
      minWidth: 30,
      maxWidth: 30,
      suppressMenu: true,
      cellStyle: { textAlign: "center" },
    },
    {
      headerName: _t("Type"),
      maxWidth: 50,
      minWidth: 50,
      field: "type",
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: ({ data }: ISCEstimateTableCellRenderer) => {
        const itemType = data?.item_type_key;
        const itemTypeName = data?.item_type_name;
        const itemTypeIcon = SCIcons[itemType as IconKey];
        return itemTypeIcon ? (
          <Tooltip title={itemTypeName}>
            <FontAwesomeIcon
              className="w-4 h-4 text-primary-900 mx-auto"
              icon={itemTypeIcon}
            />
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: "",
      field: "",
      minWidth: 130,
      maxWidth: 130,
      suppressMenu: true,
      cellStyle: { textAlign: "center" },
    },
    {
      headerName: _t("Item Name"),
      field: "subject",
      minWidth: 150,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,

      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      editable:
        !isReadOnly &&
        (details.response == "draft" ||
          details.response == "committed" ||
          details.response == "sub_contract_pending"),
      valueGetter: (params: ISCEstimateTableCellRenderer) =>
        HTMLEntities.decode(sanitizeString(params?.data?.subject)),
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          let nVal = params.newValue;
          if (nVal == "") {
            notification.error({
              description: "Item Name is required.",
            });
            return false;
          }
          if (!nVal || nVal == null) {
            const updatedData = {
              ...params.data,
              subject: "",
            };
            params.node.setData(updatedData);
            updateCellValueApi(params, "subject");
            return false;
          }
          const updatedData = {
            ...params.data,
            subject: nVal,
          };
          params.node.setData(updatedData);
          updateCellValueApi(params, "subject");
        }
        return true;
      },
      cellRenderer: ({ data }: ISCEstimateTableCellRenderer) => {
        const itemName = HTMLEntities.decode(sanitizeString(data?.subject));
        return itemName ? (
          <Tooltip title={itemName}>
            <Typography className="table-tooltip-text">{itemName}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Cost Code"),
      field: "cost_code",
      minWidth: 130,
      flex: 1,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      suppressKeyboardEvent,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellEditor: "agRichSelectCellEditor",
      editable:
        !isReadOnly &&
        (details.response == "draft" ||
          details.response == "committed" ||
          details.response == "sub_contract_pending"),
      cellEditorParams: {
        values: codeCostData?.map(
          (item: ICostCode) =>
            `${item?.cost_code_name}${
              item?.csi_code && item?.csi_code !== null
                ? ` (${item?.csi_code})`
                : ""
            }`
        ),
        filterList: true,
        searchType: "matchAny",
        allowTyping: true,
        valueListMaxHeight:
          itemlistFilter?.length == 1
            ? 60
            : itemlistFilter?.length == 2
            ? 90
            : itemlistFilter?.length == 3
            ? 120
            : itemlistFilter?.length == 4
            ? 150
            : 180,
      },
      valueGetter: (params: ValueGetterParams) => {
        return params.data?.cost_code_name
          ? `${params.data?.cost_code_name}${
              params.data?.cost_code ? ` (${params.data?.cost_code})` : ""
            }`
          : "";
      },
      valueSetter: (params: ValueSetterParams) => {
        const { newValue } = params;
        const updatedData = {
          ...params.data,
          cost_code_name: newValue,
        };

        if (params && params.node) {
          params.node.setData(updatedData);
        }
        updateCellValueApi(params, "cost_code_id");
        return true;
      },
      cellRenderer: ({ data }: ISCEstimateTableCellRenderer) => {
        const costCode = `${data?.cost_code_name ?? ""}${
          data?.cost_code ? ` (${data?.cost_code})` : ""
        }${data?.code_is_deleted == 1 ? ` (Archived)` : ""}`;

        return costCode ? (
          <Tooltip title={HTMLEntities.decode(sanitizeString(costCode))}>
            <Typography className="table-tooltip-text">
              {HTMLEntities.decode(sanitizeString(costCode))}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("QTY"),
      field: "qty",
      maxWidth: 80,
      minWidth: 80,
      suppressMovable: false,
      suppressMenu: true,

      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      editable:
        !isReadOnly &&
        (details.response == "draft" ||
          details.response == "committed" ||
          details.response == "sub_contract_pending"),
      cellEditor: "agNumberCellEditor",
      suppressKeyboardEvent,
      valueGetter: (params: ISCEstimateTableCellRenderer) =>
        params?.data?.quantity,
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          let nVal = params.newValue;

          if (!nVal || nVal == null) {
            const updatedData = {
              ...params.data,
              quantity: "",
            };
            params.node.setData(updatedData);
            updateCellValueApi(params, "quantity");
            return false;
          }
          const checkNum = qtyNumberCheck(nVal);
          const integerPartLength = nVal.toString().split(".")[0].length;

          if (integerPartLength > 6 || !checkNum) {
            notification.error({
              description: "Quantity should be less than or equal to 6 digits.",
            });
            return false;
          }
          if (!floatWithNegativeRegex.test(nVal)) {
            notification.error({
              description: _t(
                "Decimal part should be less than or equal to 2 digits."
              ),
            });
            return false;
          }
          if (nVal.toString().includes(".")) {
            nVal = nVal.toFixed(2);
          }

          const updatedData = {
            ...params.data,
            quantity: nVal,
          };
          params.node.setData(updatedData);
          updateCellValueApi(params, "quantity");
        }
        return true;
      },
      cellRenderer: ({ data }: ISCEstimateTableCellRenderer) => {
        const quantityUnit =
          formatter(formatAmount(Number(data.quantity), { isQuantity: true }))
            .value || 0;

        return (
          <Tooltip title={quantityUnit}>
            <Typography className="table-tooltip-text">
              {quantityUnit}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Unit Cost"),
      field: "unit_cost",
      maxWidth: 130,
      minWidth: 130,
      suppressMovable: false,
      suppressMenu: true,

      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      editable:
        !isReadOnly &&
        (details.response == "draft" ||
          details.response == "committed" ||
          details.response == "sub_contract_pending"),
      cellEditor: "agNumberCellEditor",
      suppressKeyboardEvent,
      valueGetter: (params: ISCEstimateTableCellRenderer) =>
        Number(params?.data?.unit_cost) / 100,
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          let nVal = params.newValue;
          if (Number(nVal) < 0) {
            notification.error({
              description: _t("Negative values are not allowed for Unit Cost."),
            });
            return false;
          }
          if (!nVal || nVal == null) {
            const updatedData = {
              ...params.data,
              unit_cost: "",
            };
            params.node.setData(updatedData);
            updateCellValueApi(params, "unit_cost");
            return false;
          }
          const checkNum = qtyNumberCheck(nVal);
          const cleanedValue = BigInt(Math.floor(Number(nVal))).toString();
          if (cleanedValue?.length > 10) {
            notification.error({
              description:
                "Unit cost should be less than or equal to 10 digits.",
            });
            return false;
          }
          if (!floatWithNegativeRegex.test(nVal)) {
            notification.error({
              description: _t(
                "Decimal part should be less than or equal to 2 digits."
              ),
            });
            return false;
          }
          if (nVal.toString().includes(".")) {
            nVal = nVal.toFixed(2);
          }

          const updatedData = {
            ...params.data,
            unit_cost: nVal * 100,
          };
          params.node.setData(updatedData);
          updateCellValueApi(params, "unit_cost");
        }
        return true;
      },
      cellRenderer: ({ data }: ISCEstimateTableCellRenderer) => {
        const nValue = Number(data?.unit_cost) / 100;
        const unitCost = formatter(formatAmount(nValue)).value_with_symbol;
        return unitCost ? (
          <Tooltip title={unitCost}>
            <Typography className="table-tooltip-text">{unitCost}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Unit"),
      field: "unit",
      maxWidth: 100,
      minWidth: 100,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      editable:
        !isReadOnly &&
        (details.response == "draft" ||
          details.response == "committed" ||
          details.response == "sub_contract_pending"),
      ...(window.ENV.ENABLE_UNIT_DROPDOWN && {
        suppressKeyboardEvent: (params) => {
          if (params.event.key === "Enter") {
            params.event.preventDefault();
            return true; // Block Ag-Grid's default behavior
          }
          return false;
        },
        cellEditorParams: {
          values: units,
          onKeyDown: (
            e: React.KeyboardEvent<HTMLInputElement>,
            data: ISCOriginalScope
          ) => {
            if (e.key === "Enter") {
              const value = e?.currentTarget?.value?.trim();
              const newType = onEnterSelectSearchValue(
                e,
                units?.map((unit) => ({
                  label: unit?.name,
                  value: "",
                })) || []
              );
              if (newType) {
                setNewUnitName(newType);
                setSelectedData(data);
              } else if (value) {
                notification.error({
                  description:
                    "Records already exist, no new records were added.",
                });
              }
            }
          },
        },
        cellEditor: UnitCellEditor<ISCOriginalScope>,
      }),
      valueGetter: (params: ISCEstimateTableCellRenderer) => params?.data?.unit,
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          let nVal = window.ENV.ENABLE_UNIT_DROPDOWN
            ? (params?.newValue?.name?.trim() || "")?.toString()
            : (params?.newValue || "")?.toString();

          if (!nVal || nVal == null) {
            const updatedData = {
              ...params.data,
              unit: "",
            };
            params.node.setData(updatedData);
            updateCellValueApi(params, "unit");
            return false;
          }

          if (nVal && nVal.toString().length > 15) {
            notification.error({
              description: "Unit should be less than 15 characters.",
            });
            return false;
          }

          const updatedData = {
            ...params.data,
            unit: nVal,
          };
          params.node.setData(updatedData);
          updateCellValueApi(params, "unit");
        }
        return true;
      },
      cellRenderer: ({ data }: ISCEstimateTableCellRenderer) => {
        const unit = data?.unit;
        return unit ? (
          <Tooltip title={unit}>
            <Typography className="table-tooltip-text">{unit}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Billed"),
      field: "billed",
      minWidth: 160,
      maxWidth: 160,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-right",
      hide: details.sc_multi_bill_count === "0",
      cellRenderer: ({ data }: ISCEstimateTableCellRenderer) => {
        const nValueBilled = Number(data.total_sc_paid_bill_amt) / 100;
        const billed = formatter(formatAmount(nValueBilled)).value_with_symbol;
        const billPer = Number(data.total_sc_paid_bill_percentage);

        return billed ? (
          <div className="flex gap-1 overflow-hidden w-full justify-end">
            <Tooltip title={billed}>
              <Typography className="table-tooltip-text !max-w-[calc(100%-63px)] block truncate">
                {billed}
              </Typography>
            </Tooltip>
            <Typography className="table-tooltip-text">
              {`(${formatAmount(billPer)}%)`}
            </Typography>
          </div>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Remaining"),
      field: "remaining",
      minWidth: 160,
      maxWidth: 160,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-right",
      hide: details.sc_multi_bill_count === "0",
      cellRenderer: ({ data }: ISCEstimateTableCellRenderer) => {
        const nValueRemaining =
          (Number(data?.no_mu_total) - Number(data?.total_sc_paid_bill_amt)) /
          100;
        const remaining = formatter(
          formatAmount(nValueRemaining)
        ).value_with_symbol;
        const reminingPer = (
          100 - Number(data.total_sc_paid_bill_percentage)
        ).toFixed(2);

        return remaining ? (
          <div className="flex gap-1 overflow-hidden w-full justify-end">
            <Tooltip title={remaining}>
              <Typography className="table-tooltip-text !max-w-[calc(100%-63px)] block truncate">
                {remaining}
              </Typography>
            </Tooltip>
            <Typography className="table-tooltip-text">
              {`(${formatAmount(reminingPer)}%)`}
            </Typography>
          </div>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "no_mu_total",
      minWidth: 120,
      maxWidth: 120,
      suppressMovable: false,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: ({ data }: ISCEstimateTableCellRenderer) => {
        const nValue = Number(data?.no_mu_total) / 100;
        const total = formatter(formatAmount(nValue)).value_with_symbol;
        return total ? (
          <Tooltip title={total}>
            <Typography className="table-tooltip-text">{total}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "",
      maxWidth: 80,
      minWidth: 80,
      suppressMenu: true,
      cellRenderer: (params: ISCEstimateTableCellRenderer) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={() => {
                handleTableAction(params, isReadOnly ? "view" : "edit");
              }}
            />
            {!isReadOnly &&
              details?.response !== "accept" &&
              details?.response !== "closed" && (
                <ButtonWithTooltip
                  tooltipTitle={_t("Delete")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-trash-can"
                  onClick={() => {
                    setSelectedSCOItemId(Number(params?.data?.item_id) || 0);
                    setIsDeleteConfirmOpen(true);
                  }}
                />
              )}
          </div>
        );
      },
    },
  ];

  const selectedItems = useMemo(() => {
    if (selectedItemId) {
      return (
        itemlistFilter.find((section) =>
          section.items.some((item) => item.item_id == selectedItemId)
        )?.items || []
      );
    }
    return [];
  }, [JSON.stringify(itemlistFilter), selectedItemId]);

  return (
    <>
      {(isEstimateItemLoading || itemlistFilter?.length > 0) && (
        <div className="grid gap-2.5">
          {isEstimateItemLoading && (
            <Spin className="w-full h-20 flex items-center justify-center" />
          )}
          {!isEstimateItemLoading &&
            itemlistFilter?.length > 0 &&
            itemlistFilter.map((items) => {
              let total = 0;
              if (items?.items?.length > 0) {
                items?.items.forEach((entry) => {
                  const amount = entry.no_mu_total
                    ? parseFloat(entry.no_mu_total)
                    : 0;
                  total += amount;
                });
              }
              return (
                <CollapseSingleTable
                  title={items?.section_name}
                  key={items?.section_id}
                  totalRecordIcon={true}
                  defaultActiveKey={items?.items?.length > 0 ? 1 : 0}
                  totalRecord={
                    formatter(formatAmount(+total / 100)).value_with_symbol
                  }
                  children={
                    <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[33px] before:top-5 before:bg-gradient-to-r from-primary-500">
                      <div className="ag-theme-alpine">
                        <StaticTable
                          ref={gridRef}
                          className="static-table"
                          columnDefs={columnDefs}
                          stopEditingWhenCellsLoseFocus={true}
                          rowData={items?.items?.length > 0 ? items?.items : []}
                          noRowsOverlayComponent={() => (
                            <NoRecords
                              image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                            />
                          )}
                        />
                      </div>
                    </div>
                  }
                />
              );
            })}
        </div>
      )}

      {isDeleteConfirmOpen && (
        <ConfirmModal
          isOpen={isDeleteConfirmOpen}
          modaltitle={_t("Delete")}
          description={_t("Are you sure you want to delete this Item?")}
          modalIcon="fa-regular fa-trash-can"
          isLoading={isDeleting}
          onAccept={handleDeleteSCItem}
          onDecline={() => {
            setIsDeleteConfirmOpen(false);
          }}
          onCloseModal={() => {
            setIsDeleteConfirmOpen(false);
          }}
        />
      )}

      {Boolean(selectedItemId) && (
        <AddSubContractItem
          isOpen={Boolean(selectedItemId)}
          moduleName={gConfig?.module_singular_name || ""}
          recordId={selectedItemId}
          allRecords={selectedItems}
          type="estimateItem"
          isViewOnly={isViewOnly}
          isShowNextPre={true}
          onClose={() => setSelectedItemId(0)}
        />
      )}
      {newUnitName.trim() && window.ENV.ENABLE_UNIT_DROPDOWN && (
        <ConfirmModal
          isOpen={Boolean(newUnitName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newUnitName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => {
            setNewUnitName("");
            setSelectedData(undefined);
          }}
          onAccept={async () => {
            if (!isAddingCustomData && newUnitName) {
              setIsAddingCustomData(true);
              const response = (await addUnit({
                name: newUnitName,
              })) as IUnitAddResponse & {
                data: {
                  data: IUnitListResponseDataAndStatusCode["units"][0];
                };
              };
              if (response.success) {
                const newUnits = [response.data.data, ...units];
                setUnits(newUnits);
                setNewUnitName("");
                setSelectedData(undefined);
                const api = gridRef.current?.api;
                if (!api) return;

                const renderedNodes = api.getRenderedNodes();

                if (renderedNodes) {
                  const currentRowNode = renderedNodes.find(
                    (node) => node.data?.item_id === selectedData?.item_id
                  );
                  if (currentRowNode && currentRowNode.data) {
                    const oldData = { ...currentRowNode.data };
                    currentRowNode?.setData({
                      ...currentRowNode.data,
                      unit: newUnitName,
                    });
                    try {
                      const formData: ISCOriginalItemFormData = {
                        items: [
                          {
                            quantity: selectedData?.quantity || "",
                            item_type_key: selectedData?.item_type_key || "",
                            subject: selectedData?.subject,
                            unit: newUnitName || "",
                            unit_cost: selectedData?.unit_cost || "",
                            total: selectedData?.total,
                            cost_code: selectedData?.cost_code,
                            item_id: selectedData?.item_id,
                            reference_item_id: selectedData?.reference_item_id,
                            item_type: selectedData?.item_type,
                            assigned_to: selectedData?.assigned_to,
                            description: selectedData?.description,
                            internal_notes: selectedData?.internal_notes,
                            total_sc_paid_bill_amt:
                              selectedData?.total_sc_paid_bill_amt,
                            total_sc_paid_bill_percentage:
                              selectedData?.total_sc_paid_bill_percentage,
                            sub_contract_item_no:
                              selectedData?.sub_contract_item_no,
                          },
                        ],
                      };

                      //? Update redux
                      const updatedItems = estimateItem?.map((section) => ({
                        ...section,
                        items: section?.items?.map((item) =>
                          item.item_id == formData?.items[0]?.item_id
                            ? { ...item, ...formData?.items[0] }
                            : item
                        ),
                      }));

                      dispatch(updateEstimateItems(updatedItems));

                      const response = (await updateSCOriginalItemsApi({
                        ...formData,
                        id: sCId,
                      })) as ISCOriginItemUpRes;

                      if (response?.success) {
                        dispatch(resetDash());
                      } else {
                        notification.error({
                          description:
                            response?.message ||
                            _t("Something went wrong. Please try again."),
                        });
                        currentRowNode?.setData(oldData);
                        dispatch(
                          fetchSCEstimateItem({
                            id: sCId || "",
                            is_separate_estimate_sections: 1,
                            isHideLoading: true,
                          })
                        );
                      }
                    } catch (error) {
                      currentRowNode?.setData(oldData);
                      dispatch(
                        fetchSCEstimateItem({
                          id: sCId || "",
                          is_separate_estimate_sections: 1,
                          isHideLoading: true,
                        })
                      );
                    }
                  }
                }
                const existingColDefs = api.getColumnDefs();
                if (!existingColDefs) return;

                const updatedColDefs = existingColDefs.map((col) =>
                  "field" in col && col.field === "unit"
                    ? {
                        ...col,
                        filterParams: {
                          values:
                            newUnits.map((unit) => ({
                              label: unit.name?.toString(),
                              value: unit.name?.toString(),
                            })) ?? [],
                        },
                        cellEditorParams: {
                          ...col.cellEditorParams,
                          values: newUnits,
                        },
                      }
                    : col
                );

                api.setColumnDefs(updatedColDefs);

                // Ensure the grid header re-renders
                api.refreshHeader();
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => {
            setNewUnitName("");
            setSelectedData(undefined);
          }}
        />
      )}
    </>
  );
};

export default SubContractItems;
