import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { useParams } from "@remix-run/react";
import { getGModuleByKey } from "~/zustand";
import {
  GridReadyEvent,
  IRowNode,
  SelectionChangedEvent,
} from "ag-grid-community";
// Hook Redux
import { useTranslation } from "~/hook";
import { fetchSCScheduleValueItem } from "../../../../redux/action";
import { useAppSCDispatch, useAppSCSelector } from "../../../../redux/store";
import { sanitizeString } from "~/helpers/helper";
import { defaultConfig } from "~/data";
import { SCIcons } from "~/modules/financials/pages/subcontracts/utils/constants";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import ItemsTable from "~/shared/components/molecules/itemsTable/ItemsTable";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";

const ProjectBudgetItems = ({
  isOpen,
  onClose,
  isDataUpdating,
  scheduleValueSovItemsHandler,
  scheduleValueChanageOrderItemsHandler,
  scheduleValueWorkOrderItemsHandler,
  scheduleValueEsHandler,
}: ISCProjectBudgetItemsProps) => {
  const { _t } = useTranslation();
  const { id: sCId }: RouteParams = useParams();
  const dispatch = useAppSCDispatch();
  const { formatter } = useCurrencyFormatter();

  const moduleWO: GModule | undefined = getGModuleByKey(
    defaultConfig.work_order_module
  );
  const moduleCO: GModule | undefined = getGModuleByKey(
    defaultConfig.change_order_module
  );
  const moduleEst: GModule | undefined = getGModuleByKey(
    defaultConfig.estimate_module
  );

  const {
    changeOrder,
    estimateItem,
    originalScope,
    workOrderItem,
  }: ISCItemsInitialState = useAppSCSelector(
    (state) => state.subContractsItems
  );

  const {
    scheduleValueItems,
    isScheduleValueLoading,
    isScheduleValueFetched,
  }: ISCImportItemsInitialState = useAppSCSelector(
    (state) => state.subContractsImportItemList
  );

  const [withChangeOrderId, setWithChangeOrderId] = useState<ISCChangeOrder[]>(
    []
  );
  const [withoutChangeOrderId, setWithoutChangeOrderId] = useState<
    ISCOriginalScope[]
  >([]);
  const [withWorkOrderId, setWithWorkOrderId] = useState<ISCWorkOrder[]>([]);

  const [defaultselectedData, setDefaultselectedData] = useState<
    ISCOriginalScope[]
  >([]);
  const [defaultselectedEstimateData, setDefaultselectedEstimateData] =
    useState<ISCEstimateItem[]>([]);
  const [defaultselectedWorkOrderData, setDefaultselectedWorkOrderData] =
    useState<ISCWorkOrder[]>([]);
  const [defaultselectedChangeOrderData, setDefaultselectedChangeOrderData] =
    useState<ISCChangeOrder[]>([]);
  const [selectedItemsByTable, setSelectedItemsByTable] = useState({});
  const [selectedOriginalScopeItems, setSelectedOriginalScopeItems] = useState<
    ISCOriginalScope[]
  >([]);
  const [selectedChangeOrderItems, setSelectedChangeOrderItems] = useState<
    ISCChangeOrder[]
  >([]);
  const [selectedWorkOrderItems, setSelectedWorkOrderItems] = useState<
    ISCWorkOrder[]
  >([]);
  const [selectedEsItems, setSelectedEsItems] = useState<ISCEstimateItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    if (scheduleValueItems?.items) {
      const [withChangeOrderId, withoutChangeOrderId, withWorkOrderId] =
        scheduleValueItems.items.reduce(
          (
            scheduleValuesArr: [
              ISCChangeOrder[],
              ISCOriginalScope[],
              ISCWorkOrder[]
            ],
            item
          ) => {
            const hasChangeOrderId =
              item.change_order_id !== 0 && item.change_order_id !== "0";
            const hasWorkOrderId =
              item.work_order_id !== 0 && item.work_order_id !== "0";

            if (hasChangeOrderId) {
              scheduleValuesArr[0].push(item as unknown as ISCChangeOrder);
            } else if (hasWorkOrderId) {
              scheduleValuesArr[2].push(item as unknown as ISCWorkOrder);
            } else {
              scheduleValuesArr[1].push(item as ISCOriginalScope);
            }

            return scheduleValuesArr;
          },
          [[], [], []]
        );

      setWithChangeOrderId(withChangeOrderId);
      setWithoutChangeOrderId(withoutChangeOrderId);
      setWithWorkOrderId(withWorkOrderId);
    }
  }, [JSON.stringify(scheduleValueItems?.items)]);

  useEffect(() => {
    if (selectedItemsByTable) {
      const result = Object?.values(
        selectedItemsByTable
      )?.flat() as ISCEstimateItem[];
      setSelectedEsItems(result);
    }
  }, [selectedItemsByTable]);

  useEffect(() => {
    if (withoutChangeOrderId && !!withoutChangeOrderId?.length) {
      const defaultSelection: ISCOriginalScope[] = withoutChangeOrderId.filter(
        (jobItem) =>
          originalScope.some(
            (item) =>
              item?.budget_item_id?.toString() === jobItem?.item_id?.toString()
          )
      );

      setDefaultselectedData(defaultSelection);
    }
    if (withChangeOrderId && !!withChangeOrderId?.length) {
      const defaultChangeOrderSelection: ISCChangeOrder[] =
        withChangeOrderId.filter((jobItem) =>
          changeOrder.some(
            (item) =>
              item?.reference_module_item_id?.toString() ===
              jobItem?.reference_item_id?.toString()
          )
        );

      setDefaultselectedChangeOrderData(defaultChangeOrderSelection);
    }
    if (scheduleValueItems?.estimate_section) {
      const defaultEstimateSelection =
        scheduleValueItems.estimate_section.flatMap(
          (jobItem: ISCEstimateItemData) => {
            return jobItem.items.filter((item: ISCEstimateItem) =>
              estimateItem.some((estData: ISCEstimateItemData) =>
                estData.items.some(
                  (estimateItem: ISCEstimateItem) =>
                    estimateItem?.reference_module_item_id?.toString() ===
                    item?.reference_item_id?.toString()
                )
              )
            );
          }
        );

      setDefaultselectedEstimateData(defaultEstimateSelection);
    }
    if (withWorkOrderId && !!withWorkOrderId?.length) {
      const defaultWorkOrderSelection: ISCWorkOrder[] = withWorkOrderId.filter(
        (jobItem) =>
          workOrderItem.some(
            (item) =>
              item?.reference_module_item_id?.toString() ===
              jobItem?.reference_item_id?.toString()
          )
      );

      setDefaultselectedWorkOrderData(defaultWorkOrderSelection);
    }
  }, [
    JSON.stringify(scheduleValueItems?.estimate_section),
    JSON.stringify(withWorkOrderId),
    JSON.stringify(withChangeOrderId),
    JSON.stringify(withoutChangeOrderId),
  ]);

  const onGridReady = (params: GridReadyEvent) => {
    const gridApiRef = params.api;
    gridApiRef.forEachNode((node) => {
      const { item_id } = node.data;
      if (defaultselectedData.some((item) => item.item_id == item_id)) {
        node.setSelected(true);
      }
    });
  };
  const onGridChangeOrderReady = (params: GridReadyEvent) => {
    const gridApiRefChangeOrder = params.api;
    gridApiRefChangeOrder.forEachNode((node) => {
      const { item_id } = node.data;
      if (
        defaultselectedChangeOrderData.some((item) => item.item_id == item_id)
      ) {
        node.setSelected(true);
      }
    });
  };
  const onGridWorkOrderReady = async (params: GridReadyEvent) => {
    setIsLoading(true);
    const gridApiRefWorkOrder = params.api;
    await gridApiRefWorkOrder.forEachNode((node) => {
      const { item_id } = node.data;
      if (
        defaultselectedWorkOrderData.some((item) => item.item_id == item_id)
      ) {
        node.setSelected(true);
      }
    });
    setIsLoading(false);
  };
  const onGridEstimateReady = () => (params: GridReadyEvent) => {
    const gridApiRefEstimate = params.api;
    gridApiRefEstimate.forEachNode((node) => {
      const { item_id } = node.data;
      if (
        defaultselectedEstimateData.some((item) => item?.item_id == item_id)
      ) {
        node.setSelected(true);
      }
    });
  };

  const handleSelectionDirectItemChanged = (
    event: SelectionChangedEvent
  ): void => {
    const selectedNodes = event.api.getSelectedNodes();
    const selected = selectedNodes.map((node: IRowNode) => {
      return node.data;
    });
    setSelectedOriginalScopeItems(selected);
  };

  const handleSelectionEstimateItemChanged = (
    event: SelectionChangedEvent,
    sectionId: string
  ): void => {
    const selectedNodes = event.api.getSelectedNodes();
    const selectedData = selectedNodes.map((node) => node.data);
    setSelectedItemsByTable((prev) => ({ ...prev, [sectionId]: selectedData }));
  };

  const handleSelectionChangeOrderChanged = (
    event: SelectionChangedEvent
  ): void => {
    const selectedChangeOrderNodes = event.api.getSelectedNodes();
    const selected = selectedChangeOrderNodes.map((node: IRowNode) => {
      return node.data;
    });
    setSelectedChangeOrderItems(selected);
  };

  const handleSelectionWorkOrderChanged = async (
    event: SelectionChangedEvent
  ) => {
    const selectedNodes = event.api.getSelectedNodes();
    const selected = await selectedNodes.map((node: IRowNode) => {
      return node.data;
    });
    setSelectedWorkOrderItems(selected);
  };

  useEffect(() => {
    if (!isScheduleValueFetched) {
      dispatch(fetchSCScheduleValueItem({ id: sCId || "" }));
    }
  }, [isOpen, isScheduleValueFetched]);

  const handleSaveScheduleValueItems = useCallback(async () => {
    const itemsToAdd: ISCOriginalScope[] = [];
    const itemsToAddChangeOrder: ISCChangeOrder[] = [];
    const itemsToAddWorkOrder: ISCWorkOrder[] = [];
    const itemsToAddEstimateItem: ISCEstimateItem[] = [];

    let itemToBeDelete: ISCOriginalScope[] = [];
    let itemToBeDeleteChangeOrder: ISCChangeOrder[] = [];
    let itemToBeDeleteWorkOrder: ISCWorkOrder[] = [];
    let itemToBeDeleteEstimateItem: ISCEstimateItem[] = [];

    let itemToBeDeleteId: number[];
    let itemToBeDeleteIdChangeOrder: number[];
    let itemToBeDeleteIdWorkOrder: number[] = [];
    let itemToBeDeleteIdEstimateItem: number[] = [];

    // Filter Direct SOV items
    selectedOriginalScopeItems.forEach((item: ISCOriginalScope) => {
      if (
        !defaultselectedData.some(
          (i: ISCOriginalScope) => i.item_id === item.item_id
        )
      ) {
        itemsToAdd.push(item);
      }
    });

    defaultselectedData.forEach((item: ISCOriginalScope) => {
      if (
        !selectedOriginalScopeItems.some(
          (i: ISCOriginalScope) => i.item_id === item.item_id
        )
      ) {
        itemToBeDelete.push(item);
      }
    });
    itemToBeDeleteId =
      itemToBeDelete.map((item) => {
        const mainItemId = originalScope.find(
          (i) => i.budget_item_id === item.item_id
        )?.item_id as number;
        return mainItemId;
      }) ?? [];

    // Filter changeOrder items
    selectedChangeOrderItems.forEach((item: ISCChangeOrder) => {
      if (
        !defaultselectedChangeOrderData.some(
          (i: ISCChangeOrder) => i.item_id === item.item_id
        )
      ) {
        itemsToAddChangeOrder.push(item);
      }
    });

    defaultselectedChangeOrderData.forEach((item: ISCChangeOrder) => {
      if (
        !selectedChangeOrderItems.some(
          (i: ISCChangeOrder) => i.item_id === item.item_id
        )
      ) {
        itemToBeDeleteChangeOrder.push(item);
      }
    });

    itemToBeDeleteIdChangeOrder =
      itemToBeDeleteChangeOrder?.map((item: ISCChangeOrder) => {
        const mainItemId = changeOrder.find(
          (i) =>
            i.reference_module_item_id?.toString() ===
            item.reference_item_id.toString()
        )?.item_id as number;

        return mainItemId;
      }) ?? [];

    // Filter workOrder items
    selectedWorkOrderItems.forEach((item: ISCWorkOrder) => {
      if (
        !defaultselectedWorkOrderData.some(
          (i: ISCWorkOrder) => i.item_id === item.item_id
        )
      ) {
        itemsToAddWorkOrder.push(item);
      }
    });
    defaultselectedWorkOrderData.forEach((item: ISCWorkOrder) => {
      if (
        !selectedWorkOrderItems.some(
          (i: ISCWorkOrder) => i.item_id === item.item_id
        )
      ) {
        itemToBeDeleteWorkOrder.push(item);
      }
    });
    itemToBeDeleteIdWorkOrder =
      itemToBeDeleteWorkOrder.map((item) => {
        const mainItemId = workOrderItem.find(
          (i) =>
            i.reference_module_item_id?.toString() ===
            item.reference_item_id.toString()
        )?.item_id as number;
        return mainItemId;
      }) ?? [];

    // Filter Estimate items
    selectedEsItems.forEach((item) => {
      if (
        !defaultselectedEstimateData.some(
          (i: ISCEstimateItem) => i.item_id === item.item_id
        )
      ) {
        itemsToAddEstimateItem.push(item);
      }
    });

    defaultselectedEstimateData.forEach((item: ISCEstimateItem) => {
      if (!selectedEsItems.some((i) => i.item_id === item.item_id)) {
        itemToBeDeleteEstimateItem.push(item);
      }
    });

    itemToBeDeleteIdEstimateItem =
      itemToBeDeleteEstimateItem?.map((item: ISCEstimateItem) => {
        const mainItemId = estimateItem
          .find((section) =>
            section.items.some(
              (i) =>
                i.reference_module_item_id?.toString() ===
                item.reference_item_id?.toString()
            )
          )
          ?.items.find(
            (i) =>
              i.reference_module_item_id?.toString() ===
              item.reference_item_id?.toString()
          )?.item_id as number;

        return mainItemId;
      }) ?? [];

    scheduleValueEsHandler({
      itemsToAddEstimateItem,
      itemToBeDeleteIdEstimateItem,
    });
    scheduleValueSovItemsHandler({ itemsToAdd, itemToBeDeleteId });
    scheduleValueChanageOrderItemsHandler({
      itemsToAddChangeOrder,
      itemToBeDeleteIdChangeOrder,
    });
    scheduleValueWorkOrderItemsHandler({
      itemsToAddWorkOrder,
      itemToBeDeleteIdWorkOrder,
    });
    onClose();
  }, [
    JSON.stringify(selectedOriginalScopeItems),
    JSON.stringify(defaultselectedData),
    JSON.stringify(selectedChangeOrderItems),
    JSON.stringify(defaultselectedChangeOrderData),
    JSON.stringify(selectedWorkOrderItems),
    JSON.stringify(defaultselectedWorkOrderData),
    JSON.stringify(selectedEsItems),
    JSON.stringify(defaultselectedEstimateData),
  ]);

  const isFormModified = useMemo(() => {
    return (
      JSON.stringify(selectedOriginalScopeItems) !==
        JSON.stringify(defaultselectedData) ||
      JSON.stringify(selectedChangeOrderItems) !==
        JSON.stringify(defaultselectedChangeOrderData) ||
      JSON.stringify(selectedWorkOrderItems) !==
        JSON.stringify(defaultselectedWorkOrderData) ||
      JSON.stringify(selectedEsItems) !==
        JSON.stringify(defaultselectedEstimateData)
    );
  }, [
    selectedOriginalScopeItems,
    defaultselectedData,
    selectedChangeOrderItems,
    defaultselectedChangeOrderData,
    selectedWorkOrderItems,
    defaultselectedWorkOrderData,
    selectedEsItems,
    defaultselectedEstimateData,
  ]);

  return (
    <Drawer
      open={isOpen}
      rootClassName="drawer-open"
      width={980}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-file-invoice-dollar"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(`Project Budget Items`)}
            </Header>
          </div>
          <div className="flex items-center justify-end px-4">
            <Tooltip
              title={_t(
                "You can only add items that have been imported into your Schedule of Values Items. To add additional items to this list, go to your Project > Schedule of Values tab > Import or Manually Add Items."
              )}
              placement="bottom"
            >
              <Typography className="cursor-pointer text-[#B94A48] text-sm">
                {_t("Missing Something?")}
              </Typography>
            </Tooltip>
          </div>
        </div>
      }
      closeIcon={<CloseButton onClick={() => onClose()} />}
    >
      <div className="py-4">
        <div className="sidebar-body h-[calc(100vh-132px)] overflow-y-auto">
          <div className="grid gap-4 px-4">
            <SidebarCardBorder addGap={true}>
              {isScheduleValueLoading ? (
                <Spin className="w-full h-20 flex items-center justify-center" />
              ) : (
                <ItemsTable
                  title={_t("Items (Directly Added to SOV)")}
                  tableProps={{
                    rowSelection: "multiple",
                    columnDefs: [
                      {
                        headerName: "",
                        field: "",
                        minWidth: 36,
                        maxWidth: 36,
                        headerCheckboxSelection: true,
                        checkboxSelection: true,
                        suppressMenu: true,
                      },
                      {
                        headerName: _t("Type"),
                        maxWidth: 60,
                        minWidth: 60,
                        field: "item_type",
                        suppressMenu: true,
                        headerClass: "ag-header-center",
                        cellClass: "ag-cell-center",
                        cellRenderer: ({
                          data,
                        }: ISubContractorOSItemsCellRenderer) => {
                          const itemType = data?.item_type_key;
                          const itemTypeName = data?.item_type_name;
                          const itemTypeIcon = SCIcons[itemType as IconKey];
                          return itemTypeIcon ? (
                            <Tooltip title={itemTypeName}>
                              <FontAwesomeIcon
                                className="w-4 h-4 text-primary-900 mx-auto"
                                icon={itemTypeIcon}
                              />
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                      {
                        headerName: "",
                        field: "",
                        minWidth: 120,
                        maxWidth: 120,
                        suppressMenu: true,
                      },
                      {
                        headerName: _t("Item Name"),
                        field: "item_name",
                        maxWidth: 150,
                        minWidth: 150,
                        cellClass: "ag-cell-left",
                        headerClass: "ag-header-left",
                        suppressMenu: true,
                        cellRenderer: (
                          params: ISubContractorOSItemsCellRenderer
                        ) => {
                          const { data } = params;
                          return data.subject ? (
                            <Tooltip title={data.subject}>
                              <Typography className="table-tooltip-text">
                                {data.subject}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                      {
                        headerName: _t("Description"),
                        field: "description",
                        minWidth: 150,
                        flex: 2,
                        cellClass: "ag-cell-left",
                        headerClass: "ag-header-left",
                        suppressMenu: true,
                        cellRenderer: (
                          params: ISubContractorOSItemsCellRenderer
                        ) => {
                          const { data } = params;
                          return data.description ? (
                            <Tooltip title={data.description}>
                              <Typography className="table-tooltip-text">
                                {data.description}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                      {
                        headerName: _t("QTY"),
                        field: "qty",
                        maxWidth: 70,
                        minWidth: 70,
                        suppressMenu: true,
                        cellClass: "ag-cell-right",
                        headerClass: "ag-header-right",
                        cellRenderer: (
                          params: ISubContractorOSItemsCellRenderer
                        ) => {
                          const { data } = params;
                          return data.quantity ? (
                            <Tooltip title={data.quantity}>
                              <Typography className="table-tooltip-text">
                                {data.quantity}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                      {
                        headerName: _t("Cost/Unit"),
                        field: "cost_unit",
                        minWidth: 120,
                        maxWidth: 120,
                        suppressMenu: true,
                        cellClass: "ag-cell-right",
                        headerClass: "ag-header-right",
                        cellRenderer: (
                          params: ISubContractorOSItemsCellRenderer
                        ) => {
                          const { data } = params;
                          const nValue = Number(data?.modified_unit_cost) / 100;
                          const unitCost = formatter(
                            nValue.toFixed(2)
                          ).value_with_symbol;
                          const sOVunit = data?.unit ? `/${data?.unit}` : "";
                          return unitCost || sOVunit ? (
                            <Tooltip title={`${unitCost}${sOVunit}`}>
                              <Typography className="table-tooltip-text">
                                {`${unitCost}${sOVunit}`}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                      {
                        headerName: _t("Total"),
                        field: "total",
                        minWidth: 120,
                        maxWidth: 120,
                        suppressMenu: true,
                        cellClass: "ag-cell-right",
                        headerClass: "ag-header-right",
                        cellRenderer: (
                          params: ISubContractorOSItemsCellRenderer
                        ) => {
                          const { data } = params;
                          const total = formatter(
                            (Number(data?.no_mu_total) / 100).toFixed(2)
                          ).value_with_symbol;
                          return total ? (
                            <Tooltip title={total}>
                              <Typography className="table-tooltip-text">
                                {total}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                    ],
                    rowData: withoutChangeOrderId,
                    loadingCellRenderer: isScheduleValueLoading,
                    rowMultiSelectWithClick: true,
                    suppressRowClickSelection: true,
                    onSelectionChanged: handleSelectionDirectItemChanged,
                    onGridReady: onGridReady,
                    noRowsOverlayComponent: () => (
                      <NoRecords
                        image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                      />
                    ),
                  }}
                />
              )}
            </SidebarCardBorder>

            {!isScheduleValueLoading &&
              scheduleValueItems?.estimate_section.length > 0 && (
                <SidebarCardBorder addGap={true}>
                  {scheduleValueItems?.estimate_section.map(
                    (items: ISCEstimateItemData, index: number) => {
                      let total = 0;
                      if (items?.items?.length > 0) {
                        items?.items.forEach((entry: ISCEstimateItem) => {
                          const amount = entry.no_mu_total
                            ? parseFloat(entry.no_mu_total)
                            : 0;
                          total += amount;
                        });
                      }
                      const titleToShow =
                        index === 0
                          ? moduleEst?.module_name
                            ? `${HTMLEntities.decode(
                                sanitizeString(moduleEst?.module_name)
                              )} ${_t("Items")}`
                            : _t("Estimate Items")
                          : undefined;
                      return (
                        <ItemsTable
                          title={titleToShow}
                          subTitle={items?.section_name}
                          tableProps={{
                            rowSelection: "multiple",
                            columnDefs: [
                              {
                                headerName: "",
                                field: "",
                                minWidth: 36,
                                maxWidth: 36,
                                headerCheckboxSelection: true,
                                checkboxSelection: true,
                                suppressMenu: true,
                              },
                              {
                                headerName: _t("Type"),
                                maxWidth: 60,
                                minWidth: 60,
                                field: "item_type",
                                suppressMenu: true,
                                headerClass: "ag-header-center",
                                cellClass: "ag-cell-center",
                                cellRenderer: ({
                                  data,
                                }: ISubContractorEstimateItemsCellRenderer) => {
                                  const itemType = data?.item_type_key;
                                  const itemTypeName = data?.item_type_name;
                                  const itemTypeIcon =
                                    SCIcons[itemType as IconKey];

                                  return itemTypeIcon ? (
                                    <Tooltip title={itemTypeName}>
                                      <FontAwesomeIcon
                                        className="w-4 h-4 text-primary-900 mx-auto"
                                        icon={itemTypeIcon}
                                      />
                                    </Tooltip>
                                  ) : (
                                    <>-</>
                                  );
                                },
                              },
                              {
                                headerName: "",
                                field: "",
                                minWidth: 120,
                                maxWidth: 120,
                                suppressMenu: true,
                              },
                              {
                                headerName: _t("Item Name"),
                                field: "item_name",
                                minWidth: 150,
                                maxWidth: 150,
                                cellClass: "ag-cell-left",
                                headerClass: "ag-header-left",
                                suppressMenu: true,
                                cellRenderer: (
                                  params: ISubContractorEstimateItemsCellRenderer
                                ) => {
                                  const { data } = params;
                                  return HTMLEntities.decode(
                                    sanitizeString(data.subject)
                                  ) ? (
                                    <Tooltip
                                      title={HTMLEntities.decode(
                                        sanitizeString(data.subject)
                                      )}
                                    >
                                      <Typography className="table-tooltip-text">
                                        {HTMLEntities.decode(
                                          sanitizeString(data.subject)
                                        )}
                                      </Typography>
                                    </Tooltip>
                                  ) : (
                                    <>-</>
                                  );
                                },
                              },
                              {
                                headerName: _t("Description"),
                                field: "description",
                                minWidth: 150,
                                flex: 2,
                                cellClass: "ag-cell-left",
                                headerClass: "ag-header-left",
                                suppressMenu: true,
                                cellRenderer: (
                                  params: ISubContractorEstimateItemsCellRenderer
                                ) => {
                                  const { data } = params;
                                  return data.description ? (
                                    <Tooltip
                                      title={HTMLEntities.decode(
                                        sanitizeString(data.description)
                                      )}
                                    >
                                      <Typography className="table-tooltip-text">
                                        {HTMLEntities.decode(
                                          sanitizeString(data.description)
                                        )}
                                      </Typography>
                                    </Tooltip>
                                  ) : (
                                    <>-</>
                                  );
                                },
                              },
                              {
                                headerName: _t("QTY"),
                                field: "qty",
                                maxWidth: 70,
                                minWidth: 70,
                                suppressMenu: true,
                                cellClass: "ag-cell-right",
                                headerClass: "ag-header-right",
                                cellRenderer: (
                                  params: ISubContractorEstimateItemsCellRenderer
                                ) => {
                                  const { data } = params;
                                  return data.quantity ? (
                                    <Tooltip title={data.quantity}>
                                      <Typography className="table-tooltip-text">
                                        {data.quantity}
                                      </Typography>
                                    </Tooltip>
                                  ) : (
                                    <>-</>
                                  );
                                },
                              },
                              {
                                headerName: _t("Cost/Unit"),
                                field: "cost_unit",
                                minWidth: 120,
                                maxWidth: 120,
                                suppressMenu: true,
                                cellClass: "ag-cell-right",
                                headerClass: "ag-header-right",
                                cellRenderer: (
                                  params: ISubContractorEstimateItemsCellRenderer
                                ) => {
                                  const { data } = params;
                                  const nValue =
                                    Number(data?.modified_unit_cost) / 100;
                                  const unitCost = formatter(
                                    nValue.toFixed(2)
                                  ).value_with_symbol;
                                  const estUnit = data?.unit
                                    ? `/${data?.unit}`
                                    : "";
                                  return unitCost || estUnit ? (
                                    <Tooltip title={`${unitCost}${estUnit}`}>
                                      <Typography className="table-tooltip-text">
                                        {`${unitCost}${estUnit}`}
                                      </Typography>
                                    </Tooltip>
                                  ) : (
                                    <>-</>
                                  );
                                },
                              },
                              {
                                headerName: _t("Total"),
                                field: "total",
                                minWidth: 120,
                                maxWidth: 120,
                                suppressMenu: true,
                                cellClass: "ag-cell-right",
                                headerClass: "ag-header-right",
                                cellRenderer: (
                                  params: ISubContractorEstimateItemsCellRenderer
                                ) => {
                                  const { data } = params;
                                  const total = formatter(
                                    (Number(data?.no_mu_total) / 100).toFixed(2)
                                  ).value_with_symbol;
                                  return total ? (
                                    <Tooltip title={total}>
                                      <Typography className="table-tooltip-text">
                                        {total}
                                      </Typography>
                                    </Tooltip>
                                  ) : (
                                    <>-</>
                                  );
                                },
                              },
                            ],
                            rowData:
                              items?.items?.length > 0 ? items?.items : [],
                            onSelectionChanged: (event) =>
                              handleSelectionEstimateItemChanged(
                                event,
                                items.section_id
                              ),
                            rowMultiSelectWithClick: true,
                            suppressRowClickSelection: true,
                            onGridReady: onGridEstimateReady(),
                            noRowsOverlayComponent: () => (
                              <NoRecords
                                image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                              />
                            ),
                          }}
                        />
                      );
                    }
                  )}
                </SidebarCardBorder>
              )}

            {!isScheduleValueLoading && (
              <SidebarCardBorder addGap={true}>
                <ItemsTable
                  title={
                    moduleCO?.plural_name
                      ? HTMLEntities.decode(
                          sanitizeString(moduleCO?.plural_name)
                        ) + _t(" Items")
                      : _t("Change Order Items")
                  }
                  tableProps={{
                    rowSelection: "multiple",
                    columnDefs: [
                      {
                        headerName: "",
                        field: "",
                        minWidth: 36,
                        maxWidth: 36,
                        headerCheckboxSelection: true,
                        checkboxSelection: true,
                        suppressMenu: true,
                      },
                      {
                        headerName: _t("Type"),
                        maxWidth: 60,
                        minWidth: 60,
                        field: "item_type",
                        suppressMenu: true,
                        headerClass: "ag-header-center",
                        cellClass: "ag-cell-center",
                        cellRenderer: ({
                          data,
                        }: ISubContractorChangeOrderItemsCellRenderer) => {
                          const itemType = data?.item_type_key;
                          const itemTypeName = data?.item_type_name;
                          const itemTypeIcon = SCIcons[itemType as IconKey];

                          return itemTypeIcon ? (
                            <Tooltip title={itemTypeName}>
                              <FontAwesomeIcon
                                className="w-4 h-4 text-primary-900 mx-auto"
                                icon={itemTypeIcon}
                              />
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                      {
                        headerName: _t("Source"),
                        field: "source",
                        minWidth: 120,
                        maxWidth: 120,
                        suppressMenu: true,
                        cellRenderer: (
                          params: ISubContractorChangeOrderItemsCellRenderer
                        ) => {
                          const { data } = params;
                          const source = `${_t("CO")} ${
                            data?.company_order_id || ""
                          }`;
                          return source ? (
                            <Tooltip title={source}>
                              <Typography className="table-tooltip-text">
                                {source}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                      {
                        headerName: _t("Item Name"),
                        field: "item_name",
                        maxWidth: 150,
                        minWidth: 150,
                        suppressMenu: true,
                        cellClass: "ag-cell-left",
                        headerClass: "ag-header-left",
                        cellRenderer: (
                          params: ISubContractorChangeOrderItemsCellRenderer
                        ) => {
                          const { data } = params;
                          return data.subject ? (
                            <Tooltip title={data.subject}>
                              <Typography className="table-tooltip-text">
                                {data.subject}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                      {
                        headerName: _t("Description"),
                        field: "description",
                        minWidth: 150,
                        flex: 2,
                        cellClass: "ag-cell-left",
                        headerClass: "ag-header-left",
                        suppressMenu: true,
                        cellRenderer: (
                          params: ISubContractorChangeOrderItemsCellRenderer
                        ) => {
                          const { data } = params;
                          return data.description ? (
                            <Tooltip title={data.description}>
                              <Typography className="table-tooltip-text">
                                {data.description}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                      {
                        headerName: _t("QTY"),
                        field: "qty",
                        maxWidth: 70,
                        minWidth: 70,
                        suppressMenu: true,
                        cellClass: "ag-cell-right",
                        headerClass: "ag-header-right",
                        cellRenderer: (
                          params: ISubContractorChangeOrderItemsCellRenderer
                        ) => {
                          const { data } = params;
                          return data.quantity ? (
                            <Tooltip title={data.quantity}>
                              <Typography className="table-tooltip-text">
                                {data.quantity}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                      {
                        headerName: _t("Cost/Unit"),
                        field: "cost_unit",
                        minWidth: 120,
                        maxWidth: 120,
                        suppressMenu: true,
                        cellClass: "ag-cell-right",
                        headerClass: "ag-header-right",
                        cellRenderer: (
                          params: ISubContractorChangeOrderItemsCellRenderer
                        ) => {
                          const { data } = params;
                          const nValue = Number(data?.modified_unit_cost) / 100;
                          const unitCost = formatter(
                            nValue.toFixed(2)
                          ).value_with_symbol;
                          const cOUnit = data?.unit ? `/${data?.unit}` : "";
                          return unitCost || cOUnit ? (
                            <Tooltip title={`${unitCost}${cOUnit}`}>
                              <Typography className="table-tooltip-text">
                                {`${unitCost}${cOUnit}`}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                      {
                        headerName: _t("Total"),
                        field: "total",
                        minWidth: 120,
                        maxWidth: 120,
                        suppressMenu: true,
                        cellClass: "ag-cell-right",
                        headerClass: "ag-header-right",
                        cellRenderer: (
                          params: ISubContractorChangeOrderItemsCellRenderer
                        ) => {
                          const { data } = params;
                          const total = formatter(
                            (Number(data?.no_mu_total) / 100).toFixed(2)
                          ).value_with_symbol;
                          return total ? (
                            <Tooltip title={total}>
                              <Typography className="table-tooltip-text">
                                {total}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                    ],
                    rowData: withChangeOrderId,
                    loadingCellRenderer: isScheduleValueLoading,
                    loadingCellRendererParams: isScheduleValueLoading,
                    rowMultiSelectWithClick: true,
                    suppressRowClickSelection: true,
                    onSelectionChanged: handleSelectionChangeOrderChanged,
                    onGridReady: onGridChangeOrderReady,
                    noRowsOverlayComponent: () => (
                      <NoRecords
                        image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                      />
                    ),
                  }}
                />
              </SidebarCardBorder>
            )}

            {!isScheduleValueLoading && (
              <SidebarCardBorder addGap={true}>
                <ItemsTable
                  title={
                    moduleWO?.plural_name
                      ? HTMLEntities.decode(
                          sanitizeString(moduleWO?.plural_name)
                        ) + _t(" Items")
                      : _t("Work Order Items")
                  }
                  tableProps={{
                    rowSelection: "multiple",
                    columnDefs: [
                      {
                        headerName: "",
                        field: "",
                        minWidth: 36,
                        maxWidth: 36,
                        headerCheckboxSelection: true,
                        checkboxSelection: true,
                        suppressMenu: true,
                      },
                      {
                        headerName: _t("Type"),
                        maxWidth: 60,
                        minWidth: 60,
                        field: "item_type",
                        suppressMenu: true,
                        headerClass: "ag-header-center",
                        cellClass: "ag-cell-center",
                        cellRenderer: ({
                          data,
                        }: ISubContractorWorkOrderItemsCellRenderer) => {
                          const itemType = data?.item_type_key;
                          const itemTypeName = data?.item_type_name;
                          const itemTypeIcon = SCIcons[itemType as IconKey];

                          return itemTypeIcon ? (
                            <Tooltip title={itemTypeName}>
                              <FontAwesomeIcon
                                className="w-4 h-4 text-primary-900 mx-auto"
                                icon={itemTypeIcon}
                              />
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                      {
                        headerName: _t("Source"),
                        field: "source",
                        minWidth: 120,
                        maxWidth: 120,
                        suppressMenu: true,
                        cellRenderer: (
                          params: ISubContractorWorkOrderItemsCellRenderer
                        ) => {
                          const { data } = params;
                          const source = `${_t("WO")} ${
                            data?.company_order_id || ""
                          }`;
                          return source ? (
                            <Tooltip title={source}>
                              <Typography className="table-tooltip-text">
                                {source}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                      {
                        headerName: _t("Item Name"),
                        field: "item_name",
                        maxWidth: 150,
                        minWidth: 150,
                        suppressMenu: true,
                        cellClass: "ag-cell-left",
                        headerClass: "ag-header-left",
                        cellRenderer: (
                          params: ISubContractorWorkOrderItemsCellRenderer
                        ) => {
                          const { data } = params;
                          return data.subject ? (
                            <Tooltip title={data.subject}>
                              <Typography className="table-tooltip-text">
                                {data.subject}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                      {
                        headerName: _t("Description"),
                        field: "description",
                        minWidth: 150,
                        flex: 2,
                        cellClass: "ag-cell-left",
                        headerClass: "ag-header-left",
                        suppressMenu: true,
                        cellRenderer: (
                          params: ISubContractorWorkOrderItemsCellRenderer
                        ) => {
                          const { data } = params;
                          return data.description ? (
                            <Tooltip title={data.description}>
                              <Typography className="table-tooltip-text">
                                {data.description}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                      {
                        headerName: _t("QTY"),
                        field: "qty",
                        maxWidth: 70,
                        minWidth: 70,
                        suppressMenu: true,
                        cellClass: "ag-cell-right",
                        headerClass: "ag-header-right",
                        cellRenderer: (
                          params: ISubContractorWorkOrderItemsCellRenderer
                        ) => {
                          const { data } = params;
                          return data.quantity ? (
                            <Tooltip title={data.quantity}>
                              <Typography className="table-tooltip-text">
                                {data.quantity}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                      {
                        headerName: _t("Cost/Unit"),
                        field: "cost_unit",
                        minWidth: 120,
                        maxWidth: 120,
                        suppressMenu: true,
                        cellClass: "ag-cell-right",
                        headerClass: "ag-header-right",
                        cellRenderer: (
                          params: ISubContractorWorkOrderItemsCellRenderer
                        ) => {
                          const { data } = params;
                          const nValue = Number(data?.modified_unit_cost) / 100;
                          const unitCost = formatter(
                            nValue.toFixed(2)
                          ).value_with_symbol;
                          const wOUnit = data?.unit ? `/${data?.unit}` : "";
                          return wOUnit || unitCost ? (
                            <Tooltip title={`${unitCost}${wOUnit}`}>
                              <Typography className="table-tooltip-text">
                                {`${unitCost}${wOUnit}`}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                      {
                        headerName: _t("Total"),
                        field: "total",
                        minWidth: 120,
                        maxWidth: 120,
                        suppressMenu: true,
                        cellClass: "ag-cell-right",
                        headerClass: "ag-header-right",
                        cellRenderer: (
                          params: ISubContractorWorkOrderItemsCellRenderer
                        ) => {
                          const { data } = params;
                          const total = formatter(
                            (Number(data?.no_mu_total) / 100).toFixed(2)
                          ).value_with_symbol;
                          return total ? (
                            <Tooltip title={total}>
                              <Typography className="table-tooltip-text">
                                {total}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <>-</>
                          );
                        },
                      },
                    ],
                    rowData: withWorkOrderId,
                    loadingCellRenderer: isScheduleValueLoading,
                    loadingCellRendererParams: isScheduleValueLoading,
                    rowMultiSelectWithClick: true,
                    suppressRowClickSelection: true,
                    onSelectionChanged: handleSelectionWorkOrderChanged,
                    onGridReady: onGridWorkOrderReady,
                    noRowsOverlayComponent: () => (
                      <NoRecords
                        image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                      />
                    ),
                  }}
                />
              </SidebarCardBorder>
            )}
          </div>
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
          <PrimaryButton
            type="primary"
            className="w-full justify-center primary-btn"
            htmlType="submit"
            onClick={() => {
              handleSaveScheduleValueItems();
            }}
            isLoading={isDataUpdating}
            disabled={
              isDataUpdating ||
              isLoading ||
              isScheduleValueLoading ||
              !isFormModified
            }
            buttonText={_t("Save & Close")}
          />
        </div>
      </div>
    </Drawer>
  );
};

export default ProjectBudgetItems;
