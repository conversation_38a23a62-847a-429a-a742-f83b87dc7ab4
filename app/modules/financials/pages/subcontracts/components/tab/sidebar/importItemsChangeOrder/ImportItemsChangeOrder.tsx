import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { GridReadyEvent, SelectionChangedEvent } from "ag-grid-community";
// Hook Redux + store
import { useTranslation } from "~/hook";
import { getGConfig, getGModule<PERSON>y<PERSON><PERSON> } from "~/zustand";
import { fetchSCChangeOrderListApi } from "../../../../redux/action";
import { useAppSCDispatch, useAppSCSelector } from "../../../../redux/store";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import ItemsTable from "~/shared/components/molecules/itemsTable/ItemsTable";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { ModuleItemsFilter } from "~/shared/components/molecules/moduleItemsFilter";

// Other
import { defaultConfig } from "~/data";
import { formatAmount, sanitizeString } from "~/helpers/helper";

const ImportItemsChangeOrder = ({
  isOpen,
  onClose,
  isDataUpdating,
  changeOrderItemHandler,
}: IImportItemsChangeOrderProps) => {
  const { _t } = useTranslation();
  const { module_id }: GConfig = getGConfig();
  const dispatch = useAppSCDispatch();
  const { formatter } = useCurrencyFormatter();
  const moduleCO: GModule | undefined = getGModuleByKey(
    defaultConfig.change_order_module
  );

  const { details }: ISCDetailsInitialState = useAppSCSelector(
    (state) => state.subContractsDetails
  );
  const {
    changeOrderItem,
    isChangeOrderLoading,
    isChangeOrderFetched,
  }: ISCImportItemsInitialState = useAppSCSelector(
    (state) => state.subContractsImportItemList
  );

  const { changeOrder }: ISCItemsInitialState = useAppSCSelector(
    (state) => state.subContractsItems
  );

  const [isSelectionChanged, setIsSelectionChanged] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const [filter, setFilter] = useState<ISelectItemFilter["key"][]>([]);
  const [defaultselectedData, setDefaultselectedData] = useState<
    ISCChangeOrder[]
  >([]);

  const [selectedItems, setSelectedItems] = useState<ISCChangeOrder[]>([]);
  const [selectedItemsByTable, setSelectedItemsByTable] = useState({});
  const [tableKey, setTableKey] = useState<number>(0);

  const changeOrderlistFilter = useMemo(() => {
    if (filter.length === 0) {
      return changeOrderItem;
    }

    return changeOrderItem.filter(
      (item) =>
        item?.item_type_key !== undefined &&
        filter.includes(item?.item_type_key)
    );
  }, [JSON.stringify(changeOrderItem), JSON.stringify(filter)]);

  useEffect(() => {
    setTableKey((prevKey) => prevKey + 1);
  }, [JSON.stringify(filter)]);

  const onGridReady = (params: GridReadyEvent) => {
    const gridApi = params.api;
    gridApi.forEachNode((node) => {
      const { item_id } = node.data;
      if (
        [...defaultselectedData, ...selectedItems].some(
          (item) => item.item_id == item_id
        )
      ) {
        node.setSelected(true);
      }
    });
  };

  const handleSelectionItemChange = (
    event: SelectionChangedEvent,
    sectionId: string
  ): void => {
    const selectedNodes = event.api.getSelectedNodes();
    const selectedData = selectedNodes.map((node) => node.data);
    setSelectedItemsByTable((prev) => ({ ...prev, [sectionId]: selectedData }));
    setIsSelectionChanged(true);
  };

  useEffect(() => {
    const result = Object?.values(
      selectedItemsByTable
    )?.flat() as ISCChangeOrder[];
    setSelectedItems(result);

    // Compare with default and set flag
    setIsSelectionChanged(isSelectionDifferent(result, defaultselectedData));
  }, [selectedItemsByTable, defaultselectedData]);

  useEffect(() => {
    if (!isChangeOrderFetched) {
      const params: IGetImportItemsListParams = {
        module_id: module_id,
        project_id: Number(details.project_id),
      };
      dispatch(fetchSCChangeOrderListApi(params));
    }
  }, [isOpen, isChangeOrderFetched]);

  const separatedArrays = useMemo(() => {
    const grouped: Record<string, typeof changeOrderlistFilter> = {};

    changeOrderlistFilter.forEach((item) => {
      if (!grouped[item.source_name]) {
        grouped[item.source_name] = [];
      }
      grouped[item.source_name].push(item);
    });

    return Object.entries(grouped).map(([source_name, items]) => ({
      section_id: source_name,
      items,
    }));
  }, [changeOrderlistFilter]);

  useEffect(() => {
    if (changeOrderlistFilter && !!changeOrderlistFilter.length) {
      const defaultselectedDataArr = changeOrderlistFilter
        .filter((jobItem) =>
          changeOrder?.some(
            (item) =>
              item?.reference_module_item_id?.toString() ===
              jobItem?.item_id?.toString()
          )
        )
        .map((item) => {
          return item;
        });
      setDefaultselectedData(defaultselectedDataArr);
    }
  }, [
    JSON.stringify(changeOrder),
    changeOrderlistFilter,
    // JSON.stringify(separatedArrays),
  ]);

  const handleSaveChangeOrderItems = useCallback(async () => {
    const itemsToAdd: ISCChangeOrder[] = [];
    let itemToBeDelete: ISCChangeOrder[] = [];
    let itemToBeDeleteId: number[];

    selectedItems.forEach((item) => {
      if (
        !defaultselectedData.some(
          (i: ISCChangeOrder) => i.item_id === item.item_id
        )
      ) {
        itemsToAdd.push(item);
      }
    });

    defaultselectedData.forEach((item: ISCChangeOrder) => {
      if (!selectedItems.some((i) => i.item_id === item.item_id)) {
        itemToBeDelete.push(item);
      }
    });

    itemToBeDeleteId =
      itemToBeDelete?.map((item: ISCChangeOrder) => {
        const mainItemId = changeOrder.find(
          (i) =>
            i.reference_module_item_id?.toString() === item.item_id.toString()
        )?.item_id as number;

        return mainItemId;
      }) ?? [];

    changeOrderItemHandler({ itemsToAdd, itemToBeDeleteId });
    onClose();
    setIsSelectionChanged(false);
  }, [JSON.stringify(selectedItems), JSON.stringify(defaultselectedData)]);

  const isSelectionDifferent = (
    selected: ISCChangeOrder[],
    defaults: ISCChangeOrder[]
  ): boolean => {
    const selectedIds = selected.map((item) => item.item_id).sort();
    const defaultIds = defaults.map((item) => item.item_id).sort();
    return JSON.stringify(selectedIds) !== JSON.stringify(defaultIds);
  };

  useEffect(() => {
    if (!isOpen) {
      setIsSelectionChanged(false);
    }
  }, [isOpen]);

  const columnDefs = [
    {
      headerName: _t("Source"),
      field: "source",
      minWidth: 150,
      maxWidth: 150,
      cellClass: "ag-cell-left",
      headerClass: "ag-header-left",
      headerCheckboxSelection: true,
      checkboxSelection: true,
      suppressMenu: true,
      cellRenderer: (params: ISubContractorChangeOrderItemsCellRenderer) => {
        const { data } = params;
        return HTMLEntities.decode(sanitizeString(data.source_name)) ? (
          <Tooltip
            title={HTMLEntities.decode(sanitizeString(data.source_name))}
          >
            <Typography className="table-tooltip-text">
              {HTMLEntities.decode(sanitizeString(data.source_name))}
            </Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("Item Name"),
      field: "item_name",
      minWidth: 150,
      flex: 2,
      cellClass: "ag-cell-left",
      headerClass: "ag-header-left",
      suppressMenu: true,
      cellRenderer: (params: ISubContractorChangeOrderItemsCellRenderer) => {
        const { data } = params;
        return HTMLEntities.decode(sanitizeString(data.subject)) ? (
          <Tooltip title={HTMLEntities.decode(sanitizeString(data.subject))}>
            <Typography className="table-tooltip-text">
              {HTMLEntities.decode(sanitizeString(data.subject))}
            </Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("Assigned To"),
      field: "assigned_to",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      cellClass: "ag-cell-left",
      headerClass: "ag-header-left",
      cellRenderer: (params: ISubContractorChangeOrderItemsCellRenderer) => {
        const { data } = params;
        return HTMLEntities.decode(sanitizeString(data?.assignee_name)) ? (
          <Tooltip
            title={HTMLEntities.decode(sanitizeString(data?.assignee_name))}
          >
            <Typography className="table-tooltip-text">
              {HTMLEntities.decode(sanitizeString(data?.assignee_name))}
            </Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("Description"),
      field: "description",
      minWidth: 150,
      flex: 2,
      cellClass: "ag-cell-left",
      headerClass: "ag-header-left",
      suppressMenu: true,
      cellRenderer: (params: ISubContractorChangeOrderItemsCellRenderer) => {
        const { data } = params;
        return HTMLEntities.decode(sanitizeString(data.description?.trim())) ? (
          <Tooltip
            title={HTMLEntities.decode(sanitizeString(data.description))}
          >
            <Typography className="table-tooltip-text">
              {HTMLEntities.decode(sanitizeString(data.description))}
            </Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("QTY"),
      field: "qty",
      maxWidth: 70,
      minWidth: 70,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: (params: ISubContractorChangeOrderItemsCellRenderer) => {
        const { data } = params;
        return data.quantity ? (
          <Tooltip title={data.quantity}>
            <Typography className="table-tooltip-text">
              {data.quantity}
            </Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("Cost/Unit"),
      field: "cost_unit",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: (params: ISubContractorChangeOrderItemsCellRenderer) => {
        const { data } = params;
        const nValue = Number(data?.modified_unit_cost) / 100;
        const unitCost = formatter(formatAmount(nValue)).value_with_symbol;
        const cOUnit = data?.unit ? `/${data?.unit}` : "";
        return unitCost || cOUnit ? (
          <Tooltip title={`${unitCost}${cOUnit}`}>
            <Typography className="table-tooltip-text">
              {`${unitCost}${cOUnit}`}
            </Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: (params: ISubContractorChangeOrderItemsCellRenderer) => {
        const { data } = params;
        const total = formatter(
          formatAmount(Number(data?.no_mu_total) / 100)
        ).value_with_symbol;
        return total ? (
          <Tooltip title={total}>
            <Typography className="table-tooltip-text">{total}</Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
  ];

  const noDataTable = useMemo(() => {
    return (
      <StaticTable
        className="static-table"
        rowSelection="multiple"
        columnDefs={columnDefs}
        rowMultiSelectWithClick={true}
        suppressRowClickSelection={true}
        rowData={[]}
        noRowsOverlayComponent={() => (
          <NoRecords
            image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
          />
        )}
      />
    );
  }, [JSON.stringify(separatedArrays)]);

  return (
    <Drawer
      open={isOpen}
      rootClassName="drawer-open"
      width={980}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-arrow-down-arrow-up"
            />
          </div>
          <div className="flex justify-between items-center w-[calc(100%-40px)] pr-2">
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(
                `Import Items From ${
                  HTMLEntities.decode(sanitizeString(moduleCO?.module_name)) ||
                  "Change Order"
                }`
              )}
            </Header>
            <Tooltip
              title={_t(
                "You can only add items that are in your Approved COs."
              )}
              placement="bottom"
            >
              <Typography className="cursor-pointer text-[#B94A48] text-sm">
                {_t("Missing Something?")}
              </Typography>
            </Tooltip>
          </div>
        </div>
      }
      closeIcon={<CloseButton onClick={() => onClose()} />}
    >
      <div className="py-4">
        <div className="sidebar-body overflow-y-auto h-[calc(100vh-132px)] px-4">
          <div className="grid gap-4">
            <Typography className="text-primary-900 text-sm">
              {_t(
                "Not finding the CO you are looking for? Archived COs will not appear for import. If you need to use an archived CO you will need to make it active again from your Change Orders Module"
              )}
            </Typography>
            <div className="flex items-center justify-between">
              <Header level={5} className="!text-sm !mb-0 text-[#4B4B4B]">
                {_t(
                  `${
                    HTMLEntities.decode(
                      sanitizeString(moduleCO?.module_name)
                    ) || "Change Order"
                  } Items`
                )}
              </Header>
              <ModuleItemsFilter
                onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
                filter={filter}
                onChangeFilter={setFilter}
                openFilter={open}
              />
            </div>
            <div className="grid gap-4">
              {isChangeOrderLoading ? (
                <SidebarCardBorder addGap={true}>
                  <Spin className="w-full h-20 flex items-center justify-center" />
                </SidebarCardBorder>
              ) : separatedArrays?.length > 0 ? (
                separatedArrays?.map((item: any, inx) => (
                  <SidebarCardBorder addGap={true} key={item?.section_id}>
                    <div className="grid gap-2">
                      <ItemsTable
                        title={""}
                        subTitle={item?.section_name}
                        tableProps={{
                          key: `${tableKey}-${item.section_id}`,
                          rowSelection: "multiple",
                          columnDefs: columnDefs,
                          rowData: item?.items?.length > 0 ? item?.items : [],
                          onSelectionChanged: (event: any) =>
                            handleSelectionItemChange(event, item.section_id),
                          rowMultiSelectWithClick: true,
                          suppressRowClickSelection: true,
                          onGridReady: onGridReady,
                          noRowsOverlayComponent: () => (
                            <NoRecords
                              image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                            />
                          ),
                        }}
                      />
                    </div>
                  </SidebarCardBorder>
                ))
              ) : (
                <SidebarCardBorder addGap={true}>
                  <div className="p-2 common-card">
                    <div className="ag-theme-alpine">{noDataTable}</div>
                  </div>
                </SidebarCardBorder>
              )}
            </div>
          </div>
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
          <PrimaryButton
            type="primary"
            className="w-full justify-center primary-btn"
            htmlType="submit"
            onClick={() => {
              handleSaveChangeOrderItems();
            }}
            isLoading={isDataUpdating}
            disabled={!isSelectionChanged || isDataUpdating}
            buttonText={_t("Save & Close")}
          />
        </div>
      </div>
    </Drawer>
  );
};

export default ImportItemsChangeOrder;
