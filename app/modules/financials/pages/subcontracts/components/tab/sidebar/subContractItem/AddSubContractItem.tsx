import { useRef, useState, useMemo, useEffect, FormEvent } from "react";
import isEmpty from "lodash/isEmpty";
import isEqual from "lodash/isEqual";
import * as Yup from "yup";
import { useParams } from "@remix-run/react";
import { useFormik } from "formik";
import type { InputRef } from "antd";
// Hook Redux
import { useTranslation } from "~/hook";
import { defaultConfig } from "~/data";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import { getGSettings } from "~/zustand";
import {
  filterOptionBySubstring,
  generateCostCodeLabel,
  getItemTypeIcon,
  onKeyDownCurrency,
} from "~/shared/utils/helper/common";
import {
  useAppSCDispatch,
  useAppSCSelector,
} from "~/modules/financials/pages/subcontracts/redux/store";
import {
  addUpManualItemAct,
  updateEstimateItems,
} from "~/modules/financials/pages/subcontracts/redux/slices/sCItemsSlice";
import {
  addSubContractsItemsApi,
  updateSCOriginalItemsApi,
} from "~/modules/financials/pages/subcontracts/redux/action";
import { resetDash } from "~/modules/financials/pages/subcontracts/redux/slices";
// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import SubContractsSendEmail from "../../../SubContractsSendEmail";
import { addUnit, getUnitList } from "~/redux/action/unitActions";
import { isValidId } from "~/modules/financials/pages/estimates/utils/common";

type FormikSubmitEvent = FormEvent<HTMLFormElement> & {
  nativeEvent: { submitter?: HTMLButtonElement };
};

const AddSubContractItem = ({
  isOpen,
  onClose,
  isViewOnly = false,
  recordId = 0,
  isShowNextPre = false,
  allRecords = [],
  type,
  moduleName,
}: ISubContrtactItemProps) => {
  const { _t } = useTranslation();
  const { id: sCId }: RouteParams = useParams();
  const { is_cidb_auto_save }: GSettings = getGSettings();

  const { inputFormatter, unformatted, formatter } = useCurrencyFormatter();
  const costUnitRef = useRef<HTMLDivElement>(null);
  const unitCostContainerRef = useRef<HTMLDivElement>(null);
  const unitCostRef = useRef<InputRef>(null);
  const skipClose = useRef<boolean>(false);

  const dispatch = useAppSCDispatch();
  const { companyItemsData }: ISCCommonInitialState = useAppSCSelector(
    (state) => state.sCCommonData
  );
  const { details }: ISCDetailsInitialState = useAppSCSelector(
    (state) => state.subContractsDetails
  );
  const { codeCostData }: IGetCostCodeList = useAppSCSelector(
    (state) => state.costCode
  );
  const { estimateItem }: ISCItemsInitialState = useAppSCSelector(
    (state) => state.subContractsItems
  );

  const [isOpenSelectCustomer, setIsOpenSelectCustomer] =
    useState<boolean>(false);
  const [selectedAssigned, setSelectedAssigned] = useState<
    Partial<IDirectoryData>
  >({});
  const [showUnitInputs, setShowUnitInputs] = useState<boolean>(!isViewOnly);

  const [selectedRec, setSelectedRec] = useState<any>({});
  const [formEvent, setFormEvent] = useState<FormikSubmitEvent | null>(null);
  const [selRecordId, setSelRecordId] = useState<number>(recordId);
  const [isHideNextPre, setIsHideNextPre] = useState<boolean>(true);
  const [submittingFrm, setSubmittingFrm] = useState<string>("");
  const [newTypeName, setNewTypeName] = useState<string>("");
  const [unitData, setUnitData] = useState<IUnitData[]>([]);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [isOpenContactDetails, setIsOpenContactDetails] =
    useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<Partial<IDirectoryData>>({});
  const [cidbItemExistPopupData, setCidbItemExistPopupData] =
    useState<ICidbItemExistPopupData | null>();

  useEffect(() => {
    if (selRecordId && allRecords?.length > 0) {
      setSelectedRec(allRecords.find((r) => r.item_id == selRecordId));
      setIsHideNextPre(!isShowNextPre);
    } else {
      setSelectedRec({});
      setIsHideNextPre(true);
    }
  }, [selRecordId, JSON.stringify(allRecords)]);

  const getUnit = async () => {
    const unit: IUnitListResponse = await getUnitList();
    setUnitData(unit?.data?.units);
  };

  useEffect(() => {
    getUnit();
  }, []);

  const initialValues: IAddSubContractItem = useMemo(() => {
    const unitCost = selectedRec?.unit_cost
      ? (Number(selectedRec?.unit_cost) / 100).toString()
      : "";
    const total = selectedRec?.total
      ? (Number(selectedRec?.total) / 100).toString()
      : "";

    const conId = selectedRec?.assigned_to_contact_id;
    setSelectedAssigned({
      user_id: Number(selectedRec?.assigned_to || ""),
      display_name: selectedRec?.assignee_name || "",
      module_display_name: selectedRec?.assigned_to_company_name,
      type_key: selectedRec?.assigned_to_type_key
        ? selectedRec?.assigned_to_type_key
        : undefined,
      type_name: selectedRec?.assigned_to_type_name
        ? selectedRec?.assigned_to_type_name
        : undefined,
      contact_id: !!conId ? (conId != "0" ? conId : undefined) : undefined,
      image: !conId || conId == "0" ? selectedRec?.user_image : "",
    });

    return {
      item_id: selectedRec?.item_id,
      subject: selectedRec?.subject || "",
      item_type:
        selectedRec?.item_type && selectedRec?.item_type != ""
          ? selectedRec?.item_type?.toString()
          : "",
      assigned_to: selectedRec?.assigned_to?.toString() || "",
      assigned_to_contact_id:
        selectedRec?.assigned_to_contact_id?.toString() || "",
      quantity: selectedRec?.quantity?.toString() || "",
      unit_cost: unitCost,
      unit: selectedRec?.unit?.toString() || "",
      total: total,
      cost_code_id: selectedRec?.cost_code_id || "",
      description: selectedRec?.description?.toString() || "",
      internal_notes: selectedRec?.internal_notes?.toString() || "",
      add_item_to_database: selectedRec?.item_on_database || 0,
      submitAction: "",
      sub_contract_id: selectedRec?.sub_contract_id?.toString() || "",
      is_markup_percentage: selectedRec?.is_markup_percentage || 1,
      markup: selectedRec?.markup || "",
      sub_contract_item_no:
        selectedRec?.item_id && selectedRec?.item_id != 0
          ? selectedRec?.sub_contract_item_no
          : undefined,
      reference_item_id: selectedRec?.reference_item_id || "",
    };
  }, [selectedRec?.item_id]);

  const validationSchema = Yup.object().shape({
    subject: Yup.string().trim().required("This field is required."),
    item_type: Yup.string().trim().required("This field is required."),
  });

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      const isSkipClose =
        formEvent?.nativeEvent.submitter?.getAttribute("data-skip-close") ===
          "true" || skipClose.current;

      const formData = {
        ...values,
        submitAction: undefined,
        reference_item_id:
          selectedRec?.reference_item_id || values?.reference_item_id || "",
        add_item_to_database:
          selectedRec?.reference_item_id || values?.reference_item_id
            ? 0
            : values?.add_item_to_database,
      };
      if (formData?.unit_cost) {
        formData.unit_cost = (Number(formData?.unit_cost) * 100).toString();
      }
      if (formData?.total) {
        formData.total = (Number(formData?.total) * 100).toString();
      }
      if (formData.item_type && companyItemsData?.items?.length > 0) {
        formData.markup =
          companyItemsData.items?.find(
            (items) => items?.type_id == formData.item_type
          )?.mark_up || "";
      }
      const newFormData = { manual_items: [formData] };

      if (isSkipClose) {
        setSubmittingFrm("save1");
      } else {
        setSubmittingFrm(
          values.submitAction == "saveAndAddAnother"
            ? "saveAndAddAnother"
            : "save"
        );
      }
      try {
        let responseApi = { success: false, message: "", data: {} };
        if (selectedRec?.item_id && selectedRec?.item_id != "0") {
          responseApi = (await updateSCOriginalItemsApi({
            items: [formData],
            id: sCId || "",
          })) as ISCOriginItemUpRes;
        } else {
          responseApi = (await addSubContractsItemsApi(
            newFormData,
            sCId || ""
          )) as IAddSubContractsRes;
        }

        const fltCostCode = codeCostData.find(
          (item) => item.code_id == formData?.cost_code_id
        );

        if (responseApi?.success) {
          if (!isSkipClose) {
            if (values.submitAction == "saveAndAddAnother") {
              setSelectedAssigned({});
              setSelRecordId(0);
              setFormEvent(null);
              resetForm();
            } else {
              onClose();
            }
          }
          dispatch(resetDash());
          const isUpdate = selectedRec?.item_id && selectedRec?.item_id != "0";
          const itemTypeObj = itemsTypeList?.find(
            (item) => item.value == formData?.item_type
          );

          let itemTypeKey = "item_other";
          if (itemTypeObj?.value == defaultConfig.material_teb_id?.toString()) {
            itemTypeKey = "item_material";
          } else if (
            itemTypeObj?.value == defaultConfig.equipment_teb_id?.toString()
          ) {
            itemTypeKey = "item_equipment";
          } else if (
            itemTypeObj?.value == defaultConfig.labor_teb_id?.toString()
          ) {
            itemTypeKey = "item_labour";
          } else if (
            itemTypeObj?.value == defaultConfig.subcontractor_teb_id?.toString()
          ) {
            itemTypeKey = "item_sub_contractor";
          }

          const extraObj = {
            cost_code_name:
              formData?.cost_code_id &&
              formData?.cost_code_id != "0" &&
              isEmpty(fltCostCode)
                ? selectedRec?.cost_code_name
                : fltCostCode?.cost_code_name || "",
            cost_code:
              formData?.cost_code_id &&
              formData?.cost_code_id != "0" &&
              isEmpty(fltCostCode)
                ? selectedRec?.cost_code
                : fltCostCode?.csi_code || "",

            assigned_to: selectedAssigned?.user_id,
            assignee_name: selectedAssigned?.display_name || "",
            assigned_to_company_name: selectedAssigned?.module_display_name,
            assigned_to_type_key: selectedAssigned?.type_key
              ? selectedAssigned?.type_key
              : undefined,
            assigned_to_type_name: selectedAssigned?.type_name
              ? selectedAssigned?.type_name
              : undefined,
            assigned_to_contact_id: !!selectedAssigned?.contact_id
              ? selectedAssigned?.contact_id != "0"
                ? selectedAssigned?.contact_id
                : undefined
              : undefined,
            user_image: selectedAssigned?.image,
            item_on_database: formData?.add_item_to_database || 0,
            item_type_name: itemTypeObj?.name || "",
            item_type_key: itemTypeKey || "",
          };
          if (
            type === "estimateItem" &&
            selectedRec?.item_id &&
            selectedRec?.item_id != 0
          ) {
            const updatedItems = estimateItem?.map((section) => ({
              ...section,
              items: section?.items?.map((item) =>
                item.item_id == selectedRec?.item_id
                  ? {
                      ...item,
                      ...extraObj,
                      ...formData,
                      no_mu_total: formData?.total,
                    }
                  : item
              ),
            }));

            dispatch(updateEstimateItems(updatedItems));
          } else {
            dispatch(
              addUpManualItemAct({
                data: isUpdate
                  ? {
                      ...selectedRec,
                      ...extraObj,
                      ...formData,
                    }
                  : responseApi?.data,
                action: isUpdate ? "update" : "add",
              })
            );
          }
        } else {
          if (isValidId(responseApi?.data?.reference_item_id)) {
            setCidbItemExistPopupData(responseApi);
          } else {
            notification.error({
              description: responseApi?.message,
            });
          }
        }
        setSubmittingFrm("");
      } catch (error) {
        setSubmittingFrm("");
        notification.error({
          description: (error as Error).message || "",
        });
      } finally {
        setSubmittingFrm("");
      }
    },
  });

  useEffect(() => {
    formik.resetForm({ values: initialValues });
  }, [initialValues]);

  // Submit Form
  const {
    handleSubmit,
    submitForm,
    handleChange,
    setFieldValue,
    setFieldTouched,
    values,
    errors,
    touched,
    isSubmitting,
  } = formik;

  // Open unit cost
  const handleParagraphClick = () => {
    if (!isViewOnly) {
      setShowUnitInputs(true);
      setTimeout(() => {
        unitCostRef?.current?.focus();
      }, 10);
    }
  };

  const handleFocusOut = (e: React.FocusEvent<HTMLInputElement>) => {
    if (!unitCostContainerRef.current?.contains(e.relatedTarget as Node)) {
      if (
        values?.unit_cost &&
        !isEmpty(values?.unit_cost) &&
        values.unit &&
        !isEmpty(values.unit)
      ) {
        setShowUnitInputs(false);
      }
    }
  };

  useEffect(() => {
    if (
      values?.unit_cost !== "" &&
      !isEmpty(values?.unit_cost) &&
      values?.unit_cost !== undefined &&
      values?.unit !== "" &&
      !isEmpty(values?.unit) &&
      values?.unit !== undefined &&
      !unitCostContainerRef.current?.contains(document.activeElement as Node)
    ) {
      setShowUnitInputs(false);
    } else {
      setShowUnitInputs(true);
    }
  }, [values]);

  useEffect(() => {
    if (
      values?.quantity !== "" &&
      values?.unit_cost !== "" &&
      values?.quantity &&
      values?.unit_cost
    ) {
      const total = Number(values?.quantity) * Number(values?.unit_cost);
      setFieldValue("total", total.toString());
    } else {
      setFieldValue("total", "");
    }
  }, [values?.quantity, values?.unit_cost]);

  const itemsTypeList = useMemo(() => {
    return companyItemsData?.items
      ?.filter((item) => item?.type_id)
      .map((item) => ({
        label: (
          <div className="flex items-center gap-1.5">
            <FontAwesomeIcon
              icon={getItemTypeIcon({ type: item?.type_id?.toString() })}
            />
            {HTMLEntities.decode(sanitizeString(item?.name))}
          </div>
        ),
        value: item?.type_id?.toString(),
        ...item,
      }));
  }, [companyItemsData?.items]);

  const itemTypeName = useMemo(() => {
    const itemType = values.item_type;
    if (!itemType) return "";
    const itemTypeObj = itemsTypeList?.find((item) => item.value == itemType);
    if (!selectedRec?.item_id || selectedRec?.item_id == "0") {
      setFieldValue("add_item_to_database", is_cidb_auto_save);
    }
    return itemTypeObj?.name;
  }, [values.item_type, selectedRec?.item_id, is_cidb_auto_save]);

  const costCodeOptions = useMemo(() => {
    // Map the existing cost codes
    const costCodeOpts = codeCostData.map((item: ICostCode) => ({
      label: generateCostCodeLabel({
        name: item?.cost_code_name,
        code: item?.csi_code,
        isArchived: item?.is_deleted == 1,
        isAllowCodeWithoutName: true,
      }),
      value: item?.code_id,
    }));

    // Check if the selected cost code exists in the data
    const costCodeExists = codeCostData.some(
      (item) => item.code_id == selectedRec?.cost_code_id
    );

    // If not, add it as archived
    if (
      !costCodeExists &&
      selectedRec?.cost_code_id &&
      selectedRec?.cost_code_id != 0
    ) {
      costCodeOpts.push({
        label: generateCostCodeLabel({
          name: selectedRec?.cost_code_name,
          code: selectedRec?.cost_code,
          isArchived: true,
          isAllowCodeWithoutName: true,
        }),
        value: selectedRec?.cost_code_id as string,
      });
    }
    return costCodeOpts;
  }, [JSON.stringify(codeCostData), selectedRec]);

  const handleSelectCustomer = (data: Partial<IDirectoryData>) => {
    const userId = data?.user_id || "";
    const custData = {
      ...data,
      user_image: data.image,
    };
    setSelectedAssigned(custData);
    setFieldValue("assigned_to", userId);
    setFieldValue("assigned_to_contact_id", data?.contact_id);
    setFieldValue("user_image", data?.image);
    setIsOpenSelectCustomer(false);
  };

  const currentItemIndex = useMemo(() => {
    return allRecords?.findIndex((i) => i.item_id == selectedRec?.item_id);
  }, [selectedRec?.item_id]);

  const handleItemNav = async (increment: number) => {
    const oldVal = {
      subject: initialValues?.subject,
      item_type: initialValues?.item_type,
      assigned_to: initialValues?.assigned_to,
      cost_code_id: initialValues?.cost_code_id,
      quantity: initialValues?.quantity,
      unit_cost: initialValues?.unit_cost,
      unit: initialValues?.unit,
      markup: initialValues?.markup,
      description: initialValues?.description,
      internal_notes: initialValues?.internal_notes,
      add_item_to_database: initialValues?.add_item_to_database,
    };
    const newVal = {
      subject: values?.subject,
      item_type: values?.item_type,
      assigned_to: values?.assigned_to,
      cost_code_id: values?.cost_code_id,
      quantity: values?.quantity,
      unit_cost: values?.unit_cost,
      unit: values?.unit,
      markup: values?.markup,
      description: values?.description,
      internal_notes: values?.internal_notes,
      add_item_to_database: values?.add_item_to_database,
    };

    const formModified = isEqual(newVal, oldVal);

    if (!formModified || (!formik.isValid && formModified)) {
      skipClose.current = true;
      await submitForm();
      skipClose.current = false;
    }
    if (formik.isValid) {
      setSelectedRec(allRecords[currentItemIndex + increment]);
    } else {
      document.activeElement instanceof HTMLElement &&
        document.activeElement.blur();
    }
  };
  const handleEnterKeyPress = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    event.preventDefault();
  };
  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
  };

  return (
    <>
      <Drawer
        open={isOpen}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-file-lines"
              />
            </div>
            <div className="flex justify-between w-full">
              <Header
                level={5}
                className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
              >
                {`${
                  !selectedRec?.item_id || selectedRec?.item_id == "0"
                    ? _t("Add")
                    : ""
                } ${HTMLEntities.decode(sanitizeString(moduleName))} ${_t(
                  "Item"
                )}`}
              </Header>
              {!isHideNextPre && (
                <div className="flex items-center sm:gap-2 gap-0 pr-2">
                  <ButtonWithTooltip
                    tooltipTitle={_t("Previous")}
                    tooltipPlacement="top"
                    icon="fa-regular fa-chevron-left"
                    className="item-pre-next-button disabled:bg-transparent"
                    onClick={() => {
                      handleItemNav(-1);
                    }}
                    disabled={currentItemIndex == 0 || !!submittingFrm}
                  />
                  <ButtonWithTooltip
                    tooltipTitle={_t("Next")}
                    tooltipPlacement="top"
                    icon="fa-regular fa-chevron-right"
                    className="item-pre-next-button disabled:bg-transparent"
                    disabled={
                      currentItemIndex == allRecords.length - 1 ||
                      !!submittingFrm
                    }
                    onClick={() => {
                      handleItemNav(1);
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        }
        closeIcon={<CloseButton onClick={() => onClose()} />}
      >
        <form method="post" className="py-4" onSubmit={handleSubmit} noValidate>
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <InputField
                    label={_t("Item Name")}
                    labelPlacement="top"
                    isRequired={true}
                    name="subject"
                    value={HTMLEntities.decode(sanitizeString(values?.subject))}
                    onChange={handleChange}
                    errorMessage={touched?.subject ? errors?.subject : ""}
                    disabled={
                      details?.response == "accept" ||
                      details?.response == "closed" ||
                      isViewOnly
                    }
                    onPressEnter={handleEnterKeyPress}
                  />
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <SelectField
                      label={_t("Item Type")}
                      labelPlacement="top"
                      isRequired={true}
                      disabled={
                        details?.response === "accept" ||
                        details?.response === "closed"
                          ? true
                          : !selectedRec?.item_id || selectedRec?.item_id == "0"
                          ? false
                          : type === "estimateItem" ||
                            isViewOnly ||
                            (!!selectedRec?.item_id &&
                              !!selectedRec?.reference_item_id) ||
                            (!!selectedRec?.item_id &&
                              !!selectedRec.add_item_to_database) ||
                            !(
                              type === "estimateItem" ||
                              type === "originalScope"
                            )
                      }
                      name="item_type"
                      value={
                        values.item_type && values.item_type != "0"
                          ? values.item_type?.toString()
                          : undefined
                      }
                      onChange={(value) => {
                        setFieldValue("item_type", value || "");
                        setFieldTouched("item_type", value ? false : true);
                      }}
                      options={itemsTypeList}
                      errorMessage={touched?.item_type ? errors?.item_type : ""}
                    />
                  </div>
                  <div className="w-full">
                    <ButtonField
                      label={_t("Assigned To")}
                      labelPlacement="top"
                      name="assigned_to"
                      isDisabled={
                        details?.response == "accept" ||
                        details?.response == "closed" ||
                        isViewOnly
                      }
                      value={HTMLEntities.decode(
                        sanitizeString(selectedAssigned?.display_name?.trim())
                      )}
                      avatarProps={{
                        user: {
                          name: HTMLEntities.decode(
                            sanitizeString(selectedAssigned?.display_name)
                          ),
                          image: selectedAssigned?.image,
                        },
                      }}
                      onClick={() => setIsOpenSelectCustomer(true)}
                      addonBefore={
                        <div className="flex items-center gap-1">
                          {selectedAssigned?.display_name?.trim() ? (
                            <ContactDetailsButton
                              onClick={(e: React.MouseEvent) => {
                                e.stopPropagation();
                                setIsOpenContactDetails(true);
                              }}
                            />
                          ) : (
                            <></>
                          )}
                        </div>
                      }
                    />
                  </div>
                </div>
                <div className="w-full">
                  <SelectField
                    label={_t("Cost Code")}
                    labelPlacement="top"
                    showSearch
                    allowClear={true}
                    options={costCodeOptions}
                    value={
                      values?.cost_code_id
                        ? costCodeOptions.filter((item) => {
                            return (
                              values?.cost_code_id?.toString() ===
                              item?.value?.toString()
                            );
                          })
                        : []
                    }
                    onChange={(value) => {
                      setFieldValue("cost_code_id", value ? value : "");
                    }}
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                    disabled={
                      details?.response == "accept" ||
                      details?.response == "closed" ||
                      isViewOnly
                    }
                  />
                </div>
              </SidebarCardBorder>
              <SidebarCardBorder cardTitle="Pricing">
                <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex justify-between items-center">
                    <Typography className="text-13 block text-primary-900 dark:text-white/90">
                      {_t("QTY")}
                    </Typography>
                    <div className="sm:w-40 w-28">
                      <InputNumberField
                        name="quantity"
                        id="quantity"
                        rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input"
                        placeholder={_t("Item Quantity")}
                        errorMessage={errors.quantity}
                        onPaste={handlePaste}
                        labelPlacement="left"
                        formInputClassName={
                          isViewOnly ||
                          details?.response == "accept" ||
                          details?.response == "closed"
                            ? "flex items-center justify-end"
                            : ""
                        }
                        disabled={
                          details?.response == "accept" ||
                          details?.response == "closed" ||
                          isViewOnly
                        }
                        defaultValue={
                          Number(values.quantity) !== 0 ? values.quantity : ""
                        }
                        value={
                          values?.quantity?.toString() &&
                          values.quantity.toString() != "0"
                            ? values.quantity.toString()
                            : ""
                        }
                        formatter={(value) => {
                          return inputFormatter(value?.toString()).value;
                        }}
                        onChange={(value) => {
                          setFieldValue("quantity", value?.toString() || "");
                        }}
                        parser={(value) => {
                          const inputValue = value
                            ? unformatted(value.toString())
                            : "";
                          return inputValue;
                        }}
                        onKeyDown={(event) => {
                          onKeyDownCurrency(event, {
                            integerDigits: 8,
                            decimalDigits: 2,
                            unformatted,
                            allowNegative: true,
                            decimalSeparator:
                              inputFormatter().decimal_separator,
                          });
                          if (
                            event.key === "Tab" &&
                            !event.shiftKey &&
                            !event.altKey &&
                            !event.ctrlKey &&
                            !event.metaKey
                          ) {
                            event.preventDefault();
                            setShowUnitInputs(true);

                            setTimeout(() => {
                              const unitCostInput =
                                document.getElementById("unit_cost");
                              if (unitCostInput) {
                                unitCostInput.focus();
                              }
                            }, 0);
                          }
                        }}
                      />
                    </div>
                  </li>
                  <li>
                    <ul className="py-0.5 relative">
                      <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                        <FontAwesomeIcon
                          className="w-3 h-3 text-primary-900 dark:text-white"
                          icon="fa-regular fa-xmark"
                        />
                      </li>
                    </ul>
                  </li>
                  <li className="flex justify-between items-center">
                    <Typography className="text-13 block text-primary-900 dark:text-white/90">
                      {_t("Unit Cost/Unit")}
                    </Typography>
                    <div
                      className="sm:w-[260px] w-28 h-[22px]"
                      ref={costUnitRef}
                    >
                      <div
                        ref={unitCostContainerRef}
                        className="text-right !text-[#008000] leading-[22px] font-semibold text-sm"
                      >
                        {!isViewOnly &&
                          details?.response !== "accept" &&
                          details?.response !== "closed" && (
                            <>
                              {showUnitInputs ? (
                                <div className="flex gap-2">
                                  <div className="w-[calc(100%-52px)]">
                                    <InputNumberField
                                      name="unit_cost"
                                      id="unit_cost"
                                      rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input placeholder:text-13 !w-[112px]"
                                      placeholder={_t("Item Unit Cost")}
                                      disabled={isViewOnly}
                                      labelPlacement="left"
                                      errorMessage={errors.unit_cost}
                                      onPaste={handlePaste}
                                      autoFocus={Boolean(
                                        values?.unit_cost &&
                                          !isEmpty(values?.unit_cost) &&
                                          values.unit &&
                                          !isEmpty(values.unit)
                                      )}
                                      defaultValue={
                                        Number(values.unit_cost) !== 0
                                          ? values.unit_cost
                                          : ""
                                      }
                                      value={
                                        Number(values.unit_cost) != 0
                                          ? values.unit_cost
                                          : ""
                                      }
                                      onChange={(value) => {
                                        setFieldValue(
                                          "unit_cost",
                                          value?.toString() || ""
                                        );
                                      }}
                                      formatter={(value, info) => {
                                        return inputFormatter(value?.toString())
                                          .value;
                                      }}
                                      parser={(value) => {
                                        const inputValue = value
                                          ? unformatted(value.toString())
                                          : "";
                                        return inputValue;
                                      }}
                                      onKeyDown={(event) =>
                                        onKeyDownCurrency(event, {
                                          integerDigits: 10,
                                          decimalDigits: 2,
                                          unformatted,
                                          allowNegative: false,
                                          decimalSeparator:
                                            inputFormatter().decimal_separator,
                                        })
                                      }
                                      onBlur={handleFocusOut}
                                    />
                                  </div>
                                  <div className="w-[62px]">
                                    {window.ENV.ENABLE_UNIT_DROPDOWN ? (
                                      <SelectField
                                        className="!p-0 text-success select-field-text-13 text-left select-unit-filed !h-[22px] !font-semibold placeholder:font-normal"
                                        placeholder="Unit"
                                        name="unit"
                                        disabled={
                                          details?.response == "accept" ||
                                          details?.response == "closed" ||
                                          isViewOnly
                                        }
                                        labelPlacement="left"
                                        maxLength={15}
                                        value={values?.unit || null}
                                        iconView={true}
                                        popupClassName="!w-[260px]"
                                        showSearch
                                        options={
                                          unitData.map((type) => ({
                                            label: type.name.toString(),
                                            value: type.name.toString(),
                                          })) ?? []
                                        }
                                        allowClear
                                        filterOption={(input, option) =>
                                          filterOptionBySubstring(
                                            input,
                                            option?.label as string
                                          )
                                        }
                                        onChange={(value) => {
                                          setFieldValue(
                                            "unit",
                                            value ? value?.toString() : ""
                                          );
                                        }}
                                        addItem={{
                                          text: "Add Unit: Type Unit & Press Enter",
                                          icon: "fa-regular fa-plus",
                                        }}
                                        onInputKeyDown={(e) => {
                                          if (e.key === "Enter") {
                                            const value =
                                              e?.currentTarget?.value?.trim();
                                            const newType =
                                              onEnterSelectSearchValue(
                                                e,
                                                unitData?.map((unit) => ({
                                                  label: unit?.name,
                                                  value: "",
                                                })) || []
                                              );
                                            if (newType) {
                                              setNewTypeName(newType);
                                            } else if (value) {
                                              notification.error({
                                                description:
                                                  "Records already exist, no new records were added.",
                                              });
                                            }
                                          }
                                        }}
                                        onClear={() => {
                                          setFieldValue("unit", "");
                                        }}
                                        errorMessage={errors.unit}
                                        onBlur={handleFocusOut}
                                      />
                                    ) : (
                                      <InputField
                                        className={`!p-0 !pl-1.5 text-sucess field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right ${
                                          !showUnitInputs && "!hidden"
                                        }`}
                                        placeholder={_t("Unit")}
                                        labelPlacement="left"
                                        maxLength={15}
                                        onBlur={handleFocusOut}
                                        value={values?.unit}
                                        disabled={
                                          details?.response == "accept" ||
                                          details?.response == "closed" ||
                                          isViewOnly
                                        }
                                        onPaste={handlePaste}
                                        type="text"
                                        onChange={(e) => {
                                          setFieldValue("unit", e.target.value);
                                        }}
                                        onPressEnter={handleEnterKeyPress}
                                      />
                                    )}
                                  </div>
                                </div>
                              ) : (
                                <Typography
                                  className="text-[#008000] cursor-pointer text-13 font-medium"
                                  onClick={handleParagraphClick}
                                  disabled={
                                    details?.response == "accept" ||
                                    details?.response == "closed" ||
                                    isViewOnly
                                  }
                                >
                                  {
                                    formatter(
                                      formatAmount(
                                        Number(values?.unit_cost).toFixed(2)
                                      )
                                    ).value_with_symbol
                                  }
                                  /{values?.unit}
                                </Typography>
                              )}
                            </>
                          )}

                        {(details?.response == "accept" ||
                          details?.response == "closed" ||
                          isViewOnly) &&
                          (values?.unit_cost &&
                          values?.unit_cost != "0.00" &&
                          values?.unit_cost != "0" &&
                          !isEmpty(values?.unit) &&
                          !!values?.unit ? (
                            <Typography
                              className={`text-[#008000] font-medium text-13 ${
                                isViewOnly ? "cursor-no-drop" : ""
                              }`}
                            >
                              {values?.unit_cost}/{values?.unit}
                            </Typography>
                          ) : (
                            <div className="flex gap-2">
                              <div className="w-[calc(100%-52px)]">
                                <InputField
                                  ref={unitCostRef}
                                  className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                  placeholder={_t("Item Unit Cost")}
                                  type="number"
                                  name="unit_cost"
                                  id="unit_cost"
                                  maxLength={10}
                                  onPaste={handlePaste}
                                  disabled={
                                    isViewOnly ||
                                    details?.response == "accept" ||
                                    details?.response == "closed"
                                  }
                                  value={
                                    Number(values.unit_cost) != 0
                                      ? values.unit_cost
                                      : ""
                                  }
                                  onChange={() => {}}
                                />
                              </div>
                              <div className="w-11">
                                <InputField
                                  className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                  placeholder={_t("Unit")}
                                  maxLength={15}
                                  name="unit"
                                  id="unit"
                                  onPaste={handlePaste}
                                  disabled={
                                    details?.response == "accept" ||
                                    details?.response == "closed" ||
                                    isViewOnly
                                  }
                                  value={values?.unit}
                                  type="text"
                                  onChange={() => {}}
                                />
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  </li>
                  <li className="flex justify-between border-t border-dashed border-gray-300 pt-2 mt-2">
                    <Typography className="text-13 block text-primary-900 font-semibold">
                      {_t("Total Cost")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-red-600 text-13 font-semibold"
                        disabled={true}
                      >
                        {values?.total === ""
                          ? `${
                              formatter(formatAmount("0.00")).value_with_symbol
                            }`
                          : `${
                              formatter(
                                formatAmount(
                                  Number(values?.total || 0).toFixed(2)
                                )
                              ).value_with_symbol
                            }`}
                      </Typography>
                    </div>
                  </li>
                </ul>
              </SidebarCardBorder>
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Description")}
                    name="description"
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and can be optionally made visible to the recipient when submitted."
                    )}
                    value={HTMLEntities.decode(
                      sanitizeString(values?.description)
                    )}
                    disabled={
                      details?.response == "accept" ||
                      details?.response == "closed" ||
                      isViewOnly
                    }
                    onChange={handleChange}
                  />
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Internal Notes")}
                    name="internal_notes"
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and are not visible to the recipient."
                    )}
                    value={HTMLEntities.decode(
                      sanitizeString(values?.internal_notes)
                    )}
                    disabled={
                      details?.response == "accept" ||
                      details?.response == "closed" ||
                      isViewOnly
                    }
                    onChange={handleChange}
                  />
                </div>

                {!!(!selectedRec.reference_item_id && values.item_type) && (
                  <CheckBox
                    className="gap-1.5 text-primary-900 w-fit"
                    checked={
                      !!values.add_item_to_database ||
                      isValidId(values?.reference_item_id)
                    }
                    onChange={(event) => {
                      const valueToSet: number = event.target.checked ? 1 : 0;
                      setFieldValue("add_item_to_database", valueToSet);
                    }}
                    disabled={
                      details?.response === "accept" ||
                      details?.response === "closed"
                        ? true
                        : !selectedRec?.item_id || selectedRec?.item_id == "0"
                        ? false
                        : type === "estimateItem" ||
                          isViewOnly ||
                          (!!selectedRec?.item_id &&
                            !!selectedRec?.reference_item_id) ||
                          (!!selectedRec?.item_id &&
                            !!selectedRec.add_item_to_database) ||
                          !(type === "estimateItem" || type === "originalScope")
                    }
                  >
                    {`${_t("Save this item into my")} ${itemTypeName} ${_t(
                      "Items list?"
                    )}`}
                  </CheckBox>
                )}
              </SidebarCardBorder>
            </div>
          </div>
          <div className="sidebar-footer flex items-center gap-4 justify-center w-full px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              onClick={() => setFieldValue("submitAction", "saveAndClose")}
              isLoading={isSubmitting && submittingFrm == "save"}
              disabled={
                (isSubmitting && submittingFrm == "save") ||
                details?.response === "accept" ||
                details?.response === "closed" ||
                isViewOnly
              }
              buttonText={_t("Save & Close")}
            />
            <PrimaryButton
              htmlType="submit"
              onClick={() => setFieldValue("submitAction", "saveAndAddAnother")}
              isLoading={isSubmitting && submittingFrm == "saveAndAddAnother"}
              disabled={
                (isSubmitting && submittingFrm == "saveAndAddAnother") ||
                details?.response === "accept" ||
                details?.response === "closed" ||
                isViewOnly
              }
              buttonText={_t("Save & Add Another Item")}
            />
          </div>
        </form>
      </Drawer>

      {isOpenSelectCustomer && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectCustomer}
          closeDrawer={() => {
            setIsOpenSelectCustomer(false);
          }}
          singleSelecte={true}
          options={[
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.customer_key,
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            defaultConfig.misc_contact_key,
            "by_service",
            "my_project",
          ]}
          projectId={details?.project_id}
          setCustomer={(data) => {
            handleSelectCustomer(
              data.length ? (data[0] as Partial<IDirectoryData>) : {}
            );
          }}
          selectedCustomer={
            selectedAssigned?.user_id &&
            selectedAssigned?.user_id != 0 &&
            !!selectedAssigned?.display_name?.trim()
              ? ([
                  {
                    display_name: selectedAssigned?.display_name,
                    type_name: selectedAssigned?.type_name,
                    user_id: selectedAssigned?.user_id,
                    contact_id:
                      !!selectedAssigned?.contact_id &&
                      selectedAssigned.contact_id != "0"
                        ? selectedAssigned?.contact_id
                        : undefined,
                    image: selectedAssigned?.image,
                  },
                ] as unknown as TselectedContactSendMail[])
              : []
          }
          groupCheckBox={true}
          activeTab={CFConfig.contractor_key}
        />
      )}

      {isOpenContactDetails && (
        <ContactDetails
          isOpenContact={isOpenContactDetails}
          contactId={values?.assigned_to || ""}
          onCloseModal={() => {
            setIsOpenContactDetails(false);
          }}
          onEmailClick={(data) => {
            setSelectedData(data);
            setIsSendEmailSidebarOpen(true);
          }}
          readOnly={isViewOnly}
          additional_contact_id={values?.assigned_to_contact_id}
        />
      )}
      {newTypeName.trim() && (
        <ConfirmModal
          isOpen={Boolean(newTypeName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newTypeName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => setNewTypeName("")}
          onAccept={async () => {
            if (!isAddingCustomData && newTypeName) {
              setIsAddingCustomData(true);
              const response: IUnitAddResponse = await addUnit({
                name: newTypeName,
              });
              if (response.success && response.data) {
                setUnitData([{ name: newTypeName }, ...unitData]);
                setFieldValue("unit", newTypeName);
                setNewTypeName("");
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => setNewTypeName("")}
        />
      )}

      <SubContractsSendEmail
        isOpen={isSendEmailSidebarOpen}
        options={[
          defaultConfig.employee_key,
          "my_crew",
          defaultConfig.customer_key,
          defaultConfig.lead_key,
          defaultConfig.contractor_key,
          defaultConfig.vendor_key,
          defaultConfig.misc_contact_key,
          "by_service",
          "my_project",
        ]}
        onSendResponse={() => {
          setSelectedData({});
        }}
        onClose={() => {
          setSelectedData({});
          setIsSendEmailSidebarOpen(false);
        }}
        groupCheckBox={true}
        projectId={Number(details?.project_id)}
        selectedCustomer={
          selectedData?.user_id
            ? ([selectedData] as TselectedContactSendMail[])
            : []
        }
        app_access={false}
      />

      {isValidId(cidbItemExistPopupData?.data?.reference_item_id) && (
        <ConfirmModal
          isOpen={isValidId(cidbItemExistPopupData?.data?.reference_item_id)}
          modaltitle={_t("This item already exists")}
          description={
            cidbItemExistPopupData?.message ??
            `There is already an item associated with this name in your CIDB. Would you like to rename the current item or import the existing item from your CIDB?`
          }
          onAccept={() => {
            setFieldValue(
              "reference_item_id",
              cidbItemExistPopupData?.data?.reference_item_id
            );
            setFieldValue("add_item_to_database", 0);
            setCidbItemExistPopupData(null);
            handleSubmit();
          }}
          yesButtonLabel="Use Existing"
          noButtonLabel="Rename"
          onDecline={() => setCidbItemExistPopupData(null)}
          onCloseModal={() => setCidbItemExistPopupData(null)}
          descriptionclassName="flex text-center justify-center"
          modalIcon="fa-regular fa-trash-can"
        />
      )}
    </>
  );
};

export default AddSubContractItem;
