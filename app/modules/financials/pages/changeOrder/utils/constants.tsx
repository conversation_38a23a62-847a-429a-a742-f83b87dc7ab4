// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

export const SELECT_TAB_OPTIONS = [
  {
    name: "Details",
    value: "details",
    icon: (
      <FontAwesomeIcon
        className="w-5 h-5"
        icon="fa-regular fa-rectangle-list"
      />
    ),
    addButton: true,
  },
  {
    name: "Files",
    value: "files",
    icon: (
      <FontAwesomeIcon className="w-5 h-5" icon="fa-regular fa-file-image" />
    ),
    addButton: true,
  },
  {
    name: "Notes",
    value: "notes",
    icon: <FontAwesomeIcon className="w-5 h-5" icon="fa-regular fa-memo" />,
    addButton: true,
  },
];

export const CHANGE_ORDER_ICON_MAP = {
  Material: "fa-regular fa-block-brick",
  Labor: "fa-regular fa-user-helmet-safety",
  Subcontractor: "fa-regular fa-file-signature",
  Equipment: "fa-regular fa-screwdriver-wrench",
  "Other Items": "fa-regular fa-boxes-stacked",
  default: "fa-regular fa-boxes-stacked",
};

export const STATUS_LIST = [
  {
    icon: "fa-light fa-circle-pause",
    bgcolor: "bg-[#C7A27C]",
    label: "On Hold",
    default_color: "#C7A27C",
  },
  {
    icon: "fa-light fa-lock-keyhole-open",
    bgcolor: "bg-[#44539A]",
    label: "Open",
    default_color: "#44539A",
  },
  {
    icon: "fa-light fa-hourglass-clock",
    bgcolor: "",
    label: "Pending Approval",
    default_color: "#C7A27C",
  },
  {
    icon: "fa-light fa-memo-circle-info",
    bgcolor: "",
    label: "Unbilled/Approved",
    default_color: "#C7A27C",
  },
  {
    icon: "fa-light fa-file-invoice-dollar",
    bgcolor: "",
    label: "Billed",
    default_color: "#44539A",
  },
];

export const STATUS_MAP = {
  change_order_onhold_status: {
    icon: "fa-light fa-circle-pause",
  },
  change_order_pending_approval: {
    icon: "fa-light fa-hourglass-clock",
  },
  change_order_open_status: {
    icon: "fa-light fa-lock-keyhole-open",
  },
  change_order_unbilled_status: {
    icon: "fa-light fa-memo-circle-info",
  },
  change_order_billed_status: {
    icon: "fa-light fa-file-invoice-dollar",
  },
  change_order_denied_status: {
    icon: "fa-light fa-file-invoice-dollar", // need to change
  },
  cor_draft: {
    icon: "fa-light fa-file-pen",
  },
  cor_on_hold: {
    icon: "fa-light fa-circle-pause",
  },
  cor_denied: {
    icon: "fa-light fa-circle-xmark",
  },
  cor_approved: {
    icon: "fa-light fa-clipboard-check",
  },
  cor_closed: {
    icon: "fa-light fa-circle-xmark",
  },
};

export const CHANGEORDER_STATUSOPTIONS = [
  {
    label: "Complete",
    value: "complete",
  },
  {
    label: "In Progress",
    value: "in-Progress",
  },
  {
    label: "Deferred",
    value: "deferred",
  },
];

export const CO_RIGHT_BUTTON = [
  {
    label: "CO’s",
    value: "co",
  },
  {
    label: "COR’s",
    value: "cor",
  },
  {
    label: "All",
    value: "",
  },
];

export const CHANGE_ORDER_ADD_LIST = [
  {
    label: "Change Order",
    key: "change_order",
  },
  {
    label: "Change Order Request",
    key: "change_order_request",
  },
];

export const SHOW_ITEMS_BUTTON_TAB = [
  {
    label: "Yes",
    value: true,
    key: 0,
  },
  {
    label: "No",
    value: false,
    key: 1,
  },
];

export const COCommonListOptions = [
  {
    label: "Add Item to Change Order",
    value: "title",
    disabled: true,
    type: ["co", "cor"],
  },
  {
    label: `Import Items from Estimate-srl`,
    value: "estimate",
    type: ["co", "cor"],
  },
  {
    label: `Import Change Order (S) Template`,
    value: "change_order_template",
    type: ["co"],
  },
  {
    label: `Import Change Order (S) Request`,
    value: "change_order_requests",
    type: ["co", "cor"],
  },
  {
    label: "Add New Section",
    value: "add_new_section",
    type: ["co", "cor"],
  },
];

export const COSectionOptions = [
  {
    label: "Add Item to Change Order",
    value: "title",
    disabled: true,
  },
  {
    label: `Import from Cost Items Database`,
    value: "cost_item_database",
  },
  {
    label: `Add Manual Change Order Item`,
    value: "add_manual_item",
  },
  {
    label: `Apply Automatic/Bulk Markup`,
    value: "automatic_bulk_markup",
  },
  {
    label: `Add Discount`,
    value: "add_discount",
  },
  {
    label: "Copy Section",
    value: "copy_section",
  },
  {
    label: "Delete this section",
    value: "delete_section",
  },
];
