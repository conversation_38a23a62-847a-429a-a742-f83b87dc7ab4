// This is the demo
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  fetchChangeOrderItems,
  getCOItems,
} from "../action/changeOrderItemsActions";
import { RootState } from "../store";
import { itemTotalCalculator } from "../../utils/helpers";

const groupItemsBySection = (selectedItems: Record<string, any>): any => {
  return Object.values(selectedItems).reduce((grouped, item) => {
    if (!grouped[item?.section_id]) {
      grouped[item?.section_id] = {};
    }
    grouped[item?.section_id] = {
      ...grouped[item?.section_id],
      [item?.item_id]: item,
    };
    return grouped;
  }, {} as any);
};

const initialState: IChangeOrderItemsState = {
  items: [],
  sections: [],
  total_amount: {
    co_taxable_total: 0,
    current_co_items_total: 0,
    total_tax_rate: 0,
  },
  estimateItems: [],
  contractAmount: {
    current_revised_contract_amount: 0,
    original_contract_amount: 0,
    previous_revised_contract_amount: 0,
    revised_contract_amount: 0,
  },
  taxes: [],
  isDataFetched: true,
  isDashLoading: true,
  selectedItems: {},
  isMultipleSelected: false,
};

export const changeOrderItemsSlice = createSlice({
  name: "changeOrderItems",
  initialState,
  reducers: {
    resetCOItems: (state) => {
      state.isDataFetched = false;
      state.items = [];
    },
    selectItem: (
      state,
      {
        payload: { selected, data },
      }: { payload: { selected: boolean; data: Partial<ESEstimateItem> } }
    ) => {
      if (!data?.item_id || !data?.section_id) {
        return;
      }
      const prevSlectedItems = { ...state.selectedItems };
      if (selected) {
        prevSlectedItems[data?.item_id] = {
          selected: true,
          section_id: data?.section_id,
          item_id: data?.item_id,
        }; // Add item to selected
        if (!state.isMultipleSelected) {
          state.isMultipleSelected = true;
        }
      } else {
        delete prevSlectedItems?.[data?.item_id]; // Remove item from selected
        if (!Object.keys(prevSlectedItems)?.length) {
          state.isMultipleSelected = false;
        }
      }
      state.selectedItems = { ...prevSlectedItems };
    },
    cancelAll: (state) => {
      state.selectedItems = {};
      state.isMultipleSelected = false;
    },
    unselectAll: (state, { payload }: { payload: { sectionId?: number } }) => {
      // if no sectionId, un select everything
      if (!payload?.sectionId) {
        state.selectedItems = {};
        state.isMultipleSelected = false;
        return;
      }

      const selectedItems = Object.keys(state.selectedItems || {}).reduce<{
        [key: string]: SelectedItem;
      }>((acc, key) => {
        if (state?.selectedItems?.[key]?.section_id != payload?.sectionId) {
          acc[key] = state.selectedItems[key];
        }
        return acc;
      }, {});

      state.selectedItems = selectedItems || {};
      state.isMultipleSelected = false;
    },
    selectAll: (state, { payload }: { payload: { sectionId: number } }) => {
      const section = state.sections.find(
        (sec) => sec.section_id == payload?.sectionId
      );

      const selectedItems = section?.items?.reduce<{
        [key: string]: SelectedItem;
      }>((acc, item) => {
        acc[item.item_id] = {
          selected: true,
          section_id: section.section_id,
          item_id: item.item_id,
        };
        return acc;
      }, {});

      state.selectedItems = {
        ...state.selectedItems,
        ...(selectedItems || {}),
      };
      state.isMultipleSelected = true;
    },
    deleteSelected: (state) => {
      if (state?.isMultipleSelected) {
        const selectedItemsToDeleteFromItems = groupItemsBySection(
          state.selectedItems
        );
        state.sections.forEach((section) => {
          const itemsToRemove =
            selectedItemsToDeleteFromItems[section.section_id];
          if (itemsToRemove) {
            section.items = section.items.filter(
              (item) => !itemsToRemove?.[item.item_id]
            );
          }
        });
        state.selectedItems = {};
        state.isMultipleSelected = false;
      }
    },
    removeChangeOrderItem: (state, { payload }) => {
      state.items = state.items.filter(
        (item) => item.item_id !== payload.item_id
      );
    },
    addCOItems: (state, { payload }) => {
      state.items = [...state.items, ...payload];
    },
    reloadItems: (state, { payload }) => {
      state.items = payload;
    },
    updateCOItems: (state, { payload }) => {
      state.items = state.items.map((item) => {
        if (item.item_id === payload.item_id) {
          return { ...item, ...payload };
        }
        return item;
      });
    },
    updateCOTaxes: (state, { payload }) => {
      state.taxes = [...payload];
    },
    setSection: (state, { payload }) => {
      state.sections = payload;
    },
    addItemToSection: (state, { payload }) => {
      const sectionIds = new Set(
        payload.map((ele: { section_id: number }) => ele.section_id)
      );

      state.sections = state.sections.map((item) => {
        if (sectionIds.has(item.section_id)) {
          return {
            ...item,
            items: [...item.items, ...payload],
          };
        }
        return item;
      });
    },
    updateItemToSection: (
      state,
      { payload: { sectionId, itemId, updatedItem } }
    ) => {
      const section = state.sections.find(
        (sec) => sec.section_id === sectionId
      );

      if (section) {
        const item = section.items.find((item) => item.item_id === itemId);
        if (item) {
          section.is_optional_section = 0;
          Object.assign(item, updatedItem);
        }
      }
    },
    removeItemsFromSection: (state, { payload }) => {
      state.sections = state.sections.map((section) => {
        if (section.section_id === payload.sectionId) {
          return {
            ...section,
            items: section.items.filter(
              (item) => item.item_id !== payload.item_id
            ),
          };
        }
        return section;
      });
    },
    moveSectionState: (
      state,
      action: PayloadAction<{ fromIndex: number; toIndex: number }>
    ) => {
      const { fromIndex, toIndex } = action.payload;
      if (
        fromIndex < 0 ||
        toIndex < 0 ||
        fromIndex >= state.sections?.length ||
        toIndex >= state.sections?.length ||
        fromIndex === toIndex
      ) {
        return;
      }

      // Make a copy of the sections
      let updatedSections = [...state.sections];

      // Remove the item from the fromIndex
      const [movedSection] = updatedSections.splice(fromIndex, 1);

      // Insert the item at the toIndex
      updatedSections.splice(toIndex, 0, movedSection);

      // Update the state immutably
      return {
        ...state,
        sections: updatedSections,
      };
    },
    removeSection: (state, { payload: sectionId }) => {
      // Remove a section by filtering out based on section_id
      const sectionItems = state.sections.find(
        (el) => el?.section_id == sectionId
      )?.items;
      // sectionItems?.forEach((item) => {
      //   // const diff = item?.is_optional_item
      //   //   ? 0
      //   //   : itemTotalCalculator({ ...item }, true);
      //     changeOrderItemsSlice.caseReducers.updateFinancialSummary(state, {
      //     payload: {
      //       item_type_name: item?.item_type_name, // pass the appropriate type
      //       diff: (diff * -1) / 100,
      //     },
      //   });
      // });
      state.sections = state.sections.filter(
        (section) => section.section_id !== sectionId
      );
    },
    addSection: (state, { payload: { sectionId, sectionData = {} } }) => {
      if (
        sectionId &&
        state.sections.findIndex((sec) => sec.section_id === sectionId) === -1
      ) {
        const sections = state.sections;
        if (!sectionData?.items) {
          sectionData.items = [];
        }
        sections.push({
          ...sectionData,
          section_id: sectionId,
        });
        state.sections = sections;
      }
    },
    updateSection: (
      state,
      { payload: { sectionId, updatedSection, makeSectionOptional = false } }
    ) => {
      const section = state.sections.find(
        (sec) => sec.section_id === sectionId
      );
      if (section) {
        Object.assign(section, updatedSection);
        if (makeSectionOptional) {
          section.items = section.items?.map((el) => {
            const prevTotal = el?.is_optional_item
              ? 0
              : itemTotalCalculator({ ...el }, true);
            // Calculate new total
            const newTotal = 0;
            // Calculate the difference in totals
            const diff = (newTotal - prevTotal) / 100;
            // itemsSlice.caseReducers.updateFinancialSummary(state, {
            //   payload: {
            //     item_type_name: el?.item_type_name, // pass the appropriate type
            //     diff,
            //     prev_item_type_name: el?.item_type_name,
            //   },
            // });
            return {
              ...el,
              is_optional_item: 1,
            };
          });
        } else {
          section.items = section.items?.map((el) => {
            const prevTotal = el?.is_optional_item
              ? 0
              : itemTotalCalculator({ ...el }, true);
            // Calculate new total
            const newTotal = itemTotalCalculator({ ...el }, true);
            // Calculate the difference in totals
            const diff = (newTotal - prevTotal) / 100;
            // itemsSlice.caseReducers.updateFinancialSummary(state, {
            //   payload: {
            //     item_type_name: el?.item_type_name, // pass the appropriate type
            //     diff,
            //     prev_item_type_name: el?.item_type_name,
            //   },
            // });
            return {
              ...el,
              is_optional_item: 0,
            };
          });
        }
      }
    },
    removeSelectedItems: (state) => {
      state.selectedItems = {};
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchChangeOrderItems.pending, (state) => {
      state.isDashLoading = true;
    });
    builder.addCase(fetchChangeOrderItems.fulfilled, (state, { payload }) => {
      const { success, data } = payload as IChangeOrderItemsApiRes;
      if (data && success) {
        state.items = data.items;
        state.total_amount = data.total_amount;
        state.taxes = data.taxes;
      } else {
        state.items = [];
      }
      state.isDataFetched = true;
      state.isDashLoading = false;
    });
    builder.addCase(fetchChangeOrderItems.rejected, (state) => {
      state.isDashLoading = false;
      state.isDataFetched = false;
    });

    builder.addCase(getCOItems.pending, (state) => {
      state.isDashLoading = true;
    });
    builder.addCase(getCOItems.fulfilled, (state, { payload }) => {
      const { success, data } = payload as IChangeOrdersSectionApiRes;
      if (data && success) {
        state.sections = Array.isArray(data) ? data : [];
        // state.total_amount = data.total_amount;
        // state.taxes = data.taxes;
      } else {
        state.sections = [];
      }
      state.isDataFetched = true;
      state.isDashLoading = false;
    });
    builder.addCase(getCOItems.rejected, (state) => {
      state.isDashLoading = false;
      state.isDataFetched = false;
    });
  },
});

export const {
  selectItem,
  unselectAll,
  cancelAll,
  selectAll,
  deleteSelected,
  resetCOItems,
  removeChangeOrderItem,
  reloadItems,
  addCOItems,
  updateCOItems,
  updateCOTaxes,
  setSection,
  moveSectionState,
  removeSection,
  addSection,
  updateSection,
  addItemToSection,
  removeItemsFromSection,
  updateItemToSection,
  removeSelectedItems,
} = changeOrderItemsSlice.actions;
export default changeOrderItemsSlice.reducer;

export const getChangeOrderItemsData = (): ((
  dispatch: unknown,
  getState: () => RootState
) => IChangeOrderItemsState) => {
  return (_, getState) => {
    const state = getState();
    return state.changeOrderItems;
  };
};
