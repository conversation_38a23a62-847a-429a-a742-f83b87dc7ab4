// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Head<PERSON> } from "~/shared/components/atoms/header";
import { Switch } from "~/shared/components/atoms/switch";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// Organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
import OnlyRequiredCustomFields from "~/shared/components/organisms/OnlyRequiredCustomFields/OnlyRequiredCustomFields";
// Hook
import { useTranslation } from "~/hook";
import { useNavigate, useSearchParams } from "@remix-run/react";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
  type ForwardedRef,
} from "react";
import isEmpty from "lodash/isEmpty";
import { defaultModuleFilter, getGConfig, getGSettings } from "~/zustand";
import { apiRoutes } from "~/route-services/routes";
import { getApiData } from "~/helpers/axios-api-helper";

import { useFormik } from "formik";
import { formAddChangeOrderSchema, changeOrderItems } from "./utils";
import { getCustomFieldAccess } from "~/shared/utils/helper/getCustomFieldAccess";
import {
  backendDateFormat,
  displayDateFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import { useDirectoryKeyValue } from "~/shared/hooks/useCustomField/useDirectoryKeyValue";
import { useSideBarCustomField } from "~/shared/hooks/useCustomField/useSidebarCustomField";

import * as Yup from "yup";

import { setModuleAutoIncrementId } from "~/zustand/module-auto-increment-primary-id/actions";
import { getModuleAutoIncrementPrimaryId } from "~/zustand/module-auto-increment-primary-id/store";
import { getCOModuleName } from "../../utils/helpers";
import { getProjectDetails } from "~/redux/action/getProjectDetailsAction";
import dayjs from "dayjs";
import { formatCustomFieldForRequest } from "~/shared/utils/helper/customFieldSidebarFormat";
import { getGlobalProject } from "~/zustand/global/config/slice";
import {
  EVENT_LOGGER_ACTION,
  EVENT_LOGGER_NAME,
} from "~/shared/constants/event-logger";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { sanitizeString } from "~/helpers/helper";
import { filterOptionBySubstring } from "~/shared/utils/helper/common";
import { IChangeOrderListApiRes } from "../../redux/types";

//? NF Code Commented : Confirm with Dhrumil Adeshara
export interface IAddChangeOrderRef {
  toggle: (type: string) => void;
}
const AddChangeOrder = forwardRef(
  (
    { drawerOpen, setDrawerOpen, type }: IAddChangeOrderProps,
    ref: ForwardedRef<IAddChangeOrderRef>
  ) => {
    const { _t } = useTranslation();
    const navigate = useNavigate();

    const [searchParams, setSearchParams] = useSearchParams();
    const { project_id } = getGlobalProject();

    const [isSelectProOpen, setIsSelectProOpen] = useState<boolean>(false);
    const [isButtonDisabled, setButtonDisabled] = useState<boolean>(false);
    const [contactDetailDialogOpen, setContactDetailDialogOpen] =
      useState<boolean>(false);
    const [selectedProject, setSelectedProject] = useState<
      IProject | undefined | null
    >();
    const [isSubmit, setIsSubmit] = useState<boolean>(false);
    const [changeOrderRequests, setChangeOrderRequests] = useState<
      IChangeOrder[]
    >([]);
    const [selectedCO, setSelectedCO] = useState<IChangeOrder | null>(null);
    const [page, setPage] = useState(0);
    const [loading, setLoading] = useState<boolean>(false);
    const [isSuccess, setIsSuccess] = useState<boolean>(false);
    const [selectedCoItems, setSelectedCoItems] = useState([]);
    const defaultProject = useRef<IProject | null>(null);
    const templateLimit = 100;
    // const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    //   useState<boolean>(false);
    const [projectLoading, setProjectLoading] = useState<boolean>(false);

    const {
      need_to_increment,
      last_primary_id,
    }: Partial<IResponseGetModuleAutoNumber> =
      getModuleAutoIncrementPrimaryId() || {};

    const { validateArray } = formAddChangeOrderSchema("co");
    const {
      date_format,
      is_custom_change_orders_id,
      custom_change_orders_id,
      prefix_project_to_change_order,
    }: GSettings = getGSettings();
    const gConfig: GConfig = getGConfig();
    const { module_id, module_key, module_singular_name } = gConfig;
    const showPrefix = String(prefix_project_to_change_order) === "1";
    const { isNoAccessCustomField }: ICustomFieldAccess =
      getCustomFieldAccess();
    const filter = defaultModuleFilter.change_order_module;

    const { directoryKeyValue, directory }: IDirectoryFormCustomField =
      useDirectoryKeyValue();

    const { componentList, loadingCustomField, setLoadingCustomFields } =
      useSideBarCustomField(
        { directory, directoryKeyValue } as IDirectoryFormCustomField,
        {
          moduleId: gConfig?.module_id,
        } as IRequestCustomFieldForSidebar
      );

    const [copyProjectDetails, setCopyProjectDetails] = useState(false);
    const [selectedItems, setSelectedItems] = useState([
      "project_name",
      "customer",
    ]);
    const [selectAll, setSelectAll] = useState(false);

    const handleCopyProjectToggle = (data) => {
      const newState = !copyProjectDetails;
      setCopyProjectDetails(newState);

      if (newState) {
        setSelectedItems((prev) => [
          ...new Set([...prev, "project_name", "customer"]),
        ]);
      }
    };

    const handleItemSelection = (value: string) => {
      setSelectedItems(
        (prev) =>
          prev.includes(value)
            ? prev.filter((item) => item !== value) // Deselect
            : [...prev, value] // Select
      );

      const updatedSelection = selectedItems.includes(value)
        ? selectedItems.filter((item) => item !== value)
        : [...selectedItems, value];

      setSelectAll(updatedSelection.length === changeOrderItems.length);
    };
    const handleSelectAllToggle = (checked) => {
      setSelectAll(checked);
      if (checked) {
        // Select all checkbox items
        let test = [];
        if (
          selectedCO !== null &&
          selectedProject &&
          selectedProject?.id !== selectedCO?.project_id
        ) {
          test = changeOrderItems
            .filter(
              (item) =>
                selectedCO !== null &&
                selectedProject &&
                selectedProject?.id !== selectedCO?.project_id &&
                ![
                  "import_original_estimate",
                  "import_requested_by",
                  "import_approved_by",
                  "import_invoiced_to",
                ].includes(item.value)
            )
            .map((ele) => {
              return ele.value;
            });
        } else {
          test = changeOrderItems?.map((ele) => {
            return ele.value;
          });
        }
        setSelectedCoItems(test);
      } else {
        // Deselect all
        setSelectedCoItems([]);
      }
    };

    // Function to handle individual checkbox changes
    const handleCheckboxChange = (value: string, isChecked: boolean) => {
      let updatedItems;
      if (isChecked) {
        updatedItems = [...selectedCoItems, value];
      } else {
        updatedItems = selectedCoItems.filter((v) => v !== value);
      }

      setSelectedCoItems(updatedItems);

      // Update selectAll state
      const enabledCount = processedOptions.filter(
        (item) => !item.disabled
      ).length;
      const selectedEnabledCount = updatedItems.filter((val) =>
        processedOptions.some((opt) => opt.value === val && !opt.disabled)
      ).length;

      setSelectAll(selectedEnabledCount === enabledCount);
    };

    const isCustomChangeOrderIdRequired = useMemo(() => {
      if (type === "cor") return true;
      return is_custom_change_orders_id === 0 ? false : true;
    }, [is_custom_change_orders_id, type]);

    const staticValidationSchema = validateArray.reduce((acc, fieldName) => {
      if (fieldName === "custom_change_order_id") {
        acc["custom_change_order_id"] =
          type === "cor"
            ? Yup.string().trim().required("This field is required.")
            : is_custom_change_orders_id === 0
            ? Yup.string()
            : Yup.string().trim().required("This field is required.");
      } else if (fieldName === "subject") {
        acc["subject"] = Yup.string()
          .trim()
          .required("This field is required.");
      } else {
        acc[fieldName] = Yup.string().required("This field is required.");
      }
      return acc;
    }, {} as Record<string, Yup.StringSchema>);

    const dynamicValidationSchema = componentList.reduce((acc, fieldName) => {
      if (fieldName.multiple || fieldName.type === "checkbox-group") {
        acc[fieldName.name] = Yup.array()
          .of(Yup.string().required("This field is required."))
          .min(1, "This field is required.")
          .required("This field is required.");
      } else {
        acc[fieldName.name] = Yup.string().required("This field is required.");
      }
      return acc;
    }, {} as Record<string, Yup.StringSchema | Yup.AnySchema>);

    const validationSchema =
      componentList.length && !isNoAccessCustomField
        ? Yup.object().shape({
            ...staticValidationSchema,
            custom_fields: Yup.object().shape(dynamicValidationSchema),
          })
        : Yup.object().shape({
            ...staticValidationSchema,
          });
    const initialFormValues = componentList.length
      ? {
          custom_fields: componentList.reduce((acc, item) => {
            acc[item.name] = item?.value ?? "";
            return acc;
          }, {} as ICustomFieldInitValue),
        }
      : {};
    const handleValidateAndSubmit = useCallback(async () => {
      const errors = await validateForm();

      if (Object.keys(errors).length) {
        return;
      }

      if (!formik.isValid) {
        return notification.error({
          description: "Please fill all required fields",
        });
      } else {
        submitForm();
      }
    }, []);

    const onAddChangeOrder = useCallback(
      async (values: Partial<IChangeOrderAdd>) => {
        const selectedItemsList = {
          import_customer_co_number: 0,
          import_original_estimate: 0,
          import_invoiced_to: 0,
          import_requested_by: 0,
          import_approved_by: 0,
          import_time_delay: 0,
          import_items: 0,
          import_tax: 0,
          import_files: 0,
          import_notes: 0,
        };
        // Update values based on selection
        const updatedCopyItems = Object.fromEntries(
          Object.keys(selectedItemsList).map((key) => [
            key,
            selectedCoItems.includes(key) ? 1 : 0,
          ])
        );

        const formData = {
          ...values,
          ...(values.order_date !== undefined
            ? {
                order_date: backendDateFormat(
                  values.order_date as string,
                  date_format
                ),
              }
            : {}),

          custom_change_order_id:
            HTMLEntities.encode(
              sanitizeString(values.custom_change_order_id?.toString())
            ).trim() || "",
          custom_fields:
            values.custom_fields && !isNoAccessCustomField
              ? formatCustomFieldForRequest(
                  values.custom_fields,
                  componentList,
                  date_format
                ).custom_fields
              : undefined,
          type,
          access_to_custom_fields: isNoAccessCustomField ? 0 : 1,
          has_template: selectedCO == null ? 0 : 1,
          ...(selectedCO !== null && {
            co_template: {
              template_change_order_id: selectedCO?.change_order_id,
              copy_items: updatedCopyItems,
            },
          }),
        };
        setIsSubmit(true);
        setButtonDisabled(true);
        setIsSuccess(true);
        getApiData({
          url: apiRoutes.CHANGE_ORDERS.add,
          method: "post",
          data: formData,
          success: (response: {
            data: { changeOrderId: string };
            status: boolean;
            message: string;
          }) => {
            EventLogger.log(
              EVENT_LOGGER_NAME.change_orders + EVENT_LOGGER_ACTION.added,
              1
            );
            if (
              !response?.status ||
              isEmpty(response?.data?.changeOrderId?.toString())
            ) {
              setButtonDisabled(false);
            }
            if (!isEmpty(response?.data?.changeOrderId?.toString())) {
              setIsSubmit(false);
              navigate(
                `/manage-change-orders/${response?.data?.changeOrderId}`
              );
            }
          },
          callComplete: (success: boolean) => {
            if (!success) {
              setIsSuccess(false);
            }
            if (!isSubmit) {
              setButtonDisabled(false);
            }
          },
          error: (description) => {
            setButtonDisabled(false);
            setIsSubmit(false);
            setIsSuccess(false);
            notification.error({
              description,
            });
          },
        });
      },
      [type, componentList, date_format, selectedCoItems, selectedCO]
    );

    const action = searchParams.get("action");
    const project = searchParams.get("project");

    const defaultProjectId = useMemo(() => {
      let projectId = Number(project_id);
      if (action === "new" && project && project !== "") {
        projectId = Number(searchParams.get("project"));
      }
      return Number(projectId) === 0 ? undefined : Number(projectId);
    }, [project_id, action, project]);

    const formik = useFormik({
      initialValues: {
        project_id: defaultProjectId,
        subject: "",
        type: type,
        access_to_custom_fields: 0,
        custom_fields: initialFormValues.custom_fields
          ? initialFormValues.custom_fields
          : {},
        custom_change_order_id:
          type === "cor" || is_custom_change_orders_id !== 0 ? "" : undefined,
        order_date: dayjs().format(date_format),
      },
      validationSchema: validationSchema,
      enableReinitialize: true,
      validateOnChange: false,
      onSubmit: onAddChangeOrder,
    });

    const [initialValuesState, setInitialValuesState] = useState(formik.values);

    useImperativeHandle(ref, () => {
      return {
        toggle: (type: string) => {
          setDrawerOpen(!drawerOpen);
        },
      };
    });

    const {
      setFieldValue,
      validateForm,
      submitForm,
      resetForm,
      errors,
      setFieldError,
    } = formik;

    const handleInitialProject = async () => {
      setProjectLoading(true);
      if (
        defaultProjectId &&
        defaultProject.current?.project_id !== defaultProjectId
      ) {
        const proResApi = (await getProjectDetails({
          start: 0,
          limit: 1,
          projects: defaultProjectId?.toString() || "",
          need_all_projects: 0,
          global_call: true,
          is_completed: true,
          filter: { status: "0" },
        })) as IProjectDetailsRes;
        const queryPro = proResApi?.data?.projects[0];
        defaultProject.current = queryPro;
        setFieldValue("project_id", queryPro?.id);
        setInitialValuesState({
          ...initialValuesState,
          project_id: queryPro?.id,
        });
        setSelectedProject(queryPro);
        setProjectLoading(false);
      } else {
        defaultProject.current = null;
        setFieldValue("project_id", "");
        setInitialValuesState({
          ...initialValuesState,
          project_id: undefined,
        });
        setSelectedProject(undefined);
      }
      setProjectLoading(false);
    };

    useEffect(() => {
      if (
        Number(is_custom_change_orders_id) === 2 &&
        need_to_increment &&
        last_primary_id &&
        type === "co"
      ) {
        formik.setValues({
          ...formik.values,
          custom_change_order_id: (
            Number(need_to_increment) + Number(last_primary_id)
          ).toString(),
        });
        setInitialValuesState({
          ...initialValuesState,
          custom_change_order_id: (
            Number(need_to_increment) + Number(last_primary_id)
          ).toString(),
        });
      } else {
        formik.setValues({
          ...formik.values,
          custom_change_order_id: "",
        });
        setInitialValuesState({
          ...initialValuesState,
          custom_change_order_id: "",
        });
      }
    }, [
      is_custom_change_orders_id,
      need_to_increment,
      last_primary_id,
      drawerOpen,
      type,
      loadingCustomField,
    ]);

    useEffect(() => {
      //Call the auto number api only when drawer open and
      if (Number(is_custom_change_orders_id) == 2 && drawerOpen) {
        setModuleAutoIncrementId(module_id, module_key);
      }
      if (drawerOpen) {
        setLoadingCustomFields(true);
        setTimeout(() => setLoadingCustomFields(false), 3000);
      }
    }, [is_custom_change_orders_id, drawerOpen]);

    useEffect(() => {
      handleInitialProject();
    }, [defaultProjectId, drawerOpen]);

    // useEffect(() => {
    //   if (!drawerOpen) {
    //     setSelectedCO(null);
    //   }
    // }, [drawerOpen]);

    // const isFormModified = useMemo(() => {
    //   return (
    //     JSON.stringify(formik.values) !== JSON.stringify(initialValuesState)
    //   );
    // }, [formik.values, initialValuesState]);

    // const closeConfirmationModal = () => {
    //   setDrawerOpen(drawerOpen);
    //   setIsConfirmDialogOpen(false);
    // };

    // const handleAlertBox = async () => {
    //   setIsConfirmDialogOpen(false);
    //   setDrawerOpen(!drawerOpen);
    // };

    // Remove NF Open Confirmation Modal
    const handleClose = useCallback(() => {
      setSearchParams((prev) => {
        prev.delete("action");
        prev.delete("project");
        return prev;
      });
      setDrawerOpen(false);
      setPage(0);
      resetForm();
      defaultProject.current = null;
      setSelectedProject(defaultProject.current ?? undefined);
      setFieldValue("project_id", defaultProject.current?.id?.toString() ?? "");
      setContactDetailDialogOpen(false);
      setIsSubmit(false); // Reset submit state
      setSelectedCoItems([]);
      setSelectedCO(null);
      setSelectAll(false);
    }, []);

    const fetchData = async (page: number, limit: number) => {
      setLoading(true);
      const response = (await webWorkerApi<IChangeOrderListApiRes>({
        url: apiRoutes.CHANGE_ORDERS.list,
        method: "post",
        data: {
          type: type === "cor" ? "cor" : "co",
          page,
          limit,
          ignore_filter: 1,
          filter: {
            is_template: 1,
            type: type === "cor" ? "cor" : "co",
            status: 0,
            billing_status: "",
          },
        },
      })) as IChangeOrderListApiRes;
      if (response.success) {
        if (response.data.changeOrders?.length > 0) {
          setChangeOrderRequests((prevOrders) => {
            // Filter out opposite type before appending new data
            let updatedOrders =
              prevOrders?.filter((order) => order.type === type) || [];

            return [...updatedOrders, ...response.data.changeOrders];
          });

          setPage((prevPage) => prevPage + 1);
        }
      }
      setLoading(false);
    };

    const changeOrderItemList = [
      {
        label: "Customer CO Number",
        value: "import_customer_co_number",
      },
      {
        label: "Invoiced To",
        value: "import_invoiced_to",
      },
      {
        label: "Time Delay",
        value: "import_time_delay",
      },
      {
        label: "Original Estimate",
        value: "import_original_estimate",
      },
      {
        label: "Requested By",
        value: "import_requested_by",
      },
      {
        label: "Items",
        value: "import_items",
      },
      {
        label: "Notes",
        value: "import_notes",
      },

      {
        label: "Approved By",
        value: "import_approved_by",
      },
      {
        label: "Tax",
        value: "import_tax",
      },
      {
        label: "Files",
        value: "import_files",
      },
    ];

    const options = useMemo(() => {
      const seen = new Set();
      const uniqueItems = changeOrderRequests.filter((item) => {
        if (seen.has(item.change_order_id)) return false;
        seen.add(item.change_order_id);
        return true;
      });

      return uniqueItems.map((item) => {
        const label = item.type === "co" ? "CO# " : "COR# ";
        const id = showPrefix
          ? item.prefix_company_order_id
          : item.company_order_id;
        const coID = label + id;
        return {
          label: `${item?.project_name} ${coID}${
            item.customer_name
              ? ` - ${HTMLEntities.decode(sanitizeString(item.customer_name))}`
              : ""
          }`,

          value: item.change_order_id.toString(),
          subject: item.subject,
        };
      });
    }, [changeOrderRequests, showPrefix]);
    useEffect(() => {
      if (drawerOpen) {
        fetchData(page, templateLimit);
      }
    }, [drawerOpen, selectedProject]);

    const onScroll = async (event: React.UIEvent<HTMLElement>) => {
      const target = event.target as HTMLElement;

      if (
        !loading &&
        target.scrollTop + target.offsetHeight === target.scrollHeight
      ) {
        await fetchData(page, templateLimit);
      }
    };

    const processedOptions = (
      type === "cor"
        ? changeOrderItems.map((item) =>
            item.value === "import_customer_co_number"
              ? { ...item, label: "Customer COR Number" }
              : item
          )
        : changeOrderItems
    )?.map((item) => {
      const isDisabled =
        selectedCO !== null &&
        selectedProject &&
        selectedProject?.id !== selectedCO?.project_id &&
        (item.value === "import_original_estimate" ||
          item.value === "import_requested_by" ||
          item.value === "import_invoiced_to" ||
          item.value === "import_approved_by");

      return {
        ...item,
        disabled: isDisabled,
        label: item.label,
      };
    });

    const handleEnterKeyPress = (
      event: React.KeyboardEvent<HTMLInputElement>
    ) => {
      event.preventDefault();
    };
    return (
      <>
        <Drawer
          open={drawerOpen}
          rootClassName="drawer-open"
          width={718}
          maskClosable={false}
          onClose={handleClose}
          push={false}
          classNames={{
            body: "!p-0 !overflow-hidden",
          }}
          title={
            <div className="flex items-center">
              <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
                <FontAwesomeIcon
                  className="w-4 h-4"
                  icon="fa-regular fa-arrow-right-arrow-left"
                />
              </div>
              <Header
                level={5}
                className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
              >
                {_t("Add ") +
                  getCOModuleName({ gConfig, isRequest: type === "cor" })}
                {/* Add Change Request Order, header change on click */}
              </Header>
            </div>
          }
          closeIcon={
            !window.ENV.PAGE_IS_IFRAME ? (
              <CloseButton onClick={() => handleClose()} />
            ) : null
          }
        >
          <form method="post" className="py-4" onSubmit={formik.handleSubmit}>
            <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
              <div className="flex flex-col gap-5">
                <SidebarCardBorder addGap={true}>
                  <div className="w-full">
                    <ButtonField
                      label={_t("Project")}
                      name="project_id"
                      labelPlacement="top"
                      required={true}
                      loading={projectLoading}
                      addonBefore={
                        !isEmpty(selectedProject?.project_name) && (
                          <ProjectFieldRedirectionIcon
                            onClick={(e) => {
                              e.stopPropagation();
                            }}
                            projectId={selectedProject?.id?.toString() || ""}
                          />
                        )
                      }
                      errorMessage={errors?.project_id?.toString()}
                      value={selectedProject?.project_name}
                      onClick={() => {
                        setIsSelectProOpen(true);
                      }}
                    />
                  </div>

                  <div className="w-full relative">
                    <ButtonField
                      label={_t("Customer")}
                      name="custom_change_order_id"
                      labelPlacement="top"
                      value={selectedProject?.customer_name}
                      isDisabled={true}
                      avatarProps={{
                        user: {
                          name: HTMLEntities.decode(
                            sanitizeString(selectedProject?.customer_name ?? "")
                          ),
                          image: !selectedProject?.customer_contact_id
                            ? selectedProject?.cust_image
                            : "",
                        },
                      }}
                      addonBefore={
                        selectedProject?.customer_name && (
                          <div className="flex items-center gap-1">
                            <ContactDetailsButton
                              onClick={(e) => {
                                e.stopPropagation();
                                setContactDetailDialogOpen(true);
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              className="!w-5 !h-5"
                              directoryId={
                                selectedProject?.customer_id?.toString() || ""
                              }
                              directoryTypeKey={"customer"}
                            />
                            <ContactDetailsModal
                              isOpenContact={contactDetailDialogOpen}
                              onCloseModal={() =>
                                setContactDetailDialogOpen(false)
                              }
                              contactId={selectedProject?.customer_id}
                              additional_contact_id={
                                selectedProject?.customer_contact_id || 0
                              }
                            />
                          </div>
                        )
                      }
                    />
                  </div>

                  <div className="w-full">
                    <InputField
                      label={_t("Subject")}
                      labelPlacement="top"
                      isRequired={true}
                      name="subject"
                      errorMessage={errors?.subject?.toString()}
                      onChange={(e) => {
                        setFieldValue("subject", e.target.value);
                        if (e.target.value?.trim()) {
                          setFieldError("subject", "");
                        } else {
                          setFieldError("subject", "This field is required.");
                        }
                      }}
                      value={formik.values.subject}
                      onPressEnter={handleEnterKeyPress}
                    />
                  </div>
                  <div className="grid md:grid-cols-2 md:gap-4 gap-7">
                    {" "}
                    <div className="w-full">
                      {" "}
                      {/*                     is_custom_change_orders_id === 0 means start at number                    is_custom_change_orders_id === 1 means custom number                    is_custom_change_orders_id === 2 means auto number                  */}{" "}
                      <InputField
                        label={
                          type === "co" ? _t("CO") + " #" : _t("COR") + " #"
                        }
                        labelPlacement="top"
                        disabled={!isCustomChangeOrderIdRequired}
                        isRequired={isCustomChangeOrderIdRequired}
                        name="custom_change_order_id"
                        onChange={(e) => {
                          setFieldValue(
                            "custom_change_order_id",
                            e.target.value
                          );
                          if (e.target.value?.trim()) {
                            setFieldError("custom_change_order_id", "");
                          } else {
                            setFieldError(
                              "custom_change_order_id",
                              "This field is required."
                            );
                          }
                        }}
                        maxLength={21}
                        value={
                          is_custom_change_orders_id === 0 && type === "co"
                            ? "Save To View"
                            : formik.values.custom_change_order_id
                        }
                        errorMessage={errors?.custom_change_order_id?.toString()}
                        onPressEnter={handleEnterKeyPress}
                      />{" "}
                    </div>{" "}
                    <div className="w-full">
                      {" "}
                      <DatePickerField
                        label={_t("Date")}
                        labelPlacement="top"
                        format={date_format}
                        errorMessage={errors?.order_date?.toString()}
                        placeholder=""
                        name="order_date"
                        value={displayDateFormat(
                          formik?.values?.order_date?.toString().trim(),
                          date_format
                        )}
                        onChange={(value) => {
                          if (value) {
                            setFieldValue(
                              "order_date",
                              value.format(date_format)
                            );
                          } else {
                            setFieldValue("order_date", undefined);
                          }
                        }}
                      />{" "}
                    </div>{" "}
                  </div>
                </SidebarCardBorder>
                <SidebarCardBorder addGap={true}>
                  <div className="w-full">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center whitespace-nowrap gap-1.5">
                        <FieldLabel
                          children={_t(
                            `${
                              type == "co"
                                ? `Use ${module_singular_name} Template`
                                : `Use ${module_singular_name} Request Template`
                            }`
                          )}
                        />
                        <Tooltip
                          title={_t(
                            `Save a ${
                              type === "co"
                                ? `${module_singular_name}`
                                : `${module_singular_name} Request`
                            } as a Template for future use by selecting 'Save ${
                              type === "co"
                                ? `${module_singular_name}`
                                : `${module_singular_name} Request`
                            } as Template' checkbox located near the Timeline on Details page.`
                          )}
                        >
                          <FontAwesomeIcon
                            icon="fa-regular fa-circle-info"
                            className="text-primary-900"
                          />
                        </Tooltip>
                        {/* <Typography className="text-primary-900">*</Typography> */}
                      </div>
                      {/* will remove after testing */}
                      {/* <InlineField
                        label={_t("Do you want to Copy Project Template?")}
                        labelPlacement="left"
                        labelClass="text-[#808080] !text-xs !font-medium italic !max-w-fit p-0"
                        formInputClassName="!flex-row w-auto"
                        field={
                          <Switch
                            className="cf-switch success"
                            // onChange={() => { }}
                            // onChange={handleCopyProjectToggle}
                            checked={copyProjectDetails}
                            size="small"
                            onChange={(data) =>
                              handleCopyProjectToggle(data as boolean)
                            }
                          />
                        }
                      /> */}
                    </div>
                    <SelectField
                      labelPlacement="top"
                      showSearch={true}
                      allowClear={true}
                      onPopupScroll={onScroll}
                      loading={loading}
                      options={options}
                      value={
                        selectedCO
                          ? selectedCO.change_order_id.toString()
                          : undefined
                      }
                      filterOption={(input, option) =>
                        filterOptionBySubstring(
                          input,
                          `${option?.label} ${option?.subject}` as string
                        )
                      }
                      onChange={(value) => {
                        setSelectedCO(
                          changeOrderRequests.find(
                            (item) => item.change_order_id.toString() === value
                          ) || null
                        );
                        setSelectedCoItems([]);
                        setSelectAll(false);
                      }}
                    />
                  </div>
                  {!selectedCO ||
                    (Object.keys(selectedCO).length !== 0 && (
                      <div className="w-full flex flex-col gap-1.5">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center whitespace-nowrap gap-1.5">
                            <FieldLabel
                              children={_t(
                                `Select Items that should be copied into new ${
                                  type === "co"
                                    ? ` ${module_singular_name}`
                                    : ` ${module_singular_name} Request`
                                }`
                              )}
                            />
                          </div>
                          <InlineField
                            label={_t("All")}
                            labelPlacement="left"
                            labelClass="text-[#808080] !text-xs !font-medium italic !max-w-fit p-0"
                            formInputClassName="!flex-row w-auto"
                            field={
                              <Switch
                                className="cf-switch success"
                                onChange={handleSelectAllToggle}
                                checked={selectAll}
                                size="small"
                              />
                            }
                          />
                        </div>
                        {/* Manually render each checkbox */}
                        <div className="grid sm:grid-cols-3">
                          {processedOptions.map((item) =>
                            item.disabled ? (
                              <Tooltip
                                key={item.value}
                                title={_t(
                                  "This value inherited from selected Project, not from template"
                                )}
                                placement="top"
                              >
                                <CheckBox
                                  className="gap-1.5 w-fit"
                                  value={item.value}
                                  disabled={item.disabled}
                                  checked={selectedCoItems.includes(item.value)}
                                  onChange={(e) =>
                                    handleCheckboxChange(
                                      item.value,
                                      e.target.checked
                                    )
                                  }
                                  children={item.label}
                                />
                              </Tooltip>
                            ) : (
                              <CheckBox
                                className="gap-1.5 w-fit"
                                key={item.value}
                                value={item.value}
                                checked={selectedCoItems.includes(item.value)}
                                onChange={(e) =>
                                  handleCheckboxChange(
                                    item.value,
                                    e.target.checked
                                  )
                                }
                                children={item.label}
                              />
                            )
                          )}
                        </div>
                      </div>
                    ))}
                </SidebarCardBorder>
                <OnlyRequiredCustomFields
                  componentList={componentList}
                  formik={formik}
                  isSubmit={isSubmit}
                  loadingCustomField={loadingCustomField}
                />
              </div>
            </div>
            {isSelectProOpen && (
              <SelectProject
                isSingleSelect={true}
                isRequired={true}
                open={isSelectProOpen}
                setOpen={setIsSelectProOpen}
                selectedProjects={selectedProject ? [selectedProject] : []}
                onProjectSelected={(data) => {
                  setSelectedProject(data.length ? data[0] : null);
                  setSelectedCoItems([]);
                  setSelectAll(false);
                  setFieldValue("project_id", data.length ? data[0]?.id : "");
                  setPage(0); // Reset page when a new project is selected
                  if (data.length) {
                    setFieldError("project_id", "");
                  } else {
                    setFieldError("project_id", "This field is required.");
                  }
                }}
                module_key={module_key}
              />
            )}
            <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
              <PrimaryButton
                htmlType="button"
                onClick={() => {
                  setIsSubmit(true);
                  handleValidateAndSubmit();
                }}
                buttonText={
                  _t("Create ") +
                  getCOModuleName({ gConfig, isRequest: type === "cor" })
                }
                disabled={isButtonDisabled || isSuccess || loadingCustomField}
                isLoading={isButtonDisabled || isSuccess}
              />
            </div>
          </form>
        </Drawer>
        {/* {isConfirmDialogOpen && (
          <ConfirmModal
            isOpen={isConfirmDialogOpen}
            modalIcon="fa-regular fa-file-check"
            modaltitle={_t("Confirmation")}
            description={_t(
              `Do you really want to leave this page and lose your unsaved changes?`
            )}
            onCloseModal={closeConfirmationModal}
            onAccept={() => {
              handleAlertBox();
            }}
            onDecline={closeConfirmationModal}
          />
        )} */}
      </>
    );
  }
);

export default AddChangeOrder;
