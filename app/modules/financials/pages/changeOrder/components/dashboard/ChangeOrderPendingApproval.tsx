// Atoms
import { Typography } from "~/shared/components/atoms/typography";
import { Tooltip } from "~/shared/components/atoms/tooltip";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import ToolTipCell from "~/shared/components/molecules/table/ToolTipCell";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Hook
import { useTranslation } from "~/hook";
import {
  IChangeOrderDashPendingApproval,
  IChangeOrderDashState,
} from "~/modules/financials/pages/changeOrder/redux/types";
import {
  useAppDispatch,
  useAppSelector,
} from "~/modules/financials/pages/changeOrder/redux/store";
import { ColDef, ICellRendererParams } from "ag-grid-community";
import { useNavigate } from "@remix-run/react";
import { useState, useEffect } from "react";
import { fetchDashWidgts } from "../../redux/action/changeOrderDashActions";
import { formatAmount, sanitizeString } from "~/helpers/helper";
interface ICellParams
  extends ICellRendererParams<IChangeOrderDashPendingApproval> {}

const ChangeOrderPendingApproval = () => {
  const { _t } = useTranslation();
  const {
    isDashLoading,
    pendingApproval,
    pendingApprovalLastRefreshTime,
  }: IChangeOrderDashState = useAppSelector((state) => state.changeOrderDash);
  const { formatter } = useCurrencyFormatter();
  const navigate = useNavigate();
  const [pendingApprovalData, setPendingApprovalData] = useState<
    IChangeOrderDashPendingApproval[]
  >([]);
  const dispatch = useAppDispatch();
  const [isCashLoading, setIsCashLoading] = useState<boolean>(false);
  const [pendingApprovalLastRefreshTimes, setPendingApprovalLastRefreshTime] =
    useState<string | undefined>("");
  useEffect(() => {
    if (!!pendingApprovalLastRefreshTime) {
      setPendingApprovalLastRefreshTime(pendingApprovalLastRefreshTime);
    }
  }, [pendingApprovalLastRefreshTime]);
  useEffect(() => {
    if (!isCashLoading && pendingApproval) {
      setPendingApprovalData(pendingApproval);
    }
  }, [pendingApproval, isCashLoading]);

  const handleRefreshClick = async () => {
    setIsCashLoading(true);
    setPendingApprovalData([]);
    await dispatch(fetchDashWidgts({ refresh_type: "pending_approval" })).then(
      (res) => {
        setPendingApprovalLastRefreshTime(
          res?.payload?.data?.pendingApprovalLastRefreshTime
        );
      }
    );
    setIsCashLoading(false);
  };

  const columnDefs: ColDef<IChangeOrderDashPendingApproval>[] = [
    {
      headerName: _t("CO") + " #",
      field: "prefix_company_order_id",
      minWidth: pendingApprovalData?.length > 0 ? 100 : 60,
      flex: 1,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ToolTipCell,
    },
    {
      headerName: _t("Date"),
      field: "order_date",
      minWidth: pendingApprovalData?.length > 0 ? 135 : 70,
      maxWidth: pendingApprovalData?.length > 0 ? 135 : 70,
      suppressMenu: true,
      cellRenderer: ({ value }: ICellParams) => {
        return value ? <DateTimeCard format="date" date={value} /> : "-";
      },
    },
    {
      headerName: _t("Customer"),
      field: "full_name",
      minWidth: 90,
      maxWidth: 90,
      suppressMenu: true,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      cellRenderer: ({ value, data }: ICellParams) => {
        const name = HTMLEntities.decode(sanitizeString(value));
        return name?.trim() ? (
          <Tooltip title={name}>
            <div className="w-fit mx-auto">
              <AvatarProfile
                user={{
                  name,
                  image: data?.image,
                }}
                className="m-auto"
                iconClassName="text-[11px] font-semibold"
              />
            </div>
          </Tooltip>
        ) : (
          <Typography className="table-tooltip-text">-</Typography>
        );
      },
    },
    {
      headerName: _t("Amount"),
      field: "total",
      minWidth: pendingApprovalData?.length > 0 ? 130 : 70,
      maxWidth: 150,
      flex: 1,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      valueGetter: ({ data }) => {
        const total = formatter(
          formatAmount(Number(data?.total) / 100, { isDashboard: true })
        ).value_with_symbol;

        return total;
      },
      cellRenderer: ToolTipCell,
    },
  ];
  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-CO-pending-approval.svg`}
    />
  );

  const noRowsOverlay = () => (
    <StaticTableRowLoading columnDefs={columnDefs} limit={6} />
  );
  return (
    <>
      <DashboardCardHeader
        title={_t("CO’s Pending Approval")}
        isRefreshing={isCashLoading}
        showRefreshIcon={true}
        refreshIconTooltip={pendingApprovalLastRefreshTimes}
        onClickRefresh={handleRefreshClick}
      />
      <div className="py-2 px-2.5">
        <div className="ag-theme-alpine h-[209px] ag-grid-cell-pointer">
          <StaticTable
            className="static-table"
            key={isDashLoading ? "loading" : "loaded"}
            columnDefs={columnDefs}
            rowData={pendingApprovalData}
            noRowsOverlayComponent={
              (isDashLoading || isCashLoading) &&
              pendingApprovalData?.length === 0
                ? noRowsOverlay
                : pendingApprovalData.length === 0
                ? noData
                : null
            }
            enableOpenInNewTab={window.ENV.ENABLE_ALL_CLICK}
            generateOpenInNewTabUrl={(data: { change_order_id?: number }) =>
              `/manage-change-orders/${data?.change_order_id}`
            }
          />
        </div>
      </div>
    </>
  );
};

export default ChangeOrderPendingApproval;
