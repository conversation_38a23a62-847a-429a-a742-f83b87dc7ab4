// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { AmountProgressBody } from "~/shared/components/molecules/amountProgressBody";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
// Hook
import { useTranslation } from "~/hook";
import {
  IChangeOrderDashProjectsChangeOrderCount,
  IChangeOrderDashState,
} from "~/modules/financials/pages/changeOrder/redux/types";
import { useAppSelector } from "~/modules/financials/pages/changeOrder/redux/store";
import { useEffect, useMemo, useState } from "react";
import { sanitizeString } from "~/helpers/helper";
import { fetchDashWidgt } from "../../redux/action/changeOrderDashActions";

const ChangeOrderProjectItem = ({
  item,
}: {
  item: IChangeOrderDashProjectsChangeOrderCount;
}) => {
  const { _t } = useTranslation();

  const percent = useMemo(() => {
    try {
      const { open_count = "0", close_count = "0" } = item;
      const openCount = parseInt(open_count);
      const closeCount = parseInt(close_count);

      return openCount + closeCount > 0
        ? (openCount / (openCount + closeCount)) * 100
        : 0;
    } catch (error) {
      return 0;
    }
  }, [item.open_count, item.close_count]);

  return (
    <AmountProgressBody
      key={item.project_id}
      title={HTMLEntities.decode(sanitizeString(item.project_name))}
      dot={true}
      values={[
        {
          label: _t("Open:"),
          value: item.open_count,
          dotColor: "bg-[#FF766D]",
        },
        {
          label: _t("Closed:"),
          value: item.close_count,
          dotColor: "bg-[#586B8E]",
        },
      ]}
      percent={percent}
      color="#FF766D"
      trailColor="#586B8E"
      strokeLinecap="round"
    />
  );
};
const ChangeOrderProject = () => {
  const { _t } = useTranslation();
  const {
    isDashLoading,
    projectsChangeOrderCount,
    projectsChangeOrderCountLastRefreshTime,
  }: IChangeOrderDashState = useAppSelector((state) => state.changeOrderDash);

  const [recentClientData, setRecentClientData] = useState<
    IChangeOrderDashProjectsChangeOrderCount[]
  >([]);

  const [
    projectsChangeOrderCountLastRefreshTimes,
    setProjectsChangeOrderCountLastRefreshTime,
  ] = useState<string | undefined>("");

  const [isCashLoading, setIsCashLoading] = useState<boolean>(false);

  useEffect(() => {
    setRecentClientData(projectsChangeOrderCount);
    setProjectsChangeOrderCountLastRefreshTime(
      projectsChangeOrderCountLastRefreshTime
    );
  }, [projectsChangeOrderCount]);

  const handleRefreshClick = async () => {
    setIsCashLoading(true);
    try {
      const response = (await fetchDashWidgt({
        refresh_type: "projects_change_order_count",
      })) as any;
      setRecentClientData(response?.data?.projectsChangeOrderCount);
      setProjectsChangeOrderCountLastRefreshTime(
        response?.data?.projectsChangeOrderCountLastRefreshTime
      );
      setIsCashLoading(false);
    } catch (error) {
      console.error(
        `\n File: #ChangeOrderProject.tsx -> Line: #99 ->  `,
        error
      );
      setIsCashLoading(false);
    }
  };

  return (
    <>
      <DashboardCardHeader
        title={_t("CO’s by Project")}
        isRefreshing={isCashLoading}
        showRefreshIcon={true}
        refreshIconTooltip={projectsChangeOrderCountLastRefreshTimes}
        onClickRefresh={handleRefreshClick}
      />
      <div className="py-2 px-2.5">
        {isDashLoading || isCashLoading ? (
          <div className="grid gap-0.5">
            {Array.from({ length: 5 }).map((_, key) => (
              <div className="grid animate-pulse" key={key}>
                <div className="bg-black/10 rounded h-[18px] w-14"></div>
                <div className="h-[22px] flex items-center">
                  <div className="bg-black/10 rounded h-2 w-full"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <ul className="grid gap-0.5">
            {recentClientData?.length > 0 &&
              recentClientData?.map((item, index) => (
                <ChangeOrderProjectItem key={index} item={item} />
              ))}
          </ul>
        )}
        {!isDashLoading &&
          !recentClientData?.length &&
          recentClientData?.length === 0 && (
            <div className="flex justify-center items-center h-[209px]">
              <NoRecords
                image={`${window.ENV.CDN_URL}assets/images/no-records-td-open-project.svg`}
              />
            </div>
          )}
      </div>
    </>
  );
};

export default ChangeOrderProject;
