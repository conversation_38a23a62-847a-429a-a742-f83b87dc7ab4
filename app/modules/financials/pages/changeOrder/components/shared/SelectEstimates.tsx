import { useEffect, useMemo, useState } from "react";
import { getApiData } from "~/helpers/axios-api-helper";
import { getApiDefaultParams, sanitizeString } from "~/helpers/helper";
import { apiRoutes } from "~/route-services/routes";
// Molecules
import { SelectField } from "~/shared/components/molecules/selectField";
import { EstimatesFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/estimatesFieldRedirectionIcon";
// Other
import { getGConfig, useGModules } from "~/zustand";

interface ISelectEstimateProps extends ISelectFieldProps {
  projectId?: string | number;
  loadingStatus?: IFieldStatus[];
  isReadOnly?: boolean;
}

export default function SelectEstimate({
  projectId,
  onChange,
  fixStatus = "button",
  value,
  ...props
}: ISelectEstimateProps) {
  const [estimates, setEstimates] = useState<TEstimate[]>([]);
  const [loading, setLoading] = useState(true);
  const gConfig: GConfig = getGConfig();
  const { checkModuleAccessByKey } = useGModules();
  const estimate_moduleAcces = checkModuleAccessByKey(CFConfig.estimate_module);

  const options: IOption[] = useMemo(() => {
    if (!estimates) return [];
    return estimates.map((item) => ({
      label: HTMLEntities.decode(
        sanitizeString(
          `Est. #${item?.company_estimate_id} - ${item?.customer_name}`
        )
      ),
      value: item?.estimate_id.toString(),
    }));
  }, [estimates]);

  const fetchEstimates = async () => {
    getApiData({
      url: apiRoutes.GET_ESTIMATES.url,
      method: "post",
      data: getApiDefaultParams({
        otherParams: {
          module_id: gConfig.module_id,
          request_for: "sidebar",
          limited_fields: 1,
          filter: [
            {
              project: projectId?.toString(),
              status: 0,
              approval_type: "estimate_approved",
            },
          ],
        },
      }),
      success: (response: {
        success: boolean | string;
        message: string;
        data: {
          data: TEstimate[];
        };
      }) => {
        if (!response?.success) {
          notification.error({
            description: response?.message,
          });
        } else {
          setEstimates(response?.data?.data);
          setLoading(false);
        }
      },
    });
  };
  const isvalueAvailable = options.some((ele) => ele.value === value);

  useEffect(() => {
    fetchEstimates();
  }, [projectId]);

  return (
    <div className="flex items-center w-full sm:max-w-[calc(100%-171px)]">
      <div
        className={`hover:w-full focus-within:w-full focus:w-full ${
          !(!!value && options?.length) ||
          ["loading", "success", "error"].includes(fixStatus)
            ? "w-full"
            : "max-w-[calc(100%-24px)] max-[479px]:w-full"
        }`}
      >
        <SelectField
          options={options}
          onChange={onChange}
          onDropdownVisibleChange={(open) => {
            if (open && estimate_moduleAcces === "no_access") {
              notification.error({
                description: "You are not allowed to view the Estimate data.",
              });
            }
          }}
          allowClear
          showSearch={true}
          disabled={loading}
          loading={loading}
          value={options?.length && isvalueAvailable ? value : undefined}
          fixStatus={fixStatus}
          {...props}
        />
      </div>
      {!!value && options?.length && isvalueAvailable ? (
        <EstimatesFieldRedirectionIcon estimatesId={value} />
      ) : (
        ""
      )}
    </div>
  );
}
