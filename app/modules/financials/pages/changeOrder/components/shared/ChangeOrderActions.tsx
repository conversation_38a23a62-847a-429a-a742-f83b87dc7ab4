import { useEffect, useMemo, useState } from "react";
// Molecules
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { PDFFilePreview } from "~/shared/components/molecules/pdfFilePreview";
import { SendEmailDrawer } from "~/shared/components/molecules/sendEmailDrawer";
import { ShareInternalLinkModal } from "~/shared/components/molecules/shareInternalLinkModal";
// Other
import { removeFirstSlash } from "~/shared/utils/helper/common";
import { apiRoutes, routes } from "~/route-services/routes";
import {
  getGConfig,
  getGModuleByKey,
  getGSettings,
  useGModules,
} from "~/zustand";
import { useTranslation } from "~/hook";
import { getApiData } from "~/helpers/axios-api-helper";
import { getApiDefaultParams, sanitizeString } from "~/helpers/helper";
import { useAppDispatch } from "~/modules/financials/pages/changeOrder/redux/store";
import {
  removeCO,
  resetCOListCache,
} from "../../redux/slices/changeOrderListSlice";
import { ChangeOrderItemsPO } from "../tab/sidebar/changeOrderItemsPO";
import {
  IChangeOrderDetail,
  IChangeOrderSendEmailForm,
  IDownloadChangeOrderRes,
} from "../../redux/types";
import { defaultConfig } from "~/data";
import { downloadChangeOrderPDF } from "../../redux/action/changeOrderDetailsActions";
import { sendCommonEmailApi } from "~/redux/action/sendEmailAction";
import { setSendEmailOpenStatus } from "~/components/sidebars/multi-select/customer/zustand/action";
import { setShouldWidgetsRefresh } from "../../redux/slices/changeOrderDashSlice";
import { useNavigate, useSearchParams } from "@remix-run/react";
import { getCOModuleName } from "../../utils/helpers";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { useGlobalMenuModule } from "~/zustand/global/menuModules/slice";
import CustomContextMenu from "~/shared/components/molecules/CustomContextMenu";
import { faArrowUpRightFromSquare } from "@fortawesome/pro-regular-svg-icons";
export interface ICOActionData
  extends Pick<
    IChangeOrderDetail,
    | "change_order_id"
    | "ref_po_id"
    | "email_subject"
    | "is_deleted"
    | "project_name"
    | "company_order_id"
    | "prefix_company_order_id"
    | "items_count"
    | "billing_status"
    | "project_id"
    | "type"
  > {}

interface ChangeOrderActionsProps {
  changeOrder?: ICOActionData;
  iconClassName?: string;
  buttonClass?: string;
  icon?: IFontAwesomeIconProps["icon"];
  onDelete?: () => void;
  onArchive?: () => void;
  refreshTable: () => void;
  tooltipcontent?: string;
}
const ChangeOrderActions = ({
  changeOrder,
  iconClassName,
  buttonClass,
  onDelete,
  onArchive,
  icon,
  refreshTable,
  tooltipcontent,
}: ChangeOrderActionsProps) => {
  const changeOrderId = changeOrder?.change_order_id;
  const { _t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [isShareOpen, setIsShareOpen] = useState(false);
  const [shareLinkUrl, setSharelinkUrl] = useState<string>("");
  const { module_id, module_singular_name }: GConfig = getGConfig();
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] = useState(false);
  const [showChangeOrderItemsPO, setShowChangeOrderItemsPO] = useState(false);
  const [showGeneratePDF, setShowGeneratePDF] = useState(false);
  const [sendEmailOpen, setSendEmailOpen] = useState<boolean>(false);
  const [isSumbitForApprovalLoading, setIsSumbitForApprovalLoading] =
    useState(false);
  const [pdfTempId, setPdfTempId] = useState<string>("");

  const [shareLink, setShareLink] = useState("");
  const [contextMenu, setContextMenu] = useState({
    x: 0,
    y: 0,
    visible: false,
    useListMenu: false,
  });
  const gConfig = getGConfig();
  const { module_key, module_access, page_is_iframe } = gConfig;
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const {
    allow_delete_module_items = "0",
    user_id = 0,
    company_id = 0,
  } = user || {};
  const { checkModuleAccessByKey } = useGModules();
  const {
    module_name: purchaseOrderModuleName = "purchase_order_module",
    can_write: purchaseOrderWriteAccess,
  } = (getGModuleByKey(defaultConfig.purchase_order_module) as GModule) || {};
  const { date_format, image_resolution, save_a_copy_of_sent_pdf }: GSettings =
    getGSettings();

  const { checkGlobalMenuModulePermissionByKey } = useGlobalMenuModule();

  const poModulePermissionDis = checkGlobalMenuModulePermissionByKey(
    CFConfig.purchase_order_module
  );

  const poManagerDisabled = poModulePermissionDis === "disabled";

  const [delArchConfirmOpen, setDelArchConfirmOpen] = useState<string>("");
  const [isDeleting, setIsDeleting] = useState(false);

  const hasPoAccessShowMessages = useMemo(() => {
    const mAccess = checkModuleAccessByKey(defaultConfig.purchase_order_module);

    return mAccess === "no_access" || mAccess === "read_only";
  }, []);

  const [searchParams, setSearchParams] = useSearchParams();

  const iFrame = searchParams?.get("iframecall")?.trim();
  const pageOpenForm = searchParams?.get("pageOpenfrom")?.trim();
  const authToken = searchParams?.get("authorize_token")?.trim();

  const pdfOptions: CustomerEmailTab[] = [
    defaultConfig.employee_key,
    "my_crew",
    defaultConfig.customer_key,
    // defaultConfig.lead_key, // https://app.clickup.com/t/86cwyy94q
    defaultConfig.contractor_key,
    defaultConfig.vendor_key,
    defaultConfig.misc_contact_key,
    "by_service",
    "my_project",
  ];

  const CHANGEORDER_OPTIONS = [
    {
      label: "View/Email PDF",
      icon: "fa-regular fa-file-pdf",
      key: "view_email_pdf",
    },
    {
      label: `Generate ${purchaseOrderModuleName}`,
      icon: "fa-regular fa-file-invoice",
      key: "generate_purchase_order",
    },
    {
      content: "Share Internal Link",
      icon: "fa-regular fa-share-nodes",
      onlyIconView: true,
      key: "share_internal_link",
    },
    {
      content: (
        <>
          {_t("Status: Active")} <br />
          {_t("Click to Archive the item")}
        </>
      ),
      icon: "fa-regular fa-box-archive",
      onlyIconView: true,
      key: "archive",
    },
    {
      content: (
        <>
          {_t("Status: Archived")} <br />
          {_t("Click to Activate the item")}
        </>
      ),
      icon: "fa-regular fa-regular-active",
      onlyIconView: true,
      key: "activate",
    },
    {
      content: "Delete",
      icon: "fa-regular fa-trash-can",
      onlyIconView: true,
      key: "delete",
    },
  ];
  const handleClick = (key: string) => {
    switch (key) {
      case "share_internal_link":
        setIsShareOpen(true);
        break;
      case "delete":
        setDelArchConfirmOpen("delete");
        break;
      case "archive":
        setDelArchConfirmOpen("archive");
        break;
      case "activate":
        setDelArchConfirmOpen("activate");
        break;
      case "generate_purchase_order":
        if (changeOrder?.ref_po_id) {
          if (window.ENV.PAGE_IS_IFRAME) {
            navigate(
              `${routes.MANAGE_PURCHASE_ORDERS.url}/${changeOrder?.ref_po_id}?iframecall=${iFrame}&pageOpenfrom=${pageOpenForm}&authorize_token=${authToken}`
            );
          } else {
            navigate(
              `${routes.MANAGE_PURCHASE_ORDERS.url}/${changeOrder?.ref_po_id}`
            );
          }
        } else {
          setShowChangeOrderItemsPO(true);
        }
        break;
      case "view_email_pdf":
        setShowGeneratePDF(true);
        break;
      default:
        break;
    }
  };

  const options = useMemo(
    () =>
      CHANGEORDER_OPTIONS.map((o) => ({
        ...o,
        onClick: (e: { domEvent: any }) => {
          const event = e?.domEvent;

          const isNewTab =
            Number(window.ENV.ENABLE_ALL_CLICK) &&
            (event?.ctrlKey || event?.metaKey);

          if (isNewTab) {
            const url = new URL(
              !!changeOrder?.ref_po_id
                ? `${routes.MANAGE_PURCHASE_ORDERS.url}/${changeOrder?.ref_po_id}`
                : `${routes.CHANGE_ORDERS.url}/${changeOrderId}`,
              window.location.origin
            );
            if (!changeOrder?.ref_po_id) {
              url.searchParams.set("openInNewtab", "1");
            }
            window.open(url.toString(), "_blank", "noopener,noreferrer");
            return;
          } else {
            handleClick(o.key);
          }
        },
        ...(Number(window.ENV.ENABLE_ALL_CLICK) && {
          onAuxClick: (e: any) => {
            if (o.key === "generate_purchase_order" && e.button === 1) {
              const url = new URL(
                changeOrder?.ref_po_id
                  ? `${routes.MANAGE_PURCHASE_ORDERS.url}/${changeOrder?.ref_po_id}`
                  : `${routes.CHANGE_ORDERS.url}/${changeOrderId}`,
                window.location.origin
              );
              if (!changeOrder?.ref_po_id) {
                url.searchParams.set("openInNewtab", "1");
              }
              window.open(url.toString(), "_blank");
            }
          },
          onContextMenu: (e: any) => {
            if (o.key === "generate_purchase_order") {
              e.preventDefault();
              setContextMenu({
                x: e.clientX,
                y: e.clientY,
                visible: true,
                useListMenu: changeOrder?.ref_po_id ? false : true,
              });
            }
          },
        }),
        disabled:
          (o.key === "generate_purchase_order" &&
            (purchaseOrderWriteAccess === "0" || poManagerDisabled)) ||
          (o.key === "delete" &&
            (allow_delete_module_items === "0" || page_is_iframe)),
      })).filter((o) => {
        if (o.key === "generate_purchase_order")
          return (
            Number(changeOrder?.items_count) > 0 && changeOrder?.type === "co"
          );
        if (o.key === "archive") return changeOrder?.is_deleted === 0;
        if (o.key === "activate") return changeOrder?.is_deleted === 1;
        return true;
      }),
    [changeOrder, page_is_iframe, poManagerDisabled]
  );
  useEffect(() => {
    if (searchParams.get("openInNewtab") === "1") {
      setShowChangeOrderItemsPO(true);
      const sp = new URLSearchParams(searchParams);
      sp.delete("openInNewtab");
      setSearchParams(sp, { replace: true });
    }
  }, []);
  const deleteArchContent = useMemo(() => {
    if (delArchConfirmOpen === "delete") {
      return {
        title: _t("Delete"),
        description: _t("Are you sure you want to delete this Item?"),
        icon: "fa-regular fa-trash-can",
      };
    } else if (delArchConfirmOpen === "activate") {
      return {
        title: _t("Active"),
        description: _t("Are you sure you want to Activate this data?"),
        icon: "fa-regular fa-regular-active",
      };
    } else {
      return {
        title: _t("Archive"),
        description: _t(
          "Are you sure you want to Archive this item? To view it or Activate it later, set the filter to show Archived items."
        ),
        icon: "fa-regular fa-box-archive",
      };
    }
  }, [delArchConfirmOpen]);

  const onCloseDelModal = () => {
    setDelArchConfirmOpen("");
  };

  const handleDelete = () => {
    setIsDeleting(true);
    getApiData({
      url: apiRoutes.SERVICE.url,
      method: "post",
      data: getApiDefaultParams({
        op: "delete_module_record",
        user,
        otherParams: {
          primary_key: changeOrderId,
          module_key: module_key,
          comapny_id: company_id,
          user_id,
        },
      }),
      success: (response: UpdateFileNotesApiResponse) => {
        setIsDeleting(false);
        if (response.success !== "1") {
          notification.error({
            description: response.message,
          });
        }
        setIsDeleting(false);
        dispatch(removeCO(changeOrderId?.toString()));
        setDelArchConfirmOpen("");
        onDelete?.();
      },
      error: (description) => {
        setIsDeleting(false);
        notification.error({
          description,
        });
      },
      callComplete: () => {
        dispatch(setShouldWidgetsRefresh(true));
      },
    });
  };

  const handleArchive = (activate: boolean) => {
    setIsDeleting(true);
    try {
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "update_module_item_status",
          user,
          otherParams: {
            primary_key: Number(changeOrderId),
            module_key: module_key,
            status: activate ? 0 : 1,
          },
        }),
        success: (response: { success: string; message: string }) => {
          if (response.success !== "1") {
            notification.error({
              description: response.message,
            });
          } else {
            onArchive?.();
          }
        },
        error: (description) => {
          notification.error({
            description,
          });
        },
        callComplete: () => {
          dispatch(setShouldWidgetsRefresh(true));
        },
      });
    } catch (error) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const handleDeleteOrArchive = () => {
    if (delArchConfirmOpen === "delete") {
      handleDelete();
    } else if (delArchConfirmOpen === "archive") {
      handleArchive(false);
    } else if (delArchConfirmOpen === "activate") {
      handleArchive(true);
    }
  };

  const downloadPdf = async (tId: string) => {
    const res = (await downloadChangeOrderPDF({
      change_order_id: Number(changeOrderId),
      action: "download",
      t_id: tId,
    })) as IDownloadChangeOrderRes;

    if (res) {
      if (res.success) {
        const fileName = res?.data?.pdf_name;
        const link = document.createElement("a");
        link.href = res.base64_encode_pdf ?? "";
        link.download = fileName
          ? fileName.toString()
          : res.base64_encode_pdfUrl ?? "";

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        notification.error({
          description: res.message,
        });
      }
    }
  };

  const emailApiCall = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean
  ) => {
    const formData: IChangeOrderSendEmailForm = {
      ...tempFormData,
      send_me_copy: ccMailCopy ? 1 : 0,
      send_custom_email: 0,
      record_id: changeOrderId,
      module_id: module_id,
      module_key: module_key,
      t_id: pdfTempId,
      change_order_id: changeOrderId,
      action: "send",
      op: "pdf_change_order",
    };
    try {
      const res = (await sendCommonEmailApi(formData)) as ISendEmailCommonRes;
      if (res) {
        if (res.success) {
          closeSendMailSidebar();
          setIsSendEmailSidebarOpen(false);
        } else {
          notification.error({
            description: res.message,
          });
        }
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    }
  };

  const handleEmailApiCall = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean
  ) => {
    try {
      const response = (await webWorkerApi({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: {
          ...tempFormData,
          send_me_copy: ccMailCopy ? 1 : 0,
          send_custom_email: 0,
        },
      })) as Omit<IApiCallResponse, "success"> & { success: boolean | string };
      if (
        (typeof response?.success === "boolean" && response?.success) ||
        (typeof response?.success !== "boolean" &&
          response?.success?.toString() === "1")
      ) {
        closeSendMailSidebar();
        setIsShareOpen(false);
        setSendEmailOpenStatus(false);
      } else {
        notification.error({
          description: response?.message,
        });
      }
    } catch (error) {
      console.error(
        `\n File: #header-link.tsx -> Line: #47 -> `,
        (error as Error)?.message
      );
      notification.error({
        description: "Something went wrong. Please try again.",
      });
    }
  };

  const handleSubmitForApproval = async (
    subject: string,
    message: string,
    tId: string
  ) => {
    try {
      setIsSumbitForApprovalLoading(true);

      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "pdf_change_order",
          user,
          otherParams: {
            action: "send",
            change_order_id: Number(changeOrderId),
            req_from: "send_to_approval",
            send_custom_email: 0,
            custom_subject: HTMLEntities.encode(subject),
            custom_approve_message: message,
            t_id: tId,
            send_me_copy: 0,
          },
        }),
        success: (response: { message: string; email_response?: string }) => {
          setIsSumbitForApprovalLoading(false);

          if (response?.email_response) {
            try {
              const parsedEmailResponse = JSON.parse(response.email_response);

              // If ErrorCode exists and is not 0 : https://app.clickup.com/t/86czdv29g
              if (
                parsedEmailResponse?.ErrorCode !== undefined &&
                parsedEmailResponse?.ErrorCode !== 0
              ) {
                notification.error({
                  description: "Email sending failed.",
                });
                return;
              }
              if (
                parsedEmailResponse?.success !== undefined &&
                parsedEmailResponse?.success === "0"
              ) {
                notification.error({
                  description:
                    parsedEmailResponse?.message ||
                    "An unknown error occurred while sending email.",
                });
                return;
              }
            } catch (err) {
              notification.error({
                description: "Failed to parse email response.",
              });
              return;
            }
          } else {
            if (response?.success === "0") {
              notification.error({
                description: response?.message,
              });
            }
          }
          if (Number(changeOrderId) !== 0) {
            navigate(`${routes.MANAGE_CHANGE_ORDERS.url}/${changeOrderId}`);
          }
          refreshTable();
          dispatch(resetCOListCache());
        },

        error: (description) => {
          setIsSumbitForApprovalLoading(false);
          notification.error({
            description,
          });
        },

        callComplete: () => {
          dispatch(setShouldWidgetsRefresh(true));
        },
      });
    } catch (error) {
      setIsSumbitForApprovalLoading(false);
      notification.error({
        description: (error as Error).message || "",
      });
    }
  };

  const contextMenuItems = [
    {
      label: "Open in new tab",
      icon: faArrowUpRightFromSquare,
      onClick: () => {
        const url = new URL(
          `${routes.MANAGE_PURCHASE_ORDERS.url}/${changeOrder?.ref_po_id}`,
          window.location.origin
        );
        // url.searchParams.set("openInNewtab", "1");
        window.open(url.toString(), "_blank");
      },
    },
  ];

  const contextListMenuItems = [
    {
      label: "Open in new tab",
      icon: faArrowUpRightFromSquare,
      onClick: () => {
        const url = new URL(
          `${routes.CHANGE_ORDERS.url}/${changeOrderId}`,
          window.location.origin
        );
        url.searchParams.set("openInNewtab", "1");
        window.open(url.toString(), "_blank");
      },
    },
  ];

  return (
    <>
      <DropdownMenu
        options={options}
        iconClassName={iconClassName}
        buttonClass={`hover:!bg-[#0000000f] ${buttonClass}`}
        tooltipcontent={tooltipcontent}
        icon={icon ?? "fa-regular fa-ellipsis-vertical"}
        {...((hasPoAccessShowMessages || allow_delete_module_items === "0") && {
          footerText: _t(
            "Some actions might be unavailable depending on your privilege."
          ),
        })}
      />
      {!!changeOrderId && (
        <>
          {isShareOpen && (
            <ShareInternalLinkModal
              isOpen={isShareOpen}
              shareLinkParams={{
                record_id: changeOrderId,
                module_key: module_key,
                module_page: removeFirstSlash(routes.CHANGE_ORDERS.url || ""),
                is_custom_deep_link: 1,
                // frontendUrl: "http://localhost:3000",
              }}
              onEmailLinkClick={(data) => {
                setIsSendEmailSidebarOpen(true);
                setShareLink(data);
                setIsShareOpen(false);
                setSendEmailOpen(true);
                setSharelinkUrl(data);
              }}
              onCloseModal={() => {
                setIsShareOpen(false);
                setShareLink("");
              }}
            />
          )}

          {delArchConfirmOpen !== "" && (
            <ConfirmModal
              isOpen={delArchConfirmOpen !== ""}
              modaltitle={deleteArchContent.title}
              description={deleteArchContent.description}
              withConfirmText={delArchConfirmOpen === "delete"}
              modalIcon={deleteArchContent.icon}
              isLoading={isDeleting}
              onAccept={handleDeleteOrArchive}
              onDecline={onCloseDelModal}
              onCloseModal={onCloseDelModal}
            />
          )}

          {showChangeOrderItemsPO && (
            <ChangeOrderItemsPO
              changeOrderId={changeOrderId}
              changeOrderItemsPO={showChangeOrderItemsPO}
              setChangeOrderItemsPO={setShowChangeOrderItemsPO}
            />
          )}
          {showGeneratePDF && (
            <PDFFilePreview
              projectId={Number(changeOrder.project_id ?? "0")}
              isOpen={showGeneratePDF}
              isSumbitForApproval={
                Number(changeOrder?.billing_status) !== 255 &&
                Number(changeOrder?.billing_status) !== 140
              }
              onCloseModal={() => setShowGeneratePDF(false)}
              moduleId={module_id}
              op="pdf_change_order"
              idName="change_order_id"
              isLoading={false}
              emailSubject={changeOrder?.email_subject}
              options={pdfOptions}
              handleEmailApiCall={emailApiCall}
              handleDownload={downloadPdf}
              isViewAttachment={false}
              id={changeOrderId.toString()}
              submitForApprovalSubject={HTMLEntities.decode(
                sanitizeString(
                  `${getCOModuleName({ gConfig })} #${
                    changeOrder.prefix_company_order_id ||
                    changeOrder.company_order_id
                  } (${changeOrder?.project_name})`
                )
              )}
              handleSubmitForApproval={handleSubmitForApproval}
              isSumbitForApprovalLoading={isSumbitForApprovalLoading}
              moduleName={HTMLEntities.decode(
                sanitizeString(module_singular_name)
              )}
              setPdfTempId={setPdfTempId}
            />
          )}

          {sendEmailOpen && (
            <SendEmailDrawer
              closeDrawer={() => {
                setSendEmailOpen(false);
                setSendEmailOpenStatus(false);
              }}
              appUsers={true}
              contactId={0}
              isViewAttachment={false}
              openSendEmailSidebar={sendEmailOpen}
              options={[
                defaultConfig.employee_key,
                defaultConfig.contractor_key,
              ]}
              singleSelecte={false}
              emailApiCall={handleEmailApiCall}
              customEmailData={{
                body: `A link to a record within Contractor Foreman has been shared with you. <a href = ${shareLinkUrl}>View Details</a>.`,
                subject: "Shared Link",
              }}
              validationParams={{
                date_format: date_format,
                file_support_module_access: checkModuleAccessByKey(
                  defaultConfig.file_support_key
                ),
                image_resolution,
                module_key,
                module_id,
                module_access,
                save_a_copy_of_sent_pdf,
              }}
              canWrite={false}
            />
          )}
        </>
      )}
      <CustomContextMenu
        position={{ x: contextMenu.x, y: contextMenu.y }}
        visible={contextMenu.visible}
        items={
          contextMenu.useListMenu ? contextListMenuItems : contextMenuItems
        }
        onClose={() =>
          setContextMenu((c) => ({ ...c, visible: false, useListMenu: false }))
        }
      />
    </>
  );
};

export default ChangeOrderActions;
