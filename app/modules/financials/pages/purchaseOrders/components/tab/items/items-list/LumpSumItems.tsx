// atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Hook
import { useTranslation } from "~/hook";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { getGConfig } from "~/zustand";
import { getCommonColumns } from "../common/function";
import { SetStateAction, useMemo, useState } from "react";
import { updatePurchaseOrderItem } from "../../../../redux/action/POItemAction";
import {
  keysOfItem,
  updatePOItemsBulk,
  updatePOItemWithID,
} from "../../../../redux/slices/poItemsSlice";
import { useAppPODispatch, useAppPOSelector } from "../../../../redux/store";
import { useParams } from "@remix-run/react";
import { RowDragEvent } from "ag-grid-community";
import { updatePurchaseOrderItemOrder } from "../../../../redux/action/POItemAction";
import { convertToFixed } from "../../../../utils/function";
import { updatePODetail } from "../../../../redux/slices/poDetailSlice";
import { formatAmount } from "~/helpers/helper";

const LumpSumItems = ({
  filteredCodeCostData,
  isReadOnly,
  // handleDeleteSectionItem,
  // purchaseOrderItems,
  handleDeleteItemOpen,
  // handleDeleteItemClose,
  handleViewItem,
  setItemsData,
  POMatrix,
  isTaxEnabled,
  isParentReadOnly = false,
}: ItemListProps) => {
  const { _t } = useTranslation();
  const dispatch = useAppPODispatch();
  const { tab, id: purchase_order_id }: RouteParams = useParams();
  const { module_id, module_access, module_key }: GConfig = getGConfig();
  const [isActiveTable, setIsActiveTable] = useState<string[]>([]);
  const { formatter, inputFormatter } = useCurrencyFormatter();
  const [isLoadingCheckBox, setIsLoadingCheckBox] = useState<
    Record<number, boolean>
  >({});
  const { purchaseOrderSectionItems } = useAppPOSelector(
    (state) => state?.purchaseOrderItems
  );
  const { purchaseOrderDetail, isPODetailLoading } = useAppPOSelector(
    (state) => state?.purchaseOrderDetail
  );
  const { filter } = useAppPOSelector((state) => state?.comman);

  const filteredPurchaseOrderData = useMemo(() => {
    if (!purchaseOrderSectionItems?.lumpsum) {
      return [];
    }
    // if (!filter?.length) {
    //   setIsActiveTable([
    //     purchaseOrderSectionItems?.lumpsum?.length > 0 ? "1" : "0",
    //   ]);
    //   return purchaseOrderSectionItems?.lumpsum ?? [];
    // }
    const filterSet = new Set(filter);
    const returnData = purchaseOrderSectionItems?.lumpsum
      ?.filter((el) =>
        filter?.length == 0 ? true : filterSet?.has(el?.item_type_key)
      )
      ?.sort((a, b) => a?.purchase_order_item_no - b?.purchase_order_item_no);
    if (!filter?.length || returnData.length > 0) {
      setIsActiveTable([returnData?.length > 0 ? "1" : "0"]);
    }
    return returnData;
  }, [filter, purchaseOrderSectionItems?.lumpsum]);

  const handleDeliveredAllClick = async () => {
    const isDelivered = purchaseOrderSectionItems?.lumpsum?.every(
      (item) => item?.delivered_quantity == item?.quantity
    );
    if (isDelivered) return;
    const updatedItems = purchaseOrderSectionItems?.lumpsum?.map((item) => ({
      delivered_quantity: item?.quantity,
      item_id: item?.item_id,
      reference_item_id: item?.reference_item_id,
      purchase_order_item_no: item?.purchase_order_item_no,
    }));

    if (updatedItems) {
      const apiRes = await dispatch(
        updatePurchaseOrderItem({
          purchase_order_id: Number(purchase_order_id),
          items: updatedItems,
        })
      );
      const response = apiRes.payload as IPOItemsApiRes;
      if (response?.success) {
        const detail = response?.data?.detail;
        if (Object.keys(detail || {})?.length > 0) {
          if (
            detail?.billing_status_key !=
            purchaseOrderDetail?.billing_status_key
          ) {
            dispatch(updatePODetail(detail));
          }
        }
        if (response?.data?.items?.length) {
          dispatch(
            updatePOItemsBulk({
              sectionKey: keysOfItem.LUMPSUM.ITEM,
              updatedItems: response?.data?.items,
              // sectionID: work_order_id,
            })
          );
        }

        if (response?.po_status_message) {
          notification.error({
            description: response?.po_status_message,
          });
        }
        // dispatch(
        //   updatePOTableAllItems({
        //     sectionName: "lumpsum",
        //     payload: response?.data,
        //   })
        // );
      } else {
        notification.error({
          description: response?.message,
        });
      }
    }
  };
  const updateItemField = async ({
    itemId,
    updatedItem,
    itemData,
  }: {
    itemId: number;
    updatedItem: Partial<IPOItemData>;
    itemData?: IPOItemData;
  }) => {
    // if unit and quantity is updated then send total value to backend
    const updatedItemKeys = Object.keys(updatedItem);
    if (updatedItemKeys.includes("apply_global_tax")) {
      setIsLoadingCheckBox((prev) => ({ ...prev, [itemId]: true }));
    }
    if (
      itemData &&
      (updatedItemKeys.includes("quantity") ||
        updatedItemKeys.includes("unit_cost"))
    ) {
      const itemDatas = {
        ...itemData,
        ...updatedItem,
      };
      const baseTotal = parseFloat(
        (Number(itemDatas?.unit_cost) * Number(itemDatas?.quantity)).toFixed(2)
      );
      updatedItem["total"] = baseTotal?.toString();
    }
    dispatch(
      updatePOItemWithID({
        itemId,
        updatedItem,
        // sectionID: itemData?.work_order_id,
        sectionKey: keysOfItem.LUMPSUM.ITEM,
      })
    );
    // dispatch(
    //   updatePOTableGetOneItem({
    //     // sectionId: singleSection?.section_id,
    //     sectionName: "lumpsum",
    //     itemId,
    //     updatedItem,
    //   })
    // );
    const apiRes = await dispatch(
      updatePurchaseOrderItem({
        purchase_order_id: Number(purchase_order_id),
        items: [
          {
            ...updatedItem,
            item_id: Number(itemId),
          },
        ],
      })
    );
    const response = apiRes.payload as IPOItemsApiRes;
    if (response?.success) {
      const detail = response?.data?.detail;
      if (Object.keys(detail || {})?.length > 0) {
        if (
          detail?.billing_status_key != purchaseOrderDetail?.billing_status_key
        ) {
          dispatch(updatePODetail(detail));
        }
      }
      // await dispatch(getEstimateItems({ estimate_id }));
      // const isBillGenerated = response?.data?.every(
      //   (item) => Number(item?.quantity) == Number(item?.billed_quantity)
      // );
      // dispatch(
      //   updatePODetail({
      //     is_billed: isBillGenerated ? "1" : "0",
      //   })
      // );
      if (response?.po_status_message) {
        notification.error({
          description: response?.po_status_message,
        });
      }
      setIsLoadingCheckBox((prev) => ({ ...prev, [itemId]: false }));
    } else {
      dispatch(
        updatePOItemWithID({
          itemId,
          updatedItem: itemData ?? {},
          // sectionID: itemData?.work_order_id,
          sectionKey: keysOfItem.LUMPSUM.ITEM,
        })
      );
      // dispatch(
      //   updatePOTableGetOneItem({
      //     // sectionId: singleSection?.section_id,
      //     sectionName: "lumpsum",
      //     itemId,
      //     updatedItem: itemData,
      //   })
      // );
      notification.error({
        description: response?.po_status_message
          ? response?.po_status_message
          : response?.message,
      });
      setIsLoadingCheckBox((prev) => ({ ...prev, [itemId]: false }));
    }
  };

  // const DeliveredHeader = () => {
  //   return (
  //     <div className="text-right w-[120px]">
  //       <Typography className="text-[#181d1f] font-semibold dark:text-white/90">
  //         {_t("Delivered")} <br />
  //         <Typography className="text-[#181d1f] text-xs font-semibold flex justify-end dark:text-white/90 cursor-pointer">
  //           {_t("(ALL)")}
  //           <div className="flex cursor-pointer relative z-10">
  //             <Tooltip
  //               title={_t(
  //                 "Click on (All) to set delivered quantity from QTY field for all item"
  //               )}
  //             >
  //               <FontAwesomeIcon
  //                 className="w-[13px] h-[15px] ml-1 text-[#21252966]"
  //                 icon="fa-solid fa-circle-info"
  //               />
  //             </Tooltip>
  //           </div>
  //         </Typography>
  //       </Typography>
  //     </div>
  //   );
  // };
  const handleDragEnd = async (
    event: RowDragEvent<IPOItemData, IPOItemData>
  ) => {
    if (!purchase_order_id || !Number(purchase_order_id)) {
      return;
    }
    const currentOrder: IPOItemData[] = [];
    event?.api?.forEachNode((node) => {
      if (node?.data) {
        currentOrder.push({ ...node.data });
      }
    });

    // Step 2: Extract and sort original order_numbers
    const originalOrderNumbers = currentOrder
      .map((item) => item?.purchase_order_item_no)
      .sort((a, b) => a - b); // e.g. [2, 5, 9, 10]

    // Step 3: Assign the sorted order_numbers to items based on new visual order
    const items = currentOrder.map((item, index) => ({
      item_id: item.item_id,
      order_number: originalOrderNumbers[index],
    }));

    const itemsData = currentOrder.map((item, index) => ({
      ...item,
      purchase_order_item_no: originalOrderNumbers[index],
    }));
    // Get all items in their new order
    // const items: { item_id: number; order_number: number }[] = [];
    // event?.api?.forEachNode((node, index) => {
    //   if (!node?.data?.item_id) {
    //     return;
    //   }
    //   items.push({
    //     item_id: node.data.item_id,
    //     order_number: index + 1,
    //   });
    // });

    // Call the new API with the correct payload format
    const apiRes = await dispatch(
      updatePurchaseOrderItemOrder({
        purchase_order_id: Number(purchase_order_id),
        items: items,
      })
    );
    const response = apiRes.payload as IPOItemsApiRes;

    if (response?.success) {
      dispatch(
        updatePOItemsBulk({
          sectionKey: keysOfItem.LUMPSUM.ITEM,
          updatedItems: itemsData,
        })
      );
      if (response?.po_status_message) {
        notification.error({
          description: response?.po_status_message,
        });
      }
    } else {
      notification.error({
        description: response?.message,
      });
    }
  };

  const handleDeleteItem = async (data: IPOItemData) => {
    handleDeleteItemOpen({
      itemData: data,
      sectionKey: keysOfItem.LUMPSUM.ITEM,
    });
  };

  return (
    <div className="grid gap-2.5">
      <CollapseSingleTable
        title={_t(`Lump Sum Items`)}
        // defaultActiveKey={filteredPurchaseOrderData?.length > 0 ? [1] : []}
        activeKey={isActiveTable}
        onChange={(key: string | string[]) => {
          setIsActiveTable(Array.isArray(key) ? key : [key]);
        }}
        rightsideContant={
          <div className="flex items-center gap-1.5 mr-2 bg-primary-8 text-primary-900 py-[3px] px-[9px] rounded-sm dark:bg-dark-800 dark:text-white/90 w-fit whitespace-nowrap">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-duotone fa-solid fa-money-check-dollar"
            />
            <Typography className="!mb-0 !text-sm !text-primary-900 leading-4 !font-semibold dark:!text-white/90 whitespace-nowrap">
              {
                formatter(
                  formatAmount(POMatrix?.LumpsumMatrix?.grandTotal ?? 0)
                ).value_with_symbol
              }
            </Typography>
          </div>
        }
        children={
          <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
            <div className="ag-theme-alpine double-line-header">
              <StaticTable
                className="static-table"
                columnDefs={getCommonColumns({
                  _t,
                  isReadOnly,
                  isParentReadOnly,
                  updateItemField: updateItemField,
                  filteredCodeCostData,
                  // DeliveredHeader,
                  setItemsData,
                  // setPurchaseOrderItem: () => {},
                  formatter,
                  inputFormatter,
                  isLoadingCheckBox,
                  // handleDeleteSectionItem,
                  // handleDeleteItemOpen,
                  // handleDeleteItemClose,
                  handleDeleteItem,
                  handleViewItem,
                  handleAllClick: handleDeliveredAllClick,
                  sectionName: "lumpsum",
                  rowData: filteredPurchaseOrderData,
                  ViewItemsKey: {
                    sectionKey: "lumpsum",
                  },
                  isTaxEnabled,
                })}
                rowDragManaged={!isReadOnly}
                rowData={filteredPurchaseOrderData}
                animateRows={true}
                stopEditingWhenCellsLoseFocus={true}
                suppressContextMenu={true}
                suppressDragLeaveHidesColumns={true}
                suppressMoveWhenRowDragging={true}
                suppressRowClickSelection={true}
                onRowDragEnd={(e) => handleDragEnd(e)}
                noRowsOverlayComponent={() => (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                  />
                )}
              />
            </div>
          </div>
        }
      />
    </div>
  );
};

export default LumpSumItems;
