import { useTranslation } from "~/hook";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { NoRecords } from "~/shared/components/molecules/noRecords";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { KeyboardEvent, useCallback, useEffect, useState } from "react";
import { useAppPODispatch, useAppPOSelector } from "../../../../redux/store";
import { sanitizeString } from "~/helpers/helper";
import debounce from "lodash/debounce";
import { generateBillApi } from "../../../../redux/action/PODetailAction";
import { useNavigate, useParams } from "@remix-run/react";
import { routes } from "~/route-services/routes";
import { getPOItems } from "../../../../redux/action/POItemAction";
import { updatePOIsBillGenerate } from "../../../../redux/slices/poItemsSlice";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { onKeyDownCurrency } from "~/shared/utils/helper/common";
import InputNumberField from "~/shared/components/molecules/inputNumberField/InputNumberField";

const GenerateBill = ({
  generateBill,
  setGenerateBill,
  pId,
  isDashboard,
}: IGenerateBillProps) => {
  const { _t } = useTranslation();
  const { id: purchase_order_id } = useParams();
  const navigate = useNavigate();
  const { inputFormatter, unformatted, formatter } = useCurrencyFormatter();
  const { isPOItemsLoading, purchaseOrderItems } = useAppPOSelector(
    (state) => state?.purchaseOrderItems
  );
  const { getGlobalModuleByKey } = useGlobalModule();
  const moduleBill = getGlobalModuleByKey(CFConfig?.bill_module);
  const billName =
    HTMLEntities.decode(sanitizeString(moduleBill?.module_name)) || "Bill";
  const dispatch = useAppPODispatch();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [itemList, setItemList] = useState<{
    [key: number]: IPOItemData & {
      amount: string;
      calQuantity: number;
      fixQuantity: number;
      remaining_quantity: number;
    };
  }>({});

  useEffect(() => {
    if (!isDashboard) return;
    dispatch(getPOItems({ purchase_order_id: pId }));
  }, []);

  const AmountCalculation = (
    quantity: number,
    // billed_quantity: number,
    unit_cost: number
  ) => {
    const totalAmount = (quantity * (Number(unit_cost) || 0)) / 100;
    const total = formatter(
      Number(totalAmount) == 0
        ? Number(totalAmount)?.toFixed(0)
        : Number(totalAmount)?.toFixed(2)
    )?.value_with_symbol;
    return total;
  };

  useEffect(() => {
    if (purchaseOrderItems) {
      const items = purchaseOrderItems.reduce(
        (
          acc: {
            [key: number]: IPOItemData & {
              amount: string;
              calQuantity: number;
              fixQuantity: number;
            };
          },
          item
        ) => {
          acc[item?.item_id] = {
            billed_quantity: item?.billed_quantity,
            subject: item?.subject,
            item_id: item?.item_id,
            quantity: item?.quantity,
            calQuantity:
              item?.quantity != 0 ? item?.quantity - item?.billed_quantity : 0,
            fixQuantity: item?.quantity,
            unit_cost: item?.unit_cost,
            remaining_quantity: item?.quantity - (item?.billed_quantity || 0),
            amount: AmountCalculation(
              item?.quantity != 0
                ? item?.quantity - (item?.billed_quantity || 0)
                : 0,
              // item?.billed_quantity,
              +(item?.unit_cost ?? 0)
            ),
          };
          return acc;
        },
        {}
      );
      setItemList(items);
    }
  }, [purchaseOrderItems]);

  //if debouncing using increase delay
  const debouncedHandleQuantityChange = useCallback(
    debounce((item_id: number, value: number) => {
      setItemList((prev) => ({
        ...prev,
        [item_id]: {
          ...prev[item_id],
          calQuantity: value,
          amount: AmountCalculation(
            value,
            // prev[item_id]?.billed_quantity,
            +(prev[item_id]?.unit_cost ?? 0)
          ),
        },
      }));
    }, 0),
    []
  );

  // const handleQuantityChange = (item_id: number, value: number) => {
  //   setItemList((prev) => ({
  //     ...prev,
  //     [item_id]: {
  //       ...prev[item_id],
  //       calQuantity: value,
  //     },
  //   }));
  // };
  const handleQuantityChange = (item_id: number, value: number) => {
    debouncedHandleQuantityChange(item_id, value);
  };

  const handleGenerateBill = async () => {
    try {
      setIsLoading(true);
      const response = (await generateBillApi({
        purchase_order_id: pId,
        billing_items: Object.values(itemList).map((item) => ({
          item_id: item?.item_id,
          billing_qty: item?.calQuantity,
        })),
      })) as {
        success: boolean;
        data?: { bill_id?: number };
        message?: string;
      };
      if (response?.success) {
        if (response?.data?.bill_id) {
          navigate(`/${routes.BILLS.url}/${response?.data?.bill_id}`);
          setGenerateBill(false);
          setIsLoading(false);
        }
      } else {
        notification.error({
          description: response?.message,
        });
        setIsLoading(false);
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message,
      });
      setIsLoading(false);
      // setIsConfirmOpen(false);
    } finally {
      // setIsConfirmOpen(false);
    }
  };

  const rowData = purchaseOrderItems
    ?.map((item) => ({
      ...itemList[item?.item_id],
    }))
    ?.sort((a, b) => a?.purchase_order_item_no - b?.purchase_order_item_no);

  return (
    <Drawer
      open={generateBill}
      // loading={isPOItemsLoading}
      rootClassName="drawer-open"
      width={718}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-file-invoice-dollar"
            />
          </div>
          <Header
            level={5}
            className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
          >
            {_t(`Generate a ${billName}`)}
          </Header>
        </div>
      }
      closeIcon={
        <CloseButton
          onClick={() => {
            dispatch(updatePOIsBillGenerate(false));
            setGenerateBill(false);
          }}
        />
      }
    >
      <div className="py-4">
        <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
          {isPOItemsLoading ? (
            <Spin className="w-full h-20 flex items-center justify-center" />
          ) : (
            <div className="grid gap-4">
              <SidebarCardBorder>
                <Typography className="text-sm text-primary-900">
                  {_t(
                    "Select the quantity for each item that should be recorded on the Bill."
                  )}
                </Typography>
                <div className="p-2 common-card mt-4">
                  <div className="ag-theme-alpine">
                    <StaticTable
                      className="static-table"
                      rowData={rowData}
                      columnDefs={[
                        {
                          headerName: "QTY",
                          field: "quantity",
                          maxWidth: 70,
                          minWidth: 70,
                          suppressMenu: true,
                          headerClass: "ag-header-center",
                          cellRenderer: (params: {
                            data: IPOItemData & {
                              fixQuantity: number;
                              calQuantity: number;
                            };
                          }) => {
                            const { data } = params;
                            const quantity = data?.quantity || 0;
                            const billed_Quantity = data?.billed_quantity || 0;
                            const total_Quantity =
                              quantity == 0 ? 0 : quantity - billed_Quantity;
                            const fixedTotal =
                              data?.fixQuantity == 0
                                ? 0
                                : data?.fixQuantity - billed_Quantity;
                            return (
                              <InputField
                                fieldClassName="before:hidden"
                                applyBorder={true}
                                defaultValue={data?.calQuantity}
                                formInputClassName="!w-11 inline-block ml-2"
                                className="!border-[#DFE2E7] bg-[#EFF1F3] !rounded !text-13 !px-1.5 !py-1 !h-5 text-center"
                                disabled={total_Quantity == 0}
                                onChange={(e) => {}}
                                onInput={(e) => {
                                  const input = e?.target as HTMLInputElement;
                                  if (
                                    Number(input?.value) > Number(fixedTotal)
                                  ) {
                                    input.value = fixedTotal?.toString();
                                    notification.error({
                                      message: `Please enter a QTY between or equal to 0 and ${total_Quantity}.`,
                                      description: "",
                                    });
                                  } else {
                                    input.value = input?.value?.replace(
                                      /[^0-9.]/g,
                                      ""
                                    );
                                  } // Allow only numbers and decimal
                                  const parts = input?.value?.split(".");
                                  if (parts?.length > 2) {
                                    input.value = parts?.[0] + "." + parts?.[1]; // Ensure only one decimal point
                                  } else if (parts?.[1]?.length > 2) {
                                    input.value =
                                      parts?.[0] + "." + parts?.[1].slice(0, 2); // Limit to 2 decimal places
                                  }
                                }}
                                onBlur={(e) => {
                                  const value = Number(e.target.value);
                                  if (value < 0 || value > fixedTotal) {
                                    notification.error({
                                      message: `Please enter a QTY between or equal to 0 and ${total_Quantity}.`,
                                      description: "",
                                    });
                                    handleQuantityChange(
                                      data.item_id,
                                      data?.calQuantity
                                    );
                                  } else {
                                    handleQuantityChange(data?.item_id, value);
                                  }
                                }}
                              />
                            );
                          },
                        },
                        {
                          headerName: _t("Item Name"),
                          field: "subject",
                          minWidth: 150,
                          flex: 2,
                          cellClass: "ag-cell-left",
                          headerClass: "ag-header-left",
                          cellRenderer: (params: { data: IPOItemData }) => {
                            const { data } = params;
                            const subject = HTMLEntities.decode(
                              sanitizeString(data?.subject)
                            );
                            return subject ? (
                              <Tooltip title={subject}>
                                <Typography className="table-tooltip-text">
                                  {subject}
                                </Typography>
                              </Tooltip>
                            ) : (
                              "-"
                            );
                          },
                        },
                        {
                          headerName: _t("Unit Cost"),
                          field: "unit_cost",
                          minWidth: 80,
                          maxWidth: 80,
                          cellClass: "ag-cell-right",
                          headerClass: "ag-header-right",
                          cellRenderer: (params: { data: IPOItemData }) => {
                            const { data } = params;
                            const uValue = Number(data?.unit_cost) / 100;
                            const unitCost = formatter(
                              Number(uValue) == 0
                                ? Number(uValue)?.toFixed(0)
                                : Number(uValue)?.toFixed(2)
                            )?.value_with_symbol;
                            return unitCost ? (
                              <Tooltip title={unitCost}>
                                <Typography className="table-tooltip-text">
                                  {unitCost}
                                </Typography>
                              </Tooltip>
                            ) : (
                              "-"
                            );
                          },
                        },
                        {
                          headerName: _t("Total"),
                          field: "amount",
                          minWidth: 100,
                          maxWidth: 100,
                          cellClass: "ag-cell-right",
                          headerClass: "ag-header-right",
                          cellRenderer: (params: { data: IPOItemData }) => {
                            const { data } = params;
                            return data?.amount ? (
                              <Tooltip title={data?.amount}>
                                <Typography className="table-tooltip-text">
                                  {data?.amount}
                                </Typography>
                              </Tooltip>
                            ) : (
                              "-"
                            );
                          },
                        },
                        {
                          headerName: _t("Billed #"),
                          field: "billed_quantity",
                          minWidth: 80,
                          maxWidth: 80,
                          cellClass: "ag-cell-right",
                          headerClass: "ag-header-right",
                          cellRenderer: (params: { data: IPOItemData }) => {
                            const { data } = params;
                            const billed = Number(data?.billed_quantity || 0);
                            return (
                              <Tooltip title={!!billed ? billed : ""}>
                                <Typography className="table-tooltip-text">
                                  {billed || "-"}
                                </Typography>
                              </Tooltip>
                            );
                          },
                        },
                        {
                          headerName: _t("Remaining #"),
                          field: "remaining_quantity",
                          minWidth: 100,
                          maxWidth: 100,
                          cellClass: "ag-cell-right",
                          headerClass: "ag-header-right",
                          cellRenderer: (params: { data: IPOItemData }) => {
                            const { data } = params;
                            const remaining = Number(
                              data?.remaining_quantity || 0
                            );
                            return (
                              <Tooltip title={!!remaining ? remaining : ""}>
                                <Typography className="table-tooltip-text">
                                  {!!remaining ? remaining : "-"}
                                </Typography>
                              </Tooltip>
                            );
                          },
                        },
                      ]}
                      noRowsOverlayComponent={() => (
                        <NoRecords
                          image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                        />
                      )}
                    />
                  </div>
                </div>
              </SidebarCardBorder>
            </div>
          )}
        </div>

        <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
          <PrimaryButton
            type="primary"
            className="w-full justify-center primary-btn"
            htmlType="submit"
            buttonText={_t("Generate")}
            onClick={handleGenerateBill}
            isLoading={isLoading}
            disabled={isPOItemsLoading || rowData?.length == 0 || isLoading}
          />
        </div>
      </div>
    </Drawer>
  );
};

export default GenerateBill;
