import { createAsyncThunk } from "@reduxjs/toolkit";
import { handleApiCatchError } from "~/helpers/helper";
import { projectRoutes } from "~/route-services/project.routes";
import { apiRoutes } from "~/route-services/routes";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";

export const fetchGanttScheduleTasks = createAsyncThunk(
  "proSchedule/fetchGanttScheduleTasks",
  async (paramsData: IGetScheduleTasksApiParams) => {
    try {
      const data = await getWebWorkerApiParams({
        otherParams: {
          ...paramsData,
        },
      });

      const response = (await webWorkerApi({
        url: projectRoutes.get_schedule_tasks,
        method: "post",
        data: data,
      })) as IGetScheduleTasksApiRes;
      return response;
    } catch (error) {
      return handleApiCatchError(error as Error);
    }
  }
);

export const updateGanttScheduleTasks = async (
  paramsData: IUpdateScheduleTaskApiParams
) => {
  try {
    const data = await getWebWorkerApiParams({
      otherParams: {
        ...paramsData,
      },
    });

    const response = (await webWorkerApi({
      url: projectRoutes.update_schedule_tasks,
      method: "post",
      data: data,
    })) as IUpdateScheduleTaskApiRresponse;
    return response;
  } catch (error) {
    return handleApiCatchError(error as Error);
  }
};

export const updateScheduleSettings = async (
  paramsData: TUpdateScheduleSettingsApiParams
) => {
  try {
    const data = await getWebWorkerApiParams({
      otherParams: {
        ...paramsData,
      },
    });

    const response = (await webWorkerApi({
      url: projectRoutes.update_schedule_settings,
      method: "post",
      data: data,
    })) as DefaultResponse;
    return response;
  } catch (error) {
    return handleApiCatchError(error as Error);
  }
};

export const addSchduleMppData = async (
  paramsData: IAddSchduleMapDataApiParams
) => {
  try {
    const data = await getWebWorkerApiParams({
      otherParams: {
        ...paramsData,
      },
    });

    const response = (await webWorkerApi({
      url: projectRoutes.add_mpp_data,
      method: "post",
      data: data,
    })) as DefaultResponse;
    return response;
  } catch (error) {
    return handleApiCatchError(error as Error);
  }
};

export const sendScheduleReminder = async (
  paramsData: ISendScheduleReminderApiParams
) => {
  try {
    const data = await getWebWorkerApiParams({
      otherParams: {
        ...paramsData,
      },
    });

    const response = (await webWorkerApi({
      url: projectRoutes.send_reminder,
      method: "post",
      data: data,
    })) as DefaultResponse;
    return response;
  } catch (error) {
    return handleApiCatchError(error as Error);
  }
};

export const fetchProjectListApi = createAsyncThunk(
  "fetchProjectList/fetchData",
  async (params: Partial<SetProjectsParams>) => {
    try {
      const data = await getWebWorkerApiParams({
        otherParams: {
          ...params,
        },
      });
      const response = (await webWorkerApi({
        url: apiRoutes.GET_PROJECTS.url,
        method: "post",
        data,
      })) as IProjectListApiRes;
      return response || { success: false, message: "Somthing went wrong!" };
    } catch (error) {
      return handleApiCatchError(error as Error);
    }
  }
);

export const copyExistingSchedule = async (
  paramsData: ICopyExistingScheduleApiParams
) => {
  try {
    const data = await getWebWorkerApiParams({
      otherParams: {
        ...paramsData,
      },
    });

    const response = (await webWorkerApi({
      url: projectRoutes.copy_existing_schedule,
      method: "post",
      data: data,
    })) as DefaultResponse;
    return response;
  } catch (error) {
    return handleApiCatchError(error as Error);
  }
};

export const deleteProjectSchedule = async (paramsData: {
  project_id: string;
}) => {
  try {
    const data = await getWebWorkerApiParams({
      otherParams: {
        ...paramsData,
      },
    });

    const response = (await webWorkerApi({
      url: projectRoutes.delete_project_schedule,
      method: "post",
      data: data,
    })) as DefaultResponse;
    return response;
  } catch (error) {
    return handleApiCatchError(error as Error);
  }
};
