import { createAsyncThunk } from "@reduxjs/toolkit";
import isEmpty from "lodash/isEmpty";
import { customersDataLimit } from "~/components/sidebars/multi-select/customer/zustand/utils";
import {
  escapeHtmlEntities,
  getApiDefaultParams,
  sanitizeAndRemoveSpaceString,
} from "~/helpers/helper";
import { directoryRoutes } from "~/route-services";
import { apiRoutes } from "~/route-services/routes";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";

export const getCustomerDetails = async ({
  tab,
  search,
  directoryId,
  page,
  is_favorite,
  formState,
  setFormErrorsState,
  formErrorsState,
  projectId,
  globalProject,
  additionalContactDetails = 1,
  selected_users,
  app_access,
}: ISendEmailActionProps) => {
  try {
    let res = null;
    if (formState) {
      const addData = {
        ...(formState || {}),
        type: directoryId,
        from_global_call: 1,
      };

      res = (await webWorkerApi<IgetTagsRes>({
        url: directoryRoutes.ADD_DIRECTORY.url,
        method: "post",
        data: addData,
      })) as ISendEmailActionRes;
      if (!isEmpty(res?.message)) {
        if (res?.success === "0") {
          if (
            res?.message === "First/Last Name or Company Name must be defined."
          ) {
            setFormErrorsState?.({
              ...formErrorsState,
              first_name: "First/Last Name or Company Name must be defined.",
              last_name: "First/Last Name or Company Name must be defined.",
              company_name: "First/Last Name or Company Name must be defined.",
            });
          }
        }
      }
    }

    let keyToConsider =
      tab === "my_project"
        ? "additionalContactDetails"
        : "additional_contact_details";
    const limit = res ? 1 : customersDataLimit;
    const data = await getApiDefaultParams({
      globalProject,
      otherParams: {
        filter: {
          status: "0",
          selected_users: selected_users,
        },
        is_favorite: is_favorite || undefined,
        // additional_contact_details: additionalContactDetails,
        [keyToConsider]: additionalContactDetails,
        search: search
          ? HTMLEntities.encode(sanitizeAndRemoveSpaceString(search))
          : undefined,
        group_details: tab === "my_crew" ? 1 : 0,
        directories: res?.data.user_id,
        ...(tab !== "my_project" && { directory: directoryId }),
        start: page * customersDataLimit,
        sales_address_details: 1,
        limit: limit,
        service_details: tab === "by_service" ? 1 : undefined,
        contacts_on_demand: formState ? 1 : undefined,
        projectId: projectId,
        app_users: app_access,
      },
    });
    const response = (await webWorkerApi<IgetTagsRes>({
      url:
        tab === "my_project"
          ? apiRoutes.GET_GLOBAL_PROJECT_CONTACTS.url
          : apiRoutes.COMMON.get_global_directory,
      method: "post",
      data: data,
    })) as IGetGlobalDirectoryApiResponse;
    return { ...response, limit };
  } catch (error) {
    return error;
  }
};
