interface IGetScheduleTasksApiParams {
  project?: string;
  module_key: string;
  [key: string]: any;
}

interface IGetScheduleTasksApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: TGanttTaskTypes[];
  collections: IGanttApiCollection;
  company_date_format: string;
  company_schedule_weekend: string | number;
}

interface IGanttScheduleInitialState {
  schedulerTab: string;
  projectId: string;
  tasks: TGanttTaskTypes[];
  links: TGanttLink[];
  projectList: Partial<IGetProjectsApiResponseDataProject>[];
  isGanttDataLoading: boolean;
  isGanttDataLoaded: boolean;
  settings: IGanttSetting;
  schedule_projects: IScheduleProjects[];
  company_date_format: string;
  holidays: IGanttHoliday[];
  ganttPreviousState: {
    data: TGanttTaskTypes[];
    links: TGanttLink[];
  };
}

interface IGanttApiCollection {
  holidays: IGanttHoliday[];
  links: TGanttLink[];
  schedule_projects: IScheduleProjects[];
  settings: IGanttSetting;
}

interface IGanttHoliday {
  id: number;
  title: string;
  holiday: string;
  company_id: number;
  user_id: number;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  formatted_holiday: string;
}

interface IGanttTaskContact {
  user_id: string | number;
  company_name: string;
  user_type: string | number;
  assignee_name: string;
  assigned_to_name_only: string;
  assignee_company: string;
  email: string;
  user_image: string | null;
}

interface IGanttTask {
  wbs: string;
  sortorder: string;
  assigned_to: string;
  assignees_to: IGanttTaskContact[];
  task_user_id: string;
  task_users: IGanttTaskContact[];
  temp_end_date: string;
  description: string;
  project_id: string | number;
  planned_start: string | Date | null;
  planned_end: string | Date | null;
  planned_duration: string | number | null;
  prepared_by_name: string;
  assignee_name: string;
  task_user_name: string;
  send_reminder: string | number;
  start_date_only: string;
  parent: string | number;
  color: string;
  priority: string;
  full_end_date: string;
  end_date_only: string;
  end_date: Date | string;
  contractors: Partial<TselectedContactSendMail>[];
  employees: Partial<TselectedContactSendMail>[];
}

interface IGanttSetting {
  company_id: boolean;
  critical_path: boolean;
  auto_scheduling: boolean;
  slack: boolean;
  baseline: boolean;
  date_added: string;
  date_modified: string;
  show_wbs_column: boolean;
  show_task_column: boolean;
  show_start_date_column: boolean;
  show_end_date_column: boolean;
  show_progress_column: boolean;
  show_duration_column: boolean;
  show_new_option_column: boolean;
  show_predecessors_column: boolean;
  zoom_to_fit: boolean;
  exclude_weekend_days: number;
}

interface IScheduleProjects {
  id: string;
  label: string;
  value: string;
  project_names: string;
}

interface IUpdateScheduleTaskApiParams {
  project_id: string;
  gantt_data: {
    count?: number;
    tasks?: IUpdateTaskEntity[];
    links?: IUpdateLinkEntity[];
  };
}

interface IUpdateGanttDataProps
  extends Omit<TGanttTaskTypes, "start_date" | "end_date"> {
  start_date: string;
  end_date: string;
}

interface IUpdateTaskEntity {
  entity: "task" | "project" | "milestone" | string;
  action: string;
  data: Partial<TGanttTaskTypes>;
  request: Partial<TGanttTaskTypes>;
  id?: string | number;
}

interface IUpdateLinkEntity {
  entity: "link" | string;
  action: string;
  data: TGanttLink;
  id?: string | number;
  request?: Partial<Link>;
}

type TUpdateScheduleSettingsApiParams = { option: string } & {
  [key in keyof IGanttSetting]?: number;
};

interface IUpdateScheduleTaskApiRresponse extends Omit<IDefaultAPIRes, "data"> {
  data: {
    count?: number;
    tasks?: IUpdateTaskEntity[];
    links?: IUpdateLinkEntity[];
  };
}

interface RawData {
  Start: string;
  Finish: string;
  Duration: number;
  ActualStart: string;
  ActualFinish: string | null;
  ActualDuration: number | null;
  Work: string | null;
}

interface MppTask {
  id: string;
  text: string;
  start_date: string;
  duration: string;
  progress: number;
  open: boolean;
  parent: string;
  $custom_data: any;
  resource: any[];
  $raw: RawData;
}

interface MppLink {
  id: string;
  source: string;
  target: string;
  type: string;
  lag: string | number;
  $lag_unit: string;
}

interface IAddSchduleMapDataApiParams {
  project_id: number;
  mpp_data: MppTask[];
  mpp_links: MppLink[];
}

interface IImportMSproConfig {
  duration_unit: string;
  custom_duration_unit: string;
  root_id: string;
  $custom_data: any;
  exportMethod: any;
}

interface IImportFromMSProData {
  data: MppTask[];
  links: MppLink[];
}

interface IImportFromMSPro {
  $APIVersion?: string;
  data: IImportFromMSProData;
  config: IImportMSproConfig;
}

interface ISendScheduleReminderApiParams {
  notification_users: string[];
}

interface ICopyExistingScheduleApiParams {
  project_from: number;
  project_to: number;
}
